{"fromVersion": "59.0.6", "toVersion": "59.0.7", "gitHead": "7cb696f6d1dc19041eccb89aaedc17156e334b48", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%, update_tick=%->%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id(), OLD._update_tick, NEW._update_tick;", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id();", "            END IF;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW), new_update_tick = NEW._update_tick", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%, updateTick=%->%', p_root_table_name, NEW._id, pg_current_xact_id(), log_record.old_update_tick, NEW._update_tick;", "            ELSE", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW)", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%', p_root_table_name, NEW._id, pg_current_xact_id();", "            END IF;", "        END IF;", "", "        -- do not send a notification for the update if the record was created in the same transaction", "        -- this will happen with nodes with deferred saves (sales order for instance)", "        IF (p_root_table_name = TG_TABLE_NAME) AND (log_record IS NULL) THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.report_variable ADD COLUMN IF NOT EXISTS is_main_reference BOOL;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.report_variable.is_main_reference IS '{", "  \"type\": \"boolean\",", "  \"isSystem\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.report_variable AS t0 SET is_main_reference=$1 WHERE ((t0.is_main_reference IS NULL))", "args": [false], "actionDescription": "Auto data action for property ReportVariable.isMainReference"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.report_variable AS t0 SET is_mandatory=$1 WHERE ((t0.is_mandatory IS NULL))", "args": [false], "actionDescription": "Auto data update action for column report_variable.is_mandatory (replace null values)"}, {"isSysPool": false, "sql": ["", "            UPDATE %%SCHEMA_NAME%%.report_variable rv", "            SET", "                is_main_reference = true,", "                is_mandatory = true", "            WHERE rv.type = 'reference' ::%%SCHEMA_NAME%%.meta_property_type_enum", "            AND (", "                SELECT count(*)", "                FROM %%SCHEMA_NAME%%.report_variable rv2", "                WHERE", "                rv2._tenant_id = rv._tenant_id", "                AND rv2.report = rv.report", "                AND rv2.type = 'reference' ::%%SCHEMA_NAME%%.meta_property_type_enum", "            ) = 1;", "        "], "actionDescription": ""}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.report_variable ALTER COLUMN is_mandatory SET NOT NULL, ALTER COLUMN is_main_reference SET NOT NULL;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Tags feature", "released", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Revoke full access", "released", false, "@sage/xtrem-authorization", true, "authRevokeFullAccessServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Auditing feature", "released", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Workflow feature", "released", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable asyncPerPostProcessing print engine feature", "experimental", false, "@sage/xtrem-reporting", false, "asyncPrePostProcessing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops002U6L-82319\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories Role,RoleActivity,Report,ReportVariable"}, {"action": "reload_setup_data", "args": {"factory": "Role"}}, {"action": "reload_setup_data", "args": {"factory": "RoleActivity"}}, {"action": "reload_setup_data", "args": {"factory": "Report"}}, {"action": "reload_setup_data", "args": {"factory": "ReportVariable"}}], "data": {"Role": {"metadata": {"rootFactoryName": "Role", "name": "Role", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string"}, {"name": "id", "type": "string"}, {"name": "is_billing_role", "type": "boolean"}]}, "rows": [["sage", "Y", "{\"en\":\"Administrator - Technical\"}", null, "Admin - Technical", "N"], ["sage", "Y", "{\"en\":\"Administrator\"}", null, "Admin", "N"], ["sage", "Y", "{\"en\":\"Support Access Read-only\"}", null, "Support User Read-only", "N"], ["sage", "Y", "{\"en\":\"Support Access\"}", null, "Support User", "N"], ["sage", "Y", "{\"en\":\"Operational User\"}", null, "Operational User", "Y"], ["sage", "Y", "{\"en\":\"Business User\"}", null, "Business User", "Y"]]}, "RoleActivity": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "RoleActivity", "name": "RoleActivity", "naturalKeyColumns": ["_tenant_id", "role", "activity"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "role", "type": "reference", "targetFactoryName": "Role"}, {"name": "activity", "type": "reference", "targetFactoryName": "Activity"}, {"name": "has_all_permissions", "type": "boolean"}, {"name": "permissions", "type": "stringArray"}, {"name": "is_active", "type": "boolean"}], "vitalParentColumn": {"name": "role", "type": "reference", "targetFactoryName": "Role"}}, "rows": [["sage", "100", "Support User", "role", "Y", "[]", "Y"], ["sage", "200", "Support User", "user", "Y", "[]", "Y"], ["sage", "300", "Support User", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Support User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Support User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Support User", "site", "Y", "[]", "Y"], ["sage", "21400", "Support User", "company", "Y", "[]", "Y"], ["sage", "21500", "Support User", "tenant", "Y", "[]", "Y"], ["sage", "21400", "Support User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21500", "Support User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "22200", "Support User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "22300", "Support User", "sysTag", "Y", "[]", "Y"], ["sage", "100", "Support User Read-only", "role", "N", "[\"read\"]", "Y"], ["sage", "200", "Support User Read-only", "user", "N", "[\"read\"]", "Y"], ["sage", "300", "Support User Read-only", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "400", "Support User Read-only", "groupRoleSite", "N", "[\"read\"]", "Y"], ["sage", "500", "Support User Read-only", "supportAccessHistory", "N", "[\"read\"]", "Y"], ["sage", "600", "Support User Read-only", "site", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "company", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "tenant", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "sysNotificationState", "N", "[\"read\"]", "Y"], ["sage", "22200", "Support User Read-only", "serviceOptionState", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "sysTag", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "role", "Y", "[]", "Y"], ["sage", "200", "Admin", "user", "Y", "[]", "Y"], ["sage", "300", "Admin", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Admin", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Admin", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Admin", "site", "Y", "[]", "Y"], ["sage", "700", "Admin", "tenant", "Y", "[]", "Y"], ["sage", "12400", "Admin", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "12200", "Admin", "company", "Y", "[]", "Y"], ["sage", "12500", "Admin", "sysNotificationState", "Y", "[]", "Y"], ["sage", "13800", "Admin", "serviceOptionState", "Y", "[]", "Y"], ["sage", "13900", "Admin", "sysTag", "Y", "[]", "Y"], ["sage", "18600", "Admin", "sysDeviceToken", "Y", "[]", "Y"], ["sage", "13500", "Business User", "role", "Y", "[]", "Y"], ["sage", "13600", "Business User", "user", "Y", "[]", "Y"], ["sage", "13700", "Business User", "siteGroup", "Y", "[]", "Y"], ["sage", "13800", "Business User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "13900", "Business User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "14200", "Business User", "tenant", "Y", "[]", "Y"], ["sage", "14100", "Business User", "company", "Y", "[]", "Y"], ["sage", "14150", "Business User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "14170", "Business User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "14180", "Business User", "site", "Y", "[]", "Y"], ["sage", "17200", "Business User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "80", "Operational User", "dashboardActivity", "Y", "[\"read\"]", "Y"], ["sage", "700", "Admin", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21100", "Support User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "1000", "Business User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21600", "Support User", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User Read-only", "customField", "N", "[\"read\"]", "Y"], ["sage", "13000", "Admin", "customField", "Y", "[]", "Y"], ["sage", "900", "Business User", "customField", "Y", "[]", "Y"], ["sage", "100", "Admin", "sysAuditLog", "Y", null, "Y"], ["sage", "100", "Admin", "workflowDefinition", "Y", null, "Y"], ["sage", "18700", "Admin", "workflowProcess", "Y", null, "Y"], ["sage", "700", "Admin", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "1500", "Admin - Technical", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "21100", "Support User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "1700", "Business User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "100", "Admin", "sysApp", null, "[\"read\",\"manage\"]", "Y"], ["sage", "100", "Admin", "sysSynchronization", null, "[\"read\",\"create\",\"update\",\"delete\",\"defaultInstance\"]", "Y"], ["sage", "100", "Admin", "sysEnumSynchronization", null, "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "18500", "Admin", "attachment", "Y", "[]", "Y"], ["sage", "20000", "Support User", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "20100", "Support User", "reportTemplate", "Y", "[]", "Y"], ["sage", "20200", "Support User", "report", "Y", "[]", "Y"], ["sage", "21300", "Support User", "reportResource", "Y", "[]", "Y"], ["sage", "20000", "Support User Read-only", "reportStyleVariable", "N", "[\"read\"]", "Y"], ["sage", "20100", "Support User Read-only", "reportTemplate", "N", "[\"read\"]", "Y"], ["sage", "20200", "Support User Read-only", "report", "N", "[\"read\"]", "Y"], ["sage", "21100", "Support User Read-only", "reportResource", "N", "[\"read\"]", "Y"], ["sage", "12600", "Admin", "reportResource", "Y", "[]", "Y"], ["sage", "12700", "Admin", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "12800", "Admin", "reportTemplate", "Y", "[]", "Y"], ["sage", "12900", "Admin", "report", "Y", "[]", "Y"], ["sage", "13000", "Admin", "reportAssignmentPage", "Y", "[]", "Y"], ["sage", "5200", "Admin", "sysEmailConfig", "Y", null, "Y"], ["sage", "100", "Admin", "endpoint", "Y", "[]", "Y"]]}, "Report": {"metadata": {"rootFactoryName": "Report", "name": "Report", "naturalKeyColumns": ["_tenant_id", "name"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "parent_package", "type": "string"}, {"name": "active_template", "type": "reference", "isNullable": true, "isOwnedByCustomer": true, "targetFactoryName": "ReportTemplate"}, {"name": "report_type", "type": "enum", "enumMembers": ["printedDocument", "email"]}, {"name": "is_factory", "type": "boolean"}, {"name": "printing_type", "type": "enum", "enumMembers": ["multiple", "single", "notApplicable"]}, {"name": "pre_processing_operation", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeOperation"}, {"name": "post_processing_operation", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeOperation"}]}, "rows": [["sage", "activeUsersListing", "List all active users", "xtrem-system", "", "printedDocument", "Y", "multiple", null, null], ["sage", "usersByType", "List users by type", "xtrem-system", "usersByType", "printedDocument", "Y", "multiple", null, null], ["sage", "reportDefinitions", "List contents of reports and their active templates", "xtrem-reporting", "", "printedDocument", "Y", "multiple", null, null], ["sage", "onboarding_tenant", "Onboarding email when new tenant created", "xtrem-system", "onboarding_tenant", "email", "Y", "multiple", null, null], ["sage", "onboarding_user", "Onboarding email when new user created", "xtrem-system", "onboarding_user", "email", "Y", "multiple", null, null]]}, "ReportVariable": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "ReportVariable", "name": "ReportVariable", "naturalKeyColumns": ["_tenant_id", "report", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "title", "type": "string", "isLocalized": true}, {"name": "is_mandatory", "type": "boolean"}, {"name": "type", "type": "enum", "enumMembers": ["boolean", "string", "byte", "short", "integer", "decimal", "float", "double", "enum", "date", "time", "datetime", "uuid", "binaryStream", "textStream", "json", "reference", "collection", "jsonReference", "integerArray", "enumA<PERSON>y", "referenceArray", "stringArray", "integerRange", "decimalRange", "date<PERSON><PERSON><PERSON>", "datetime<PERSON><PERSON><PERSON>"]}, {"name": "data_type", "type": "reference", "isNullable": true, "targetFactoryName": "MetaDataType"}, {"name": "report", "type": "reference", "targetFactoryName": "Report"}, {"name": "is_main_reference", "type": "boolean"}], "vitalParentColumn": {"name": "report", "type": "reference", "targetFactoryName": "Report"}}, "rows": [["sage", "100", "includeSystemUsers", "{\"base\":\"Include system users\"}", null, "boolean", null, "usersByType", null], ["sage", "100", "userName", "{\"base\":\"User name\"}", null, "string", null, "onboarding_user", null]]}}}