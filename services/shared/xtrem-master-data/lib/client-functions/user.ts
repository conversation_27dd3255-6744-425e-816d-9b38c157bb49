import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import type { FilteredUsers } from './interfaces/document';

export async function loadApprovers(page: ui.Page<GraphApi>): Promise<FilteredUsers[]> {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-system/User')
            .query(
                ui.queryUtils.edgesSelector(
                    { _id: true, email: true, firstName: true, lastName: true },
                    {
                        filter: {
                            ...authorizationFilters.user.activeApplicationUsers,
                            _not: { email: '<EMAIL>' },
                        },
                    },
                ),
            )
            .execute(),
    ).flatMap(user => {
        return {
            _id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
        };
    });
}
