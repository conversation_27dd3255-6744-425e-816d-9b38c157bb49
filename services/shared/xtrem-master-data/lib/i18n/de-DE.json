{"@sage/xtrem-master-data/activity__allergen__name": "Allergen", "@sage/xtrem-master-data/activity__bom_revision_sequence__name": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/activity__business_entity__name": "Geschäftsentität", "@sage/xtrem-master-data/activity__capability_level__name": "Kompetenzniveau", "@sage/xtrem-master-data/activity__container__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__cost_category__name": "Kostenkategorie", "@sage/xtrem-master-data/activity__currency__name": "Währung", "@sage/xtrem-master-data/activity__customer__name": "Kunde", "@sage/xtrem-master-data/activity__customer_price_reason__name": "Grund für Kundenpreis", "@sage/xtrem-master-data/activity__customer_supplier_category__name": "Kunden-/Lieferantenkategorie", "@sage/xtrem-master-data/activity__daily_shift__name": "Tägliche Schicht", "@sage/xtrem-master-data/activity__delivery_mode__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__employee__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__ghs_classification__name": "GHS-Klassifizierung", "@sage/xtrem-master-data/activity__group_resource__name": "Gruppenressource", "@sage/xtrem-master-data/activity__incoterm__name": "Incoterms-Regel", "@sage/xtrem-master-data/activity__indirect_cost_origin__name": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/activity__indirect_cost_section__name": "Bereich indirekte Kosten", "@sage/xtrem-master-data/activity__item__name": "Artikel", "@sage/xtrem-master-data/activity__item_category__name": "Artikelkategorie", "@sage/xtrem-master-data/activity__item_site__name": "Artikel-Standort", "@sage/xtrem-master-data/activity__item_site_cost__name": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/activity__item_site_supplier__name": "Artikel-Standort-Lieferant", "@sage/xtrem-master-data/activity__labour_resource__name": "Arbeitskraftressource", "@sage/xtrem-master-data/activity__license_plate_number__name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/activity__location__name": "Lagerplatz", "@sage/xtrem-master-data/activity__location_sequence__name": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/activity__location_type__name": "Lagerplatztyp", "@sage/xtrem-master-data/activity__location_zone__name": "Lagerplatzbereich", "@sage/xtrem-master-data/activity__machine_resource__name": "Maschinenressource", "@sage/xtrem-master-data/activity__payment_term__name": "Zahlungsbedingung", "@sage/xtrem-master-data/activity__reason_code__name": "Grundcode", "@sage/xtrem-master-data/activity__sequence_number__name": "Nummernkreis", "@sage/xtrem-master-data/activity__sequence_number_assignment__name": "Zuweisung Nummernkreise", "@sage/xtrem-master-data/activity__shift_detail__name": "Schichtdetails", "@sage/xtrem-master-data/activity__standard__name": "Standard", "@sage/xtrem-master-data/activity__supplier__name": "Lieferant", "@sage/xtrem-master-data/activity__supplier_certificate__name": "Lieferantenzertifikat", "@sage/xtrem-master-data/activity__tool_resource__name": "Werkzeugressource", "@sage/xtrem-master-data/activity__unit_of_measure__name": "Maßeinheit", "@sage/xtrem-master-data/activity__weekly_shift__name": "Wöchentliche Schicht", "@sage/xtrem-master-data/business_entity_address_node_only_one_primary_contact": "", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological_control_must_be_active_fr_legislation": "Nummernkreis {{sequenceN<PERSON>ber}}: Die chronologische Prüfung muss für die Rechtsordnung FR aktiv sein.", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological-control-must-be-active": "Nummernkreis {{sequenceN<PERSON>ber}}: Die chronologische Prüfung muss für die Rechtsordnung FR aktiv sein.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_higher_than_next_date": "Das Dokumentdatum {{currentDate}} liegt nach dem nächsten Dokumentdatum {{nextDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_lower_than_previous_date": "Das Dokumentdatum {{currentDate}} liegt vor dem vorherigen Dokumentdatum {{previousDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-cannot-be-later-than-today": "Das Dokumentdatum kann nicht nach dem heutigen Datum liegen.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-earlier-than-previous-document-date": "Das Dokumentdatum {{current}} liegt vor dem vorherigen Dokumentdatum {{previousDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-later-than-next-document-date": "Das Dokumentdatum {{current}} liegt nach dem nächsten Dokumentdatum {{nextDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__future_date_not_allowed": "Das Dokumentdatum kann nicht nach dem heutigen Datum liegen.", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value": "Komponententyp nicht korrekt: {{type}}", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-enum-value": "Ungültiger Wert: {{enumValue}}", "@sage/xtrem-master-data/classes__sequence-number-generator__invoice_date_higher_than_next_date": "Das Dokumentdatum {{currentDate}} liegt nach dem nächsten Dokumentdatum {{nextDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__monthly-sequence-numbers-not-allowed": "Nummernkreis {{sequenceN<PERSON>ber}}: Monatliche Nummernkreise sind nicht zulässig.", "@sage/xtrem-master-data/classes__sequence-number-generator__no-sequence-number-assigned": "Diesem Dokumenttyp wurde kein Nummernkreis zugewiesen.", "@sage/xtrem-master-data/classes__sequence-number-generator__node-instance-is-required": "Die Node-Instanz ist erforderlich.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-at-application-level-definition-and-no-input-value-for-site": "Der Nummernkreis {{id}} ist auf der Ebene {{definitionLevel}} definiert und der Standort ist nicht erfasst.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-defined-at-application-level": "Der Nummernkreis {{id}} ist nicht auf Anwendungsebene definiert. Sie müssen einen Standort oder ein Unternehmen in den Nummernkreiskomponenten angeben.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-instance-not-found": "Nummernkreis-Instanz nicht gefunden.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-not-defined": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_continue": "Fortfahren", "@sage/xtrem-master-data/client_functions__master_data__resync_status_message": "<PERSON><PERSON> sind dabei, den Status zu aktualisieren.", "@sage/xtrem-master-data/client_functions__master_data__resync_status_title": "Status prüfen und aktualisieren.", "@sage/xtrem-master-data/client_functions__master_data_resync_submitted": "Anfrage zur erneuten Synchronisierung von Dokumenten übermittelt:({{batchTaskId}}).", "@sage/xtrem-master-data/company_node_only_one_primary_address": "<PERSON>ur eine Hauptadresse ist zulässig.", "@sage/xtrem-master-data/company_node_only_one_primary_contact": "Sie können nur einen Hauptkontakt definieren.", "@sage/xtrem-master-data/control__item__landedCost_service_option_inactive": "Sie können einen Artikel mit dem Typ Einstandskosten nur erstellen, wenn die Dienstoption Einstandskosten aktiv ist.", "@sage/xtrem-master-data/control__value_should_be_positive": "<PERSON> Feldwert muss eine positive <PERSON><PERSON> sein: {{propertyName}}.", "@sage/xtrem-master-data/control-begin__sequence-number__component-length-different-than-0": "Wenn die Frequenz zum Zurücksetzen auf {{rtzLevel}} gesetzt ist, müssen Sie im Komponententyp {{type}} eine Länge er<PERSON>, die nicht 0 ist.", "@sage/xtrem-master-data/control-begin__sequence-number__definition_level_is_not_present_in_components": "Der Nummernkreis ist auf der Ebene {{definitionLevel}} definiert. Sie müssen die Ebene {{definitionLevel}} in der Komponententabelle angeben.", "@sage/xtrem-master-data/control-begin__sequence-number__no_sequence_number_component": "Sie müssen eine Nummernkreiskomponente erfassen.", "@sage/xtrem-master-data/control-begin__sequence-number__rtz_level_is_not_present_in_components": "Wenn die Frequenz zum Zurücksetzen auf {{rtzLevel}} gesetzt ist, müssen Si<PERSON> den Typ {{type}} in der Komponententabelle angeben.", "@sage/xtrem-master-data/control-begin__sequence-number__sequence-numeric-wrong-component": "Der Nummernkreistyp ist numerisch. Sie können nur numerische Komponenten erfassen.", "@sage/xtrem-master-data/create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/create-confirmation": "Datensatz erstellt", "@sage/xtrem-master-data/customer-delivery-address-used": "Sie können die Lieferadresse {{deliveryAddressName}} nicht löschen. Sie wird bereits verwendet.", "@sage/xtrem-master-data/customer-item-used": "Sie können den Kundenartikel {{customerItemName}} nicht löschen. Er wird bereits verwendet.", "@sage/xtrem-master-data/data_types__address_entity_type_enum__name": "Enum Entitätstyp Adresse", "@sage/xtrem-master-data/data_types__address_line_data_type__name": "Datentyp <PERSON>ze<PERSON>", "@sage/xtrem-master-data/data_types__amount_data_type__name": "Datentyp Betrag", "@sage/xtrem-master-data/data_types__amount_in_company_currency__name": "Betrag in Unternehmenswährung", "@sage/xtrem-master-data/data_types__amount_in_financial_site_currency__name": "Betrag in Währung Buchhaltungsstandort", "@sage/xtrem-master-data/data_types__amount_in_transaction_currency__name": "Betrag in Transaktionswährung", "@sage/xtrem-master-data/data_types__approval_status_enum__name": "Enum Genehmigungsstatus", "@sage/xtrem-master-data/data_types__base_certificate_property_data_type__name": "Datentyp Eigenschaft Zertifikat Basis", "@sage/xtrem-master-data/data_types__base_decimal__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__base_display_status_enum__name": "Enum Status Anzeige Basis", "@sage/xtrem-master-data/data_types__base_origin_enum__name": "Enum <PERSON>", "@sage/xtrem-master-data/data_types__base_price__name": "Grundpreis", "@sage/xtrem-master-data/data_types__base_sequence_number_component_type_enum__name": "Enum Basis Nummernkreiskomponententyp", "@sage/xtrem-master-data/data_types__base_status_enum__name": "Enum Status Basis", "@sage/xtrem-master-data/data_types__bom_revision_sequence__name": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/data_types__business_entity__name": "Geschäftsentität", "@sage/xtrem-master-data/data_types__business_entity_id__name": "ID Geschäftsentität", "@sage/xtrem-master-data/data_types__business_entity_type_enum__name": "Enum Typ Geschäftsentität", "@sage/xtrem-master-data/data_types__business_relation_type_enum__name": "Enum Typ Geschäftsbeziehung", "@sage/xtrem-master-data/data_types__capacity_percentage__name": "Kapazität Prozent", "@sage/xtrem-master-data/data_types__city_data_type__name": "Datentyp Stadt", "@sage/xtrem-master-data/data_types__coefficient_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__company_price_data_type__name": "Datentyp Preis Unternehmen", "@sage/xtrem-master-data/data_types__constant_sequence_data_type__name": "Datentyp Abfolge <PERSON>", "@sage/xtrem-master-data/data_types__consumption_mode_enum__name": "Enum Verbrauchsart", "@sage/xtrem-master-data/data_types__contact_position_data_type__name": "Datentyp Position Konstante", "@sage/xtrem-master-data/data_types__contact_property_data_type__name": "Datentyp Eigenschaft Kontakt", "@sage/xtrem-master-data/data_types__contact_role_enum__name": "Enum Rolle Kontakt", "@sage/xtrem-master-data/data_types__container_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__cost_calculation_method_enum__name": "Enum Methode Kostenkalkulation", "@sage/xtrem-master-data/data_types__cost_category_type_enum__name": "Enum Typ Kostenkategorie", "@sage/xtrem-master-data/data_types__cost_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__cost_valuation_method_enum__name": "Enum Methode Kostenbewertung", "@sage/xtrem-master-data/data_types__cost_value_data_type__name": "Datentyp <PERSON>t", "@sage/xtrem-master-data/data_types__currency__name": "Währung", "@sage/xtrem-master-data/data_types__customer__name": "Kunde", "@sage/xtrem-master-data/data_types__customer_display_status_enum__name": "Enum Status Anzeige Kunde", "@sage/xtrem-master-data/data_types__customer_on_hold_type_enum__name": "Enum Typ Kunde ges<PERSON>rt", "@sage/xtrem-master-data/data_types__customer_supplier_category__name": "Kunden-/Lieferantenkategorie", "@sage/xtrem-master-data/data_types__delivery_mode__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__discount_charge_calculation_basis_enum__name": "Enum Basis Berechnung Rabatt/Zuschlag", "@sage/xtrem-master-data/data_types__discount_charge_calculation_rule_enum__name": "Enum Berechnungsregel Rabatt/Zuschlag", "@sage/xtrem-master-data/data_types__discount_charge_sign_enum__name": "Enum Vorzeichen Rabatt/Zuschlag", "@sage/xtrem-master-data/data_types__discount_charge_value_type_enum__name": "Enum Typ Wert Rabatt/Zuschlag", "@sage/xtrem-master-data/data_types__discount_or_penalty_type_enum__name": "Enum Typ Skonto oder Mahngebühr", "@sage/xtrem-master-data/data_types__document_number__name": "Dokumentnummer", "@sage/xtrem-master-data/data_types__due_date_type_enum__name": "Enum Typ Fälligkeitsdatum", "@sage/xtrem-master-data/data_types__duration_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__ean_number_data_type__name": "Datentyp EAN-Nummer", "@sage/xtrem-master-data/data_types__efficiency_percentage__name": "Prozentsatz Effizienz", "@sage/xtrem-master-data/data_types__email_action_type_enum__name": "Enum Typ Aktion E-Mail", "@sage/xtrem-master-data/data_types__exchange_rate__name": "Wechselkurs", "@sage/xtrem-master-data/data_types__extra_large_string__name": "Extra-lange Zeichenkette", "@sage/xtrem-master-data/data_types__fake_site_reference_datatype__name": "Datentyp Referenz Fake-Seite", "@sage/xtrem-master-data/data_types__incoterm__name": "Incoterm", "@sage/xtrem-master-data/data_types__incoterm_data_type__name": "Datentyp Incoterm", "@sage/xtrem-master-data/data_types__indirect_cost_percentage__name": "Prozentsatz indirekte Kosten", "@sage/xtrem-master-data/data_types__input_sequence_data_type__name": "Datentyp Abfolge Ein<PERSON>be", "@sage/xtrem-master-data/data_types__item__name": "Artikel", "@sage/xtrem-master-data/data_types__item_category__name": "Artikelkategorie", "@sage/xtrem-master-data/data_types__item_category_type_enum__name": "Enum Typ Artikelkategorie", "@sage/xtrem-master-data/data_types__item_flow_type_enum__name": "Enum Flusstyp Artikel", "@sage/xtrem-master-data/data_types__item_price_type_enum__name": "Enum Typ Artikelpreis", "@sage/xtrem-master-data/data_types__item_status_enum__name": "Enum Status Artikel", "@sage/xtrem-master-data/data_types__item_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__label_format_data_type__name": "Datentyp Etikettenformat", "@sage/xtrem-master-data/data_types__large_string__name": "<PERSON>", "@sage/xtrem-master-data/data_types__legal_entity_enum__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__localized_sic_description_data_type__name": "Datentyp Bezeichnung SIC lokalisiert", "@sage/xtrem-master-data/data_types__location__name": "Lagerplatz", "@sage/xtrem-master-data/data_types__location_category_enum__name": "Enum Lagerplatzkategorie", "@sage/xtrem-master-data/data_types__location_sequence__name": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/data_types__location_sequence_type_enum__name": "Enum Nummernkreis Lagerplatz", "@sage/xtrem-master-data/data_types__lot_management_enum__name": "Enum Chargenverwaltung", "@sage/xtrem-master-data/data_types__master_data_company__name": "Stammdaten Unternehmen", "@sage/xtrem-master-data/data_types__master_data_site__name": "Stammdaten", "@sage/xtrem-master-data/data_types__medium_string__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__model_data_type__name": "Datentyp Modell", "@sage/xtrem-master-data/data_types__note__name": "Notiz", "@sage/xtrem-master-data/data_types__order_cost_data_type__name": "Datentyp Auftragskosten", "@sage/xtrem-master-data/data_types__order_type_enum__name": "Enum Auftragstyp", "@sage/xtrem-master-data/data_types__payment_method_enum__name": "Enum Zahlungsmethode", "@sage/xtrem-master-data/data_types__payment_term__name": "Zahlungsbedingung", "@sage/xtrem-master-data/data_types__payment_term_data_type__name": "Datentyp Zahlungsbedingung", "@sage/xtrem-master-data/data_types__payment_term_discount_or_penalty_type_enum__name": "Enum Typ Skonto oder Mahngebühr Zahlungsbedingung", "@sage/xtrem-master-data/data_types__percentage__name": "Prozentsatz", "@sage/xtrem-master-data/data_types__percentage_work_order_data_type__name": "Datentyp Fertigungsauftrag Prozentsatz", "@sage/xtrem-master-data/data_types__period_type_enum__name": "Enum Typ Zeitraum", "@sage/xtrem-master-data/data_types__postcode_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__potency_percentage__name": "Prozentsatz Konzentration", "@sage/xtrem-master-data/data_types__preferred_process_enum__name": "Enum bevorzugte Verarbeitung", "@sage/xtrem-master-data/data_types__price__name": "Pre<PERSON>", "@sage/xtrem-master-data/data_types__price_data_type__name": "Datentyp <PERSON>", "@sage/xtrem-master-data/data_types__price_in_sales_price__name": "Preis in Verkaufspreis", "@sage/xtrem-master-data/data_types__price_percentage__name": "Prozentsatz Preis", "@sage/xtrem-master-data/data_types__quantity__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__quantity_in_purchase_unit__name": "Menge in Einkaufseinheit", "@sage/xtrem-master-data/data_types__quantity_in_sales_unit__name": "Menge in Verkaufseinheit", "@sage/xtrem-master-data/data_types__quantity_in_stock_unit__name": "Menge in Lagereinheit", "@sage/xtrem-master-data/data_types__quantity_in_unit__name": "Menge in Einheit", "@sage/xtrem-master-data/data_types__quantity_in_volume_unit__name": "Menge in Volumeneinheit", "@sage/xtrem-master-data/data_types__quantity_in_weight_unit__name": "Menge in Gewichtseinheit", "@sage/xtrem-master-data/data_types__reason_code__name": "Grundcode", "@sage/xtrem-master-data/data_types__region_data_type__name": "Datentyp Region", "@sage/xtrem-master-data/data_types__replenishment_method_enum__name": "<PERSON><PERSON> Wiederbeschaffungsart", "@sage/xtrem-master-data/data_types__resource_cost__name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__resource_group_type_enum__name": "Enum Ressourcengruppentyp", "@sage/xtrem-master-data/data_types__run_time_data_type__name": "Datentyp Bearbeitungszeit", "@sage/xtrem-master-data/data_types__scrap_factor_percentage__name": "Prozentsatz Ausschussfaktor", "@sage/xtrem-master-data/data_types__sequence_counter_definition_level_enum__name": "Enum Definitionsebene Nummernkreiszähler", "@sage/xtrem-master-data/data_types__sequence_number__name": "Nummernkreis", "@sage/xtrem-master-data/data_types__sequence_number_field_kind_enum__name": "Enum Typ Feld Nummernkreis", "@sage/xtrem-master-data/data_types__sequence_number_reset_frequency_enum__name": "Enum Rücksetzfrequenz Nummernkreis", "@sage/xtrem-master-data/data_types__sequence_number_type_enum__name": "Enum Typ Nummernkreis", "@sage/xtrem-master-data/data_types__serial_number_management_enum__name": "Enum Seriennummernverwaltung", "@sage/xtrem-master-data/data_types__serial_number_usage_enum__name": "Enum Verwendung Seriennummer", "@sage/xtrem-master-data/data_types__setup_time_data_type__name": "Datentyp Rüstzeit", "@sage/xtrem-master-data/data_types__shift_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__standard_property_data_type__name": "Datentyp Standardeigenschaft", "@sage/xtrem-master-data/data_types__stock_management_mode_enum__name": "Enum Modus Bestandsverwaltung", "@sage/xtrem-master-data/data_types__stock_quantity__name": "Bestandsm<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__stock_quantity_variance_percentage__name": "Prozentsatz Mengenabweichung Bestand", "@sage/xtrem-master-data/data_types__stock_variation_value__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__supplier__name": "Lieferant", "@sage/xtrem-master-data/data_types__supplier_item_property_data_type__name": "Datentyp Eigenschaft Lieferantenartikel", "@sage/xtrem-master-data/data_types__supplier_type_enum__name": "Enum Typ Liefer<PERSON>", "@sage/xtrem-master-data/data_types__symbol_data_type__name": "Datentyp Symbol", "@sage/xtrem-master-data/data_types__tax_calculation_status_enum__name": "Enum Status Steuerberechnung", "@sage/xtrem-master-data/data_types__telephone_number_data_type__name": "Datentyp Telefonnummer", "@sage/xtrem-master-data/data_types__time_data_type__name": "Datentyp <PERSON>", "@sage/xtrem-master-data/data_types__time_zone__name": "Zeitzone", "@sage/xtrem-master-data/data_types__title_enum__name": "Enum Titel", "@sage/xtrem-master-data/data_types__unit_conversion_coefficient__name": "Umrechnungskoeffizient Einheit", "@sage/xtrem-master-data/data_types__unit_conversion_type_enum__name": "Enum Typ Umrechnung Einheit", "@sage/xtrem-master-data/data_types__unit_of_measure__name": "Maßeinheit", "@sage/xtrem-master-data/data_types__unit_type_enum__name": "Enum Typ Einheit", "@sage/xtrem-master-data/data_types__version_data_type__name": "Datentyp Version", "@sage/xtrem-master-data/data_types__volume_percentage__name": "Volumen Prozent", "@sage/xtrem-master-data/data_types__week_days_enum__name": "En<PERSON>", "@sage/xtrem-master-data/data_types__weight_percentage__name": "Gewicht Prozent", "@sage/xtrem-master-data/data_types__work_in_progress_document_type_enum__name": "Enum Typ Dokument Work-In-Progress", "@sage/xtrem-master-data/data_types__zone_type_enum__name": "Enum Typ Be<PERSON>ich", "@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum": "Der Prozentwert ({{value}}) darf nicht größer als {{maxValue}} sein.", "@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum": "Der Prozentwert ({{value}}) muss größer als {{minValue}} sein.", "@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range": "Der Prozentwert ({{value}}) muss zwischen {{minValue}} und {{maxValue}} liegen.", "@sage/xtrem-master-data/days": "Tag(e)", "@sage/xtrem-master-data/delete-confirmation": "Datensatz <PERSON>", "@sage/xtrem-master-data/delete-dialog-content": "<PERSON>e sind dabei, diesen Datensatz zu löschen. Bestätigen?", "@sage/xtrem-master-data/delete-dialog-title": "Löschen bestätigen", "@sage/xtrem-master-data/edit-create-customer-price": "Neuen Verkaufspreis hinzufügen", "@sage/xtrem-master-data/edit-create-line": "Neuen Kunden hinzufügen", "@sage/xtrem-master-data/edit-create-supplier": "Neuen Lieferanten hinzufügen", "@sage/xtrem-master-data/edit-create-supplier-price": "Neuen Lieferantenpreis hinzufügen", "@sage/xtrem-master-data/email-validation-error": "Ungültige E-Mail-Adresse", "@sage/xtrem-master-data/enums__address_entity_type__businessEntity": "Geschäftsentität", "@sage/xtrem-master-data/enums__address_entity_type__company": "Unternehmen", "@sage/xtrem-master-data/enums__address_entity_type__customer": "Kunde", "@sage/xtrem-master-data/enums__address_entity_type__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__address_entity_type__supplier": "Lieferant", "@sage/xtrem-master-data/enums__approval_status__approved": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__changeRequested": "Änderung angefordert", "@sage/xtrem-master-data/enums__approval_status__confirmed": "Bestätigt", "@sage/xtrem-master-data/enums__approval_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__pendingApproval": "Genehmigung ausstehend", "@sage/xtrem-master-data/enums__approval_status__rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__approved": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__changeRequested": "Änderung angefordert", "@sage/xtrem-master-data/enums__base_display_status__closed": "Abgeschlossen", "@sage/xtrem-master-data/enums__base_display_status__confirmed": "Bestätigt", "@sage/xtrem-master-data/enums__base_display_status__credited": "Gutgeschrieben", "@sage/xtrem-master-data/enums__base_display_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__error": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__invoiced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__noVariance": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__ordered": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__paid": "Be<PERSON>hlt", "@sage/xtrem-master-data/enums__base_display_status__partiallyCredited": "Teilweise gutgeschrieben", "@sage/xtrem-master-data/enums__base_display_status__partiallyInvoiced": "Teilweise fakturiert", "@sage/xtrem-master-data/enums__base_display_status__partiallyOrdered": "<PERSON>il<PERSON><PERSON> bestellt", "@sage/xtrem-master-data/enums__base_display_status__partiallyPaid": "Teilweise bezahlt", "@sage/xtrem-master-data/enums__base_display_status__partiallyReceived": "Teilweise eingegangen", "@sage/xtrem-master-data/enums__base_display_status__partiallyReturned": "<PERSON>il<PERSON><PERSON> retourniert", "@sage/xtrem-master-data/enums__base_display_status__partiallyShipped": "Teilweise versendet", "@sage/xtrem-master-data/enums__base_display_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__pendingApproval": "Genehmigung ausstehend", "@sage/xtrem-master-data/enums__base_display_status__posted": "Gebucht", "@sage/xtrem-master-data/enums__base_display_status__postingError": "Buchungsfehler", "@sage/xtrem-master-data/enums__base_display_status__postingInProgress": "Buchung in Bearbeitung", "@sage/xtrem-master-data/enums__base_display_status__quote": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__readyToProcess": "Bereit zur Verarbeitung", "@sage/xtrem-master-data/enums__base_display_status__readyToShip": "Bereit zum Versand", "@sage/xtrem-master-data/enums__base_display_status__received": "Eingegangen", "@sage/xtrem-master-data/enums__base_display_status__rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__returned": "Retourniert", "@sage/xtrem-master-data/enums__base_display_status__shipped": "Versendet", "@sage/xtrem-master-data/enums__base_display_status__stockError": "Bestandsfehler", "@sage/xtrem-master-data/enums__base_display_status__taxCalculationFailed": "Steuerberechnung fehlgeschlagen", "@sage/xtrem-master-data/enums__base_display_status__variance": "<PERSON>bwei<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__varianceApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON> genehm<PERSON>t", "@sage/xtrem-master-data/enums__base_origin__direct": "Direkt", "@sage/xtrem-master-data/enums__base_origin__invoice": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_origin__order": "Auftrag", "@sage/xtrem-master-data/enums__base_origin__purchaseCreditMemo": "Einkaufsgutschrift", "@sage/xtrem-master-data/enums__base_origin__purchaseInvoice": "Einkaufsrechnung", "@sage/xtrem-master-data/enums__base_origin__purchaseOrder": "Bestellung", "@sage/xtrem-master-data/enums__base_origin__purchaseReceipt": "Wareneingang", "@sage/xtrem-master-data/enums__base_origin__purchaseRequisition": "Bestellanforderung", "@sage/xtrem-master-data/enums__base_origin__purchaseReturn": "Einkaufsretoure", "@sage/xtrem-master-data/enums__base_origin__purchaseSuggestion": "Einkaufsvorschlag", "@sage/xtrem-master-data/enums__base_origin__return": "Re<PERSON>e", "@sage/xtrem-master-data/enums__base_origin__shipment": "Warenausgang", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__company": "Unternehmen", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__day": "Tag", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceAlpha": "Nummernkreis alphanumerisch", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__year": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__approved": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__changeRequested": "Änderung angefordert", "@sage/xtrem-master-data/enums__base_status__closed": "Abgeschlossen", "@sage/xtrem-master-data/enums__base_status__confirmed": "Bestätigt", "@sage/xtrem-master-data/enums__base_status__credited": "Gutgeschrieben", "@sage/xtrem-master-data/enums__base_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__error": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__inProgress": "In Bearbeitung", "@sage/xtrem-master-data/enums__base_status__invoiced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__noVariance": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__partiallyCredited": "Teilweise gutgeschrieben", "@sage/xtrem-master-data/enums__base_status__partiallyInvoiced": "Teilweise fakturiert", "@sage/xtrem-master-data/enums__base_status__partiallyReceived": "Teilweise eingegangen", "@sage/xtrem-master-data/enums__base_status__partiallyReturned": "<PERSON>il<PERSON><PERSON> retourniert", "@sage/xtrem-master-data/enums__base_status__partiallyShipped": "Teilweise versendet", "@sage/xtrem-master-data/enums__base_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__pendingApproval": "Genehmigung ausstehend", "@sage/xtrem-master-data/enums__base_status__posted": "Gebucht", "@sage/xtrem-master-data/enums__base_status__postingError": "Buchungsfehler", "@sage/xtrem-master-data/enums__base_status__postingInProgress": "Buchung in Bearbeitung", "@sage/xtrem-master-data/enums__base_status__quote": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__readyToProcess": "Bereit zur Verarbeitung", "@sage/xtrem-master-data/enums__base_status__readyToShip": "Bereit zum Versand", "@sage/xtrem-master-data/enums__base_status__received": "Eingegangen", "@sage/xtrem-master-data/enums__base_status__rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__returned": "Retourniert", "@sage/xtrem-master-data/enums__base_status__shipped": "Versendet", "@sage/xtrem-master-data/enums__base_status__stockError": "Bestandsfehler", "@sage/xtrem-master-data/enums__base_status__taxCalculationFailed": "Steuerberechnung fehlgeschlagen", "@sage/xtrem-master-data/enums__base_status__variance": "<PERSON>bwei<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__varianceApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON> genehm<PERSON>t", "@sage/xtrem-master-data/enums__business_entity_type__all": "Alle", "@sage/xtrem-master-data/enums__business_entity_type__customer": "Kunde", "@sage/xtrem-master-data/enums__business_entity_type__supplier": "Lieferant", "@sage/xtrem-master-data/enums__business_relation_type__customer": "Kunde", "@sage/xtrem-master-data/enums__business_relation_type__supplier": "Lieferant", "@sage/xtrem-master-data/enums__consumption_mode__none": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__consumption_mode__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__consumption_mode__time": "Zeit", "@sage/xtrem-master-data/enums__contact_role__commercialContact": "Geschäftskontakt", "@sage/xtrem-master-data/enums__contact_role__financialContact": "Finanzkontakt", "@sage/xtrem-master-data/enums__contact_role__mainContact": "Hauptkontakt", "@sage/xtrem-master-data/enums__container_type__barrel": "Fass", "@sage/xtrem-master-data/enums__container_type__bigBag": "Containersack", "@sage/xtrem-master-data/enums__container_type__box": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__container": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__other": "Sonstiges", "@sage/xtrem-master-data/enums__container_type__pack": "Verpackung", "@sage/xtrem-master-data/enums__container_type__pallet": "Palette", "@sage/xtrem-master-data/enums__cost_calculation_method__compound": "Kaskadiert", "@sage/xtrem-master-data/enums__cost_calculation_method__cumulate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__budgeted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__other": "Sonstiges", "@sage/xtrem-master-data/enums__cost_category_type__simulated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__standard": "Standard", "@sage/xtrem-master-data/enums__cost_valuation_method__averageCost": "Gewichteter Durchschnittspreis", "@sage/xtrem-master-data/enums__cost_valuation_method__fifoCost": "FIFO-Preis", "@sage/xtrem-master-data/enums__cost_valuation_method__standardCost": "Standardkosten", "@sage/xtrem-master-data/enums__customer_display_status__active": "Aktiv", "@sage/xtrem-master-data/enums__customer_display_status__inactive": "Inaktiv", "@sage/xtrem-master-data/enums__customer_display_status__onHold": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__customer_on_hold_type__blocking": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__customer_on_hold_type__none": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__customer_on_hold_type__warning": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPrice": "Bruttopreis", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPriceAndCompound": "Bruttopreis und kaskadiert", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byLine": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byUnit": "Nach Einheit", "@sage/xtrem-master-data/enums__discount_charge_sign__decrease": "Verringern", "@sage/xtrem-master-data/enums__discount_charge_sign__increase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_charge_value_type__amount": "Betrag", "@sage/xtrem-master-data/enums__discount_charge_value_type__percentage": "Prozentsatz", "@sage/xtrem-master-data/enums__discount_or_penalty_type__amount": "Betrag", "@sage/xtrem-master-data/enums__discount_or_penalty_type__percentage": "Prozentsatz", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDate": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDateAndExtendedToEndOfMonth": "Nach Rechnungsdatum und verlängert auf Monatsende", "@sage/xtrem-master-data/enums__due_date_type__afterTheEndOfTheMonthOfInvoiceDate": "Nach Ende des Monats des Rechnungsdatums", "@sage/xtrem-master-data/enums__email_action_type__approved": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__email_action_type__rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_category_type__allergen": "Allergen", "@sage/xtrem-master-data/enums__item_category_type__ghsClassification": "GHS-Klassifizierung", "@sage/xtrem-master-data/enums__item_category_type__none": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_flow_type__manufactured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_flow_type__purchased": "Eingekauft", "@sage/xtrem-master-data/enums__item_flow_type__sold": "Verkauft", "@sage/xtrem-master-data/enums__item_flow_type__subcontracted": "Fremdbearbeitet", "@sage/xtrem-master-data/enums__item_price_type__discount": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_price_type__normal": "Normal", "@sage/xtrem-master-data/enums__item_price_type__specialOffer": "Sonderangebot", "@sage/xtrem-master-data/enums__item_status__active": "Aktiv", "@sage/xtrem-master-data/enums__item_status__inDevelopment": "In Entwicklung", "@sage/xtrem-master-data/enums__item_status__notRenewed": "<PERSON>cht verlä<PERSON>t", "@sage/xtrem-master-data/enums__item_status__notUsable": "<PERSON>cht verwendbar", "@sage/xtrem-master-data/enums__item_status__obsolete": "Abgelaufen", "@sage/xtrem-master-data/enums__item_type__good": "Ware", "@sage/xtrem-master-data/enums__item_type__landedCost": "Einstandskosten", "@sage/xtrem-master-data/enums__item_type__service": "Dienstleistung", "@sage/xtrem-master-data/enums__legal_entity__corporation": "Juristische Person", "@sage/xtrem-master-data/enums__legal_entity__physicalPerson": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__location_category__customer": "Kunde", "@sage/xtrem-master-data/enums__location_category__dock": "Rampe", "@sage/xtrem-master-data/enums__location_category__internal": "Intern", "@sage/xtrem-master-data/enums__location_category__subcontract": "Fremdbearbeitung", "@sage/xtrem-master-data/enums__location_category__virtual": "Virtuell", "@sage/xtrem-master-data/enums__location_sequence_type__alphabetical": "Alphabetisch", "@sage/xtrem-master-data/enums__location_sequence_type__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/enums__location_sequence_type__numerical": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__lot_management__lotManagement": "Charge", "@sage/xtrem-master-data/enums__lot_management__lotSublotManagement": "Charge und Untercharge", "@sage/xtrem-master-data/enums__lot_management__notManaged": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__order_type__closed": "Abgeschlossen", "@sage/xtrem-master-data/enums__order_type__firm": "Fest", "@sage/xtrem-master-data/enums__order_type__planned": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__order_type__suggested": "Vorschlag", "@sage/xtrem-master-data/enums__payment_method__ACH": "ACH", "@sage/xtrem-master-data/enums__payment_method__cash": "Barmittel", "@sage/xtrem-master-data/enums__payment_method__creditCard": "Kreditkarte", "@sage/xtrem-master-data/enums__payment_method__EFT": "Elektronischer Zahlungsverkehr", "@sage/xtrem-master-data/enums__payment_method__none": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__payment_method__printedCheck": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__amount": "Betrag", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__percentage": "Prozentsatz", "@sage/xtrem-master-data/enums__payment_term_from__invoiceDate": "Rechnungsdatum", "@sage/xtrem-master-data/enums__period_type__day": "Tag", "@sage/xtrem-master-data/enums__period_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__period_type__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__period_type__year": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__preferred_process__production": "Fertigung", "@sage/xtrem-master-data/enums__preferred_process__purchasing": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__replenishment_method__byMRP": "Nach MRP", "@sage/xtrem-master-data/enums__replenishment_method__byReorderPoint": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__replenishment_method__notManaged": "<PERSON>cht verwaltet", "@sage/xtrem-master-data/enums__resource_group_type__labor": "Arbeitskraft", "@sage/xtrem-master-data/enums__resource_group_type__machine": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__resource_group_type__subcontract": "Fremdbearbeitung", "@sage/xtrem-master-data/enums__resource_group_type__tool": "Werkzeug", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__application": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__company": "Unternehmen", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__company": "Unternehmen", "@sage/xtrem-master-data/enums__sequence_number_field_kind__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/enums__sequence_number_field_kind__day": "Tag", "@sage/xtrem-master-data/enums__sequence_number_field_kind__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/enums__sequence_number_field_kind__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__year": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__monthly": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__noReset": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__yearly": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_type__alphanumeric": "Alphanumerisch", "@sage/xtrem-master-data/enums__sequence_number_type__numeric": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__serial_number_management__managed": "Verwaltet", "@sage/xtrem-master-data/enums__serial_number_management__notManaged": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__serial_number_usage__issueAndReceipt": "Abgang und Eingang", "@sage/xtrem-master-data/enums__serial_number_usage__issueOnly": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__stock_management_mode__byOrder": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__stock_management_mode__byProject": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__stock_management_mode__onStock": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__supplier_type__chemical": "Chemieindustrie", "@sage/xtrem-master-data/enums__supplier_type__foodAndBeverage": "Lebensmittel- und Getränkeindustrie", "@sage/xtrem-master-data/enums__supplier_type__other": "Sonstiges", "@sage/xtrem-master-data/enums__tax_calculation_status__done": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__tax_calculation_status__failed": "Fehlgeschlagen", "@sage/xtrem-master-data/enums__tax_calculation_status__inProgress": "In Bearbeitung", "@sage/xtrem-master-data/enums__tax_calculation_status__notDone": "Nicht durchgeführt", "@sage/xtrem-master-data/enums__title__dr": "Dr.", "@sage/xtrem-master-data/enums__title__family": "Familie", "@sage/xtrem-master-data/enums__title__master": "<PERSON>", "@sage/xtrem-master-data/enums__title__miss": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__mr": "<PERSON>", "@sage/xtrem-master-data/enums__title__mrs": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__ms": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__prof": "Prof.", "@sage/xtrem-master-data/enums__unit_conversion_type__other": "Sonstiges", "@sage/xtrem-master-data/enums__unit_conversion_type__purchase": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_conversion_type__sales": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_type__area": "Fläche", "@sage/xtrem-master-data/enums__unit_type__each": "Stück", "@sage/xtrem-master-data/enums__unit_type__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_type__temperature": "Temperatur", "@sage/xtrem-master-data/enums__unit_type__time": "Zeit", "@sage/xtrem-master-data/enums__unit_type__volume": "Volumen", "@sage/xtrem-master-data/enums__unit_type__weight": "Gewicht", "@sage/xtrem-master-data/enums__week_days__friday": "Freitag", "@sage/xtrem-master-data/enums__week_days__monday": "Montag", "@sage/xtrem-master-data/enums__week_days__saturday": "Samstag", "@sage/xtrem-master-data/enums__week_days__sunday": "Sonntag", "@sage/xtrem-master-data/enums__week_days__thursday": "Don<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__tuesday": "Dienstag", "@sage/xtrem-master-data/enums__week_days__wednesday": "Mittwoch", "@sage/xtrem-master-data/enums__work_in_progress_document_type__materialNeed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseOrder": "Bestellung", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReceipt": "Wareneingang", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReturn": "Einkaufsretoure", "@sage/xtrem-master-data/enums__work_in_progress_document_type__salesOrder": "Verkaufsauftrag", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferOrder": "Auftrag Bestandstransfer", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferReceipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__workOrder": "Fertigungsauftrag", "@sage/xtrem-master-data/enums__zone_type__chemical": "Chemikalie", "@sage/xtrem-master-data/enums__zone_type__frozen": "Tief<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__hazard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__magnetic": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__restricted": "Eingeschränkt", "@sage/xtrem-master-data/enums__zone_type__secured": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__sensitive": "Empf<PERSON>lich", "@sage/xtrem-master-data/enums__zone_type__virtual": "Virtuell", "@sage/xtrem-master-data/events__control__document_external_note_must_be_empty": "Die externe Notiz muss leer sein, wenn die Eigenschaft 'isExternalNote' falsch ist.", "@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error": "Ungültige Postleitzahl", "@sage/xtrem-master-data/events/control__address-control__postcode-validation-error": "Ungültige Postleitzahl", "@sage/xtrem-master-data/events/control__address-control__telephone-validation-error": "Ungültige Telefonnummer", "@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error": "Ungültige Postleitzahl (USA)", "@sage/xtrem-master-data/events/control__base_sequence_number_control_length": "Die Länge des Komponentennummernkreises muss mit einem der folgenden Werte übereinstimmen: {{lengths}}.", "@sage/xtrem-master-data/events/control__base_sequence_number_control_type": "Der Komponententyp ist nicht zulässig: {{type}}.", "@sage/xtrem-master-data/events/control__business-entity__customer_already_exists_with_same_name": "Es existiert bereits ein Kunde mit dem gleichen Namen.", "@sage/xtrem-master-data/events/control__business-entity__site_already_exists_with_same_name": "Es existiert bereits ein Standort mit dem gleichen Namen.", "@sage/xtrem-master-data/events/control__business-entity__supplier_already_exists_with_same_name": "Es existiert bereits ein Lieferant mit dem gleichen Namen.", "@sage/xtrem-master-data/events/control__cost-category__the_standard_cost_category_must_be_mandatory": "Erfassen Sie eine Standardkostenkategorie.", "@sage/xtrem-master-data/events/control__item__allergens-not-allowed": "Allergene sind nur für Lebensmittel zulässig.", "@sage/xtrem-master-data/events/control__item__cannot-be-bom-revision-managed": "Ein stücklistenänderungsgeführter Artikel muss gefertigt sein.", "@sage/xtrem-master-data/events/control__item__cannot-be-phantom": "Ein Phantomartikel muss gefertigt und bestandsgeführt sein, kann jedoch nicht eingekauft oder verkauft sein.", "@sage/xtrem-master-data/events/control__item__code-must-be-a-number": "Der Code {{gtinCode}} muss eine <PERSON>ummer sein.", "@sage/xtrem-master-data/events/control__item__empty-preferredProcess": "Ein Bestandsartikel muss gef<PERSON>t, <PERSON><PERSON><PERSON><PERSON><PERSON>, oder be<PERSON> sein.", "@sage/xtrem-master-data/events/control__item__ghs-classification-not-allowed": "Die GHS-Klassifizierung ist nur für Chemikalien zulässig.", "@sage/xtrem-master-data/events/control__item__incorrect-value-for-economic-quantity": "Erfassen Sie eine optimale Bestellmenge, die ein Vielfaches der Losmenge ist.", "@sage/xtrem-master-data/events/control__item__must-be-service": "Der Artikel muss eine 'Dienstleistung' sein.", "@sage/xtrem-master-data/events/control__item__must-be-service-or-landed-cost": "Der Artikel muss ein Dienstleistungsartikel oder ein Einstandskostenartikel sein.", "@sage/xtrem-master-data/events/control__item__must-not-be-service": "Der Artikel kann keine 'Dienstleistung' sein.", "@sage/xtrem-master-data/events/control__item__must-not-be-service-or-landed-cost": "Der Artikel kann kein Dienstleistungsartikel oder Einstandskostenartikel sein.", "@sage/xtrem-master-data/events/control__item__property-incorrect-for-not-stock-managed-items": "Die Eigenschaft {{property}} ist für nicht bestandsgeführte Artikel nicht korrekt.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-and-landed-cost-items": "Das Feld {{property}} ist für Dienstleistungsartikel oder Einstandskostenartikel nicht verfügbar.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-items": "Die Eigenschaft wird nicht für Dienstleistungen verwaltet.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect": "Für die bevorzugte Verarbeitung muss ein Wert für einen gekauften oder gefertigten Artikel ausgewählt werden.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-manufacturing-items": "Die bevorzugte Verarbeitung 'Fertigung' ist für Bestandsartikel, die nicht gefertigt werden, nicht korrekt.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-purchasing-items": "Die bevorzugte Verarbeitung 'Einkauf' ist für Bestandsartikel, die nicht gefertigt werden, nicht korrekt.", "@sage/xtrem-master-data/events/control__location_sequence_control__range_item_length": "Die Länge des Komponentennummernkreises muss mit der Komponentenlänge übereinstimmen: {{lengths}}.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_capital_letters": "Eine alphabetische Nummernkreiskomponente kann nur aus Großbuchstaben bestehen.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_range": "Der Startwert kann nicht nach dem Endwert liegen.", "@sage/xtrem-master-data/events/control__location_sequence_control_length": "Nummernkreis und Komponente müssen die gleiche Länge haben.", "@sage/xtrem-master-data/events/control__location_sequence_control_number_value": "Eine numerische Nummernkreiskomponente kann nur aus Zahlen bestehen.", "@sage/xtrem-master-data/events/control__location_sequence_control_numeric_range": "Der Startwert kann nicht nach dem Endwert liegen.", "@sage/xtrem-master-data/events/control__sequence-number__force-reset-with-tenant": "Dieser Nummernkreis wird zurückgesetzt, wenn Sie den Tenant zurücksetzen.", "@sage/xtrem-master-data/events/control__time-control__end-date-cannot-be-empty": "Sie müssen das Enddatum erfassen.", "@sage/xtrem-master-data/events/control__time-control__end-datetime-cannot-be-empty": "Sie müssen die Endzeit erfassen.", "@sage/xtrem-master-data/events/control__time-control__invalid-date-range": "Der Datumsbereich von {{start}} bis {{end}} ist ungültig.", "@sage/xtrem-master-data/events/control__time-control__invalid-datetime-range": "Der Datums- und Uhrzeitbereich von {{start}} bis {{end}} ist ungültig.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-empty": "Sie müssen das Startdatum erfassen.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-greater-than-end-date": "Das Startdatum muss vor dem Enddatum liegen.", "@sage/xtrem-master-data/events/control__time-control__start-datetime-cannot-be-empty": "Sie müssen die Startzeit erfassen.", "@sage/xtrem-master-data/events/control__time-control__time-cannot-be-empty": "Sie müssen die Zeit erfassen.", "@sage/xtrem-master-data/events/control__time-control__time-format-HH-MM": "Die Zeit {{timeToValidate}} muss im Format HH:MM angegeben werden.", "@sage/xtrem-master-data/found-matching-business-entities": "Passende Geschäftsentitäten gefunden", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description": "Dokumente gedruckt: {{numberOfDocuments}}.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description_error": "Ein oder mehrere Dokumente wurden nicht gedruckt. Weitere Details finden Sie auf der Seite Verlauf Batchaufgaben.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_fail": "{{reportName}}: <PERSON>uck nicht erfolg<PERSON>ich", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_success": "{{reportName}}: <PERSON><PERSON> er<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/function__customer_price_reason_priority_already_exists": "<PERSON><PERSON><PERSON> diese Priorität ist bereits ein Kundenpreisgrund vorhanden.", "@sage/xtrem-master-data/functions__business_entity__incorrect_format_siret": "Das Format ist nicht korrekt. Verwenden Sie das Format für SIRET-Nummern: {{format}}.", "@sage/xtrem-master-data/functions__business_entity__not-a-valid-tax-id": "Das Format ist nicht korrekt. Verwenden Sie das Format für Steuer-IDs: {{format}}.", "@sage/xtrem-master-data/functions__common__download_file": "<PERSON><PERSON>", "@sage/xtrem-master-data/functions__common__history": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/functions__common__invalid_characters": "Der Nummernkreiswert enthält ungültige Zeichen.", "@sage/xtrem-master-data/functions__common__sequence_number_id_is_in_use": "Dieser Nummernkreiswert wird bereits verwendet. Verwenden Sie einen anderen Wert.", "@sage/xtrem-master-data/functions__common__sequence_number_id_must_have_two_characters": "Der Nummernkreiswert muss zwei Zeichen enthalten.", "@sage/xtrem-master-data/functions__exchange-rate__no-rate-found-for-this-currency-pair": "<PERSON><PERSON><PERSON> dieses Währungspaar wurde kein Kurs gefunden.", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-enum-value": "Wert nicht korrekt: {{enumValue}}", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-length": "<PERSON><PERSON>nge nicht korrekt: {{sequenceNumberId}}", "@sage/xtrem-master-data/functions__sequence-number-lib__no_company_sequence_number_value_defined": "Erfassen Sie den Nummernkreis für das Unternehmen {{companyId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no_site_sequence_number_value_defined": "Erfassen Sie den Nummernkreis für den Standort {{siteId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no-component-of-type": "{{sequenceNumberId}}: <PERSON><PERSON> Komponente vom Typ {{componentType}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__sequence-number-exceeded": "Nummernkreis {{sequenceNumberId}} überschritten.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__different-unit-type": "Die Einheiten {{fromUnit}} und {{toUnit}} haben nicht den gleichen Einheitentyp.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__no-factors-for-the-units": "Zwischen den Einheiten {{fromUnit}} und {{toUnit}} wurde kein Umrechnungsfaktor definiert.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__unit-missing": "Sie müssen eine Lagereinheit erfassen oder auswählen, bevor Sie eine Einkaufseinheit erfassen oder auswählen.", "@sage/xtrem-master-data/generate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/info": "Info", "@sage/xtrem-master-data/invalid-period": "Zeitraum nicht korrekt: {{dates}}", "@sage/xtrem-master-data/invalid-quantity-range": "Mengenbereich nicht korrekt: {{qtyRange}}", "@sage/xtrem-master-data/item__price-cannot-be-negative": "Der <PERSON> kann nicht negativ sein.", "@sage/xtrem-master-data/item-not-sold": "Setzen Sie den Artikel {{itemName}} auf 'Verkauft'.", "@sage/xtrem-master-data/item-site-supplier-record-created": "Lieferant Artikel-Standort erstellt", "@sage/xtrem-master-data/item-site-supplier-updated.": "Lieferant Artikel-Standort aktualisiert", "@sage/xtrem-master-data/item-site-updated.": "Artikel-Standort aktualisiert", "@sage/xtrem-master-data/location_sequence_component_mandatory_constant": "Erfassen Sie eine Konstante.", "@sage/xtrem-master-data/mailer_no_mailer_redirect_url_provided": "Die Umleitungs-URL für den Mailer fehlt in der Konfigurationsdatei.", "@sage/xtrem-master-data/menu_item__declarations": "Steuerliche Meldungen", "@sage/xtrem-master-data/menu_item__dev-tools": "Entwicklungswerkzeuge", "@sage/xtrem-master-data/menu_item__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__features": "Einstellungen Funktionalität", "@sage/xtrem-master-data/menu_item__features-inventory": "Bestand", "@sage/xtrem-master-data/menu_item__features-items": "Artikel", "@sage/xtrem-master-data/menu_item__features-manufacturing": "", "@sage/xtrem-master-data/menu_item__features-purchasing": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__features-resources": "Ressourcen", "@sage/xtrem-master-data/menu_item__features-sales": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__features-stock": "Bestand", "@sage/xtrem-master-data/menu_item__finance": "Fin<PERSON>zen", "@sage/xtrem-master-data/menu_item__inventory": "Bestand", "@sage/xtrem-master-data/menu_item__inventory-data": "Bestandsdaten", "@sage/xtrem-master-data/menu_item__item-data": "Art<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__items": "Artikel", "@sage/xtrem-master-data/menu_item__licence-plate-data": "<PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/menu_item__location-data": "Lagerplatzdaten", "@sage/xtrem-master-data/menu_item__manufacturing": "Fertigung", "@sage/xtrem-master-data/menu_item__purchasing": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__resources": "Ressourcen", "@sage/xtrem-master-data/menu_item__resources-data": "Ressourcendaten", "@sage/xtrem-master-data/menu_item__sales": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__stock": "Bestand", "@sage/xtrem-master-data/menu_item__stock-data": "Bestandsdaten", "@sage/xtrem-master-data/multiple-existing-business-entities": "Mehrere passende Geschäftsentitäten gefunden. Wählen Si<PERSON> eine aus dem Feld 'Geschäftsentität' aus.", "@sage/xtrem-master-data/node__base_document__no_validation_email_allowed": "Freigabe per E-Mail nicht zulässig für {{document}}", "@sage/xtrem-master-data/node_base_resource_location_site_mismatch": "Der Lagerplatz muss den gleichen Standort wie Ihre Ressource haben.", "@sage/xtrem-master-data/node-extensions__company_extension__property__addresses": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__company_extension__property__contacts": "Kontakte", "@sage/xtrem-master-data/node-extensions__company_extension__property__country": "Land", "@sage/xtrem-master-data/node-extensions__company_extension__property__currency": "Währung", "@sage/xtrem-master-data/node-extensions__company_extension__property__customerOnHoldCheck": "Prüfung Kundensperre", "@sage/xtrem-master-data/node-extensions__company_extension__property__isSequenceNumberIdUsed": "Nummernkreis-ID verwendet", "@sage/xtrem-master-data/node-extensions__company_extension__property__priceScale": "Preisskala", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryAddress": "Hauptadresse", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryContact": "Hauptkontakt", "@sage/xtrem-master-data/node-extensions__company_extension__property__sequenceNumberId": "Nummernkreis-ID", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser": "Ist zugänglich für aktuellen Benutzer", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__failed": "Ist zugänglich für aktuellen Benutzer fehlgeschlagen.", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__nodeName": "Node-Name", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__options": "Optionen", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__propertyOrOperation": "Eigenschaft oder Vorgang", "@sage/xtrem-master-data/node-extensions__country_extension__property__currency": "Währung", "@sage/xtrem-master-data/node-extensions__site_extension__property__businessEntity": "Geschäftsentität", "@sage/xtrem-master-data/node-extensions__site_extension__property__country": "Land", "@sage/xtrem-master-data/node-extensions__site_extension__property__currency": "Währung", "@sage/xtrem-master-data/node-extensions__site_extension__property__defaultLocation": "Standardlagerplatz", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialCurrency": "Finanzwährung", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-master-data/node-extensions__site_extension__property__isFinance": "Buchhaltung", "@sage/xtrem-master-data/node-extensions__site_extension__property__isInventory": "Bestand", "@sage/xtrem-master-data/node-extensions__site_extension__property__isLocationManaged": "Lagerplatz-verwaltet", "@sage/xtrem-master-data/node-extensions__site_extension__property__isManufacturing": "Fertigung", "@sage/xtrem-master-data/node-extensions__site_extension__property__isProjectManagement": "Projektmanagement", "@sage/xtrem-master-data/node-extensions__site_extension__property__isPurchase": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSales": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSequenceNumberIdUsed": "Nummernkreis-ID verwendet", "@sage/xtrem-master-data/node-extensions__site_extension__property__itemSites": "Artikel-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__primaryAddress": "Hauptadresse", "@sage/xtrem-master-data/node-extensions__site_extension__property__sequenceNumberId": "Nummernkreis-ID", "@sage/xtrem-master-data/node-extensions__site_extension__property__siret": "SIRET", "@sage/xtrem-master-data/node-extensions__site_extension__property__stockSite": "Lagerstandort", "@sage/xtrem-master-data/node-extensions__site_extension__property__taxIdNumber": "Steueridentifikationsnummer", "@sage/xtrem-master-data/node-extensions__site_extension__property__timeZone": "Zeitzone", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones": "Zeitzonen", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones__failed": "Zeitzonen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__address__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__property__addressLine1": "Adresszeile 1", "@sage/xtrem-master-data/nodes__address__property__addressLine2": "Adresszeile 2", "@sage/xtrem-master-data/nodes__address__property__city": "Stadt", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddress": "Verkettete Adresse", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddressWithoutName": "Verkettete Adresse ohne Name", "@sage/xtrem-master-data/nodes__address__property__country": "Land", "@sage/xtrem-master-data/nodes__address__property__locationPhoneNumber": "Telefonnummer Standort", "@sage/xtrem-master-data/nodes__address__property__name": "Name", "@sage/xtrem-master-data/nodes__address__property__postcode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__property__region": "Region", "@sage/xtrem-master-data/nodes__address_base__node_name": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__addressLine1": "Adresszeile 1", "@sage/xtrem-master-data/nodes__address_base__property__addressLine2": "Adresszeile 2", "@sage/xtrem-master-data/nodes__address_base__property__city": "Stadt", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddress": "Verkettete Adresse", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddressWithoutName": "Verkettete Adresse ohne Name", "@sage/xtrem-master-data/nodes__address_base__property__country": "Land", "@sage/xtrem-master-data/nodes__address_base__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__address_base__property__locationPhoneNumber": "Telefonnummer Standort", "@sage/xtrem-master-data/nodes__address_base__property__name": "Name", "@sage/xtrem-master-data/nodes__address_base__property__postcode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__region": "Region", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__allergen__node_name": "Allergen", "@sage/xtrem-master-data/nodes__allergen__property__id": "ID", "@sage/xtrem-master-data/nodes__allergen__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__allergen__property__name": "Name", "@sage/xtrem-master-data/nodes__allergen__property__pictogram": "Piktogramm", "@sage/xtrem-master-data/nodes__base_business_relation__node_name": "Basis G<PERSON>chäftsbeziehung", "@sage/xtrem-master-data/nodes__base_business_relation__property__businessEntity": "Geschäftsentität", "@sage/xtrem-master-data/nodes__base_business_relation__property__category": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__country": "Land", "@sage/xtrem-master-data/nodes__base_business_relation__property__currency": "Währung", "@sage/xtrem-master-data/nodes__base_business_relation__property__id": "ID", "@sage/xtrem-master-data/nodes__base_business_relation__property__image": "Bild", "@sage/xtrem-master-data/nodes__base_business_relation__property__internalNote": "Interne Notiz", "@sage/xtrem-master-data/nodes__base_business_relation__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__base_business_relation__property__legalEntity": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__minimumOrderAmount": "Mindestbestellwert", "@sage/xtrem-master-data/nodes__base_business_relation__property__name": "Name", "@sage/xtrem-master-data/nodes__base_business_relation__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryAddress": "Hauptadresse", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryContact": "Hauptkontakt", "@sage/xtrem-master-data/nodes__base_business_relation__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__base_business_relation__property__taxIdNumber": "Steueridentifikationsnummer", "@sage/xtrem-master-data/nodes__base_capability__node_name": "Basiskompetenz", "@sage/xtrem-master-data/nodes__base_capability__property__capabilityLevel": "Kompetenzniveau", "@sage/xtrem-master-data/nodes__base_capability__property__dateEndValid": "Gültigkeitsende", "@sage/xtrem-master-data/nodes__base_capability__property__dateRangeValidity": "Gültigkeit Datumsbereich", "@sage/xtrem-master-data/nodes__base_capability__property__dateStartValid": "Gültigkeitsbeginn", "@sage/xtrem-master-data/nodes__base_capability__property__id": "ID", "@sage/xtrem-master-data/nodes__base_capability__property__name": "Name", "@sage/xtrem-master-data/nodes__base_certificate__node_name": "Basiszertifikat", "@sage/xtrem-master-data/nodes__base_certificate__property__certificationBody": "Zertifizierungsstelle", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfCertification": "Zertifizierungsdatum", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfOriginalCertification": "Ursprüngliches Zertifizierungsdatum", "@sage/xtrem-master-data/nodes__base_certificate__property__id": "ID", "@sage/xtrem-master-data/nodes__base_certificate__property__standard": "Standard", "@sage/xtrem-master-data/nodes__base_certificate__property__validUntil": "Gültig bis", "@sage/xtrem-master-data/nodes__base_distribution_document__fx_rate_not_found": "<PERSON><PERSON> gefunden.", "@sage/xtrem-master-data/nodes__base_distribution_document__node_name": "Basisdokument Distribution", "@sage/xtrem-master-data/nodes__base_distribution_document__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-master-data/nodes__base_distribution_document__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-master-data/nodes__base_distribution_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_distribution_document_line__node_name": "Basisdokumentzeile Distribution", "@sage/xtrem-master-data/nodes__base_distribution_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo": "Zeile aktualisieren", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__failed": "Zeile aktualisieren fehlgeschlagen.", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentIds": "Dokument-IDs", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentLineIds": "IDs Dokumentzeile", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync": "Massensynchronisierung", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync__failed": "Massensynchronisierung fehlgeschlagen.", "@sage/xtrem-master-data/nodes__base_document__financial_site_is_not_financial": "Der Standort muss ein Buchhaltungsstandort sein.", "@sage/xtrem-master-data/nodes__base_document__header_currency_not_updatable": "Die Währung für diesen Datensatz kann nicht geändert werden.", "@sage/xtrem-master-data/nodes__base_document__id_already_exists": "Die ID existiert bereits. Dem aktuellen Dokument wird kein Nummernkreis zugewiesen.", "@sage/xtrem-master-data/nodes__base_document__lines_mandatory": "Das Dokument muss mindestens eine Zeile enthalten.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail": "Genehmigungsanforderungs-E-Mail senden", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__failed": "Genehmigungsanforderungs-E-Mail senden fehlgeschlagen.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__document": "Dokument", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__mutation__setIsPrintedIsSent": "", "@sage/xtrem-master-data/nodes__base_document__mutation__setIsPrintedIsSent__failed": "", "@sage/xtrem-master-data/nodes__base_document__mutation__setIsPrintedIsSent__parameter__document": "", "@sage/xtrem-master-data/nodes__base_document__mutation__setIsPrintedIsSent__parameter__send": "", "@sage/xtrem-master-data/nodes__base_document__no_financial_site": "<PERSON>in Buchhaltungsstandort für den aktuellen Standort gefunden.", "@sage/xtrem-master-data/nodes__base_document__node_name": "Basisdokument", "@sage/xtrem-master-data/nodes__base_document__order_date_not_updatable": "Das Auftragsdatum kann nicht geändert werden, wenn der Status des Datensatzes den Wert In Bearbeitung hat.", "@sage/xtrem-master-data/nodes__base_document__property__approvalPage": "Genehmigungsseite", "@sage/xtrem-master-data/nodes__base_document__property__approvalStatus": "Genehmigungsstatus", "@sage/xtrem-master-data/nodes__base_document__property__approvalUrl": "URL Genehmigung", "@sage/xtrem-master-data/nodes__base_document__property__businessEntityAddress": "Geschäftsentitätenadresse", "@sage/xtrem-master-data/nodes__base_document__property__canPrint": "<PERSON>nn drucken", "@sage/xtrem-master-data/nodes__base_document__property__canUpdateClosedDocument": "Kann abgeschlossenes Dokument aktualisieren", "@sage/xtrem-master-data/nodes__base_document__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-master-data/nodes__base_document__property__currency": "Währung", "@sage/xtrem-master-data/nodes__base_document__property__date": "Datum", "@sage/xtrem-master-data/nodes__base_document__property__displayStatus": "Status anzeigen", "@sage/xtrem-master-data/nodes__base_document__property__documentDate": "Dokumentdatum", "@sage/xtrem-master-data/nodes__base_document__property__documentUrl": "Dokument-URL", "@sage/xtrem-master-data/nodes__base_document__property__externalNote": "Externe Notiz", "@sage/xtrem-master-data/nodes__base_document__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForResync": "Aktualisierung für Resync erzwingen", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-master-data/nodes__base_document__property__internalNote": "Interne Notiz", "@sage/xtrem-master-data/nodes__base_document__property__isExternalNote": "Ist externe Notiz", "@sage/xtrem-master-data/nodes__base_document__property__isOverwriteNote": "Ist Notiz überschreiben", "@sage/xtrem-master-data/nodes__base_document__property__isPrinted": "<PERSON><PERSON> g<PERSON><PERSON><PERSON>t", "@sage/xtrem-master-data/nodes__base_document__property__isSent": "Ist gesendet", "@sage/xtrem-master-data/nodes__base_document__property__isTransferHeaderNote": "Ist Übertragung Notiz Kopfzeile", "@sage/xtrem-master-data/nodes__base_document__property__isTransferLineNote": "Ist Übertragung Notiz Zeile", "@sage/xtrem-master-data/nodes__base_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__page": "Seite", "@sage/xtrem-master-data/nodes__base_document__property__postingDate": "Buchungsdatum", "@sage/xtrem-master-data/nodes__base_document__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__siteAddress": "Standortadresse", "@sage/xtrem-master-data/nodes__base_document__property__status": "Status", "@sage/xtrem-master-data/nodes__base_document__property__stockSite": "Lagerstandort", "@sage/xtrem-master-data/nodes__base_document__property__text": "Text", "@sage/xtrem-master-data/nodes__base_document__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-master-data/nodes__base_document__site_is_not_inventory": "Der Standort muss ein Lagerstandort sein.", "@sage/xtrem-master-data/nodes__base_document__stock_site_is_not_inventory": "Der Standort muss ein Lagerstandort sein.", "@sage/xtrem-master-data/nodes__base_document__stock_site_legal_company_mismatch": "Der Standort muss zum gleichen Unternehmen gehören.", "@sage/xtrem-master-data/nodes__base_document__update_not_allowed_status_closed": "Der Datensatz für das Dokument kann nicht aktualisiert werden, wenn er abgeschlossen ist: {{number}}.", "@sage/xtrem-master-data/nodes__base_document_item_line__node_name": "Basisdokumentartikelzeile", "@sage/xtrem-master-data/nodes__base_document_item_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentId": "Dokument-ID", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-master-data/nodes__base_document_item_line__property__externalNote": "Externe Notiz", "@sage/xtrem-master-data/nodes__base_document_item_line__property__forceUpdateForStock": "Aktualisierung für Bestand erzwingen", "@sage/xtrem-master-data/nodes__base_document_item_line__property__internalNote": "Interne Notiz", "@sage/xtrem-master-data/nodes__base_document_item_line__property__isExternalNote": "Ist externe Notiz", "@sage/xtrem-master-data/nodes__base_document_item_line__property__item": "Artikel", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemDescription": "Artikelbezeichnung", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemSite": "Artikel-Standort", "@sage/xtrem-master-data/nodes__base_document_item_line__property__origin": "Ursprung", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_item_line__property__siteLinkedAddress": "Verknüpfte Standortadresse", "@sage/xtrem-master-data/nodes__base_document_item_line__property__status": "Status", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSite": "Lagerstandort", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSiteLinkedAddress": "Verknüpfte Lagerstandortadresse", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unit": "Einheit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unitToStockUnitConversionFactor": "Umrechnungsfaktor Einheit in Lagereinheit", "@sage/xtrem-master-data/nodes__base_document_line__node_name": "Basisdokumentzeilen", "@sage/xtrem-master-data/nodes__base_document_line__property__documentId": "Dokument-ID", "@sage/xtrem-master-data/nodes__base_document_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__node_name": "Abfrage Basisdokumentzeile", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__company": "Unternehmen", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__date": "Datum", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__fromItem": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__itemCategory": "Artikelkategorie", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__toItem": "Artikel bis", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_inbound_document__node_name": "Basisdokument eingehend", "@sage/xtrem-master-data/nodes__base_inbound_document__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-master-data/nodes__base_inbound_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_inbound_document_line__node_name": "Basisdokumentzeile e<PERSON>hend", "@sage/xtrem-master-data/nodes__base_inbound_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__node_name": "Basisdokument Eingang eingehend", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__node_name": "Basisdokumentzeile Eingang eingehend", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_line_discount_charge__improper_calculation_rule": "Die Berechnungsregel ist nicht korrekt. Verwenden Sie für Prozentsätze die Berechnungsregel 'Nach Einheit'.", "@sage/xtrem-master-data/nodes__base_line_discount_charge__node_name": "Basiszeile Rabatt oder Zuschlag", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__amount": "Betrag", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basisDeterminated": "<PERSON><PERSON> ermit<PERSON>t", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationBasis": "Berechnungsgrundlage", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationRule": "Berechnungsregel", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__sign": "Vorzeichen", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__value": "Wert", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueDeterminated": "<PERSON><PERSON> ermittelt", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueType": "Werttyp", "@sage/xtrem-master-data/nodes__base_line_to_line__node_name": "Basiszeile zu Zeile", "@sage/xtrem-master-data/nodes__base_line_to_line__property__amount": "Betrag", "@sage/xtrem-master-data/nodes__base_line_to_line__property__currency": "Währung", "@sage/xtrem-master-data/nodes__base_line_to_line__property__from": "<PERSON>", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-master-data/nodes__base_line_to_line__property__stockUnit": "Lagereinheit", "@sage/xtrem-master-data/nodes__base_line_to_line__property__to": "Bis", "@sage/xtrem-master-data/nodes__base_line_to_line__property__unit": "Einheit", "@sage/xtrem-master-data/nodes__base_outbound_document__node_name": "Basisdokument ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_document__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-master-data/nodes__base_outbound_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_document_line__node_name": "Basisdokumentzeile ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_outbound_order_document__node_name": "Basisdokument Auftrag ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_order_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__node_name": "Basisdokumentzeile Auftrag ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__node_name": "Basisdokument Versand ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__node_name": "Basisdokumentzeile Versand ausgehend", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__property__document": "Dokument", "@sage/xtrem-master-data/nodes__base_resource__node_name": "Basisressource", "@sage/xtrem-master-data/nodes__base_resource__property__activeFrom": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/nodes__base_resource__property__activeRange": "Aktiver Bereich", "@sage/xtrem-master-data/nodes__base_resource__property__activeTo": "Aktiv bis", "@sage/xtrem-master-data/nodes__base_resource__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__base_resource__property__efficiency": "Effizienz", "@sage/xtrem-master-data/nodes__base_resource__property__id": "ID", "@sage/xtrem-master-data/nodes__base_resource__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__base_resource__property__location": "Lagerplatz", "@sage/xtrem-master-data/nodes__base_resource__property__name": "Name", "@sage/xtrem-master-data/nodes__base_resource__property__resourceCostCategories": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/nodes__base_resource__property__resourceImage": "Bild <PERSON>", "@sage/xtrem-master-data/nodes__base_resource__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_resource__property__weeklyShift": "Wöchentliche Schicht", "@sage/xtrem-master-data/nodes__base_sequence_number__node_name": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__property__componentLength": "Länge Komponente", "@sage/xtrem-master-data/nodes__base_sequence_number__property__components": "Komponenten", "@sage/xtrem-master-data/nodes__base_sequence_number__property__definitionLevel": "Definitionsebene", "@sage/xtrem-master-data/nodes__base_sequence_number__property__id": "ID", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isChronological": "<PERSON><PERSON> chronologisch", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isClearedByReset": "Ist Löschen durch Zurücksetzen", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isUsed": "Wird verwendet", "@sage/xtrem-master-data/nodes__base_sequence_number__property__legislation": "Rechtsordnung", "@sage/xtrem-master-data/nodes__base_sequence_number__property__minimumLength": "Mindestlänge", "@sage/xtrem-master-data/nodes__base_sequence_number__property__name": "Name", "@sage/xtrem-master-data/nodes__base_sequence_number__property__rtzLevel": "Rücksetzebene", "@sage/xtrem-master-data/nodes__base_sequence_number__property__sequenceNumberAssignments": "Zuweisungen Nummernkreise", "@sage/xtrem-master-data/nodes__base_sequence_number__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames": "Dokumentnodenamen abrufen", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames__failed": "Dokumentnodenamen abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__base_sequence_number_component__node_name": "Basis Nummernkreiskomponente", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__bom_revision_sequence__node_name": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__components": "Komponenten", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isDefault": "Ist Standard", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isSequenceGenerated": "Ist Nummernkreis generiert", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__node_name": "Komponente Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__business_entity__address_mandatory": "Die Geschäftsentiät muss mindestens eine Adresse haben.", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret": "Das Format ist nicht korrekt. Verwenden Sie das Format für SIRET-Nummern: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__node_name": "Geschäftsentität", "@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id": "Das Format ist nicht korrekt. Verwenden Sie das Format für Steuer-IDs: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__primary_address": "Die Geschäftsentität kann nur eine Hauptadresse haben.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_active": "Sie müssen die Hauptadresse der Geschäftsentität aktivieren.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_mandatory": "<PERSON><PERSON> Si<PERSON> dieser Geschäftsentiät mindestens eine Hauptadresse zu.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact": "Die Adresse muss mindestens einen Hauptkontakt haben.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact_active": "Sie müssen den Hauptkontakt der Adresse aktivieren.", "@sage/xtrem-master-data/nodes__business_entity__property__addresses": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__contacts": "Kontakte", "@sage/xtrem-master-data/nodes__business_entity__property__country": "Land", "@sage/xtrem-master-data/nodes__business_entity__property__currency": "Währung", "@sage/xtrem-master-data/nodes__business_entity__property__customer": "Kunde", "@sage/xtrem-master-data/nodes__business_entity__property__id": "ID", "@sage/xtrem-master-data/nodes__business_entity__property__image": "Bild", "@sage/xtrem-master-data/nodes__business_entity__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__business_entity__property__isCustomer": "Kunde", "@sage/xtrem-master-data/nodes__business_entity__property__isSite": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__isSupplier": "Lieferant", "@sage/xtrem-master-data/nodes__business_entity__property__legalEntity": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__name": "Name", "@sage/xtrem-master-data/nodes__business_entity__property__parent": "Übergeordnet", "@sage/xtrem-master-data/nodes__business_entity__property__primaryAddress": "Hauptadresse", "@sage/xtrem-master-data/nodes__business_entity__property__primaryContact": "Hauptkontakt", "@sage/xtrem-master-data/nodes__business_entity__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__business_entity__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__business_entity__property__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/nodes__business_entity__property__website": "Webseite", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity_address__node_name": "Neue Geschäftsentitätenadresse", "@sage/xtrem-master-data/nodes__business_entity_address__property__businessEntity": "Geschäftsentität", "@sage/xtrem-master-data/nodes__business_entity_address__property__concatenatedAddress": "Verkettete Adresse", "@sage/xtrem-master-data/nodes__business_entity_address__property__contacts": "Kontakte", "@sage/xtrem-master-data/nodes__business_entity_address__property__deliveryDetail": "Lieferdetails", "@sage/xtrem-master-data/nodes__business_entity_address__property__isPrimary": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity_address__property__primaryContact": "Hauptkontakt", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity_contact__node_name": "Kontakt Geschäftsentität", "@sage/xtrem-master-data/nodes__business_entity_contact__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity_contact__property__businessEntity": "Geschäftsentität", "@sage/xtrem-master-data/nodes__business_entity_contact__property__isPrimary": "<PERSON><PERSON> p<PERSON>", "@sage/xtrem-master-data/nodes__business-entity-type-control-customer": "Diese Zahlungsbedingung ist bereits Kunden zugewiesen.", "@sage/xtrem-master-data/nodes__business-entity-type-control-supplier": "Diese Zahlungsbedingung ist bereits Lieferanten zugewiesen.", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__capability_level__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__capability_level__node_name": "Kompetenzniveau", "@sage/xtrem-master-data/nodes__capability_level__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__capability_level__property__id": "ID", "@sage/xtrem-master-data/nodes__capability_level__property__level": "Niveau", "@sage/xtrem-master-data/nodes__capability_level__property__name": "Name", "@sage/xtrem-master-data/nodes__company__address_mandatory": "<PERSON>sen Si<PERSON> dem Unternehmen mindestens eine Adresse zu.", "@sage/xtrem-master-data/nodes__company__primary_address": "<PERSON> Unternehmen kann nur eine Hauptadress<PERSON> haben.", "@sage/xtrem-master-data/nodes__company__primary_address_mandatory": "<PERSON>sen Si<PERSON> dem Unternehmen mindestens eine Hauptadresse zu.", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__company_address__node_name": "Unternehmensadresse", "@sage/xtrem-master-data/nodes__company_address__property__company": "Unternehmen", "@sage/xtrem-master-data/nodes__company_address__property__contacts": "Kontakte", "@sage/xtrem-master-data/nodes__company_address__property__isPrimary": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__company_contact__node_name": "Kontakt Unternehmen", "@sage/xtrem-master-data/nodes__company_contact__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__company_contact__property__company": "Unternehmen", "@sage/xtrem-master-data/nodes__company_contact__property__isPrimary": "<PERSON><PERSON> p<PERSON>", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__contact__node_name": "Kontakt", "@sage/xtrem-master-data/nodes__contact__property__email": "E-Mail", "@sage/xtrem-master-data/nodes__contact__property__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact__property__image": "Bild", "@sage/xtrem-master-data/nodes__contact__property__lastName": "Nachname", "@sage/xtrem-master-data/nodes__contact__property__locationPhoneNumber": "Telefonnummer Standort", "@sage/xtrem-master-data/nodes__contact__property__position": "Position", "@sage/xtrem-master-data/nodes__contact__property__preferredName": "Bevorzugter Name", "@sage/xtrem-master-data/nodes__contact__property__role": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact__property__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact_base__node_name": "Basis Ko<PERSON>kt", "@sage/xtrem-master-data/nodes__contact_base__not-a-valid-email": "E-Mail-<PERSON><PERSON><PERSON> nicht korrekt: {{email}}", "@sage/xtrem-master-data/nodes__contact_base__property__contact": "Kontakt", "@sage/xtrem-master-data/nodes__contact_base__property__displayName": "Anzeigename", "@sage/xtrem-master-data/nodes__contact_base__property__email": "E-Mail", "@sage/xtrem-master-data/nodes__contact_base__property__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact_base__property__image": "Bild", "@sage/xtrem-master-data/nodes__contact_base__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__contact_base__property__lastName": "Nachname", "@sage/xtrem-master-data/nodes__contact_base__property__locationPhoneNumber": "Telefonnummer Standort", "@sage/xtrem-master-data/nodes__contact_base__property__position": "Position", "@sage/xtrem-master-data/nodes__contact_base__property__preferredName": "Bevorzugter Name", "@sage/xtrem-master-data/nodes__contact_base__property__role": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact_base__property__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__container__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__container__property__consumedLocationCapacity": "Verbrauchte Lagerplatzkapazität", "@sage/xtrem-master-data/nodes__container__property__id": "ID", "@sage/xtrem-master-data/nodes__container__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__container__property__isInternal": "Intern", "@sage/xtrem-master-data/nodes__container__property__isSingleItem": "Einzelartikel", "@sage/xtrem-master-data/nodes__container__property__isSingleLot": "Einzelcharge", "@sage/xtrem-master-data/nodes__container__property__labelFormat": "Etikettenformat", "@sage/xtrem-master-data/nodes__container__property__name": "Name", "@sage/xtrem-master-data/nodes__container__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__container__property__storageCapacity": "Lagerkapazität", "@sage/xtrem-master-data/nodes__container__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__container__sequence-number-not-required": "<PERSON><PERSON>r den externen Behälter ist kein Nummernkreis erforderlich.", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__cost_category__node_name": "Kostenkategorie", "@sage/xtrem-master-data/nodes__cost_category__property__costCategoryType": "Typ Kostenkategorie", "@sage/xtrem-master-data/nodes__cost_category__property__id": "ID", "@sage/xtrem-master-data/nodes__cost_category__property__isMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__cost_category__property__name": "Name", "@sage/xtrem-master-data/nodes__cost-category__can-have-only-one-cost-type-of": "Sie können nur eine Kostenart vom Typ {{costCategoryType}} haben.", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__currency__deleting-record": "Löscht den Datensatz {{exchangeRateId}} (<PERSON><PERSON>nge {{exchangeRateLength}}) aus den Wechselkursen.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate": "Wechselkurs speichern", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__failed": "Wechselkurs speichern fehlgeschlagen.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__base": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__dateRate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__rate": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__node_name": "Währung", "@sage/xtrem-master-data/nodes__currency__property__currentExchangeRates": "Aktuelle Wechselkurse", "@sage/xtrem-master-data/nodes__currency__property__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/nodes__currency__property__exchangeRates": "Wechselkurse", "@sage/xtrem-master-data/nodes__currency__property__exchangeRatesDestinationInverse": "Umkehrung Ziel Wechselkurse", "@sage/xtrem-master-data/nodes__currency__property__icon": "Symbol", "@sage/xtrem-master-data/nodes__currency__property__id": "ID", "@sage/xtrem-master-data/nodes__currency__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__currency__property__lastUpdate": "Letzte Aktualisierung", "@sage/xtrem-master-data/nodes__currency__property__name": "Name", "@sage/xtrem-master-data/nodes__currency__property__rounding": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__property__symbol": "Symbol", "@sage/xtrem-master-data/nodes__currency_id": "Die ID muss aus 3 Zeichen bestehen.", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer__at_least_one_active_delivery_address_mandatory": "<PERSON>sen Si<PERSON> diesem Kunden mindestens eine aktive Lieferadresse zu.", "@sage/xtrem-master-data/nodes__customer__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__customer__enter_a_shipping_address_for_customer": "Erfassen Sie eine Lieferadresse für den Kunden.", "@sage/xtrem-master-data/nodes__customer__incorrect_format_siret": "{{siret}} ist keine gültige SIRET-Nummer. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__customer__node_name": "Kunde", "@sage/xtrem-master-data/nodes__customer__not-a-valid-tax-id": "{{taxIdNumber}} ist keine gültige Steuer-ID. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address": "Der Kunde kann nur eine Hauptlieferadresse haben.", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address_mandatory": "Weisen Sie dem Kunden eine aktive Hauptlieferadresse zu.", "@sage/xtrem-master-data/nodes__customer__primary_ship_to_address_mandatory": "Der Kunde muss mindestens eine aktive Hauptlieferadresse haben.", "@sage/xtrem-master-data/nodes__customer__property__billToAddress": "Re<PERSON>nungsadress<PERSON>", "@sage/xtrem-master-data/nodes__customer__property__billToCustomer": "Rechnungsempfänger", "@sage/xtrem-master-data/nodes__customer__property__category": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__creditLimit": "Kreditlimit", "@sage/xtrem-master-data/nodes__customer__property__deliveryAddresses": "Lieferadressen", "@sage/xtrem-master-data/nodes__customer__property__displayStatus": "Status anzeigen", "@sage/xtrem-master-data/nodes__customer__property__isOnHold": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__itemPrices": "Artikelpreise", "@sage/xtrem-master-data/nodes__customer__property__items": "Artikel", "@sage/xtrem-master-data/nodes__customer__property__payByAddress": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__payByCustomer": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-master-data/nodes__customer__property__primaryShipToAddress": "Hauptlieferadresse", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer_price_reason__node_name": "Grund für Kundenpreis", "@sage/xtrem-master-data/nodes__customer_price_reason__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__customer_price_reason__property__id": "ID", "@sage/xtrem-master-data/nodes__customer_price_reason__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__customer_price_reason__property__name": "Name", "@sage/xtrem-master-data/nodes__customer_price_reason__property__priority": "Priorität", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer_supplier_category__change_not_possible_category_is_used_on_customer": "<PERSON>e können einen Kundenkategorietyp nur ändern, wenn er keinem Kunden zugewiesen ist.", "@sage/xtrem-master-data/nodes__customer_supplier_category__node_name": "Lieferanten- und Kundenkategorie", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__id": "ID", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isCustomer": "Kunde", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSequenceNumberManagement": "Ist Verwaltung Nummernkreise", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSupplier": "Lieferant", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__name": "Name", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__customer-minimum__order_amount-cannot-be-negative": "Der Mindestbestellwert kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__customer-supplier-category__customer_or_supplier": "Sie müssen Kunde oder Lieferant auswählen.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-cannot-be-set": "Der Nummernkreis kann nicht gesetzt werden.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-is-mandatory": "Der Nummernkreis ist erforderlich.", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift__no_details_on_full_day_shift": "Eine Ganztagesschicht kann keine Schichtdetails haben.", "@sage/xtrem-master-data/nodes__daily_shift__no_overlap_on_shift_details": "Die Schichtdetails dürfen sich nicht überschneiden.", "@sage/xtrem-master-data/nodes__daily_shift__node_name": "Tägliche Schicht", "@sage/xtrem-master-data/nodes__daily_shift__property__capacity": "Kapazität", "@sage/xtrem-master-data/nodes__daily_shift__property__formattedCapacity": "Formatierte Kapazität", "@sage/xtrem-master-data/nodes__daily_shift__property__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift__property__isFullDay": "Ganzer Tag", "@sage/xtrem-master-data/nodes__daily_shift__property__name": "Name", "@sage/xtrem-master-data/nodes__daily_shift__property__shiftDetails": "Schichtdetails", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift_detail__node_name": "Details tägliche Schicht", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__dailyShift": "Tägliche Schicht", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__shiftDetail": "Schichtdetails", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__delivery_detail__node_name": "Lieferdetails", "@sage/xtrem-master-data/nodes__delivery_detail__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__incoterm": "Incoterms-Regel", "@sage/xtrem-master-data/nodes__delivery_detail__property__isActive": "Ist aktiv", "@sage/xtrem-master-data/nodes__delivery_detail__property__isFridayWorkDay": "Ist Arbeitstag Freitag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isMondayWorkDay": "Ist Arbeitstag Montag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isPrimary": "<PERSON><PERSON> p<PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSaturdayWorkDay": "Ist Arbeitstag Samstag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSundayWorkDay": "Ist Arbeitstag Sonntag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isThursdayWorkDay": "Ist Arbeitstag Donnerstag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isTuesdayWorkDay": "Ist Arbeitstag Dienstag", "@sage/xtrem-master-data/nodes__delivery_detail__property__isWednesdayWorkDay": "Ist Arbeitstag Mittwoch", "@sage/xtrem-master-data/nodes__delivery_detail__property__leadTime": "Frist", "@sage/xtrem-master-data/nodes__delivery_detail__property__mode": "Modus", "@sage/xtrem-master-data/nodes__delivery_detail__property__shipmentSite": "Versandstandort", "@sage/xtrem-master-data/nodes__delivery_detail__property__workDaysSelection": "Auswahl Arbeitstage", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__delivery_mode__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__delivery_mode__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__delivery_mode__property__id": "ID", "@sage/xtrem-master-data/nodes__delivery_mode__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__delivery_mode__property__name": "Name", "@sage/xtrem-master-data/nodes__detailed_resource__node_name": "Detaillierte Ressource", "@sage/xtrem-master-data/nodes__detailed_resource__property__efficiency": "Effizienz", "@sage/xtrem-master-data/nodes__detailed_resource__property__location": "Lagerplatz", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceCostCategories": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceGroup": "Ressourcengruppe", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__dev_tools__node_name": "Entwicklungswerkzeuge", "@sage/xtrem-master-data/nodes__dev_tools__property__id": "ID", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__employee__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__employee__property__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__employee__property__id": "ID", "@sage/xtrem-master-data/nodes__employee__property__image": "Bild", "@sage/xtrem-master-data/nodes__employee__property__isActive": "Ist aktiv", "@sage/xtrem-master-data/nodes__employee__property__lastName": "Nachname", "@sage/xtrem-master-data/nodes__employee__property__name": "Name", "@sage/xtrem-master-data/nodes__employee__property__resource": "Ressource", "@sage/xtrem-master-data/nodes__employee__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__exchange_rate__node_name": "Wechselkurs", "@sage/xtrem-master-data/nodes__exchange_rate__property__base": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__property__dateRate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__property__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__property__divisor": "Divisor", "@sage/xtrem-master-data/nodes__exchange_rate__property__rate": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__property__shortDescription": "Kurzbezeichnung", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__failed": "Kurs umrechnen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__amount": "Betrag", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__base": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__rateDate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf": "Verwenden Sie für die NAF-Nummer das folgende Format: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs": "Verwenden Sie für die RCS-Nummer das folgende Format: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren": "Verwenden Sie für die SIREN-Nummer das folgende Format: {{format}}", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__ghs_classification__node_name": "GHS-Klassifizierung", "@sage/xtrem-master-data/nodes__ghs_classification__property__hazard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__ghs_classification__property__id": "ID", "@sage/xtrem-master-data/nodes__ghs_classification__property__name": "Name", "@sage/xtrem-master-data/nodes__ghs_classification__property__pictogram": "Piktogramm", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__group_resource__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__group_resource__node_name": "Ressourcengruppe", "@sage/xtrem-master-data/nodes__group_resource__property__efficiency": "Effizienz", "@sage/xtrem-master-data/nodes__group_resource__property__minCapabilityLevel": "Mindest-Kompetenzniveau", "@sage/xtrem-master-data/nodes__group_resource__property__replacements": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__group_resource__property__resources": "Ressourcen", "@sage/xtrem-master-data/nodes__group_resource__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__incoterm__node_name": "Incoterms-Regel", "@sage/xtrem-master-data/nodes__incoterm__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__incoterm__property__id": "ID", "@sage/xtrem-master-data/nodes__incoterm__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__incoterm__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_origin__node_name": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section__node_name": "Bereich indirekte Kosten", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__calculationMethod": "Berechnungsmethode", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__node_name": "Zeile Bereich indirekte Kosten", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostOrigin": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostSection": "Bereich indirekte Kosten", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__percentage": "Prozentsatz", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item__bom_revision_sequence_number_not_managed": "Sie können einer Stückliste nur dann eine Änderungsnummer hinzufügen, wenn der Artikel stücklistenänderungssgeführt ist.", "@sage/xtrem-master-data/nodes__item__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__item__cannot_change_purchased_property_supplier_exists": "Sie sind nicht dazu berechtigt, die Eigenschaft eingekauft zu ändern. In den Lieferanten sind bereits Datensätze vorhanden.", "@sage/xtrem-master-data/nodes__item__cannot_change_sold_property_customers_exists": "Sie sind nicht dazu berechtigt, die Eigenschaft verkauft zu ändern. In den Kunden sind bereits Datensätze vorhanden.", "@sage/xtrem-master-data/nodes__item__commodity_code_EU_format": "Verwenden Sie für die EU-Warennummer das folgende Format: {{format}}", "@sage/xtrem-master-data/nodes__item__commodity_code_format": "Verwenden Sie für die Warennummer das folgende Format: {{format}}", "@sage/xtrem-master-data/nodes__item__community_code_EU_format": "{{communityCodeEU}} ist keine gültige EU-Nummer. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__item__density-cannot-be-negative": "Die Dichte kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item__expiration_mangement_cannot_be_enabled_without_lot_management": "Die Ablaufverwaltung kann für nicht chargenverwaltete Artikel nicht aktiviert werden.", "@sage/xtrem-master-data/nodes__item__invalid-quantity": "Die Höchstmenge muss größer als die Mindestmenge sein.", "@sage/xtrem-master-data/nodes__item__mandatory_property": "Erfassen Sie einen Wert für seriennummernverwaltete Artikel.", "@sage/xtrem-master-data/nodes__item__min-price-cannot-be-set-if-no-currency-is-defined": "<PERSON>e müssen eine Währung auswählen, bevor <PERSON> einen Mindestpreis erfassen können.", "@sage/xtrem-master-data/nodes__item__node_name": "Artikel", "@sage/xtrem-master-data/nodes__item__not-a-volume-measure": "Die Einheit {{unitOfMeasure}} bezieht sich nicht auf das Volumen.", "@sage/xtrem-master-data/nodes__item__not-a-weight-measure": "Die Einheit {{unitOfMeasure}} bezieht sich nicht auf das Gewicht.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-negative": "Der <PERSON> kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-set-if-no-currency-is-defined": "<PERSON>e müssen eine Währung auswählen, bevor <PERSON> einen Grundpreis erfassen können.", "@sage/xtrem-master-data/nodes__item__property__allergens": "Allergene", "@sage/xtrem-master-data/nodes__item__property__basePrice": "Grundpreis", "@sage/xtrem-master-data/nodes__item__property__bomRevisionSequenceNumber": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/nodes__item__property__capacity": "Kapazität", "@sage/xtrem-master-data/nodes__item__property__category": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__classifications": "Klassifizierungen", "@sage/xtrem-master-data/nodes__item__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__currency": "Währung", "@sage/xtrem-master-data/nodes__item__property__customerPrices": "Kundenpreise", "@sage/xtrem-master-data/nodes__item__property__customers": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__density": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__item__property__eanNumber": "EAN", "@sage/xtrem-master-data/nodes__item__property__id": "ID", "@sage/xtrem-master-data/nodes__item__property__image": "Bild", "@sage/xtrem-master-data/nodes__item__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__item__property__isBomRevisionManaged": "Ist stücklistsenänderungsgeführt", "@sage/xtrem-master-data/nodes__item__property__isBought": "Eingekauft", "@sage/xtrem-master-data/nodes__item__property__isExpiryManaged": "Ablauf-verwal<PERSON>t", "@sage/xtrem-master-data/nodes__item__property__isManufactured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__isPhantom": "Ist Phantom", "@sage/xtrem-master-data/nodes__item__property__isPotencyManagement": "Konzentrationsverwaltung", "@sage/xtrem-master-data/nodes__item__property__isSold": "Verkauft", "@sage/xtrem-master-data/nodes__item__property__isStockManaged": "Bestandsgeführt", "@sage/xtrem-master-data/nodes__item__property__isTraceabilityManagement": "Verwaltung Rückverfolgung", "@sage/xtrem-master-data/nodes__item__property__itemSites": "Artikel-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__lotManagement": "Chargenverwaltung", "@sage/xtrem-master-data/nodes__item__property__lotSequenceNumber": "Nummernkreis Charge", "@sage/xtrem-master-data/nodes__item__property__maximumSalesQuantity": "Höchstverkaufsmenge", "@sage/xtrem-master-data/nodes__item__property__minimumPrice": "Mindestpreis", "@sage/xtrem-master-data/nodes__item__property__minimumSalesQuantity": "Mindestverkaufsmenge", "@sage/xtrem-master-data/nodes__item__property__name": "Name", "@sage/xtrem-master-data/nodes__item__property__prices": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__purchaseUnit": "Einkaufseinheit", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversion": "Umrechnung Einkaufseinheit in Lagereinheit", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversionDedicated": "Dedizierte Umrechnung Einkaufseinheit in Lagereinheit", "@sage/xtrem-master-data/nodes__item__property__salesUnit": "Verkaufseinheit", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversion": "Umrechnung Verkaufs- in Lagereinheit", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversionDedicated": "Dedizierte Umrechnung Verkaufseinheit in Lagereinheit", "@sage/xtrem-master-data/nodes__item__property__serialNumberManagement": "Seriennummernverwaltung", "@sage/xtrem-master-data/nodes__item__property__serialNumberSequenceNumber": "Nummernkreis Seriennummer", "@sage/xtrem-master-data/nodes__item__property__serialNumberUsage": "Verwendung Seriennummer", "@sage/xtrem-master-data/nodes__item__property__status": "Status", "@sage/xtrem-master-data/nodes__item__property__stockUnit": "Lagereinheit", "@sage/xtrem-master-data/nodes__item__property__supplierPrices": "Lieferantenpreise", "@sage/xtrem-master-data/nodes__item__property__suppliers": "Lieferanten", "@sage/xtrem-master-data/nodes__item__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__useSupplierSerialNumbers": "Lieferantenseriennummern verwenden", "@sage/xtrem-master-data/nodes__item__property__volume": "Volumen", "@sage/xtrem-master-data/nodes__item__property__volumeUnit": "Volumeneinheit", "@sage/xtrem-master-data/nodes__item__property__weight": "Gewicht", "@sage/xtrem-master-data/nodes__item__property__weightUnit": "Gewichtseinheit", "@sage/xtrem-master-data/nodes__item__purchase_unit_not_0_decimal_places": "Die Einkaufseinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/nodes__item__sales_unit_not_0_decimal_places": "Die Verkaufseinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/nodes__item__standard_unit_of_measure_not_changeable": "Die Umrechnung von {{from}} nach {{to}} ist Standard. Sie kann nicht geändert werden.", "@sage/xtrem-master-data/nodes__item__stock_unit_not_0_decimal_places": "Die Lagereinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/nodes__item__the_lot_and_serial_number_cannot_use_the_same_sequence_number": "Die Charge und die Seriennummer können nicht den gleichen Nummernkreis verwenden.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_lot": "Die Eigenschaft kann nicht mit nicht chargenverwalteten Artikeln verwendet werden.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_serial_number": "<PERSON>ür nicht seriennummernverwaltete Artikel kann kein Seriennummernkreis verwendet werden.", "@sage/xtrem-master-data/nodes__item__volume-cannot-be-negative": "Das <PERSON> kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item__weight-cannot-be-negative": "<PERSON> Gewicht kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_allergen__node_name": "Allergen Artikel", "@sage/xtrem-master-data/nodes__item_allergen__property__allergen": "Allergen", "@sage/xtrem-master-data/nodes__item_allergen__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used": "<PERSON>e können den Typ für eine Artikelkategorie, die verwendet wird, nicht ändern.", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used_on_supplier": "Sie können einen Lieferantenkategorietyp nur ändern, wenn er keinem Lieferanten zugewiesen ist.", "@sage/xtrem-master-data/nodes__item_category__node_name": "Artikelkategorie", "@sage/xtrem-master-data/nodes__item_category__property__id": "ID", "@sage/xtrem-master-data/nodes__item_category__property__isSequenceNumberManagement": "Ist Verwaltung Nummernkreise", "@sage/xtrem-master-data/nodes__item_category__property__name": "Name", "@sage/xtrem-master-data/nodes__item_category__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__item_category__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_classifications__node_name": "Klassifizierungen Artikel", "@sage/xtrem-master-data/nodes__item_classifications__property__classification": "Klassifizierung", "@sage/xtrem-master-data/nodes__item_classifications__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_customer__node_name": "Artikel-Kunde", "@sage/xtrem-master-data/nodes__item_customer__property__customer": "Kunde", "@sage/xtrem-master-data/nodes__item_customer__property__id": "ID", "@sage/xtrem-master-data/nodes__item_customer__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__item_customer__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_customer__property__maximumSalesQuantity": "Höchstverkaufsmenge", "@sage/xtrem-master-data/nodes__item_customer__property__minimumSalesQuantity": "Mindestverkaufsmenge", "@sage/xtrem-master-data/nodes__item_customer__property__name": "Name", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnit": "Verkaufseinheit", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnitToStockUnitConversion": "Umrechnung Verkaufs- in Lagereinheit", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_customer_price__node_name": "Preis Artikel-Kunde", "@sage/xtrem-master-data/nodes__item_customer_price__property__charge": "Z<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__currency": "Währung", "@sage/xtrem-master-data/nodes__item_customer_price__property__customer": "Kunde", "@sage/xtrem-master-data/nodes__item_customer_price__property__discount": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__endDate": "Enddatum", "@sage/xtrem-master-data/nodes__item_customer_price__property__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__item_customer_price__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__item_customer_price__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_customer_price__property__price": "Pre<PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__priceReason": "Preisgrund", "@sage/xtrem-master-data/nodes__item_customer_price__property__salesSite": "Verkaufsstandort", "@sage/xtrem-master-data/nodes__item_customer_price__property__startDate": "Startdatum", "@sage/xtrem-master-data/nodes__item_customer_price__property__stockSite": "Lagerstandort", "@sage/xtrem-master-data/nodes__item_customer_price__property__toQuantity": "Menge bis", "@sage/xtrem-master-data/nodes__item_customer_price__property__unit": "Einheit", "@sage/xtrem-master-data/nodes__item_customer_price__property__validUnits": "Gültige Einheiten", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice": "Verkaufspreis abrufen", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__failed": "Verkaufspreis abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__parameter__priceParameters": "Preisparameter", "@sage/xtrem-master-data/nodes__item_not_active": "<PERSON>e müssen die inaktiven Artikel entfernen, bevor <PERSON> den Dokumentstatus ändern.", "@sage/xtrem-master-data/nodes__item_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_price__node_name": "Artikelpreis", "@sage/xtrem-master-data/nodes__item_price__property__currency": "Währung", "@sage/xtrem-master-data/nodes__item_price__property__dateValid": "Gültigkeitsdatum", "@sage/xtrem-master-data/nodes__item_price__property__dateValidFrom": "Gültigkeitsdatum von", "@sage/xtrem-master-data/nodes__item_price__property__dateValidTo": "Gültigkeitsdatum bis", "@sage/xtrem-master-data/nodes__item_price__property__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__item_price__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_price__property__price": "Pre<PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__priority": "Priorität", "@sage/xtrem-master-data/nodes__item_price__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__item_price__property__toQuantity": "Menge bis", "@sage/xtrem-master-data/nodes__item_price__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__unit": "Einheit", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice": "Einkaufspreis abrufen", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice__parameter__priceParameters": "Preisparameter", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site__deletion_forbidden": "Es existiert ein Work-In-Progress-Datensatz mit diesem Artikel-Standort. Löschen nicht möglich.", "@sage/xtrem-master-data/nodes__item_site__node_name": "Artikel-Standort", "@sage/xtrem-master-data/nodes__item_site__property__batchQuantity": "Losmenge", "@sage/xtrem-master-data/nodes__item_site__property__completedProductDefaultLocation": "Standardlagerplatz fertiggestellter Artikel", "@sage/xtrem-master-data/nodes__item_site__property__costs": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__defaultSupplier": "Standardlieferant", "@sage/xtrem-master-data/nodes__item_site__property__economicOrderQuantity": "Optimale Bestellmenge", "@sage/xtrem-master-data/nodes__item_site__property__expectedQuantity": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__id": "ID", "@sage/xtrem-master-data/nodes__item_site__property__inboundDefaultLocation": "Standardlagerplatz Eingänge", "@sage/xtrem-master-data/nodes__item_site__property__indirectCostSection": "Bereich indirekte Kosten", "@sage/xtrem-master-data/nodes__item_site__property__isOrderToOrder": "Auftrag-zu-Auftrag", "@sage/xtrem-master-data/nodes__item_site__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_site__property__itemSiteCost": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/nodes__item_site__property__outboundDefaultLocation": "Standardlagerplatz Ausgänge", "@sage/xtrem-master-data/nodes__item_site__property__preferredProcess": "Bevorzugte Verarbeitung", "@sage/xtrem-master-data/nodes__item_site__property__prodLeadTime": "Fertigungszeit", "@sage/xtrem-master-data/nodes__item_site__property__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/nodes__item_site__property__reorderPoint": "Meldebestand", "@sage/xtrem-master-data/nodes__item_site__property__replenishmentMethod": "Wiederbeschaffungsart", "@sage/xtrem-master-data/nodes__item_site__property__requiredQuantity": "Erforderliche Menge", "@sage/xtrem-master-data/nodes__item_site__property__safetyStock": "Sicherheitsbestand", "@sage/xtrem-master-data/nodes__item_site__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__stdCostValue": "Standardkostenwert", "@sage/xtrem-master-data/nodes__item_site__property__stockUnit": "Lagereinheit", "@sage/xtrem-master-data/nodes__item_site__property__suppliers": "Lieferanten", "@sage/xtrem-master-data/nodes__item_site__property__valuationMethod": "Bewertungsmethode", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite": "Bewerteten Artikelstandort abrufen", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__failed": "Bewerteten Artikel-Standort abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__parameter__searchCriteria": "Suchkriterien", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site_cost__node_name": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__costCategory": "Kostenkategorie", "@sage/xtrem-master-data/nodes__item_site_cost__property__forQuantity": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__property__fromDate": "<PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__item_site_cost__property__indirectCost": "Indirekte Kosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__isCalculated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__property__isUpdatingPreviousCost": "Ist Aktualisierung vorherige Kosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__itemSite": "Artikel-Standort", "@sage/xtrem-master-data/nodes__item_site_cost__property__laborCost": "Arbeitskraftkosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__machineCost": "Maschinenkosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__materialCost": "Materialkosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__stockUnit": "Lagereinheit", "@sage/xtrem-master-data/nodes__item_site_cost__property__toDate": "Datum bis", "@sage/xtrem-master-data/nodes__item_site_cost__property__toolCost": "Werkzeugkosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-master-data/nodes__item_site_cost__property__unitCost": "Stückkosten", "@sage/xtrem-master-data/nodes__item_site_cost__property__version": "Version", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost": "Artikelstandortkosten abrufen", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__failed": "Artikel-Standort-Kosten abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__effectiveDate": "Aktivierungsdatum", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__item": "Artikel", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site_supplier__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__item_site_supplier__node_name": "Artikel-Standort-Lieferant", "@sage/xtrem-master-data/nodes__item_site_supplier__property__isDefaultItemSupplier": "Standard-Artikel-Lieferant", "@sage/xtrem-master-data/nodes__item_site_supplier__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSite": "Artikel-Standort", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSupplier": "Artikel-Lieferant", "@sage/xtrem-master-data/nodes__item_site_supplier__property__minimumPurchaseOrderQuantity": "Mindestbestellmenge", "@sage/xtrem-master-data/nodes__item_site_supplier__property__priority": "Priorität", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseUnit": "Einkaufseinheit", "@sage/xtrem-master-data/nodes__item_site_supplier__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__item_site_supplier__property__uStoredPriority": "Gespeicherte Priorität", "@sage/xtrem-master-data/nodes__item_site_supplier__suppliers-dont-match": "Der Lieferant muss der gleiche wie der im Artikel-Lieferant referenzierte sein.", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier__node_name": "Artikel-Lieferant", "@sage/xtrem-master-data/nodes__item_supplier__property__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__item_supplier__property__isDefaultItemSupplier": "Standard-Artikel-Lieferant", "@sage/xtrem-master-data/nodes__item_supplier__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_supplier__property__minimumPurchaseQuantity": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseUnitOfMeasure": "Einkaufseinheit", "@sage/xtrem-master-data/nodes__item_supplier__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemCode": "Code Lieferantenartikel", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemName": "Name <PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierPriority": "Lieferantenpriorität", "@sage/xtrem-master-data/nodes__item_supplier__purchase_unit_forbidden": "Definieren Sie einen Umrechnungsfaktor für die Umrechnung von der Lagereinheit {{stockUnit}} in die Einkaufseinheit {{purchaseUnit}} für den Artikel {{item}}.", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier_price__node_name": "Artikel-Lieferant-Preis", "@sage/xtrem-master-data/nodes__item_supplier_price__property__currency": "Währung", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValid": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidFrom": "<PERSON><PERSON> g<PERSON> von", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidTo": "Datum gültig bis", "@sage/xtrem-master-data/nodes__item_supplier_price__property__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__item_supplier_price__property__item": "Artikel", "@sage/xtrem-master-data/nodes__item_supplier_price__property__itemSupplier": "Artikellieferant", "@sage/xtrem-master-data/nodes__item_supplier_price__property__price": "Pre<PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__priority": "Priorität", "@sage/xtrem-master-data/nodes__item_supplier_price__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__item_supplier_price__property__toQuantity": "Menge bis", "@sage/xtrem-master-data/nodes__item_supplier_price__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__unit": "Einheit", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice": "Einkaufspreis abrufen", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__failed": "Einkaufspreis abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__parameter__priceParameters": "Preisparameter", "@sage/xtrem-master-data/nodes__item-category__sequence-number-cannot-be-set": "Der Nummernkreis kann nicht gesetzt werden.", "@sage/xtrem-master-data/nodes__item-category__sequence-number-is-mandatory": "Der Nummernkreis ist erforderlich.", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate-greater-than-toDate": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity_improper_range": "Der Mengenbereich kann sich nicht mit einem anderen Mengenbereich überschneiden.", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity-greater-than-toQuantity": "Erfassen Sie einen Wert für die 'Menge von', der kleiner ist als die 'Menge bis'.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_infinite_range": "Der neue Datumsbereich kann keinen unbestimmten Zeitraum abdecken, wenn eine Zeile mit einem spezifischen Zeitraum existiert.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_specific_to_infinite": "Der neue Datumsbereich kann keinen spezifischen Zeitraum abdecken, wenn eine Zeile mit einem unbestimmten Zeitraum existiert.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_improper_range": "Der Datumsbereich kann sich nicht mit einem anderen Datumsbereich überschneiden.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate-greater-than-endDate": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Startdatum, das vor dem Enddatum liegt.", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-range": "Mengenbereich nicht korrekt: {{qtyRange}}", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-unit-of-measure": "Die Einheit {{uom}} bezieht sich nicht auf die Menge.", "@sage/xtrem-master-data/nodes__item-price__price-cannot-be-negative": "Der <PERSON> kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item-price__price-overlap": "Preisüberschneidung: {{err<PERSON><PERSON>}}", "@sage/xtrem-master-data/nodes__item-price__priority-cannot-be-negative": "Die Priorität kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item-price__quantity-cannot-be-negative": "Die Menge kann nicht negativ sein.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory": "Wählen Sie einen Lagerstandort aus.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-purchase-site": "Wählen Sie einen Lager- oder Einkaufsstandort aus.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-or-purchase-site": "Wählen Sie einen Lager-, Verkaufs- oder Einkaufsstandort aus.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-site": "Wählen Sie einen Lager- oder Verkaufsstandort aus.", "@sage/xtrem-master-data/nodes__item-site__valuation-method-must-be-standard-cost": "Nicht bestandsgeführte Artikel müssen nach der Standardkostenmethode bewertet werden.", "@sage/xtrem-master-data/nodes__item-site-cost__calculated-cost-only-for-manufactured-item": "Berechnete Kosten sind nur mit einem gefertigten Artikel kompatibel.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_deletion_impossible_if_before_today": "Löschen nicht zulässig. Das Startdatum der Artikel-Standort-Kosten liegt vor dem aktuellen Datum.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_update_impossible": "Das Startdatum der Artikel-Standort-Kosten liegt vor dem oder am aktuellen Datum.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-from-date-already-set": "Es existieren bereits Kosten mit dem gleichen Startdatum.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-updating-previous-cost": "Aktualisierung der vorherigen Kosten fehlgeschlagen: {{errors}}", "@sage/xtrem-master-data/nodes__item-site-cost__incorrect-key-changes": "Der Artikel-Standort und die Kategorie der Artikel-Standort-Kosten können nicht aktualisiert werden.", "@sage/xtrem-master-data/nodes__item-site-cost__labor-cost-only-for-manufactured-item": "Arbeitskraftkosten sind nur mit einem gefertigten Artikel kompatibel.", "@sage/xtrem-master-data/nodes__item-site-cost__material-cost-only-for-manufactured-item": "Maschinenkosten sind nur mit einem gefertigten Artikel kompatibel.", "@sage/xtrem-master-data/nodes__item-site-cost__tool-cost-only-for-manufactured-item": "Werkzeugkosten sind nur mit einem gefertigten Artikel kompatibel.", "@sage/xtrem-master-data/nodes__item-supplier-price__fromQuantity_improper_range": "Der Mengenbereich kann sich nicht mit einem anderen Mengenbereich überschneiden.", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__labor_capability__node_name": "Arbeitskraftkompetenz", "@sage/xtrem-master-data/nodes__labor_capability__property__labor": "Arbeitskraft", "@sage/xtrem-master-data/nodes__labor_capability__property__machine": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__labor_capability__property__service": "Dienstleistung", "@sage/xtrem-master-data/nodes__labor_capability__property__tool": "Werkzeug", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__labor_resource__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__labor_resource__node_name": "Arbeitskraftressource", "@sage/xtrem-master-data/nodes__labor_resource__property__capabilities": "Kompetenzen", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers": "Massen-Behälter-IDs erstellen", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__failed": "Massen-Behälter-IDs erstellen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__containerId": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleItem": "Einzelartikel", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleLot": "Einzelcharge", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__locationId": "Lagerplatz-ID", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__numberToCreate": "<PERSON><PERSON> erstellende Nummer", "@sage/xtrem-master-data/nodes__license_plate_number__node_name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__consumedCapacity": "Verbrauchte Kapazität", "@sage/xtrem-master-data/nodes__license_plate_number__property__container": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleItem": "Einzelartikel", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleLot": "Einzelcharge", "@sage/xtrem-master-data/nodes__license_plate_number__property__location": "Lagerplatz", "@sage/xtrem-master-data/nodes__license_plate_number__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__owner": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license-plate-number__location-required-for-automatic-number-generation": "Die Behälter-ID kann nicht automatisch für den Behälter {{containerId}} ohne Lageprlatz generiert werden.", "@sage/xtrem-master-data/nodes__license-plate-number__no_sequence_counter": "<PERSON><PERSON> mit ID {{sequenceId}}.", "@sage/xtrem-master-data/nodes__license-plate-number__no-default-sequence": "<PERSON> Behälter-ID kann nicht generiert werden. Erfassen Sie einen Nummernkreis für den Behälter {{containerId}}.", "@sage/xtrem-master-data/nodes__license-plate-number__owner-not-required": "<PERSON><PERSON><PERSON> die Behälter-ID ist kein Besitzer erforderlich.", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location__location_category_virtual_not_allowed": "Die virtuelle Lagerplatzkategorie ist nicht gültig. Sie müssen eine andere Lagerplatzkategorie auswählen.", "@sage/xtrem-master-data/nodes__location__location_zone_virtual_not_allowed": "Der virtuelle Lagerplatzbereich ist nicht gültig. Si<PERSON> müssen einen anderen Lagerplatzbereich auswählen.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations": "Massenlagerplätze erstellen", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__failed": "Massenlagerplätze erstellen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locations": "Lagerplätze", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locationSequence": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/nodes__location__mutation__getLocations": "Lagerplätze abrufen", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__failed": "Lagerplätze abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__locationSequence": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__requiredCombinations": "Erforderliche Kombinationen", "@sage/xtrem-master-data/nodes__location__node_name": "Lagerplatz", "@sage/xtrem-master-data/nodes__location__property__dangerousGoodAllowed": "Gefahrgüter zulässig", "@sage/xtrem-master-data/nodes__location__property__id": "ID", "@sage/xtrem-master-data/nodes__location__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__location__property__isVirtualAllowed": "<PERSON><PERSON> <PERSON><PERSON> zul<PERSON>g", "@sage/xtrem-master-data/nodes__location__property__locationType": "Lagerplatztyp", "@sage/xtrem-master-data/nodes__location__property__locationZone": "Lagerplatzbereich", "@sage/xtrem-master-data/nodes__location__property__name": "Name", "@sage/xtrem-master-data/nodes__location__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_sequence__node_name": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/nodes__location_sequence__property__components": "Komponenten", "@sage/xtrem-master-data/nodes__location_sequence__property__counterLength": "Zä<PERSON>länge", "@sage/xtrem-master-data/nodes__location_sequence__property__id": "ID", "@sage/xtrem-master-data/nodes__location_sequence__property__lastSequenceUsed": "Zuletzt verwendeter Nummernkreis", "@sage/xtrem-master-data/nodes__location_sequence__property__name": "Name", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocations": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsRemaining": "Anzahl verbleibende Lagerplätze", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsUsed": "Anzahl verwendete Lagerplätze", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_sequence_component__node_name": "Nummernkreiskomponente Lagerplatz", "@sage/xtrem-master-data/nodes__location_sequence_component__property__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/nodes__location_sequence_component__property__endValue": "Endwert", "@sage/xtrem-master-data/nodes__location_sequence_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_sequence_component__property__locationSequence": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/nodes__location_sequence_component__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__location_sequence_component__property__startValue": "Startwert", "@sage/xtrem-master-data/nodes__location_sequence_component__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_type__node_name": "Lagerplatztyp", "@sage/xtrem-master-data/nodes__location_type__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__location_type__property__id": "ID", "@sage/xtrem-master-data/nodes__location_type__property__isVirtualAllowed": "<PERSON><PERSON> <PERSON><PERSON> zul<PERSON>g", "@sage/xtrem-master-data/nodes__location_type__property__locationCategory": "Lagerplatzkategorie", "@sage/xtrem-master-data/nodes__location_type__property__name": "Name", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_zone__node_name": "Lagerplatzbereich", "@sage/xtrem-master-data/nodes__location_zone__property__id": "ID", "@sage/xtrem-master-data/nodes__location_zone__property__isVirtualAllowed": "<PERSON><PERSON> <PERSON><PERSON> zul<PERSON>g", "@sage/xtrem-master-data/nodes__location_zone__property__locations": "Lagerplätze", "@sage/xtrem-master-data/nodes__location_zone__property__name": "Name", "@sage/xtrem-master-data/nodes__location_zone__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_zone__property__zoneType": "Bereichstyp", "@sage/xtrem-master-data/nodes__location-type__location_category_virtual_not_allowed": "Eine virtuelle Lagerplatzkategorie ist nicht zulässig.", "@sage/xtrem-master-data/nodes__location-zone__site_modify": "Der Standort kann nicht aktualisiert werden. Es sind Lagerplätze mit diesem Lagerplatzbereich verbunden.", "@sage/xtrem-master-data/nodes__location-zone__zone_type_virtual_not_allowed": "Ein virtueller Lagerplatzbereichtyp ist nicht zulässig.", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__machine_resource__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__machine_resource__node_name": "Maschinenressource", "@sage/xtrem-master-data/nodes__machine_resource__property__contractId": "Vertrags-ID", "@sage/xtrem-master-data/nodes__machine_resource__property__contractName": "Vertragsname", "@sage/xtrem-master-data/nodes__machine_resource__property__minCapabilityLevel": "Mindest-Kompetenzniveau", "@sage/xtrem-master-data/nodes__machine_resource__property__model": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__machine_resource__property__serialNumber": "Seriennummer", "@sage/xtrem-master-data/nodes__machine_resource__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__payment_term__node_name": "Zahlungsbedingung", "@sage/xtrem-master-data/nodes__payment_term__property__businessEntityType": "Typ Geschäftsentität", "@sage/xtrem-master-data/nodes__payment_term__property__days": "Tage", "@sage/xtrem-master-data/nodes__payment_term__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__payment_term__property__discountAmount": "Skontobetrag", "@sage/xtrem-master-data/nodes__payment_term__property__discountDate": "Skontodatum", "@sage/xtrem-master-data/nodes__payment_term__property__discountFrom": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-master-data/nodes__payment_term__property__discountType": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__payment_term__property__dueDateType": "Typ Fälligkeitsdatum", "@sage/xtrem-master-data/nodes__payment_term__property__id": "ID", "@sage/xtrem-master-data/nodes__payment_term__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__payment_term__property__name": "Name", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyType": "Ma<PERSON>typ", "@sage/xtrem-master-data/nodes__payment_term__property__setupId": "ID Einstellungen", "@sage/xtrem-master-data/nodes__payment_term_discount_amount_percentage_error": "Der Skontobetrag muss kleiner als 100% sein.", "@sage/xtrem-master-data/nodes__payment_term_discount_date_should_be_before_due_date": "Das Skontodatum muss an oder nach dem Fälligkeitsdatum liegen.", "@sage/xtrem-master-data/nodes__payment_term_discount_from_needs_to_match_due_date_type": "Der Typ für das Fälligkeitsdatum und der Typ für das Skonto müssen identisch sein.", "@sage/xtrem-master-data/nodes__payment_term_discount_mandatory": "<PERSON>n Si<PERSON> ein Datum er<PERSON>, sind der Skontotyp und der Betrag erforderlich.", "@sage/xtrem-master-data/nodes__payment_term_penalty_amount_percentage_error": "Die Mahngebühr muss kleiner als 100% sein.", "@sage/xtrem-master-data/nodes__range_sequence_component__node_name": "Bereich Nummernkreiskomponente", "@sage/xtrem-master-data/nodes__range_sequence_component__property__endValue": "Endwert", "@sage/xtrem-master-data/nodes__range_sequence_component__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__range_sequence_component__property__startValue": "Startwert", "@sage/xtrem-master-data/nodes__range_sequence_number__last_sequence_used_not_found": "Das Format der Stücklistenänderungsnummer ist falsch. Erfassen Sie eine neue Stücklistenänderungsnummer.", "@sage/xtrem-master-data/nodes__range_sequence_number__no_combinations_found": "Der Nummernkreis hat den Endwert erreicht. Es können keine weiteren Nummernkreisnummern mehr generiert werden.", "@sage/xtrem-master-data/nodes__range_sequence_number__node_name": "Bereich Nummernkreis", "@sage/xtrem-master-data/nodes__range_sequence_number__property__components": "Komponenten", "@sage/xtrem-master-data/nodes__range_sequence_number__property__definitionLevel": "Definitionsebene", "@sage/xtrem-master-data/nodes__range_sequence_number__property__isChronological": "<PERSON><PERSON> chronologisch", "@sage/xtrem-master-data/nodes__range_sequence_number__property__legislation": "Rechtsordnung", "@sage/xtrem-master-data/nodes__range_sequence_number__property__numberOfCombinations": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__range_sequence_number__property__rtzLevel": "Rücksetzebene", "@sage/xtrem-master-data/nodes__range_sequence_number__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__range_sequence_number__required_combinations_must_be_greater_than_zero": "<PERSON><PERSON> müssen einen Wert größer als Null erfassen.", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__reason_code__node_name": "Grundcode", "@sage/xtrem-master-data/nodes__reason_code__property__id": "ID", "@sage/xtrem-master-data/nodes__reason_code__property__isActive": "Ist aktiv", "@sage/xtrem-master-data/nodes__reason_code__property__name": "Name", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__resource_cost_category__node_name": "Ressourcenkostenkategorie", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costCategory": "Kostenkategorie", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costUnit": "Kosteneinheit", "@sage/xtrem-master-data/nodes__resource_cost_category__property__indirectCostSection": "Bereich indirekte Kosten", "@sage/xtrem-master-data/nodes__resource_cost_category__property__resource": "Ressource", "@sage/xtrem-master-data/nodes__resource_cost_category__property__runCost": "Bearbeitungskosten", "@sage/xtrem-master-data/nodes__resource_cost_category__property__setupCost": "Rüstkosten", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__resource_group_replacement__node_name": "Austausch Ressourcengruppe", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__replacement": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__resourceGroup": "Ressourcengruppe", "@sage/xtrem-master-data/nodes__resource-cost-category__the-cost-category-is-mandatory": "Die Kostenkategorie {{costCategory}} ist erforderlich.", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number__node_name": "Nummernkreis", "@sage/xtrem-master-data/nodes__sequence_number__property__componentLength": "Länge Komponente", "@sage/xtrem-master-data/nodes__sequence_number__property__components": "Komponenten", "@sage/xtrem-master-data/nodes__sequence_number__property__definitionLevel": "Definitionsebene", "@sage/xtrem-master-data/nodes__sequence_number__property__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number__property__isChronological": "Chronologisch", "@sage/xtrem-master-data/nodes__sequence_number__property__isClearedByReset": "Ist Löschen durch Zurücksetzen", "@sage/xtrem-master-data/nodes__sequence_number__property__isUsed": "Verwendet", "@sage/xtrem-master-data/nodes__sequence_number__property__legislation": "Rechtsordnung", "@sage/xtrem-master-data/nodes__sequence_number__property__minimumLength": "Mindestlänge", "@sage/xtrem-master-data/nodes__sequence_number__property__name": "Name", "@sage/xtrem-master-data/nodes__sequence_number__property__rtzLevel": "Rücksetzebene", "@sage/xtrem-master-data/nodes__sequence_number__property__sequenceNumberAssignments": "Zuweisungen Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number__property__values": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number__query__getDocumentNodeNames": "Dokumentnodenamen abrufen", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_company_mandatory": "Erfassen Sie einen Nummernkreis für das Unternehmen {{companyId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_site_mandatory": "Erfassen Sie einen Nummernkreis für den Standort {{siteId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__node_name": "Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment__only_allowed_for_sales_invoice_credit_memo_fr_legislation": "Gebuchte Nummernkreise können nur Verkaufsrechnungen und Gutschriften für die Rechtsordnung Frankreich zugewiesen werden.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__company": "Unternehmen", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislationId": "Aktuelle Rechtsordnungs-ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isActive": "Ist aktiv", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isAssignOnPosting": "Zuweisung bei Buchung", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isDefaultAssignment": "Standardzuweisung", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isUsed": "Verwendet", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__legislation": "Rechtsordnung", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumberAssignmentDocumentType": "Dokumenttyp Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__setupId": "ID Einstellungen", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__node_name": "Dokumenttyp Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__displayOrder": "Anzeigereihenfolge", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeFactory": "Node-Standard", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeValues": "Node-Werte", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignmentModule": "Modul Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignments": "Zuweisungen Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__setupId": "ID Einstellungen", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__node_name": "Modul Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__name": "Name", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__nodes": "Nodes", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__node_name": "Einrichtung Zuweisung Nummernkreise", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__property__modules": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_component__node_name": "Nummernkreiskomponente", "@sage/xtrem-master-data/nodes__sequence_number_component__property__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/nodes__sequence_number_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__sequence_number_component__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues": "Nummernkreiswerte aktualisieren", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues__parameter__sequenceNumberValues": "Nummernkreiswerte", "@sage/xtrem-master-data/nodes__sequence_number_value__node_name": "Nummernkreiswert", "@sage/xtrem-master-data/nodes__sequence_number_value__property__additionalInfo": "Zusätzliche Informationen", "@sage/xtrem-master-data/nodes__sequence_number_value__property__company": "Unternehmen", "@sage/xtrem-master-data/nodes__sequence_number_value__property__period": "Zeitraum", "@sage/xtrem-master-data/nodes__sequence_number_value__property__periodDate": "<PERSON><PERSON>um", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceNumber": "Nummernkreis", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceValue": "Nummernkreiswert", "@sage/xtrem-master-data/nodes__sequence_number_value__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence-number__length_exceed": "Länge des Nummernkreises überschritten. Sie müssen einen Nummernkreis mit weniger Ziffern erfassen.", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__shift_detail__node_name": "Schichtdetails", "@sage/xtrem-master-data/nodes__shift_detail__property__duration": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__shift_detail__property__formattedDuration": "Formati<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__shift_detail__property__id": "ID", "@sage/xtrem-master-data/nodes__shift_detail__property__name": "Name", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftStart": "Schichtbeginn", "@sage/xtrem-master-data/nodes__site__incorrect_format_siret": "{{siret}} ist keine gültige SIRET-Nummer. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__site__not-a-valid-tax-id": "{{taxIdNumber}} ist keine gültige Steuer-ID. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__site_extension__corporation_and_site": "Sie können die Rechtsperson nicht als {{legalEntity}} definieren, da die Geschäftsentität als ein Standort definiert ist.", "@sage/xtrem-master-data/nodes__site_extension__current_site_is_financial_site": "Der aktuelle Standort ist bereits ein Lagerstandort.", "@sage/xtrem-master-data/nodes__site_extension__financial_site_mandatory": "Dieser Standort ist kein Buchhaltungsstandort. Erfassen Sie einen Buchhaltungsstandort.", "@sage/xtrem-master-data/nodes__site_extension__invalid_timezone": "Der Zeitzonenwert ist ungültig: ({{value}}).", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__standard__node_name": "Standard", "@sage/xtrem-master-data/nodes__standard__property__code": "Code", "@sage/xtrem-master-data/nodes__standard__property__id": "ID", "@sage/xtrem-master-data/nodes__standard__property__industrySector": "Industriebranche", "@sage/xtrem-master-data/nodes__standard__property__name": "Name", "@sage/xtrem-master-data/nodes__standard__property__sdo": "Standard", "@sage/xtrem-master-data/nodes__standard__property__version": "Version", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__standard_industrial_classification__node_name": "Standard Industrial Classification", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__legislation": "Rechtsordnung", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicCode": "SIC-Code", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicDescription": "Bezeichnung SIC", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__supplier__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__supplier__incorrect_format_siret": "{{siret}} ist keine gültige SIRET-Nummer. Das erwartete Format ist: {{format}}", "@sage/xtrem-master-data/nodes__supplier__node_name": "Lieferant", "@sage/xtrem-master-data/nodes__supplier__property__billByAddress": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__billBySupplier": "Rechnungssteller", "@sage/xtrem-master-data/nodes__supplier__property__category": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__certificates": "Zertifikate", "@sage/xtrem-master-data/nodes__supplier__property__deliveryMode": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__incoterm": "Incoterms-Regel", "@sage/xtrem-master-data/nodes__supplier__property__itemPrices": "Artikelpreise", "@sage/xtrem-master-data/nodes__supplier__property__items": "Artikel", "@sage/xtrem-master-data/nodes__supplier__property__parent": "Übergeordnet", "@sage/xtrem-master-data/nodes__supplier__property__paymentMethod": "Zahlungsart", "@sage/xtrem-master-data/nodes__supplier__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-master-data/nodes__supplier__property__payToAddress": "<PERSON><PERSON><PERSON> Zahlungsempfänger", "@sage/xtrem-master-data/nodes__supplier__property__payToSupplier": "Zahlungsempfänger", "@sage/xtrem-master-data/nodes__supplier__property__prices": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__returnToAddress": "Retouradresse", "@sage/xtrem-master-data/nodes__supplier__property__returnToSupplier": "Retourlieferant", "@sage/xtrem-master-data/nodes__supplier__property__standardIndustrialClassification": "Standard Industrial Classification", "@sage/xtrem-master-data/nodes__supplier__property__supplierType": "Lieferantentyp", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier": "Standardlieferant abrufen", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__failed": "Standardlieferant abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__item": "Artikel", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate": "Zertif<PERSON><PERSON> er<PERSON>n", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__failed": "Zertifikat erneuern fehlgeschlagen.", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__newCertificateDate": "Neues Zertifikatdatum", "@sage/xtrem-master-data/nodes__supplier_certificate__node_name": "Lieferantenzertifikat", "@sage/xtrem-master-data/nodes__supplier_certificate__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__team__node_name": "Team", "@sage/xtrem-master-data/nodes__team__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__team__property__id": "ID", "@sage/xtrem-master-data/nodes__team__property__name": "Name", "@sage/xtrem-master-data/nodes__team__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__tool_resource__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-master-data/nodes__tool_resource__node_name": "Werkzeugressource", "@sage/xtrem-master-data/nodes__tool_resource__property__consumptionMode": "Verbrauchsart", "@sage/xtrem-master-data/nodes__tool_resource__property__hoursTracked": "Stunden rückgemeldet", "@sage/xtrem-master-data/nodes__tool_resource__property__item": "Artikel", "@sage/xtrem-master-data/nodes__tool_resource__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__tool_resource__property__unitProduced": "Gefertigte Einheiten", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__item": "Der Kunde muss einen Artikel für die Einheitenumrechnung haben.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__type": "<PERSON><PERSON> müssen eine Flussart 'Verkauf' er<PERSON>, bevor <PERSON> einen Kunden erfassen können.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__flow_without_item": "Sie können keinen Einheitenumrechnungsfaktor für einen Fluss ohne Artikel haben.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_delete": "Sie können den Einheitenumrechnungsfaktor nicht löschen. Die Eigenschaft 'isStandard' ist wahr.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_property_update": "Sie können die Eigenschaft 'isStandard' nicht aktualisieren.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_update": "Sie können einen Einheitenumrechnungsfaktor nicht aktualisieren, löschen oder hinzufügen, wenn die Eigenschaft 'isStandard' wahr ist.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__item__type": "<PERSON>e müssen einen Artikel erfassen, bevor <PERSON> eine Flussart erfassen können.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__purchase__customer": "Die Flussart Einkauf und der Kunde können nicht kombiniert werden.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__sales__supplier": "Die Flussart Verkauf und der Lieferant können nicht kombiniert werden.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__customer": "Es muss nur der Lieferant oder der Kunde gesetzt werden, nicht beide.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__item": "Der Lieferant muss einen Artikel für die Einheitenumrechnung haben.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__type": "<PERSON>e müssen eine Flussart 'Ein<PERSON>uf' er<PERSON>, bevor Si<PERSON> einen Lieferanten erfassen können.", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__unit_conversion_factor__node_name": "Umrechnungsfaktor Einheit", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__coefficient": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__customer": "Kunde", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__fromUnit": "<PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__isStandard": "Standard", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__item": "Artikel", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__toUnit": "Nach Einheit", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__unit_of_measure__decrease_not_possible_unit_of_measure_is_used": "<PERSON>e können die Dezimalstellen für eine Maßeinheit, die verwendet wird, nicht verringern.", "@sage/xtrem-master-data/nodes__unit_of_measure__node_name": "Maßeinheit", "@sage/xtrem-master-data/nodes__unit_of_measure__property__conversionFactor": "Umrechnungsfaktor", "@sage/xtrem-master-data/nodes__unit_of_measure__property__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/nodes__unit_of_measure__property__description": "Bezeichnung", "@sage/xtrem-master-data/nodes__unit_of_measure__property__id": "ID", "@sage/xtrem-master-data/nodes__unit_of_measure__property__isActive": "Aktiv", "@sage/xtrem-master-data/nodes__unit_of_measure__property__name": "Name", "@sage/xtrem-master-data/nodes__unit_of_measure__property__symbol": "Symbol", "@sage/xtrem-master-data/nodes__unit_of_measure__property__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo": "<PERSON><PERSON><PERSON><PERSON> von nach", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__failed": "<PERSON>re<PERSON><PERSON> von nach fehlgeschlagen.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__conversionFactor": "Umrechnungsfaktor", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__customer": "Kunde", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__formatToUnitDecimalDigits": "Format nach Einheit Dezimalstellen", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__fromUnit": "<PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__item": "Artikel", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__toUnit": "Nach Einheit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit": "Einkaufseinheit abrufen", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__failed": "Einkaufseinheit abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__item": "Artikel", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor": "Einheitenumrechnungsfaktor abrufen", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__failed": "Einheitenumrechnungsfaktor abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__customer": "Kunde", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__fromUnit": "<PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__item": "Artikel", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__supplier": "Lieferant", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__toUnit": "Nach Einheit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__version_information__node_name": "Versionsinformationen", "@sage/xtrem-master-data/nodes__version_information__property__text": "Text", "@sage/xtrem-master-data/nodes__version_information__property__version": "Version", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__weekly_shift__no_daily_shift_on_full_week_shift": "Eine ganzwöchige Schicht kann keine Tagesschichten haben.", "@sage/xtrem-master-data/nodes__weekly_shift__node_name": "Wöchentliche Schicht", "@sage/xtrem-master-data/nodes__weekly_shift__property__capacity": "Kapazität", "@sage/xtrem-master-data/nodes__weekly_shift__property__formattedCapacity": "Formatierte Kapazität", "@sage/xtrem-master-data/nodes__weekly_shift__property__fridayShift": "Schicht Freitag", "@sage/xtrem-master-data/nodes__weekly_shift__property__id": "ID", "@sage/xtrem-master-data/nodes__weekly_shift__property__isFullWeek": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__mondayShift": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__name": "Name", "@sage/xtrem-master-data/nodes__weekly_shift__property__saturdayShift": "Schicht Samstag", "@sage/xtrem-master-data/nodes__weekly_shift__property__sundayShift": "<PERSON><PERSON><PERSON> Sonntag", "@sage/xtrem-master-data/nodes__weekly_shift__property__thursdayShift": "Schicht Donnerstag", "@sage/xtrem-master-data/nodes__weekly_shift__property__tuesdayShift": "Schicht Dienstag", "@sage/xtrem-master-data/nodes__weekly_shift__property__wednesdayShift": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__node_name": "Work-In-Progress", "@sage/xtrem-master-data/nodes__work_in_progress__property__actualQuantity": "Ist-Menge", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentId": "Dokument-ID", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentLine": "Dokumentzeile", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentNumber": "Dokumentnummer", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentType": "Dokumenttyp", "@sage/xtrem-master-data/nodes__work_in_progress__property__endDate": "Enddatum", "@sage/xtrem-master-data/nodes__work_in_progress__property__expectedQuantity": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__property__item": "Artikel", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentLine": "Ursprüngliche Dokumentzeile", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentType": "Ursprünglicher Dokumenttyp", "@sage/xtrem-master-data/nodes__work_in_progress__property__outstandingQuantity": "<PERSON>ss<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__property__remainingQuantityToAllocate": "Verbleibende zu reservierende Menge", "@sage/xtrem-master-data/nodes__work_in_progress__property__site": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__property__startDate": "Startdatum", "@sage/xtrem-master-data/nodes__work_in_progress__property__status": "Status", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite": "Work-In-Progress-Menge pro Artikelstandort abrufen", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__failed": "Work-In-Progress-Menge pro Artikelstandort abrufen fehlgeschlagen.", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentDate": "Aktuelles Datum", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentItem": "Aktueller Artikel", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentSite": "Aktueller Standort", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentStatus": "Aktueller Status", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__originDocumentType": "Ursprünglicher Dokumenttyp", "@sage/xtrem-master-data/nodes_sequence_number_assignment_company_leg": "Die Rechtsordnung {{legislation}} muss Ihrer Unternehmensrechtsordnung {{companyLegislation}} entsprechen.", "@sage/xtrem-master-data/nodes_sequence_number_assignment_site_error": "Ihr Standort muss zu {{company}} gehören.", "@sage/xtrem-master-data/nodes-resource-group-cannot-be-replaced-by-itself": "Die Ressourcengruppe kann nicht sich selbst ersetzen.", "@sage/xtrem-master-data/nodes-resource-group-replacement-resource-group-should-be-replaced-by-another-one-with-same-site": "Die Ressourcengruppe muss durch einen anderen vom gleichen Standort ersetzt werden.", "@sage/xtrem-master-data/or": "<PERSON><PERSON>", "@sage/xtrem-master-data/package__name": "Master", "@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available": "<PERSON><PERSON> verfügbar.", "@sage/xtrem-master-data/page__item_customer_price_panel__price_is_zero": "Der Preis ist gleich <PERSON>ull.", "@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available": "<PERSON><PERSON> verfügbar.", "@sage/xtrem-master-data/page-extensions__user_extension____navigationPanel__listItem__line11__title": "Ausgewähltes Dashboard", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__firstName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____lookupDialogTitle": "Dashboard auswählen", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____title": "Ausgewähltes Dashboard", "@sage/xtrem-master-data/pages__address__country____columns__title__continent": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__address__deleteAddress____title": "Löschen", "@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory": "Weisen Sie der Geschäftsentität eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory": "<PERSON>sen Sie dem Unternehmen eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory": "Weisen Sie dem Kunden eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__address_functions__primary_active_address_mandatory": "<PERSON>sen Si<PERSON> der {{addressEntityTypeLocalized}} einen aktiven Hauptkontakt zu.", "@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory": "<PERSON>sen Sie dem Standort eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory": "<PERSON>sen Sie dem Lieferanten eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__address_functions_add_new____title": "Neue Adresse {{addressEntityTypeLocalized}}", "@sage/xtrem-master-data/pages__address_functions_address_contacts____title": "Kontakte für Adresse {{addressEntityTypeLocalized}} {{addressName}}.", "@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title": "Neue Geschäftsentitätenadresse", "@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title": "Kontakte für Geschäftsentitätenadresse {{addressName}}", "@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title": "Geschäftsentitätenadresse bearbeiten", "@sage/xtrem-master-data/pages__address_functions_edit____title": "Adresse {{addressEntityTypeLocalized}} bearbeiten", "@sage/xtrem-master-data/pages__address_panel__edit____title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active": "<PERSON><PERSON> Hauptadresse muss aktiv sein.", "@sage/xtrem-master-data/pages__address_panel__isPrimaryForAnotherEntity____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__address_panel__new____title": "Neue Adresse", "@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title": "Neuer Kontakt", "@sage/xtrem-master-data/pages__address-contacts_panel_edit____title": "Kontakt bearbeiten", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active": "Aktiv", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive": "Inaktiv", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__allergen____navigationPanel__listItem__pictogram__title": "Piktogramm", "@sage/xtrem-master-data/pages__allergen____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__allergen____objectTypePlural": "Allergene", "@sage/xtrem-master-data/pages__allergen____objectTypeSingular": "Allergen", "@sage/xtrem-master-data/pages__allergen____title": "Allergen", "@sage/xtrem-master-data/pages__allergen___id____title": "ID", "@sage/xtrem-master-data/pages__allergen__allergenImageBlock____title": "Piktogramm", "@sage/xtrem-master-data/pages__allergen__allergenInformationBlock____title": "<PERSON><PERSON> zu Allergenen", "@sage/xtrem-master-data/pages__allergen__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__allergen__id____title": "ID", "@sage/xtrem-master-data/pages__allergen__idBlock____title": "ID", "@sage/xtrem-master-data/pages__allergen__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__allergen__name____title": "Name", "@sage/xtrem-master-data/pages__already_used_message": "Der vorherige Nummernkreis wurde bereits für die Generierung einer Dokumentnummer verwendet.", "@sage/xtrem-master-data/pages__already_used_title": "Nummernkreis verwendet", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__componentLength__title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__isDefault__title": "Standard", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypePlural": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypeSingular": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/pages__bom_revision_sequence____title": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/pages__bom_revision_sequence__addComponent____title": "Hinzufügen", "@sage/xtrem-master-data/pages__bom_revision_sequence__capital_letters_only": "Eine alphabetische Nummernkreiskomponente kann nur aus Großbuchstaben bestehen.", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentLength____title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__endValue": "Endwert", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__startValue": "Startwert", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____title": "Komponenten", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentsBlock____title": "Komponenten", "@sage/xtrem-master-data/pages__bom_revision_sequence__default": "Standard", "@sage/xtrem-master-data/pages__bom_revision_sequence__digits_only": "Eine numerische Nummernkreiskomponente kann nur aus Zahlen bestehen.", "@sage/xtrem-master-data/pages__bom_revision_sequence__id____title": "ID", "@sage/xtrem-master-data/pages__bom_revision_sequence__invalid_range": "Der Startwert kann nicht nach dem Endwert liegen.", "@sage/xtrem-master-data/pages__bom_revision_sequence__isDefault____title": "Standard", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__bom_revision_sequence__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__listItem__line6__title": "Steuer-ID", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__business_entity____objectTypePlural": "Geschäftsentitäten", "@sage/xtrem-master-data/pages__business_entity____objectTypeSingular": "Geschäftsentität", "@sage/xtrem-master-data/pages__business_entity____title": "Geschäftsentität", "@sage/xtrem-master-data/pages__business_entity___id____title": "ID", "@sage/xtrem-master-data/pages__business_entity__address_active": "Aktiv", "@sage/xtrem-master-data/pages__business_entity__address_inactive": "Inaktiv", "@sage/xtrem-master-data/pages__business_entity__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__contacts": "Kontakte", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__3": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__4": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__business_entity__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__contact_active": "Aktiv", "@sage/xtrem-master-data/pages__business_entity__contact_inactive": "Inaktiv", "@sage/xtrem-master-data/pages__business_entity__contacts____addButtonText": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__isPrimary": "Hauptkontakt", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__2": "Als Hauptkontakt definieren", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__business_entity__contacts____headerLabel__title": "Ist aktiv", "@sage/xtrem-master-data/pages__business_entity__contactSection____title": "Kontakte", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__regionLabel": "Bezeichnung Region", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__zipLabel": "Bezeichnung <PERSON>itzahl", "@sage/xtrem-master-data/pages__business_entity__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__business_entity__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__business_entity__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__business_entity__currency____title": "Währung", "@sage/xtrem-master-data/pages__business_entity__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__displayAddresses____columns__title": "Kontakte", "@sage/xtrem-master-data/pages__business_entity__id____title": "ID", "@sage/xtrem-master-data/pages__business_entity__idBlock____title": "ID", "@sage/xtrem-master-data/pages__business_entity__imageBlock____title": "Bild", "@sage/xtrem-master-data/pages__business_entity__isCustomer____title": "Kunde", "@sage/xtrem-master-data/pages__business_entity__isNaturalPerson____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__isSite____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__isSupplier____title": "Lieferant", "@sage/xtrem-master-data/pages__business_entity__legalEntity____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity__parent____lookupDialogTitle": "Übergeordnete Geschäftsentität auswählen", "@sage/xtrem-master-data/pages__business_entity__parent____title": "Übergeordnete Geschäftsentität", "@sage/xtrem-master-data/pages__business_entity__roleBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__save____title": "Speichern", "@sage/xtrem-master-data/pages__business_entity__siret____title": "SIRET", "@sage/xtrem-master-data/pages__business_entity__taxIdNumber____title": "Steuer-ID", "@sage/xtrem-master-data/pages__business_entity__website____title": "Webseite", "@sage/xtrem-master-data/pages__business_entity_address_panel____title": "Bereich Geschäftsentitätenadresse", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine1____title": "Adresszeile 1", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine2____title": "Adresszeile 2", "@sage/xtrem-master-data/pages__business_entity_address_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__business_entity_address_panel__city____title": "Stadt", "@sage/xtrem-master-data/pages__business_entity_address_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryDetail____title": "Lieferdetails", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____postfix": "Tag(e)", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____title": "Lieferzeit", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____lookupDialogTitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____lookupDialogTitle": "Incoterms-Regel auswählen", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____title": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__business_entity_address_panel__informationBlock____title": "Versandinformationen", "@sage/xtrem-master-data/pages__business_entity_address_panel__isActiveShippingAddress____title": "Aktiv", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimary____title": "Hauptadresse", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimaryShippingAddress____title": "Hauptlieferadresse", "@sage/xtrem-master-data/pages__business_entity_address_panel__isShippingAddress____title": "Lieferadresse", "@sage/xtrem-master-data/pages__business_entity_address_panel__locationPhoneNumber____title": "Telefonnummer", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainBlock____title": "Adressinformationen", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____title": "Versandstandort", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingBlock____title": "Versandinformationen", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingSection____title": "Informationen", "@sage/xtrem-master-data/pages__business_entity_address_panel__workDaysSelection____title": "Arbeitstage", "@sage/xtrem-master-data/pages__business_entity_contact_panel____title": "Bereich Kontakt Geschäftsentitätenadresse", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____lookupDialogTitle": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__business_entity_contact_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_contact_panel__email____title": "E-Mail", "@sage/xtrem-master-data/pages__business_entity_contact_panel__firstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isPrimary____title": "Hauptkontakt", "@sage/xtrem-master-data/pages__business_entity_contact_panel__lastName____title": "Nachname", "@sage/xtrem-master-data/pages__business_entity_contact_panel__locationPhoneNumber____title": "Telefonnummer", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainBlock____title": "Kontaktinformationen", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity_contact_panel__position____title": "Position", "@sage/xtrem-master-data/pages__business_entity_contact_panel__preferredName____title": "Bevorzugter Name", "@sage/xtrem-master-data/pages__business_entity_contact_panel__role____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__title____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____subtitle": "<PERSON>ählen Sie eine Geschäftsentität, die Ihren Anforderungen entspricht.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____title": "Einen Kunden aus einer Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressBlock____title": "Definition Lieferadresse.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title": "Arbeitstage", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail": "Lieferadresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isActive": "Status Lieferadresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isPrimary": "Hauptlieferadresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__leadTime": "Lieferzeit", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__mode__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Versandstandort", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__3": "Als Hauptlieferadresse definieren", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__4": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____lookupDialogTitle": "Geschäftsentität auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity_already_a_customer": "Diese Geschäftsentität ist bereits als ein Kunde gesetzt.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialBlock____title": "Finanzen.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalBlock____title": "Wählen Sie die Geschäftsentität aus, um aus dieser den Kunden zu erstellen.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__paymentTerm____lookupDialogTitle": "Zahlungsbedingung auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__primary_ship_to_address_mandatory": "Der Kunde muss mindestens eine aktive Hauptlieferadresse haben.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____subtitle": "<PERSON>ählen Sie eine Geschäftsentität, die Ihren Anforderungen entspricht.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____title": "Einen Standort aus einer Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____lookupDialogTitle": "Geschäftsentität auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity_already_a_site": "Diese Geschäftsentität ist bereits als ein Standort gesetzt.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalBlock____title": "Wählen Sie die Geschäftsentität aus, um aus dieser den Standort zu erstellen.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__isFinance____title": "Buchhaltung", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementBlock____title": "Verwaltung", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementSection____title": "Verwaltung", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____subtitle": "<PERSON>ählen Sie eine Geschäftsentität, die Ihren Anforderungen entspricht.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____title": "Einen Lieferanten aus einer Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____lookupDialogTitle": "Geschäftsentität auswählen", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity_already_a_supplier": "Diese Geschäftsentität ist bereits als ein Lieferant gesetzt.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialBlock____title": "Finanzen.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalBlock____title": "Wählen Sie die Geschäftsentität aus, um aus dieser den Lieferanten zu erstellen.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__paymentTerm____lookupDialogTitle": "Zahlungsbedingung auswählen", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__listItem__level__title": "Niveau", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__capability_level____objectTypePlural": "Kompetenzniveaus", "@sage/xtrem-master-data/pages__capability_level____objectTypeSingular": "Kompetenzniveau", "@sage/xtrem-master-data/pages__capability_level____title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__capability_level__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__capability_level__id____title": "ID", "@sage/xtrem-master-data/pages__capability_level__level____title": "Niveau", "@sage/xtrem-master-data/pages__capability_level__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__capability_level__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__capability_level__name____title": "Name", "@sage/xtrem-master-data/pages__capability_panel____title": "Kompetenz", "@sage/xtrem-master-data/pages__capability_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__capability_panel__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__description": "Bezeichnung", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__id": "ID", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__level": "Niveau", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__name": "Name", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____lookupDialogTitle": "Kompetenzniveau auswählen", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__capability_panel__create____title": "OK", "@sage/xtrem-master-data/pages__capability_panel__createAction____title": "OK", "@sage/xtrem-master-data/pages__capability_panel__dateEndValid____title": "Aktiv bis", "@sage/xtrem-master-data/pages__capability_panel__dateStartValid____title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__capability_panel__id____title": "ID", "@sage/xtrem-master-data/pages__capability_panel__labor____columns__title___id": "ID", "@sage/xtrem-master-data/pages__capability_panel__labor____lookupDialogTitle": "Arbeitskraft auswählen", "@sage/xtrem-master-data/pages__capability_panel__labor____title": "Arbeitskraft", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__description": "Bezeichnung", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__id": "ID", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__minCapabilityLevel__level": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__model": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__name": "Name", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__serialNumber": "Seriennummer", "@sage/xtrem-master-data/pages__capability_panel__machine____lookupDialogTitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__name____title": "Name", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__description": "Bezeichnung", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__id": "ID", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__name": "Name", "@sage/xtrem-master-data/pages__capability_panel__service____lookupDialogTitle": "Dienstleistungsartikel auswählen", "@sage/xtrem-master-data/pages__capability_panel__service____title": "Dienstleistungsartikel", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__description": "Bezeichnung", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__id": "ID", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__name": "Name", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__resourceGroup__name": "Ressourcengruppe", "@sage/xtrem-master-data/pages__capability_panel__tool____lookupDialogTitle": "Werkzeug auswählen", "@sage/xtrem-master-data/pages__capability_panel__tool____title": "Werkzeug", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title": "Geschäftsentitätenadresse bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title": "Unternehmensadresse bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title": "Kundenadresse bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title": "Standort<PERSON><PERSON><PERSON> bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title": "Lieferantenadresse bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title": "Neue Geschäftsentitätenadresse", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title": "Neue Unternehmensadresse", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title": "Neue Kundenadresse", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title": "Neue Standortadresse", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title": "Neue Lieferantenadresse", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title": "Kontakt bearbeiten", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title": "Neuer Kontakt", "@sage/xtrem-master-data/pages__company____add_site": "Standort hinzufügen", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line10__title": "Rechtsform", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line11__title": "Nummernkreiswert", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line8__title": "NAF (APE)", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__company____objectTypePlural": "Unternehmen", "@sage/xtrem-master-data/pages__company____objectTypeSingular": "Unternehmen", "@sage/xtrem-master-data/pages__company____title": "Unternehmen", "@sage/xtrem-master-data/pages__company__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__company__addresses____columns__title__contacts": "Kontakte", "@sage/xtrem-master-data/pages__company__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__company__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__3": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__4": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__company__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____addButtonText": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__company__contacts____columns__title__isPrimary": "Hauptkontakt", "@sage/xtrem-master-data/pages__company__contacts____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__company__contacts____columns__title__role": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__2": "Als Hauptkontakt definieren", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__company__contacts____headerLabel__title": "Ist aktiv", "@sage/xtrem-master-data/pages__company__contactSection____title": "Kontakte", "@sage/xtrem-master-data/pages__company__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__company__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__company__creditLimitBlock____title": "Kreditlimit", "@sage/xtrem-master-data/pages__company__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__company__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__company__currency____placeholder": "Auswählen...", "@sage/xtrem-master-data/pages__company__currency____title": "Währung", "@sage/xtrem-master-data/pages__company__customerOnHoldCheck____title": "Prüfung Kundensperre", "@sage/xtrem-master-data/pages__company__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__company__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__id____title": "ID", "@sage/xtrem-master-data/pages__company__isSequenceNumberIdUsed____title": "Verwendet", "@sage/xtrem-master-data/pages__company__legalForm____title": "Rechtsform", "@sage/xtrem-master-data/pages__company__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__company__legislation____placeholder": "Auswählen ...", "@sage/xtrem-master-data/pages__company__mainBlock____title": "Unternehmensinformationen", "@sage/xtrem-master-data/pages__company__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__company__managementSection____title": "Verwaltung", "@sage/xtrem-master-data/pages__company__naf____title": "NAF (APE)", "@sage/xtrem-master-data/pages__company__name____title": "Name", "@sage/xtrem-master-data/pages__company__paymentTrackingBlock____title": "Zahlungsverfolgung", "@sage/xtrem-master-data/pages__company__rcs____title": "RCS", "@sage/xtrem-master-data/pages__company__save____title": "Speichern", "@sage/xtrem-master-data/pages__company__sequenceNumberId____title": "Nummernkreiswert", "@sage/xtrem-master-data/pages__company__siren____title": "SIREN", "@sage/xtrem-master-data/pages__company__siteBlock____title": "Organisation", "@sage/xtrem-master-data/pages__compare__number_remaining_to__required": "Die erforderlichen Kombinationen können nicht die verbleibenden Kombinationen überschreiten.", "@sage/xtrem-master-data/pages__contact_panel__isPrimary_must_be_active": "Ein Hauptkontakt muss aktiv sein.", "@sage/xtrem-master-data/pages__contact_selection_panel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__email": "E-Mail", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__lastName": "Nachname", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____title": "Kontakte", "@sage/xtrem-master-data/pages__contact_selection_panel__contactSelectionBlock____title": "Kontakte auswählen", "@sage/xtrem-master-data/pages__contact_selection_panel__ok____title": "OK", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__lastName": "Nachname", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____title": "Ausgewählter Kontakt", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__consumedLocationCapacity__title": "Erforderliche Lagerkapazität", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isInternal__title": "Intern", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleItem__title": "Einzelartikel", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleLot__title": "Einzelcharge", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__labelFormat__title": "Etikettenformat", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__locationCategory__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__sequenceNumber__title": "Nummernkreis", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__storageCapacity__title": "Lagerkapazität", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__container____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container__consumedLocationCapacity____title": "Erforderliche Lagerkapazität", "@sage/xtrem-master-data/pages__container__id____title": "ID", "@sage/xtrem-master-data/pages__container__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__container__isInternal____title": "Intern", "@sage/xtrem-master-data/pages__container__isSingleItem____title": "Einzelartikel", "@sage/xtrem-master-data/pages__container__isSingleLot____title": "Einzelcharge", "@sage/xtrem-master-data/pages__container__labelFormat____title": "Etikettenformat", "@sage/xtrem-master-data/pages__container__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__container__name____title": "Name", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__id": "ID", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__name": "Name", "@sage/xtrem-master-data/pages__container__sequenceNumber____lookupDialogTitle": "Nummernkreis auswählen", "@sage/xtrem-master-data/pages__container__sequenceNumber____title": "Nummernkreis", "@sage/xtrem-master-data/pages__container__storageCapacity____title": "Lagerkapazität", "@sage/xtrem-master-data/pages__container__type____title": "Behältertyp", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line2__title": "ID", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__cost_category____objectTypePlural": "Kostenkategorien", "@sage/xtrem-master-data/pages__cost_category____objectTypeSingular": "Kostenkategorie", "@sage/xtrem-master-data/pages__cost_category____title": "Kostenkategorie", "@sage/xtrem-master-data/pages__cost_category__costCategoryType____title": "Art", "@sage/xtrem-master-data/pages__cost_category__id____title": "ID", "@sage/xtrem-master-data/pages__cost_category__isMandatory____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__cost_category__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__cost_category__name____title": "Name", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line_5__title": "ISO 3166-1 Alpha-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line2__title": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line3__title": "Währung", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line5__title": "ISO 3166-1 Alpha-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line6__title": "EU-Mitgliedsstaat", "@sage/xtrem-master-data/pages__country____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__country____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country____objectTypeSingular": "Land", "@sage/xtrem-master-data/pages__country____title": "Land", "@sage/xtrem-master-data/pages__country__continent____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__countryFlagBlock____title": "Flagge", "@sage/xtrem-master-data/pages__country__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__country__currency____columns__title__name": "Name", "@sage/xtrem-master-data/pages__country__currency____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__country__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__country__currency____title": "Währung", "@sage/xtrem-master-data/pages__country__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__country__id____title": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__country__isEuMember____title": "EU-Mitgliedsstaat", "@sage/xtrem-master-data/pages__country__iso31661Alpha3____title": "ISO 3166-1 Alpha-3", "@sage/xtrem-master-data/pages__country__legislation____placeholder": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__country__legislation____title": "Rechtsordnung", "@sage/xtrem-master-data/pages__country__mainBlock____title": "Länderinformationen", "@sage/xtrem-master-data/pages__country__name____title": "Name", "@sage/xtrem-master-data/pages__country__regionLabel____title": "Bezeichnung für Region", "@sage/xtrem-master-data/pages__country__zipLabel____title": "Bezeichnung für Postleitzahl", "@sage/xtrem-master-data/pages__country_invalid_id": "Die ID muss aus 2 Zeichen bestehen.", "@sage/xtrem-master-data/pages__country_invalid_iso_code": "Der Code muss aus 3 Zeichen bestehen.", "@sage/xtrem-master-data/pages__create_test_data____title": "Testdaten erstellen", "@sage/xtrem-master-data/pages__create_test_data__instructions____content": "", "@sage/xtrem-master-data/pages__create_test_data__linkField____title": "Confluence-Ceite", "@sage/xtrem-master-data/pages__create_test_data__mainBlock____title": "Informationen", "@sage/xtrem-master-data/pages__create_test_data__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__create-test-data__instuctions": "Diese Seite ermöglicht den Zugriff auf Entwicklungswerkzeuge zur Erstellung großer Datensätze für Testzwecke.\n            \nAusführliche Anweisungen zu den Werten der einzelnen Eigenschaften finden Sie auf der Confluence-Seite.", "@sage/xtrem-master-data/pages__create-test-data__link_instuctions": "Detaillierte Anweisungen zur Erstellung von Testdaten.", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line_4__title": "Dezimalstellen", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line3__title": "ISO 4217", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line4__title": "Dezimalstellen", "@sage/xtrem-master-data/pages__currency____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__currency____objectTypePlural": "Währungen", "@sage/xtrem-master-data/pages__currency____objectTypeSingular": "Währung", "@sage/xtrem-master-data/pages__currency____title": "Währung", "@sage/xtrem-master-data/pages__currency___id____title": "ID", "@sage/xtrem-master-data/pages__currency__addExchangeRate____title": "Hinzufügen", "@sage/xtrem-master-data/pages__currency__addRateSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__icon": "Kennzeichen", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____title": "<PERSON>", "@sage/xtrem-master-data/pages__currency__converterResult____placeholder": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterResult____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterSection____title": "Währungsrechner", "@sage/xtrem-master-data/pages__currency__converterToAmount____placeholder": "Umzurechnender Betrag", "@sage/xtrem-master-data/pages__currency__converterToAmount____title": "Umzurechnender Betrag", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__icon": "Kennzeichen", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__id": "ID", "@sage/xtrem-master-data/pages__currency__converterToCurrency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__converterToCurrency____placeholder": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__converterToCurrency____title": "Nach", "@sage/xtrem-master-data/pages__currency__currencyRate____placeholder": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currencyRate____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__dateRate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__symbol": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__rate": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__shortDescription": "Kurzbezeichnung", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__image__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line2Right__title": "Kurzbezeichnung", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line3Right__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__subtitleRight__title": "Kurzbezeichnung", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__title__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRatesSection____title": "Details", "@sage/xtrem-master-data/pages__currency__decimalDigits____title": "Dezimalstellen", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__icon": "Kennzeichen", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__destinationCurrency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__destinationCurrency____placeholder": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__destinationCurrency____title": "Zielwährung", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderBlock____title": "Aktuelle Kurse", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderSection____title": "Aktuelle Wechselkurse", "@sage/xtrem-master-data/pages__currency__divisor____title": "Ursprungswährung", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__dateRate": "Datum", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__id": "Währung", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__name": "Name", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__rate": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRates____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__currency__exchangeRates____title": "Wechselkurse", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____chart__xAxis__title": "Datum", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____title": "Wechselkurse", "@sage/xtrem-master-data/pages__currency__exchangeRatesSection____title": "Wechselkurse", "@sage/xtrem-master-data/pages__currency__icon____title": "Flagge", "@sage/xtrem-master-data/pages__currency__id____title": "ISO 4217", "@sage/xtrem-master-data/pages__currency__invalid_id": "Die ID muss aus 3 Zeichen bestehen.", "@sage/xtrem-master-data/pages__currency__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__currency__name____title": "Name", "@sage/xtrem-master-data/pages__currency__rateDate____placeholder": "Auswählen ...", "@sage/xtrem-master-data/pages__currency__rateDate____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__rounding____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__icon": "Kennzeichen", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____title": "Währung", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message": "Die Währungen und Daten sind bereits mit einem Wechselkurs verknüpft. Möchten Sie den bestehenden Kurs beibehalten oder Ihren neuen Kurs anwenden?", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title": "Dieser Kurs ist bereits vorhanden.", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title": "Wechselkurs hinzufügen", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message": "Der Umkehrkurs ist für das angegebene Datum bereits vorhanden. Möchten Sie den bestehenden Kurs beibehalten oder Ihren neuen Kurs anwenden?", "@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title": "Der Umkehrkurs ist bereits vorhanden.", "@sage/xtrem-master-data/pages__currency__symbol____title": "Symbol", "@sage/xtrem-master-data/pages__customer____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON>zen", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_4__title": "Primäres Land", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_5__title": "Stadt", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line10__title": "E-Mail", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line12__title": "Steuer-ID", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line13__title": "Status", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line18__title": "Mindestbestellwert", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line19__title": "Zahlungsbedingung", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line20__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line21__title": "Kundenkategorie", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line22__title": "Kreditlimit", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line6__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line8__title": "Hauptkontakt", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line9__title": "Telefonnummer Hauptkontakt", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__id": "ID", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__name": "Name", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__title": "Name", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title": "Alle offenen Statuswerte", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__2": "Alle", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__3": "Aktiv", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__4": "Inaktiv", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____objectTypePlural": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____objectTypeSingular": "Kunde", "@sage/xtrem-master-data/pages__customer____title": "Kunde", "@sage/xtrem-master-data/pages__customer__addItem____title": "Hinzufügen", "@sage/xtrem-master-data/pages__customer__addItemPriceLine____title": "Hinzufügen", "@sage/xtrem-master-data/pages__customer__address_assigned_be_primary": "<PERSON>e können eine Hauptadresse für die Geschäftsentität nicht löschen.", "@sage/xtrem-master-data/pages__customer__address_assigned_primary": "Sie können einen Hauptkontakt nicht löschen.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_billToAddress": "Diese Adresse ist der Hauptrechnungsempfängeradresse zugewiesen.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_deliveryAddress": "Diese Adresse ist einer Versandadresse zugewiesen.", "@sage/xtrem-master-data/pages__customer__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title": "Arbeitstage", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail": "Lieferadresse", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isActive": "Status Lieferadresse", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isPrimary": "Hauptlieferadresse", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__leadTime": "Lieferzeit", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__mode__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Versandstandort", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__3": "Als Hauptlieferadresse definieren", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__4": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__5": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__customer__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_id": "Diese ID ist bereits einem Kunden zugewiesen.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_name": "Dieser Name ist bereits einem Kunden zugewiesen.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber": "Diese Steueridentifikationsnummer ist bereits einem Kunden zugewiesen.", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine1": "Zeile 1", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine2": "Zeile 2", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON> (USA)", "@sage/xtrem-master-data/pages__customer__billToAddress____title": "Haup<PERSON>chnungsadresse", "@sage/xtrem-master-data/pages__customer__billToCustomer____title": "Rechnungsempfänger", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____columns__title__concatenatedAddress": "Haup<PERSON>chnungsadresse", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____title": "Haup<PERSON>chnungsadresse", "@sage/xtrem-master-data/pages__customer__businessEntity____columns__title__isNaturalPerson": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__category____columns__title__sequenceNumber__name": "Nummernkreis", "@sage/xtrem-master-data/pages__customer__category____lookupDialogTitle": "Kategorie auswählen", "@sage/xtrem-master-data/pages__customer__commercialBlock____title": "G<PERSON><PERSON><PERSON><PERSON>lich", "@sage/xtrem-master-data/pages__customer__commercialSection____title": "G<PERSON><PERSON><PERSON><PERSON>lich", "@sage/xtrem-master-data/pages__customer__contact_assigned_primary": "Sie können einen Hauptkontakt nicht löschen.", "@sage/xtrem-master-data/pages__customer__contacts____addButtonText": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__isPrimary": "Hauptkontakt", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__2": "Als Hauptkontakt definieren", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__customer__contacts____headerLabel__title": "Ist aktiv", "@sage/xtrem-master-data/pages__customer__contactSection____title": "Kontakte", "@sage/xtrem-master-data/pages__customer__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__customer__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__customer__createCustomer____title": "<PERSON>eu", "@sage/xtrem-master-data/pages__customer__createFromBusinessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__customer__creditLimit____title": "Kreditlimit", "@sage/xtrem-master-data/pages__customer__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____addButtonText": "Lieferadresse hinzufügen", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title___id": "ID", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__name": "Name", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__regionLabel": "Bezeichnung Region", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__zipLabel": "Bezeichnung <PERSON>itzahl", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__3": "Zeile 2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__4": "Stadt", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__5": "Land", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__6": "Telefonnummer", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__7": "ID", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__workDaysSelection": "Arbeitstage", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title": "Lieferadresse bearbeiten", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__2": "<PERSON><PERSON> primä<PERSON> defini<PERSON>n", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__3": "<PERSON><PERSON> primä<PERSON> defini<PERSON>n", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__4": "Löschen", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____headerLabel__title": "Aktiv", "@sage/xtrem-master-data/pages__customer__display_address_active": "Aktiv", "@sage/xtrem-master-data/pages__customer__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__2": "Hauptadresse", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__3": "Lieferadresse", "@sage/xtrem-master-data/pages__customer__displayStatus____title": "Status anzeigen", "@sage/xtrem-master-data/pages__customer__financialBlock____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__customer__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__customer__imageBlock____title": "Bild", "@sage/xtrem-master-data/pages__customer__internalNote____helperText": "Notizen werden auf internen Dokumenten angezeigt.", "@sage/xtrem-master-data/pages__customer__internalNote____title": "Interne Notizen", "@sage/xtrem-master-data/pages__customer__isNaturalPerson____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__isOnHold____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__currency__id": "Währung", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__customer__businessEntity__name": "Kunde", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__endDate": "Enddatum", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__price": "Pre<PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__priceReason__name": "Preisgrund", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__salesSite__id": "Verkaufsstandort", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__startDate": "Startdatum", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__stockSite__id": "Lagerstandort", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__unit__id": "Einheit", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPricesSection____title": "Artikelpreise", "@sage/xtrem-master-data/pages__customer__items____columns__columns__item__name__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____columns__title__id": "ID Artikel-Kunde", "@sage/xtrem-master-data/pages__customer__items____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__customer__items____columns__title__maximumSalesQuantity": "Höchstverkaufsmenge", "@sage/xtrem-master-data/pages__customer__items____columns__title__minimumSalesQuantity": "Mindestverkaufsmenge", "@sage/xtrem-master-data/pages__customer__items____columns__title__name": "Name Artikel-Kunde", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnit__name": "Verkaufseinheit", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnitToStockUnitConversion": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__customer__items____title": "Artikel", "@sage/xtrem-master-data/pages__customer__itemSection____title": "Artikel", "@sage/xtrem-master-data/pages__customer__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__customer__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__customer__minimumOrderAmount____title": "Mindestbestellwert", "@sage/xtrem-master-data/pages__customer__noteBlock____title": "Notizen", "@sage/xtrem-master-data/pages__customer__notesSection____title": "Notizen", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine1": "Zeile 1", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine2": "Zeile 2", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON> (USA)", "@sage/xtrem-master-data/pages__customer__payByAddress____title": "<PERSON>uptad<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__payByCustomer____title": "Rechnungsempfänger", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____columns__title__concatenatedAddress": "<PERSON>uptad<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____title": "<PERSON>uptad<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__paymentTerm____lookupDialogTitle": "Zahlungsbedingung auswählen", "@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory": "Weisen Sie der Adresse einen aktiven Hauptkontakt zu.", "@sage/xtrem-master-data/pages__customer__primary_active_address_mandatory": "Weisen Sie dem Kunden eine aktive Hauptadresse zu.", "@sage/xtrem-master-data/pages__customer__primary_ship_to_address_mandatory": "Der Kunde muss mindestens eine aktive Hauptlieferadresse haben.", "@sage/xtrem-master-data/pages__customer__put_on_hold": "<PERSON>nde ges<PERSON>rt", "@sage/xtrem-master-data/pages__customer__putOnHold____title": "<PERSON><PERSON><PERSON>zen", "@sage/xtrem-master-data/pages__customer__remove_on_hold": "Kundensperre entfernt", "@sage/xtrem-master-data/pages__customer__removeOnHold____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__save____title": "Speichern", "@sage/xtrem-master-data/pages__customer__ShippingSection____title": "<PERSON>ers<PERSON>", "@sage/xtrem-master-data/pages__customer__website____title": "Webseite", "@sage/xtrem-master-data/pages__customer_address_add_new____title": "Neue Kundenadresse", "@sage/xtrem-master-data/pages__customer_address_contacts____title": "Kontakte für Kundenadresse: {{value}}", "@sage/xtrem-master-data/pages__customer_address_edit____title": "Kundenadresse bearbeiten", "@sage/xtrem-master-data/pages__customer_address_panel_new__isPrimaryShippingAddress_must_be_active": "Aktivieren Sie die Hauptlieferadresse.", "@sage/xtrem-master-data/pages__customer_contact_list__primary_contact": "Hauptkontakt", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypePlural": "Gründe für Kundenpreis", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypeSingular": "Grund für Kundenpreis", "@sage/xtrem-master-data/pages__customer_price_reason____title": "Grund für Kundenpreis", "@sage/xtrem-master-data/pages__customer_price_reason__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__customer_price_reason__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__customer_price_reason__name____title": "Name", "@sage/xtrem-master-data/pages__customer_price_reason__priority____title": "Priorität", "@sage/xtrem-master-data/pages__customer_price_reason__priority_already_exists": "Priorität {{priority}} bereits vorhanden.", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Verwaltung Nummernkreise", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line_4__title": "Lieferant", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line3__title": "Kunde", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line4__title": "Lieferant", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__sequenceNumber__title": "Nummernkreis", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypePlural": "Lieferanten- und Kundenkategorien", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypeSingular": "Lieferanten- und Kundenkategorie", "@sage/xtrem-master-data/pages__customer_supplier_category____title": "Lieferanten- und Kundenkategorie", "@sage/xtrem-master-data/pages__customer_supplier_category__id____title": "ID", "@sage/xtrem-master-data/pages__customer_supplier_category__isCustomer____title": "Kunde", "@sage/xtrem-master-data/pages__customer_supplier_category__isSequenceNumberManagement____title": "Verwaltung Nummernkreise", "@sage/xtrem-master-data/pages__customer_supplier_category__isSupplier____title": "Lieferant", "@sage/xtrem-master-data/pages__customer_supplier_category__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__customer_supplier_category__name____title": "Name", "@sage/xtrem-master-data/pages__customer_supplier_category__sequenceNumber____title": "Nummernkreis", "@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content": "Die von Ihnen ausgewählte Kategorie ist mit einem Nummernkreis verknüpft. Möchten Sie eine neue ID generieren oder die aktuelle beibehalten?", "@sage/xtrem-master-data/pages__customer-supplier__generate_ID": "ID generieren", "@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-": "Aktuelle ID beibehalten", "@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title": "ID-<PERSON><PERSON><PERSON> auswählen", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-customer": "ID Nummernkreis Kunde auswählen", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier": "ID Nummernkreis Lieferant auswählen", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier-customer": "ID Nummernkreis Lieferant und Kunde auswählen", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__formattedCapacity__title": "Kapazität", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__isFullDay__title": "Ganzer Tag", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__daily_shift____objectTypePlural": "Tägliche Schichten", "@sage/xtrem-master-data/pages__daily_shift____objectTypeSingular": "Tägliche Schicht", "@sage/xtrem-master-data/pages__daily_shift____title": "Tägliche Schicht", "@sage/xtrem-master-data/pages__daily_shift___id____title": "ID", "@sage/xtrem-master-data/pages__daily_shift__addShiftDetail____title": "Schichtdetails hinzufügen", "@sage/xtrem-master-data/pages__daily_shift__detailsBlock____title": "Details", "@sage/xtrem-master-data/pages__daily_shift__formattedCapacity____title": "Kapazität", "@sage/xtrem-master-data/pages__daily_shift__isFullDay____title": "Ganzer Tag", "@sage/xtrem-master-data/pages__daily_shift__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__daily_shift__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__daily_shift__name____title": "Name", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__formattedDuration": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__id": "ID", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__name": "Name", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftStart": "Schichtbeginn", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____title": "Schichtdetails", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__formattedDuration": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftStart": "Schichtbeginn", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____lookupDialogTitle": "Schichtdetails auswählen", "@sage/xtrem-master-data/pages__delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-master-data/pages__delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content": "<PERSON><PERSON> sind da<PERSON>, diese Zeile zu löschen. Diese Aktion kann nicht mehr rückgängig gemacht werden, nachdem das Dokument gespeichert wurde.", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title": "Duplizieren", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title__duplicate": "Duplizieren", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__delivery_mode____objectTypePlural": "Lieferarten", "@sage/xtrem-master-data/pages__delivery_mode____objectTypeSingular": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__delivery_mode__id____title": "ID", "@sage/xtrem-master-data/pages__delivery_mode__name____title": "Name", "@sage/xtrem-master-data/pages__delivery_mode__section____title": "Allgemein", "@sage/xtrem-master-data/pages__email_exception": "Die E-Mail konnte nicht gesendet werden: ({{exception}})", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2Right__title": "Ressource", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__employee____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee__firstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee__id____title": "ID", "@sage/xtrem-master-data/pages__employee__image____title": "Bild", "@sage/xtrem-master-data/pages__employee__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__employee__lastName____title": "Nachname", "@sage/xtrem-master-data/pages__employee__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__employee__resource____columns__title__resourceGroup__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee__resource____lookupDialogTitle": "Ressource auswählen", "@sage/xtrem-master-data/pages__employee__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__enter_email_address_and_last_name": "Die E-Mail wurde nicht gesendet. Sie müssen eine E-Mail-Adresse und einen Nachnamen erfassen.", "@sage/xtrem-master-data/pages__ghs_classification____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__ghs_classification____objectTypePlural": "GHS-Klassifizierungen", "@sage/xtrem-master-data/pages__ghs_classification____objectTypeSingular": "GHS-Klassifizierung", "@sage/xtrem-master-data/pages__ghs_classification____title": "GHS-Klassifizierung", "@sage/xtrem-master-data/pages__ghs_classification__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__ghs_classification__ghsClassificationInformationBlock____title": "GHS-Klassifizierung", "@sage/xtrem-master-data/pages__ghs_classification__hazard____title": "Gefahren", "@sage/xtrem-master-data/pages__ghs_classification__id____title": "ID", "@sage/xtrem-master-data/pages__ghs_classification__idBlock____title": "ID", "@sage/xtrem-master-data/pages__ghs_classification__name____title": "Name", "@sage/xtrem-master-data/pages__ghs_classification__pictogram____title": "Piktogramm", "@sage/xtrem-master-data/pages__ghs_classification__pictogramBlock____title": "Piktogramm", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__title": "Effizienz", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__location__title": "Lagerplatz", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__minCapabilityLevel__title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__type__title": "Ressourcentyp", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__weeklyShift__title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__group_resource____objectTypePlural": "Ressourcengruppen", "@sage/xtrem-master-data/pages__group_resource____objectTypeSingular": "Ressourcengruppe", "@sage/xtrem-master-data/pages__group_resource____title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__group_resource__addCostCategory____title": "Kostenkategorie hinzufügen", "@sage/xtrem-master-data/pages__group_resource__addReplacementLine____title": "Hinzufügen", "@sage/xtrem-master-data/pages__group_resource__addResource____helperText": "Resso<PERSON>ce hinzufügen", "@sage/xtrem-master-data/pages__group_resource__addResource____title": "Resso<PERSON>ce hinzufügen", "@sage/xtrem-master-data/pages__group_resource__blockDetails____title": "Einstellungen", "@sage/xtrem-master-data/pages__group_resource__blockReplacements____title": "Ressourcengruppenalternative", "@sage/xtrem-master-data/pages__group_resource__blockResources____title": "Ressourcen", "@sage/xtrem-master-data/pages__group_resource__blockWeekly____title": "Details wöchentliche Schicht", "@sage/xtrem-master-data/pages__group_resource__costBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__costSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__group_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__group_resource__efficiency____title": "Effizienz", "@sage/xtrem-master-data/pages__group_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__group_resource__id____title": "ID", "@sage/xtrem-master-data/pages__group_resource__location____columns__title__locationType__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__group_resource__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____lookupDialogTitle": "Kompetenzniveau auswählen", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__group_resource__name____title": "Name", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title___sortValue": "Priorität", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__id": "ID", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__name": "Name", "@sage/xtrem-master-data/pages__group_resource__replacements____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__group_resource__replacements____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCapacity____title": "Wöchentliche Kapazität", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Typ Kostenkategorie", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costCategory__name": "Kostenkategorie", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costUnit__name": "Kosteneinheit", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirekte Kosten", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__runCost": "Bearbeitung", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____title": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__weeklyShift__id": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__2": "Ausschließen", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__group_resource__resources____title": "Ressourcen", "@sage/xtrem-master-data/pages__group_resource__section____title": "Allgemein", "@sage/xtrem-master-data/pages__group_resource__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__group_resource__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__group_resource__transferInResource____helperText": "Einschließen", "@sage/xtrem-master-data/pages__group_resource__transferInResource____title": "Einschließen", "@sage/xtrem-master-data/pages__group_resource__type____title": "Ressourcentyp", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____columns__title__dailyShift": "Tägliche Schicht", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____title": "Wochendetails", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____columns__title__formattedCapacity": "Kapazität", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____lookupDialogTitle": "Wöchentliche Schicht auswählen", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__incoterm____objectTypePlural": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__incoterm____objectTypeSingular": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__incoterm____title": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__incoterm__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__incoterm__id____title": "ID", "@sage/xtrem-master-data/pages__incoterm__name____title": "Name", "@sage/xtrem-master-data/pages__incoterm__section____title": "Allgemein", "@sage/xtrem-master-data/pages__indirect_cost_origin____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypePlural": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypeSingular": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_origin____title": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_origin__id____title": "ID", "@sage/xtrem-master-data/pages__indirect_cost_origin__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__indirect_cost_origin__name____title": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line_4__title": "Berechnungsmethode", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line4__title": "Berechnungsmethode", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypePlural": "Bereiche indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypeSingular": "Bereich indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_section____title": "Bereich indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_section__addOrigin____title": "Hinzufügen", "@sage/xtrem-master-data/pages__indirect_cost_section__calculationMethod____title": "Berechnungsmethode", "@sage/xtrem-master-data/pages__indirect_cost_section__id____title": "ID", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__id": "Ursprung indirekte Kosten", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__name": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__percentage": "Prozentsatz", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____title": "Details", "@sage/xtrem-master-data/pages__indirect_cost_section__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__indirect_cost_section__name____title": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section__originsBlock____title": "Details", "@sage/xtrem-master-data/pages__indirect_cost_section__totalPercentage____title": "Prozentsatz gesamt", "@sage/xtrem-master-data/pages__invalid-email": "Die E-Mail-Adresse ist nicht korrekt: {{email}}.", "@sage/xtrem-master-data/pages__item____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__item____navigationPanel__dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item____navigationPanel__emptyStateClickableText": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen Artikel.", "@sage/xtrem-master-data/pages__item____navigationPanel__inlineActions__title__duplicate": "Duplizieren", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__basePrice__title": "Grundpreis", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__commodityCode__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__title": "Verkaufswährung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__expirationDate__title": "Ablaufdatumsverwaltung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBomRevisionManaged__title": "Stücklistenänderung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBought__title": "Eingekauft", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isManufactured__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isPhantom__title": "Phantom", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isSold__title": "Verkauft", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isStockManaged__title": "Bestandsverwaltung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line2Right__title": "GTIN-13", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line3Right__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotManagement__title": "Chargenverwaltung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotSequenceNumber__title": "Nummernkreis Charge", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__maximumSalesQuantity__title": "Höchstverkaufsmenge", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumPrice__title": "Mindestpreis", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumSalesQuantity__title": "Mindestverkaufsmenge", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__purchaseUnit__title": "Einkaufseinheit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__salesUnit__title": "Verkaufseinheit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberManagement__title": "Seriennummernverwaltung", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberSequenceNumber__title": "Nummernkreis Seriennummer", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__stockUnit__title": "Lagereinheit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__volumeUnit__title": "Volumeneinheit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__weightUnit__title": "Gewichtseinheit", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__3": "In Entwicklung", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__4": "<PERSON>cht verlä<PERSON>t", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__5": "Abgelaufen", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__6": "<PERSON>cht verwendbar", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__7": "Dienstleistung", "@sage/xtrem-master-data/pages__item____objectTypePlural": "Artikel", "@sage/xtrem-master-data/pages__item____objectTypeSingular": "Artikel", "@sage/xtrem-master-data/pages__item____title": "Artikel", "@sage/xtrem-master-data/pages__item___id____title": "ID", "@sage/xtrem-master-data/pages__item__addItemSite____title": "Hinzufügen", "@sage/xtrem-master-data/pages__item__addNewAllergen____title": "Allergen auswählen", "@sage/xtrem-master-data/pages__item__addNewAllergenAction____title": "Allergen hinzufügen", "@sage/xtrem-master-data/pages__item__addNewClassification____title": "GHS-Klassifizierung auswählen", "@sage/xtrem-master-data/pages__item__addNewClassificationAction____title": "Klassifizierung hinzufügen", "@sage/xtrem-master-data/pages__item__addSupplier____title": "Hinzufügen", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__id": "ID", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__name": "Name", "@sage/xtrem-master-data/pages__item__allergens____dropdownActions__title__remove": "Entfernen", "@sage/xtrem-master-data/pages__item__allergens____mobileCard__subtitle__title": "ID", "@sage/xtrem-master-data/pages__item__allergens____title": "Allergene", "@sage/xtrem-master-data/pages__item__allergenSection____title": "Allergen", "@sage/xtrem-master-data/pages__item__basePrice____title": "Grundpreis", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____helperText": "<PERSON>ählen Sie einen Nummernkreis aus oder lassen Sie das Feld leer, um die Stücklistenänderungsnummern manuell zu erfassen.", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____lookupDialogTitle": "Nummernkreis Stücklistenänderung auswählen", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____title": "Nummernkreis Stücklistenänderung", "@sage/xtrem-master-data/pages__item__capacity____title": "Kapazität", "@sage/xtrem-master-data/pages__item__category____columns__title__sequenceNumber__name": "Nummernkreis", "@sage/xtrem-master-data/pages__item__category____lookupDialogTitle": "Artikelkategorie auswählen", "@sage/xtrem-master-data/pages__item__category____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__classifications____mobileCard__subtitle__title": "ID", "@sage/xtrem-master-data/pages__item__commodityCode____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__commodityCodeEU____title": "EU-Warennummer", "@sage/xtrem-master-data/pages__item__communityCodeEU____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__customers____columns__columns__salesUnit__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__businessEntity__id": "Kunden-ID", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__name": "Kunde", "@sage/xtrem-master-data/pages__item__customers____columns__title__id": "ID Artikel-Kunde", "@sage/xtrem-master-data/pages__item__customers____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__item__customers____columns__title__maximumSalesQuantity": "Höchstverkaufsmenge", "@sage/xtrem-master-data/pages__item__customers____columns__title__minimumSalesQuantity": "Mindestverkaufsmenge", "@sage/xtrem-master-data/pages__item__customers____columns__title__name": "Artikel-Kunde", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnit__name": "Verkaufseinheit", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnitToStockUnitConversion": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-master-data/pages__item__customers____dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item__customers____inlineActions__title__openLinePanel": "Zeilenbereich ö<PERSON>", "@sage/xtrem-master-data/pages__item__customers____mobileCard__title__title": "Kunde", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__item__customers____sidebar__headerDropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__item__customers____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__customerSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__customsUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__density____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__item__eanNumber____title": "GTIN-13", "@sage/xtrem-master-data/pages__item__financialBlock____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__item__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__item__generateId": "ID generieren", "@sage/xtrem-master-data/pages__item__generateNewId": "Die von Ihnen ausgewählte Kategorie ist bereits mit einem Nummernkreis verknüpft. Möchten Sie die ID beibehalten oder eine neue ID generieren?", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__id": "ID", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__name": "Name", "@sage/xtrem-master-data/pages__item__ghsClassifications____dropdownActions__title__remove": "Entfernen", "@sage/xtrem-master-data/pages__item__ghsClassifications____title": "GHS-Klassifizierungen", "@sage/xtrem-master-data/pages__item__ghsClassificationSection____title": "GHS-Klassifizierung", "@sage/xtrem-master-data/pages__item__good_stock_block_title": "Bestand", "@sage/xtrem-master-data/pages__item__good_stock_unit_title": "Lagereinheit", "@sage/xtrem-master-data/pages__item__headerSection____title": "Kopfberei<PERSON>", "@sage/xtrem-master-data/pages__item__image____title": "Bild", "@sage/xtrem-master-data/pages__item__imageBlock____title": "Bild", "@sage/xtrem-master-data/pages__item__inventoryBlock____title": "Bestand", "@sage/xtrem-master-data/pages__item__isBomRevisionManaged____title": "Stücklistenänderung", "@sage/xtrem-master-data/pages__item__isBought____title": "Eingekauft", "@sage/xtrem-master-data/pages__item__isExpiryManaged____title": "Ablaufdatumsverwaltung", "@sage/xtrem-master-data/pages__item__isManufactured____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__isPhantom____title": "Phantom", "@sage/xtrem-master-data/pages__item__isPotencyManagement____title": "Konzentration", "@sage/xtrem-master-data/pages__item__isSold____title": "Verkauft", "@sage/xtrem-master-data/pages__item__isStockManaged____title": "Bestandsverwaltung", "@sage/xtrem-master-data/pages__item__isTraceabilityManagement____title": "Rückverfolgung", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__indirectCostSection__name__title__3": "Berechnungsmethode", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title": "Name", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title__2": "ID", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title___id": "ID", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__title__4": "Buchhaltungswährung", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__title": "Unternehmen", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__prodLeadTime": "Tag(e)", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__purchaseLeadTime": "Tag(e)", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__batchQuantity": "Losmenge", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__economicOrderQuantity": "Optimale Bestellmenge", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__expectedQuantity": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__preferredProcess": "Bevorzugte Verarbeitung", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__prodLeadTime": "Fertigungszeit", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__replenishmentMethod": "Wiederbeschaffungsart", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__requiredQuantity": "Erforderliche Menge", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__safetyStock": "Sicherheitsbestand", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__stdCostValue": "Stückkosten", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__valuationMethod": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__edit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____title": "Artikel-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__keepCurrentId": "Aktuelle ID verwenden", "@sage/xtrem-master-data/pages__item__lotManagement____title": "Chargenverwaltung", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____lookupDialogTitle": "Nummernkreis Charge auswählen", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____title": "Nummernkreis Charge", "@sage/xtrem-master-data/pages__item__mainBlock____title": "Verwaltung", "@sage/xtrem-master-data/pages__item__mainSection____title": "Informationen", "@sage/xtrem-master-data/pages__item__managementSection____title": "Verwaltung", "@sage/xtrem-master-data/pages__item__manufacturingBlock____title": "Fertigung", "@sage/xtrem-master-data/pages__item__maximumSalesQuantity____title": "Höchstmenge", "@sage/xtrem-master-data/pages__item__minimumPrice____title": "Mindestpreis", "@sage/xtrem-master-data/pages__item__minimumSalesQuantity____title": "Mindestmenge", "@sage/xtrem-master-data/pages__item__name____title": "Name", "@sage/xtrem-master-data/pages__item__positionField1____title": "Positionsfeld 1", "@sage/xtrem-master-data/pages__item__positionField2____title": "Positionsfeld 2", "@sage/xtrem-master-data/pages__item__positionField3____title": "Positionsfeld 3", "@sage/xtrem-master-data/pages__item__priceSection____title": "Lieferantenpreise", "@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places": "Die Einkaufseinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__purchaseUnit____lookupDialogTitle": "Einkaufseinheit auswählen", "@sage/xtrem-master-data/pages__item__purchaseUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversion____title": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversionDedicated____title": "Dedizierter Faktor", "@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places": "Die Verkaufseinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/pages__item__salesBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item__salesCurrency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__item__salesCurrency____title": "Währung", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title": "Name", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title__2": "ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title": "Name", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__2": "ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__3": "Unternehmen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__currency__name": "Währung auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__customer": "Kunde auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__priceReason__name": "Preisgrund auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__salesSite": "Standort auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__stockSite": "Standort auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__unit__name": "Einheit auswählen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__charge": "Z<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__name": "Währung", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__businessEntity__id": "Kunden-ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__id": "Kunde", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__name": "Kunde", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__discount": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__endDate": "Gültig bis", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__item__id": "Artikel", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__price": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__priceReason__name": "Preisgrund", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite": "Verkaufsstandort", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite__name": "Verkaufsstandort", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__startDate": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite": "Lagerstandort", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite__id": "Lagerstandort", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__id": "Einheit", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__name": "Einheit", "@sage/xtrem-master-data/pages__item__salesPrices____dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item__salesPrices____inlineActions__title__openLinePanel": "Zeilenbereich ö<PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__title__title": "Kunde", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__titleRight__title": "Preisgrund", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__item__salesPrices____sidebar__headerDropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__item__salesPrices____title": "Kundenpreise", "@sage/xtrem-master-data/pages__item__salesPricesSection____title": "Verkaufspreise", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesUnit____lookupDialogTitle": "Verkaufseinheit auswählen", "@sage/xtrem-master-data/pages__item__salesUnitBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversion____title": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversionDedicated____title": "Dedizierter Faktor", "@sage/xtrem-master-data/pages__item__saveItem____title": "Speichern", "@sage/xtrem-master-data/pages__item__selectId": "ID-<PERSON><PERSON><PERSON> auswählen", "@sage/xtrem-master-data/pages__item__serialNumberManagement____title": "Seriennummernverwaltung", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____lookupDialogTitle": "Nummernkreis Seriennummer auswählen", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____title": "Nummernkreis Seriennummer", "@sage/xtrem-master-data/pages__item__serialNumberUsage____title": "Verwendung Seriennummer", "@sage/xtrem-master-data/pages__item__service_stock_block_title": "Einheit", "@sage/xtrem-master-data/pages__item__service_stock_unit_title": "Basiseinheit", "@sage/xtrem-master-data/pages__item__siteSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__status____title": "Status", "@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places": "Die Lagereinheit ({{unitOfMeasure}}) kann keine Dezimalstellen für seriennummerierte Artikel haben.", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__stockUnit____lookupDialogTitle": "Lagereinheit auswählen", "@sage/xtrem-master-data/pages__item__stockUnit____title": "Lagereinheit", "@sage/xtrem-master-data/pages__item__storageBlock____title": "Eigenschaften", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title": "Name", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__2": "ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__3": "Unternehmen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title": "Name", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__2": "ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__3": "Steuer-ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__4": "Land", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__currency__name": "Währung auswählen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__site": "Standort auswählen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__supplier": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__unit__name": "Einheit auswählen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__name": "Währung", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValid": "Gültigkeit", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidFrom": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidTo": "Gültig bis", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__price": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__priority": "Priorität", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__site__businessEntity__id": "Standort-ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__businessEntity__id": "ID Lieferant", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__id": "Lieferant", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__name": "Name", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__id": "Einheit", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__name": "Maßeinheit", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__symbol": "Symbol", "@sage/xtrem-master-data/pages__item__supplierPrices____dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item__supplierPrices____inlineActions__title__openLinePanel": "Zeilenbereich ö<PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__title__title": "Lieferant", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____sidebar__headerDropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__item__supplierPrices____title": "Lieferantenpreise", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title": "Name", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__2": "ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__3": "Steuer-ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__4": "Land", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__purchaseUnitOfMeasure": "Einheit auswählen", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__supplier": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isDefaultItemSupplier": "Standardlieferant", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__minimumPurchaseQuantity": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseUnitOfMeasure": "Einkaufseinheit", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier___id": "ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__businessEntity__id": "ID Lieferant", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__id": "Lieferant", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__name": "Name", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemCode": "ID Artikel-Lieferant", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemName": "Artikel-Lieferant", "@sage/xtrem-master-data/pages__item__suppliers____dropdownActions__title__delete": "Löschen", "@sage/xtrem-master-data/pages__item__suppliers____inlineActions__title__openLinePanel": "Zeilenbereich ö<PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____mobileCard__title__title": "Lieferant", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__item__suppliers____sidebar__headerDropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__item__suppliers____title": "Lieferanten", "@sage/xtrem-master-data/pages__item__supplierSection____title": "Lieferanten", "@sage/xtrem-master-data/pages__item__type____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__typeBlock____title": "Artikelinformationen", "@sage/xtrem-master-data/pages__item__unitBlock____title": "Bestand", "@sage/xtrem-master-data/pages__item__unitSection____title": "Einheiten", "@sage/xtrem-master-data/pages__item__volume____title": "Volumen", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__volumeUnit____lookupDialogTitle": "Volumeneinheit auswählen", "@sage/xtrem-master-data/pages__item__weight____title": "Gewicht", "@sage/xtrem-master-data/pages__item__weightUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__weightUnit____lookupDialogTitle": "Gewichtseinheit auswählen", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Verwaltung Nummernkreise", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__sequenceNumber__title": "Nummernkreis Artikel-ID", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__type__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_category____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item_category____objectTypePlural": "Artikelkategorien", "@sage/xtrem-master-data/pages__item_category____objectTypeSingular": "Artikelkategorie", "@sage/xtrem-master-data/pages__item_category____title": "Artikelkategorie", "@sage/xtrem-master-data/pages__item_category__declarationsBlock____title": "Kennzeichnungen", "@sage/xtrem-master-data/pages__item_category__generalBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__item_category__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__item_category__id____title": "ID", "@sage/xtrem-master-data/pages__item_category__isSequenceNumberManagement____title": "Verwaltung Nummernkreise", "@sage/xtrem-master-data/pages__item_category__name____title": "Name", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____lookupDialogTitle": "Nummernkreis Artikel-ID auswählen", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____title": "Nummernkreis Artikel-ID", "@sage/xtrem-master-data/pages__item_category__type____title": "Kennzeichnungen", "@sage/xtrem-master-data/pages__item_customer__edit____title": "Artikel-Kunde bearbeiten", "@sage/xtrem-master-data/pages__item_customer_panel____title": "Artikel-Kunde hinzufügen", "@sage/xtrem-master-data/pages__item_customer_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_customer_panel__id____title": "ID Artikel-Kunde", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__item_customer_panel__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive": "Der Artikel ist nicht aktiv. Der Status ist {{status}}.", "@sage/xtrem-master-data/pages__item_customer_panel__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__item_customer_panel__maximumSalesQuantity____title": "Höchstverkaufsmenge", "@sage/xtrem-master-data/pages__item_customer_panel__minimumSalesQuantity____title": "Mindestverkaufsmenge", "@sage/xtrem-master-data/pages__item_customer_panel__name____title": "Name Artikel-Kunde", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____lookupDialogTitle": "Verkaufseinheit auswählen", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____title": "Verkaufseinheit", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnitToStockUnitConversion____title": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-master-data/pages__item_customer_panel__save____title": "OK", "@sage/xtrem-master-data/pages__item_customer_price_panel____title": "Kundenpreis", "@sage/xtrem-master-data/pages__item_customer_price_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____title": "Z<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__confirm____title": "Speichern", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____title": "Währung", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____lookupDialogTitle": "Kunde auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____title": "Kunde", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__edit____title": "Verkaufspreis bearbeiten", "@sage/xtrem-master-data/pages__item_customer_price_panel__endDate____title": "Enddatum", "@sage/xtrem-master-data/pages__item_customer_price_panel__fromQuantity____title": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_customer_price_panel__new____title": "Neuer Verkaufspreis", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100": "Erfassen Sie einen Prozentsatz kleiner als 100.", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative": "Erfassen Sie einen positiven Prozentsatz.", "@sage/xtrem-master-data/pages__item_customer_price_panel__price____title": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____lookupDialogTitle": "Preisgrund auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____title": "Preisgrund", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____lookupDialogTitle": "Verkaufsstandort auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____title": "Verkaufsstandort", "@sage/xtrem-master-data/pages__item_customer_price_panel__startDate____title": "Startdatum", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____lookupDialogTitle": "Lagerstandort auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____title": "Lagerstandort", "@sage/xtrem-master-data/pages__item_customer_price_panel__toQuantity____title": "Menge bis", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____lookupDialogTitle": "Einheit auswählen", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____title": "Maßeinheit", "@sage/xtrem-master-data/pages__item_customer_price_panel__validUnits____title": "Gültige Einheiten", "@sage/xtrem-master-data/pages__item_customer_price_view_panel____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__idBlock____title": "ID", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__columns__priceReason__name__title": "Priorität", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title___id": "ID", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__currency__id": "Währung", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__customer__id": "Kunde", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__endDate": "Enddatum", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__id": "Artikel", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__price": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__priceReason__name": "Preisgrund", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__id": "Verkaufsstandort", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__name": "Verkaufsstandort", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__startDate": "Startdatum", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__id": "Lagerstandort", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__name": "Lagerstandort", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__unit__id": "Einheit", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__mainSection____title": "Artikel", "@sage/xtrem-master-data/pages__item_price_panel____title": "Lieferantenpreis", "@sage/xtrem-master-data/pages__item_price_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_price_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__item_price_panel__currency____title": "Währung", "@sage/xtrem-master-data/pages__item_price_panel__dateValid____title": "Gültigkeitsdatum", "@sage/xtrem-master-data/pages__item_price_panel__edit____title": "Lieferantenpreis bearbeiten", "@sage/xtrem-master-data/pages__item_price_panel__fromDate____title": "Startdatum", "@sage/xtrem-master-data/pages__item_price_panel__fromQuantity____title": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title": "Bezeichnung", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__2": "Dezimalstellen", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__3": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__stockUnit__description": "Bezeichnung", "@sage/xtrem-master-data/pages__item_price_panel__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__item_price_panel__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_price_panel__new____title": "Neuer Lieferantenpreis", "@sage/xtrem-master-data/pages__item_price_panel__price____title": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__priority____title": "Priorität", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__3": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__item_price_panel__site____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__3": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__4": "Dezimalstellen", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__country__name": "Land", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__currency__name": "Währung", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item_price_panel__supplier____title": "Lieferant", "@sage/xtrem-master-data/pages__item_price_panel__toDate____title": "Enddatum", "@sage/xtrem-master-data/pages__item_price_panel__toQuantity____title": "Menge bis", "@sage/xtrem-master-data/pages__item_price_panel__type____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__unit____lookupDialogTitle": "Einheit auswählen", "@sage/xtrem-master-data/pages__item_price_panel__unit____title": "Maßeinheit", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__averageDailyConsumption__title": "Durchschnittlicher täglicher Verbrauch", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__batchQuantity__title": "Losmenge", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__economicOrderQuantity__title": "Optimale Bestellmenge", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__expectedQuantity__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemCategory__title": "Artikelkategorie", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemDescription__title": "Artikelbezeichnung", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemId__title": "Artikel-ID", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__preferredProcess__title": "Bevorzugte Verarbeitung", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__postfix": "Tage", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__title": "Einkaufszeit", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__reorderPoint__title": "Meldebestand", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__replenishmentMethod__title": "Wiederbeschaffungsart", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__requiredQuantity__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__safetyStock__title": "Sicherheitsbestand", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__stdCostValue__title": "Standardkosten", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__title__title": "Artikel", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__valuationMethod__title": "Bewertungsmethode", "@sage/xtrem-master-data/pages__item_site____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item_site____objectTypePlural": "Artikel-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____objectTypeSingular": "Artikel-Standort", "@sage/xtrem-master-data/pages__item_site____title": "Artikel-Standort", "@sage/xtrem-master-data/pages__item_site___id____title": "ID", "@sage/xtrem-master-data/pages__item_site___valuationMethod____title": "Bewertungsmethode", "@sage/xtrem-master-data/pages__item_site__addItemSiteCost____title": "Hinzufügen", "@sage/xtrem-master-data/pages__item_site__addSupplier____title": "Hinzufügen", "@sage/xtrem-master-data/pages__item_site__averageDailyConsumption____title": "Durchschnittlicher täglicher Verbrauch", "@sage/xtrem-master-data/pages__item_site__batchQuantity____title": "Losmenge", "@sage/xtrem-master-data/pages__item_site__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____lookupDialogTitle": "Standardlagerplatz auswählen", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____title": "Freigegebener Artikel", "@sage/xtrem-master-data/pages__item_site__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site__costBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__costCategoryType": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_site__costCategory____title": "Kostenkategorie", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__costCategory__name": "Kostenkategorie", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__forQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__fromDate": "Startdatum", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__isCalculated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__totalCost": "<PERSON><PERSON> gesamt", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__unitCost": "Stückkosten", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__3": "Zeitraum hinzufügen", "@sage/xtrem-master-data/pages__item_site__costs____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costsBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__counting-in-progress": "Invent<PERSON> l<PERSON>", "@sage/xtrem-master-data/pages__item_site__countingInProgress____title": "Invent<PERSON> l<PERSON>", "@sage/xtrem-master-data/pages__item_site__countingInProgressMention____title": "Hinweis Inventur läuft", "@sage/xtrem-master-data/pages__item_site__economicOrderQuantity____title": "Optimale Bestellmenge", "@sage/xtrem-master-data/pages__item_site__edit____title": "Artikel-Standort bearbeiten", "@sage/xtrem-master-data/pages__item_site__expectedQuantity____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__forQuantity____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__fromDate____title": "Startdatum", "@sage/xtrem-master-data/pages__item_site__id____title": "Artikel", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____lookupDialogTitle": "Standardlagerplatz auswählen", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__indirectCost____title": "Indirekt", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____lookupDialogTitle": "Bereich indirekte Kosten auswählen", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____title": "Bereich indirekte Kosten", "@sage/xtrem-master-data/pages__item_site__isCalculated____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__isOrderToOrder____title": "Auftrag-zu-Auftrag", "@sage/xtrem-master-data/pages__item_site__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__item_site__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_site__laborCost____title": "Arbeitskraft", "@sage/xtrem-master-data/pages__item_site__locationBlock____title": "Standardlagerplatz", "@sage/xtrem-master-data/pages__item_site__machineCost____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__item_site__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site__new____title": "Neuer Artikel-Standort", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____lookupDialogTitle": "Standardlagerplatz auswählen", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____title": "Ausgänge", "@sage/xtrem-master-data/pages__item_site__preferredProcess____title": "Bevorzugte Verarbeitung", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____postfix": "Tag(e)", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____title": "Fertigungszeit", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____postfix": "Tag(e)", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____title": "Einkaufszeit", "@sage/xtrem-master-data/pages__item_site__qualityControlBlock____title": "Standardstatus Qualitätskontrolle", "@sage/xtrem-master-data/pages__item_site__reorderPoint____title": "Meldebestand", "@sage/xtrem-master-data/pages__item_site__replenishmentBlock____title": "Wiederbeschaffung", "@sage/xtrem-master-data/pages__item_site__replenishmentMethod____title": "Wiederbeschaffungsart", "@sage/xtrem-master-data/pages__item_site__replenishmentSection____title": "Wiederbeschaffung", "@sage/xtrem-master-data/pages__item_site__requiredQuantity____title": "Erforderliche Menge", "@sage/xtrem-master-data/pages__item_site__safetyStock____title": "Sicherheitsbestand", "@sage/xtrem-master-data/pages__item_site__saveItemSite____title": "Speichern", "@sage/xtrem-master-data/pages__item_site__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__item_site__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__item_site__stdCostBlock____title": "Standardkosten", "@sage/xtrem-master-data/pages__item_site__stdCostValue____title": "Standardkosten", "@sage/xtrem-master-data/pages__item_site__stockBlock____title": "Bestand", "@sage/xtrem-master-data/pages__item_site__stockRulesSection____title": "Bestandsregeln", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title___id": "ID", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__isDefaultItemSupplier": "Standard", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__minimumPurchaseOrderQuantity": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseUnit__name": "Einheit", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__id": "ID Lieferant", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__item_site__suppliers____title": "Lieferanten", "@sage/xtrem-master-data/pages__item_site__suppliersBlock____title": "Lieferanten", "@sage/xtrem-master-data/pages__item_site__suppliersSection____title": "Lieferanten", "@sage/xtrem-master-data/pages__item_site__toDate____title": "Enddatum", "@sage/xtrem-master-data/pages__item_site__toolCost____title": "Werkzeug", "@sage/xtrem-master-data/pages__item_site__totalCost____title": "<PERSON><PERSON> gesamt", "@sage/xtrem-master-data/pages__item_site__unitCost____title": "Kosten pro Einheit", "@sage/xtrem-master-data/pages__item_site__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__listItem__line3__title": "Startdatum", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item_site_cost____objectTypePlural": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_site_cost____objectTypeSingular": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_site_cost____title": "Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_site_cost___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_cost__calculate____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__calculateAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__chartBlock____title": "Kostendiagramm", "@sage/xtrem-master-data/pages__item_site_cost__costBlock____title": "Kostendetail", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__costCategoryType": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__isMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____lookupDialogTitle": "Kostenkategorie auswählen", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____title": "Kostenkategorie", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriod____title": "<PERSON>euer Zeitraum", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriodAction____title": "Neue Periode", "@sage/xtrem-master-data/pages__item_site_cost__forQuantity____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__fromDate____title": "Startdatum", "@sage/xtrem-master-data/pages__item_site_cost__isCalculated____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__name": "Artikelname", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__status": "Artikelstatus", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__type": "Artikeltyp", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site": "Standortname", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site__id": "Standort-ID", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____lookupDialogTitle": "Artikel-Standort auswählen", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____title": "Artikel-Standort", "@sage/xtrem-master-data/pages__item_site_cost__laborCost____title": "Arbeitskraft", "@sage/xtrem-master-data/pages__item_site_cost__machineCost____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__item_site_cost__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost__site____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__toDate____title": "Enddatum", "@sage/xtrem-master-data/pages__item_site_cost__toolCost____title": "Werkzeug", "@sage/xtrem-master-data/pages__item_site_cost__totalCost____title": "<PERSON><PERSON> gesamt", "@sage/xtrem-master-data/pages__item_site_cost__unitCost____title": "Stückkosten", "@sage/xtrem-master-data/pages__item_site_cost__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_cost_panel____subtitle": "Bereich Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_site_cost_panel____title": "Bereich Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_site_cost_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_site_cost_panel__chartBlock____title": "Kostendiagramm", "@sage/xtrem-master-data/pages__item_site_cost_panel__costBlock____title": "Kostendetail", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__costCategoryType": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__isMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____lookupDialogTitle": "Kostenkategorie auswählen", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____title": "Kostenkategorie", "@sage/xtrem-master-data/pages__item_site_cost_panel__costChart____title": "Kostendiagramm", "@sage/xtrem-master-data/pages__item_site_cost_panel__deleteAction____title": "Löschen", "@sage/xtrem-master-data/pages__item_site_cost_panel__forQuantity____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__fromDate____title": "Startdatum", "@sage/xtrem-master-data/pages__item_site_cost_panel__invalid-from-date": "Das Startdatum kann nicht vor dem heutigen Datum liegen.", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__site__id__title": "Symbol", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__batchQuantity": "Losmenge", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__name": "Artikelname", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__id": "Standort-ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__name": "Standortname", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____lookupDialogTitle": "Artikel-Standort auswählen", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____title": "Artikel-Standort", "@sage/xtrem-master-data/pages__item_site_cost_panel__laborCost____title": "Arbeitskraft", "@sage/xtrem-master-data/pages__item_site_cost_panel__machineCost____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost_panel__save____title": "Speichern", "@sage/xtrem-master-data/pages__item_site_cost_panel__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__item_site_cost_panel__toDate____title": "Enddatum", "@sage/xtrem-master-data/pages__item_site_cost_panel__toolCost____title": "Werkzeug", "@sage/xtrem-master-data/pages__item_site_cost_panel__totalCost____title": "<PERSON><PERSON> gesamt", "@sage/xtrem-master-data/pages__item_site_cost_panel__unitCost____title": "Stückkosten", "@sage/xtrem-master-data/pages__item_site_cost_panel__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_supplier____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypePlural": "Artikel-Standort-Lieferanten", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypeSingular": "Artikel-Standort-Lieferant", "@sage/xtrem-master-data/pages__item_site_supplier____title": "Artikel-Standort-Lieferant", "@sage/xtrem-master-data/pages__item_site_supplier___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_site_supplier__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site_supplier__createItemSiteSupplier____title": "<PERSON>eu", "@sage/xtrem-master-data/pages__item_site_supplier__deleteItemSiteSupplier____title": "Löschen", "@sage/xtrem-master-data/pages__item_site_supplier__identificationBlock____title": "Identifikation", "@sage/xtrem-master-data/pages__item_site_supplier__isDefaultItemSupplier____title": "Standard-Artikel-Standort-Lieferant", "@sage/xtrem-master-data/pages__item_site_supplier__itemDetailsBlock____title": "Artikeldetails", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__name": "Artikelname", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__status": "Artikelstatus", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__type": "Artikeltyp", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site": "Standortname", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site__id": "Standort-ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____lookupDialogTitle": "Artikel-Standort auswählen", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____title": "Artikel-Standort", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__country__name": "Land", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____title": "Lieferant", "@sage/xtrem-master-data/pages__item_site_supplier__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__item_site_supplier__minimumPurchaseOrderQuantity____title": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____postfix": "Tage", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____title": "Einkaufszeit", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseUnit____lookupDialogTitle": "Einkaufseinheit auswählen", "@sage/xtrem-master-data/pages__item_site_supplier__saveItemSiteSupplier____title": "Speichern", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__country__name": "Land", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name__2": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item_supplier__item_cost_edit____title": "Artikel-Standort-<PERSON><PERSON> bearbeiten", "@sage/xtrem-master-data/pages__item_supplier__item_cost_new____title": "Neue Artikel-Standort-Kosten", "@sage/xtrem-master-data/pages__item_supplier__item_edit____title": "Artikel-Standort-Lieferant bearbeiten", "@sage/xtrem-master-data/pages__item_supplier__item_new____title": "Neuer Artikel-Standort-Lieferant", "@sage/xtrem-master-data/pages__item_supplier__purchaseUnitOfMeasure____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_panel____title": "Lieferantenpreis", "@sage/xtrem-master-data/pages__item_supplier_price_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____title": "Währung", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValid____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidFrom____title": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidTo____title": "Datum bis", "@sage/xtrem-master-data/pages__item_supplier_price_panel__fromQuantity____title": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____title": "Artikel", "@sage/xtrem-master-data/pages__item_supplier_price_panel__price____title": "Pre<PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__priority____title": "Priorität", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__country__name": "Land", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__toQuantity____title": "Menge bis", "@sage/xtrem-master-data/pages__item_supplier_price_panel__type____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____lookupDialogTitle": "Einheit auswählen", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____title": "Maßeinheit", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__idBlock____title": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__currency__id": "Währung", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidFrom": "Gültigkeitsbeginn", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidTo": "Gültigkeitsende", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__site__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__mainSection____title": "Artikel", "@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value": "Die Umrechnung Verkauf in Lager darf nicht kleiner oder gleich 0 sein.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value": "Die Höchstverkaufsmenge darf nicht kleiner als die Mindestverkaufsmenge sein.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value": "Die Höchstverkaufsmenge darf nicht kleiner als 0 sein.", "@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value": "Die Mindestverkaufsmenge darf nicht kleiner als 0 sein.", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_buy_to_order": "<PERSON><PERSON> auf Bestellung", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_make_to_order": "Fertigung auf Bestellung", "@sage/xtrem-master-data/pages__item-site__preferred_process_cannot_be": "Ein Bestandsartikel muss gef<PERSON>t, <PERSON><PERSON><PERSON><PERSON><PERSON>, oder be<PERSON> sein.", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeFrom__title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeTo__title": "Aktiv bis", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__resourceGroup__title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__weeklyShift__title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__labor_resource____objectTypePlural": "Arbeitskraftressourcen", "@sage/xtrem-master-data/pages__labor_resource____objectTypeSingular": "Arbeitskraftressource", "@sage/xtrem-master-data/pages__labor_resource____title": "Arbeitskraftressource", "@sage/xtrem-master-data/pages__labor_resource___id____title": "ID", "@sage/xtrem-master-data/pages__labor_resource__activeFrom____title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__labor_resource__activeTo____title": "Aktiv bis", "@sage/xtrem-master-data/pages__labor_resource__addCapability____title": "Hinzufügen", "@sage/xtrem-master-data/pages__labor_resource__addCostCategory____title": "Kostenkategorie hinzufügen", "@sage/xtrem-master-data/pages__labor_resource__blockCapabilities____title": "Kompetenzen", "@sage/xtrem-master-data/pages__labor_resource__blockDetails____title": "Einstellungen", "@sage/xtrem-master-data/pages__labor_resource__blockWeekly____title": "Details wöchentliche Schicht", "@sage/xtrem-master-data/pages__labor_resource__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__labor_resource__cancelSidePanel____title": "Abbrechen", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__capabilityLevel__name": "Kompetenzniveau", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateEndValid": "Enddatum", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateStartValid": "Startdatum", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__machine__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__service__name": "Dienstleistung", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__tool__name": "Werkzeug", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____title": "Kompetenzen", "@sage/xtrem-master-data/pages__labor_resource__capabilitiesSection____title": "Kompetenzen", "@sage/xtrem-master-data/pages__labor_resource__costBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__costSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__labor_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__labor_resource__efficiency____title": "Effizienz", "@sage/xtrem-master-data/pages__labor_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__labor_resource__id____title": "ID", "@sage/xtrem-master-data/pages__labor_resource__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__labor_resource__location____columns__title__locationType__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__labor_resource__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__labor_resource__name____title": "Name", "@sage/xtrem-master-data/pages__labor_resource__resourceCapacity____title": "Wöchentliche Kapazität", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Typ Kostenkategorie", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costCategory__name": "Kostenkategorie", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costUnit__name": "Kosteneinheit", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirekte Kosten", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__runCost": "Bearbeitung", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____title": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____lookupDialogTitle": "Ressourcengruppe auswählen", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__labor_resource__resourceImage____title": "Bild", "@sage/xtrem-master-data/pages__labor_resource__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__labor_resource__saveSidePanel____title": "Speichern", "@sage/xtrem-master-data/pages__labor_resource__section____title": "Allgemein", "@sage/xtrem-master-data/pages__labor_resource__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__labor_resource__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__capacity": "Kapazität", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__dailyShift": "Tägliche Schicht", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__day": "Tag", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift1": "Schicht 1", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift2": "Schicht 2", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift3": "Schicht 3", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift4": "Schicht 4", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift5": "Schicht 5", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____title": "Wochendetails", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____columns__title__formattedCapacity": "Kapazität", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____lookupDialogTitle": "Wöchentliche Schicht auswählen", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__postfix": "kg", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__title": "Verbrauchte Kapazität", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isInternalIcon__title": "Intern", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isSingleLot__title": "Einzelcharge", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__location__title": "Lagerplatz", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__sSingleItem__title": "Einzelartikel", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__license_plate_number____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>-IDs", "@sage/xtrem-master-data/pages__license_plate_number____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____title": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-master-data/pages__license_plate_number___id____title": "ID", "@sage/xtrem-master-data/pages__license_plate_number__consumedCapacity____title": "Verbrauchte Kapazität", "@sage/xtrem-master-data/pages__license_plate_number__container____columns__title__isInternal": "Intern", "@sage/xtrem-master-data/pages__license_plate_number__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__containerType____title": "Behältertyp", "@sage/xtrem-master-data/pages__license_plate_number__isInternal____title": "Intern", "@sage/xtrem-master-data/pages__license_plate_number__isSingleItem____title": "Einzelartikel", "@sage/xtrem-master-data/pages__license_plate_number__isSingleLot____title": "Einzelcharge", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title": "Name", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title__2": "ID", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title": "Name", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title__2": "ID", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__title__locationType__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__license_plate_number__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__license_plate_number__locationSite____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__locationType____title": "Lagerplatztyp", "@sage/xtrem-master-data/pages__license_plate_number__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__license_plate_number__mass_update__success": "<PERSON><PERSON><PERSON><PERSON>-IDs aktualisiert.", "@sage/xtrem-master-data/pages__license_plate_number__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__license_plate_number__owner____lookupDialogTitle": "Besitzer auswählen", "@sage/xtrem-master-data/pages__license_plate_number__owner____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation____title": "Massenerstellung Behälter-IDs", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__cancelLicensePlateNumbers____title": "Abbrechen", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleItem": "Einzelartikel", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleLot": "Einzelcharge", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__sequenceNumber__id": "Nummernkreis", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleItem____title": "Einzelartikel", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleLot____title": "Einzelcharge", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__id": "ID", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__name": "Name", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleItem": "Einzelartikel", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleLot": "Einzelcharge", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__locationType__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__name": "Lagerplatz", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__site__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____title": "<PERSON><PERSON><PERSON><PERSON>-IDs", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbersBlock____title": "<PERSON><PERSON><PERSON><PERSON>-IDs", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____columns__title__locationType__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationSite____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__numberToCreate____title": "<PERSON><PERSON> gene<PERSON>-IDs", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__saveLicensePlateNumbers____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__searchButton____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__dangerousGoodAllowed__title": "Gefahrgüter zulässig", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationType__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationZone__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__location____objectTypePlural": "Lagerplätze", "@sage/xtrem-master-data/pages__location____objectTypeSingular": "Lagerplatz", "@sage/xtrem-master-data/pages__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__location__dangerousGoodAllowed____title": "Gefahrgüter zulässig", "@sage/xtrem-master-data/pages__location__id____title": "ID", "@sage/xtrem-master-data/pages__location__locationType____columns__title__locationCategory": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location__locationType____lookupDialogTitle": "Lagerplatztyp auswählen", "@sage/xtrem-master-data/pages__location__locationType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location__locationZone____columns__title__zoneType": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location__locationZone____lookupDialogTitle": "Lagerplatzbereich auswählen", "@sage/xtrem-master-data/pages__location__locationZone____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location__name____title": "Name", "@sage/xtrem-master-data/pages__location__section____title": "Allgemein", "@sage/xtrem-master-data/pages__location__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__location__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__location_mass_creation____title": "Massenerstellung Lagerplätze", "@sage/xtrem-master-data/pages__location_mass_creation__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__location_mass_creation__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__location_mass_creation__createLocations____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationType__name__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationZone__name__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationType__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationZone__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__location_mass_creation__locationsBlock____title": "Lagerplätze", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__lastSequenceUsed": "Zuletzt verwendeter Nummernkreis", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocations": "Zuweisungen gesamt", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocationsRemaining": "Verbleibende Zuweisungen", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberOfCombinations": "Zuweisungen gesamt", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____lookupDialogTitle": "Nummernkreis Lagerplatz auswählen", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____title": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____columns__title__locationCategory": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____lookupDialogTitle": "Lagerplatztyp auswählen", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____columns__title__zoneType": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____lookupDialogTitle": "Lagerplatzbereich auswählen", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__location_mass_creation__requiredCombinations____title": "Erforderliche Kombinationen", "@sage/xtrem-master-data/pages__location_mass_creation__searchButton____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__location_mass_creation__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__location_mass_creation__site____placeholder": "Auswählen ...", "@sage/xtrem-master-data/pages__location_mass_creation__site____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__componentLength__title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__counterLength__title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberLocations__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberOfCombinations__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence____objectTypePlural": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/pages__location_sequence____objectTypeSingular": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/pages__location_sequence____title": "Nummernkreis Lagerplatz", "@sage/xtrem-master-data/pages__location_sequence___id____title": "ID", "@sage/xtrem-master-data/pages__location_sequence__addComponent____title": "Hinzufügen", "@sage/xtrem-master-data/pages__location_sequence__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__location_sequence__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__location_sequence__capital_letters_only": "Eine alphabetische Nummernkreiskomponente kann nur aus Großbuchstaben bestehen.", "@sage/xtrem-master-data/pages__location_sequence__componentLength____title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__endValue": "Endwert", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__startValue": "Startwert", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__components____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__location_sequence__components____title": "Komponenten", "@sage/xtrem-master-data/pages__location_sequence__componentsBlock____title": "Komponenten", "@sage/xtrem-master-data/pages__location_sequence__constant_invalid_length": "Prüfen Sie die Länge des Konstantenwerts.", "@sage/xtrem-master-data/pages__location_sequence__counterLength____title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__location_sequence__createAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__deleteAction____title": "Löschen", "@sage/xtrem-master-data/pages__location_sequence__digits_only": "Eine numerische Nummernkreiskomponente kann nur aus Zahlen bestehen.", "@sage/xtrem-master-data/pages__location_sequence__id____title": "ID", "@sage/xtrem-master-data/pages__location_sequence__invalid_range": "Der Startwert kann nicht nach dem Endwert liegen.", "@sage/xtrem-master-data/pages__location_sequence__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__location_sequence__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__location_sequence__name____title": "Name", "@sage/xtrem-master-data/pages__location_sequence__numberLocations____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__numberOfCombinations____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__location_sequence__saveLocationSequence____title": "Speichern", "@sage/xtrem-master-data/pages__location_type____navigationPanel__listItem__locationCategory__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__2": "Intern", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__3": "Rampe", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__4": "Kunde", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__5": "Fremdbearbeitung", "@sage/xtrem-master-data/pages__location_type____objectTypePlural": "Lagerplatztypen", "@sage/xtrem-master-data/pages__location_type____objectTypeSingular": "Lagerplatztyp", "@sage/xtrem-master-data/pages__location_type____title": "Lagerplatztyp", "@sage/xtrem-master-data/pages__location_type__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__location_type__id____title": "ID", "@sage/xtrem-master-data/pages__location_type__locationCategory____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__location_type__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__location_type__name____title": "Name", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__zoneType__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__2": "Tief<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__3": "Empf<PERSON>lich", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__4": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__5": "Eingeschränkt", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__6": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__7": "Chemikalie", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__8": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____objectTypePlural": "Lagerplatzbereiche", "@sage/xtrem-master-data/pages__location_zone____objectTypeSingular": "Lagerplatzbereich", "@sage/xtrem-master-data/pages__location_zone____title": "Lagerplatzbereich", "@sage/xtrem-master-data/pages__location_zone__id____title": "ID", "@sage/xtrem-master-data/pages__location_zone__name____title": "Name", "@sage/xtrem-master-data/pages__location_zone__section____title": "Allgemein", "@sage/xtrem-master-data/pages__location_zone__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__location_zone__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__location_zone__zoneType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeFrom__title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeTo__title": "Aktiv bis", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__title": "Effizienz", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__location__title": "Lagerplatz", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__minCapabilityLevel__title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__model__title": "<PERSON><PERSON><PERSON>modell", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__resourceGroup__title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__serialNumber__title": "Serienn<PERSON>mer Ma<PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__weeklyShift__title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__machine_resource____objectTypePlural": "Maschinenressourcen", "@sage/xtrem-master-data/pages__machine_resource____objectTypeSingular": "Maschinenressource", "@sage/xtrem-master-data/pages__machine_resource____title": "Maschinenressource", "@sage/xtrem-master-data/pages__machine_resource___id____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__activeFrom____title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__machine_resource__activeTo____title": "Aktiv bis", "@sage/xtrem-master-data/pages__machine_resource__addCostCategory____title": "Kostenkategorie hinzufügen", "@sage/xtrem-master-data/pages__machine_resource__blockContract____title": "Vertrag", "@sage/xtrem-master-data/pages__machine_resource__blockDetails____title": "Einstellungen", "@sage/xtrem-master-data/pages__machine_resource__blockWeekly____title": "Details wöchentliche Schicht", "@sage/xtrem-master-data/pages__machine_resource__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__machine_resource__cancelSidePanel____title": "Abbrechen", "@sage/xtrem-master-data/pages__machine_resource__contractId____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__contractName____title": "Name", "@sage/xtrem-master-data/pages__machine_resource__contractSection____title": "Vertrag", "@sage/xtrem-master-data/pages__machine_resource__costBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__costSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__machine_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__machine_resource__efficiency____title": "Effizienz", "@sage/xtrem-master-data/pages__machine_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__machine_resource__id____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__machine_resource__location____columns__title__locationType__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__machine_resource__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____columns__title__description": "Bezeichnung", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____lookupDialogTitle": "Kompetenzniveau auswählen", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____title": "Kompetenzniveau", "@sage/xtrem-master-data/pages__machine_resource__model____title": "<PERSON><PERSON><PERSON>modell", "@sage/xtrem-master-data/pages__machine_resource__name____title": "Name", "@sage/xtrem-master-data/pages__machine_resource__resourceCapacity____title": "Wöchentliche Kapazität", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Typ Kostenkategorie", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costCategory__name": "Kostenkategorie", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costUnit__name": "Kosteneinheit", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirekte Kosten", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__runCost": "Bearbeitung", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____title": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____lookupDialogTitle": "Ressourcengruppe auswählen", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__machine_resource__resourceImage____title": "Bild", "@sage/xtrem-master-data/pages__machine_resource__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__machine_resource__section____title": "Allgemein", "@sage/xtrem-master-data/pages__machine_resource__serialNumber____title": "Serienn<PERSON>mer Ma<PERSON>", "@sage/xtrem-master-data/pages__machine_resource__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__machine_resource__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__country": "Land", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__taxIdNumber": "Steuer-ID", "@sage/xtrem-master-data/pages__machine_resource__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__machine_resource__weeklyDetails____columns__title__dailyShift": "Tägliche Schicht", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____columns__title__formattedCapacity": "Kapazität", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____lookupDialogTitle": "Wöchentliche Schicht auswählen", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__master_data__cancel": "Abbrechen", "@sage/xtrem-master-data/pages__master_data__confirm": "Bestätigen", "@sage/xtrem-master-data/pages__master_data__update": "Aktualisieren", "@sage/xtrem-master-data/pages__master_data__warning-dialog-content": "<PERSON><PERSON> sind dabei, den Nummernkreiswert zu aktualisieren.", "@sage/xtrem-master-data/pages__multiple__location__creation__success_multi": "{{num}} Lagerplätze erstellt", "@sage/xtrem-master-data/pages__multiple_location_creation__success": "{{num}} Lagerplatz erstellt", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__listItem__line3__title": "Typ Geschäftsentität", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__payment_term____objectTypePlural": "Zahlungsbedingungen", "@sage/xtrem-master-data/pages__payment_term____objectTypeSingular": "Zahlungsbedingung", "@sage/xtrem-master-data/pages__payment_term____title": "Zahlungsbedingung", "@sage/xtrem-master-data/pages__payment_term___id____title": "ID", "@sage/xtrem-master-data/pages__payment_term__amount": "Betrag", "@sage/xtrem-master-data/pages__payment_term__blockDiscount____title": "Skonto", "@sage/xtrem-master-data/pages__payment_term__blockDue____title": "Fälligkeitsdatum", "@sage/xtrem-master-data/pages__payment_term__blockPenalty____title": "Mahngebühr", "@sage/xtrem-master-data/pages__payment_term__businessEntityType____title": "Typ Geschäftsentität", "@sage/xtrem-master-data/pages__payment_term__days____title": "<PERSON><PERSON><PERSON>e", "@sage/xtrem-master-data/pages__payment_term__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__payment_term__discountDate____title": "Tag", "@sage/xtrem-master-data/pages__payment_term__discountFrom____title": "Ab", "@sage/xtrem-master-data/pages__payment_term__discountType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term__dueDateType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term__id____title": "ID", "@sage/xtrem-master-data/pages__payment_term__name____title": "Name", "@sage/xtrem-master-data/pages__payment_term__penaltyType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term__percentage": "Prozentsatz", "@sage/xtrem-master-data/pages__payment_term__section____title": "Allgemein", "@sage/xtrem-master-data/pages__reason_code____objectTypePlural": "Grundcodes", "@sage/xtrem-master-data/pages__reason_code____objectTypeSingular": "Grundcode", "@sage/xtrem-master-data/pages__reason_code____title": "Grundcode", "@sage/xtrem-master-data/pages__reason_code__id____title": "ID", "@sage/xtrem-master-data/pages__reason_code__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__reason_code__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__reason_code__name____title": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog____title": "Genehmigungsanforderung", "@sage/xtrem-master-data/pages__request_approval_dialog__approverSelectionBlock____title": "<PERSON><PERSON><PERSON> au<PERSON>w<PERSON>hl<PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__default_approver": "Standard", "@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_not_sent": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval": "", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____helperText": "Eine Genehmigungsanforderungs-E-Mail wird an diese Adresse gesendet.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____title": "An", "@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email": "Ungültige E-Mail-Adresse: {{value}}", "@sage/xtrem-master-data/pages__request_approval_dialog__requestApprovalSection____title": "Genehmigungsanforderung", "@sage/xtrem-master-data/pages__request_approval_dialog__selectApprover____title": "<PERSON><PERSON><PERSON> au<PERSON>w<PERSON>hl<PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__email": "E-Mail", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__firstName": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__lastName": "Nachname", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____lookupDialogTitle": "Ausgewählten Benutzer auswählen", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____title": "Ausgewählter Benutzer", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_confirm_title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content": "", "@sage/xtrem-master-data/pages__request_approval_dialog__sendApprovalRequestButton____title": "Senden", "@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__email": "E-Mail", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__firstName": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__lastName": "Nachname", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__type": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__users____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes": "{{hours}} Stunden {{minutes}} Minuten", "@sage/xtrem-master-data/pages__resource_group_transfer____title": "Übertragung Ressourcengruppe", "@sage/xtrem-master-data/pages__resource_group_transfer__block____title": "ID", "@sage/xtrem-master-data/pages__resource_group_transfer__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__resource_group_transfer__confirm____title": "Speichern", "@sage/xtrem-master-data/pages__resource_group_transfer__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____columns__title__weeklyShift__id": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____lookupDialogTitle": "Ressource auswählen", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____title": "Ressource", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____columns__title__weeklyShift__id": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____lookupDialogTitle": "Ressourcengruppe auswählen", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__resource_group_transfer__type____title": "Ressourcengruppentyp", "@sage/xtrem-master-data/pages__select_sold_to_contact_button_text": "Kontakt Auftraggeber auswählen", "@sage/xtrem-master-data/pages__send_button_text": "Senden", "@sage/xtrem-master-data/pages__send_email_panel____title": "Verkaufsangebot senden", "@sage/xtrem-master-data/pages__send_email_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____helperText": "Eine E-Mail wird an diese Adresse gesendet.", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____title": "E-Mail", "@sage/xtrem-master-data/pages__send_email_panel__emailFirstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__send_email_panel__emailLastName____title": "Nachname", "@sage/xtrem-master-data/pages__send_email_panel__emailTitles____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__send_email_panel__selectSoldToContact____title": "Kontakt Auftraggeber auswählen", "@sage/xtrem-master-data/pages__send_email_panel__sendEmailBlock____title": "An", "@sage/xtrem-master-data/pages__send_email_panel__sendSalesOrderButton____title": "Auftrag senden", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_4__title": "Definitionsebene", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_5__title": "Rücksetzfrequenz", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line3__title": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line4__title": "Definitionsebene", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line5__title": "Rücksetzfrequenz", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line6__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line7__title": "Chronologische Prüfung", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__sequence_number____objectTypePlural": "Nummernkreise", "@sage/xtrem-master-data/pages__sequence_number____objectTypeSingular": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number____title": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number__addComponent____title": "Komponente hinzufügen", "@sage/xtrem-master-data/pages__sequence_number__chronologicalControl____title": "Chronologische Prüfung", "@sage/xtrem-master-data/pages__sequence_number__componentLength____title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__constant": "<PERSON><PERSON>ante", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title__2": "Komponente", "@sage/xtrem-master-data/pages__sequence_number__components____title": "Komponenten", "@sage/xtrem-master-data/pages__sequence_number__componentsBlock____title": "Komponenten", "@sage/xtrem-master-data/pages__sequence_number__counterLength____title": "Länge Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number__createAction____title": "<PERSON>eu", "@sage/xtrem-master-data/pages__sequence_number__createValue____title": "Startwert erstellen", "@sage/xtrem-master-data/pages__sequence_number__definitionLevel____title": "Definitionsebene", "@sage/xtrem-master-data/pages__sequence_number__delete____title": "Löschen", "@sage/xtrem-master-data/pages__sequence_number__id____title": "ID", "@sage/xtrem-master-data/pages__sequence_number__isChronological____title": "Chronologische Prüfung", "@sage/xtrem-master-data/pages__sequence_number__isClearedByReset____title": "Zurücksetzen mit Tenant erzwingen", "@sage/xtrem-master-data/pages__sequence_number__isUsed____title": "Verwendet", "@sage/xtrem-master-data/pages__sequence_number__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__sequence_number__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__sequence_number__name____title": "Name", "@sage/xtrem-master-data/pages__sequence_number__propertiesBlock____title": "Details Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number__resetBlock____title": "Details Zurücksetzen", "@sage/xtrem-master-data/pages__sequence_number__resetToZero____title": "Z<PERSON>ücksetzen", "@sage/xtrem-master-data/pages__sequence_number__rtzLevel____title": "Rücksetzfrequenz", "@sage/xtrem-master-data/pages__sequence_number__save____title": "Speichern", "@sage/xtrem-master-data/pages__sequence_number__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__sequence_number__sequence____title": "Nummernkreistyp", "@sage/xtrem-master-data/pages__sequence_number__sequenceNumberType____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__type____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__updateValue____title": "Wert aktualisieren", "@sage/xtrem-master-data/pages__sequence_number_assignment_filter": "Filter", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup____title": "Zuweisung Nummernkreise", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__addEditAssignmentLine____title": "Hinzufügen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____title": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__isDefaultAssignment____title": "Standardwerte einschließen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____title": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__lineBlock____title": "Kriterien", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__resultsBlock____title": "Ergebnisse", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__company__name": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isDefaultAssignment": "Standard", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__legislation__name": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__name": "Dokument", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__title": "Dokument", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__sequenceNumber__name": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____title": "Ergebnisse", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____placeholder": "Standort auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel____title": "Bereich Zuweisung Nummernkreise", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__2": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__3": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__title__legislation__id": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____placeholder": "Unternehmen auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____title": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__edit____title": "Zuweisung Nummernkreise bearbeiten", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isAssignOnPosting____title": "Zuweisung bei Buchung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isUsed____title": "Verwendet", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____placeholder": "Rechtsordnung auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____title": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__new____title": "Neue Zuweisung Nummernkreise", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__save____title": "Speichern", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__2": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__3": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__id": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__legislation__id": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__name": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____lookupDialogTitle": "Nummernkreis auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____title": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__columns__sequenceNumberAssignmentModule__id__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__node": "Node", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeFactory__name": "Dokument", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__sequenceNumberAssignmentModule__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__setupId": "ID Einstellungen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____lookupDialogTitle": "Dokumenttyp auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____placeholder": "Nummernkreis auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____title": "Dokumenttyp", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title___id": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__id": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__name": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__2": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__3": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__4": "Rechtsordnung", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__title__legalCompany__id": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____placeholder": "Standort auswählen", "@sage/xtrem-master-data/pages__sequence_number_dialog____title": "Nummernkreiswert aktualisieren", "@sage/xtrem-master-data/pages__sequence_number_dialog__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__sequence_number_dialog__definitionLevel____title": "Definitionsebene", "@sage/xtrem-master-data/pages__sequence_number_dialog__length____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_dialog__rtzLevel____title": "Rücksetzfrequenz", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumber____title": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title___updateStamp": "Zuletzt aktualisiert am", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__id": "Unternehmens-ID", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__name": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__newNextValue": "Neuer nächster Wert", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__period": "Zeitraum", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__sequenceValue": "Nächster Wert", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__id": "Standort-ID", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_dialog__update____title": "Aktualisieren", "@sage/xtrem-master-data/pages__sequence_number_dialog_records_updated": "Datensatz aktualisiert.", "@sage/xtrem-master-data/pages__sequence_number_value____title": "Nummernkreiswert erstellen", "@sage/xtrem-master-data/pages__sequence_number_value__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__sequence_number_value__confirm_action_dialog_content": "<PERSON><PERSON> sind dabei, den Nummernkreiswert zu erstellen.", "@sage/xtrem-master-data/pages__sequence_number_value__confirm-continue": "Fortfahren", "@sage/xtrem-master-data/pages__sequence_number_value__definitionLevel____title": "Definitionsebene", "@sage/xtrem-master-data/pages__sequence_number_value__id____title": "Nummernkreis", "@sage/xtrem-master-data/pages__sequence_number_value__minimumLength____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__rtzLevel____title": "Rücksetzfrequenz", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title___updateStamp": "Zuletzt aktualisiert am", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__id": "Unternehmens-ID", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__name": "Unternehmen", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__newNextValue": "Neuer nächster Wert", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__periodDate": "<PERSON><PERSON>um", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__sequenceValue": "Nächster Wert", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__id": "Standort-ID", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value_confirm_action_dialog_title": "Bestätigen", "@sage/xtrem-master-data/pages__sequence-number_value_add_new____title": "Nummernkreiswert erstellen", "@sage/xtrem-master-data/pages__sequence-number_value_create____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence-number_value_edit____title": "Wert aktualisieren", "@sage/xtrem-master-data/pages__sequence-number_value_update____title": "Aktualisieren", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftEnd__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftStart__title": "Begin<PERSON>", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__shift_detail____objectTypePlural": "Schichtdetails", "@sage/xtrem-master-data/pages__shift_detail____objectTypeSingular": "Schichtdetails", "@sage/xtrem-master-data/pages__shift_detail____title": "Schichtdetails", "@sage/xtrem-master-data/pages__shift_detail__formattedDuration____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__shift_detail__id____title": "ID", "@sage/xtrem-master-data/pages__shift_detail__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__shift_detail__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__shift_detail__name____title": "Name", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____title": "Begin<PERSON>", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line_4__title": "Unternehmen", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line10__title": "Buchhaltungsstandort", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__sequenceNumber__title": "Nummernkreiswert", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__taxId__title": "Steuer-ID", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__site____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addressAndContactBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__site__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__site__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__3": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__4": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__site__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__already_exists_with_same_id": "Diese ID ist bereits einem Standort zugewiesen.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_name": "Dieser Name ist bereits einem Standort zugewiesen.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber": "Diese Steueridentifikationsnummer ist bereits einem Standort zugewiesen.", "@sage/xtrem-master-data/pages__site__businessEntity____columns__title__isNaturalPerson": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__contacts____addButtonText": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__site__contacts____columns__title__isPrimary": "Hauptkontakt", "@sage/xtrem-master-data/pages__site__contacts____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__2": "Als Hauptkontakt definieren", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__site__contacts____headerLabel__title": "Ist aktiv", "@sage/xtrem-master-data/pages__site__contactSection____title": "Kontakte", "@sage/xtrem-master-data/pages__site__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__site__country____columns__title__regionLabel": "Bezeichnung Region", "@sage/xtrem-master-data/pages__site__country____columns__title__zipLabel": "Bezeichnung <PERSON>itzahl", "@sage/xtrem-master-data/pages__site__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__site__createFromBusinessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__site__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__site__defaultLocation____lookupDialogTitle": "Standardlagerplatz auswählen", "@sage/xtrem-master-data/pages__site__defaultLocation____title": "Standardlagerplatz", "@sage/xtrem-master-data/pages__site__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__site__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title": "Kontakte", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__2": "Hauptadresse", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Primär für eine andere Entiät", "@sage/xtrem-master-data/pages__site__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__site__financialSite____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createStamp": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updateStamp": "<PERSON>ktual<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__groupRoleSites____title": "Autorisierungsgruppen", "@sage/xtrem-master-data/pages__site__hierarchyChartContent____title": "Organisation", "@sage/xtrem-master-data/pages__site__imageBlock____title": "Bild", "@sage/xtrem-master-data/pages__site__isFinance____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__site__isInventory____title": "Bestand", "@sage/xtrem-master-data/pages__site__isLocationManaged____title": "Lagerplatzverwaltung", "@sage/xtrem-master-data/pages__site__isManufacturing____title": "Fertigung", "@sage/xtrem-master-data/pages__site__isPurchase____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__isSales____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__isSequenceNumberIdUsed____title": "Nummernkreis verwendet", "@sage/xtrem-master-data/pages__site__legalCompany____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__site__legalCompany____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-master-data/pages__site__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__site__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__site__managementSection____title": "Verwaltung", "@sage/xtrem-master-data/pages__site__save____title": "Speichern", "@sage/xtrem-master-data/pages__site__sequenceNumberId____title": "Nummernkreiswert", "@sage/xtrem-master-data/pages__site__siteGroupBlock____title": "Standortgruppen", "@sage/xtrem-master-data/pages__site__siteGroups____columns__title__isLegalCompany": "Unternehmen", "@sage/xtrem-master-data/pages__site__siteGroups____title": "Standortgruppen", "@sage/xtrem-master-data/pages__site__siteGroupSection____title": "Standortgruppen", "@sage/xtrem-master-data/pages__site__timeZone____title": "Zeitzone", "@sage/xtrem-master-data/pages__site__userGroupBlock____title": "Autorisierungsgruppen", "@sage/xtrem-master-data/pages__site__userGroupSection____title": "Benutzergruppen", "@sage/xtrem-master-data/pages__site__website____title": "Webseite", "@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity": "Die Steuer-ID ist erford<PERSON>lich, wenn keine Geschäftsentität ausgewählt ist.", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line3__title": "Standard", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line6__title": "Branche", "@sage/xtrem-master-data/pages__standard____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__standard____objectTypePlural": "Standards", "@sage/xtrem-master-data/pages__standard____objectTypeSingular": "Standard", "@sage/xtrem-master-data/pages__standard____title": "Standards", "@sage/xtrem-master-data/pages__standard__code____title": "Code", "@sage/xtrem-master-data/pages__standard__createStandard____title": "<PERSON>eu", "@sage/xtrem-master-data/pages__standard__deleteStandard____title": "Löschen", "@sage/xtrem-master-data/pages__standard__id____title": "ID", "@sage/xtrem-master-data/pages__standard__idBlock____title": "ID", "@sage/xtrem-master-data/pages__standard__industrySector____title": "Branche", "@sage/xtrem-master-data/pages__standard__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__standard__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__standard__name____title": "Name", "@sage/xtrem-master-data/pages__standard__saveStandard____title": "Speichern", "@sage/xtrem-master-data/pages__standard__sdo____title": "Standard", "@sage/xtrem-master-data/pages__standard__version____title": "Version", "@sage/xtrem-master-data/pages__stock_journal_inquiry": "Abfrage Bestandsjournal", "@sage/xtrem-master-data/pages__stock_posting_error": "Bestandsbuchungsfehler", "@sage/xtrem-master-data/pages__supplier____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__isActive__title": "Aktiv", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line10__title": "Kundenkategorie", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line7__title": "Mindestbestellwert", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line8__title": "Zahlungsbedingung", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__id": "ID", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__name": "Name", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__title": "Name", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__supplier____objectTypePlural": "Lieferanten", "@sage/xtrem-master-data/pages__supplier____objectTypeSingular": "Lieferant", "@sage/xtrem-master-data/pages__supplier____title": "Lieferant", "@sage/xtrem-master-data/pages__supplier___id____title": "ID", "@sage/xtrem-master-data/pages__supplier__addCertificate____title": "Hinzufügen", "@sage/xtrem-master-data/pages__supplier__addItem____title": "Hinzufügen", "@sage/xtrem-master-data/pages__supplier__addPriceLine____title": "Hinzufügen", "@sage/xtrem-master-data/pages__supplier__addressAndContactBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__concatenatedAddressWithoutName": "<PERSON><PERSON><PERSON> ohne <PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__isPrimary": "Hauptadresse", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title": "<PERSON><PERSON><PERSON> bear<PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__2": "Als Hauptadresse definieren", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__3": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__4": "Adresse und Kontakte löschen", "@sage/xtrem-master-data/pages__supplier__addresses____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id": "Diese ID ist bereits einem Lieferanten zugewiesen.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name": "Dieser Name ist bereits einem Lieferanten zugewiesen.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber": "Diese Steueridentifikationsnummer ist bereits einem Lieferanten zugewiesen.", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__concatenatedAddress": "Hauptadresse Rechnungssteller", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__billByAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__billByAddress____title": "Hauptadresse Rechnungssteller", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine1": "Zeile 1", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine2": "Zeile 2", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__country__name": "Land", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON> (USA)", "@sage/xtrem-master-data/pages__supplier__billBySupplier____title": "Rechnungssteller", "@sage/xtrem-master-data/pages__supplier__businessEntity____columns__title__isNaturalPerson": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__category____columns__title__sequenceNumber__name": "Nummernkreis", "@sage/xtrem-master-data/pages__supplier__category____lookupDialogTitle": "Kategorie auswählen", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__certificationBody": "Zertifizierungsstelle", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfCertification": "Zertifizierungsdatum", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfOriginalCertification": "Ursprüngliches Zertifizierungsdatum", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__id": "Zertifikatreferenz", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__standard__id": "Standard", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__validUntil": "Gültig bis", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__supplier__certificates____title": "Zertifikate", "@sage/xtrem-master-data/pages__supplier__certificateSection____title": "Zertifikate", "@sage/xtrem-master-data/pages__supplier__commercialBlock____title": "G<PERSON><PERSON><PERSON><PERSON>lich", "@sage/xtrem-master-data/pages__supplier__commercialSection____title": "G<PERSON><PERSON><PERSON><PERSON>lich", "@sage/xtrem-master-data/pages__supplier__contacts____addButtonText": "Kontakt hinzufügen", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__isPrimary": "Hauptkontakt", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__2": "Als Hauptkontakt definieren", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__3": "Löschen", "@sage/xtrem-master-data/pages__supplier__contacts____headerLabel__title": "Ist aktiv", "@sage/xtrem-master-data/pages__supplier__contactSection____title": "Kontakte", "@sage/xtrem-master-data/pages__supplier__country____columns__title__id": "ISO 3166-1 Alpha-2", "@sage/xtrem-master-data/pages__supplier__country____lookupDialogTitle": "Land auswählen", "@sage/xtrem-master-data/pages__supplier__createFromBusinessEntity____title": "Aus Geschäftsentität erstellen", "@sage/xtrem-master-data/pages__supplier__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-master-data/pages__supplier__deliveryMode____lookupDialogTitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__deliveryMode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__display_primary_address": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title": "Kontakte", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__2": "Hauptadresse", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Primär für eine andere Entiät", "@sage/xtrem-master-data/pages__supplier__euVatNumber____title": "EU-USt-Id-Nr.", "@sage/xtrem-master-data/pages__supplier__financialBlock____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__supplier__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-master-data/pages__supplier__imageBlock____title": "Bild", "@sage/xtrem-master-data/pages__supplier__incoterm____lookupDialogTitle": "Incoterms-Regel auswählen", "@sage/xtrem-master-data/pages__supplier__incoterm____title": "Incoterms®-Regel", "@sage/xtrem-master-data/pages__supplier__industrySpecificBlock____title": "Zertifikate", "@sage/xtrem-master-data/pages__supplier__internalNote____title": "Interne Notizen", "@sage/xtrem-master-data/pages__supplier__isNaturalPerson____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__itemBlock____title": "Artikel", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isActive": "Aktiv", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isDefaultItemSupplier": "Standardlieferant", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__supplier__items____columns__title__minimumPurchaseQuantity": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseLeadTime": "Einkaufszeit", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__id": "Einkaufseinheit", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__name": "Einkaufseinheit", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemCode": "Code Artikel-Lieferant", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemName": "Name <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title__2": "Löschen", "@sage/xtrem-master-data/pages__supplier__items____title": "Artikel", "@sage/xtrem-master-data/pages__supplier__itemSection____title": "Artikel", "@sage/xtrem-master-data/pages__supplier__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__supplier__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__supplier__minimumOrderAmount____title": "Mindestbestellwert", "@sage/xtrem-master-data/pages__supplier__noteBlock____title": "Notizen", "@sage/xtrem-master-data/pages__supplier__noteSection____title": "Notizen", "@sage/xtrem-master-data/pages__supplier__parent____lookupDialogTitle": "Übergeordneten Lieferanten auswählen", "@sage/xtrem-master-data/pages__supplier__parent____title": "Übergeordnet", "@sage/xtrem-master-data/pages__supplier__paymentMethod____title": "Zahlungsart", "@sage/xtrem-master-data/pages__supplier__paymentTerm____lookupDialogTitle": "Zahlungsbedingung auswählen", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__concatenatedAddress": "Hauptadresse Zahlungsempfänger", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__payToAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__payToAddress____title": "Hauptadresse Zahlungsempfänger", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine1": "Zeile 1", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine2": "Zeile 2", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__country__name": "Land", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON> (USA)", "@sage/xtrem-master-data/pages__supplier__payToSupplier____title": "Rechnungssteller", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title": "Name", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__2": "ISO 3166-1 Alpha-2-Code", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__3": "ID", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__4": "Bezeichnung Region", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__5": "Bezeichnung <PERSON>itzahl", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__title__country__name": "Land", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__concatenatedAddress": "Hauptretouradresse", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__returnToAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__returnToAddress____title": "Hauptretouradresse", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine1": "Zeile 1", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine2": "Zeile 2", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__country__name": "Land", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__locationPhoneNumber": "Telefonnummer", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON> (USA)", "@sage/xtrem-master-data/pages__supplier__save____title": "Speichern", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__currency__id": "Währung", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidFrom": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidTo": "Gültig bis", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__fromQuantity": "<PERSON><PERSON> von", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__description": "Artikelbezeichnung", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__id": "Artikel-ID", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__toQuantity": "Menge bis", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____title": "Lieferantenpreise", "@sage/xtrem-master-data/pages__supplier__supplierPricesBlock____title": "Lieferantenpreise", "@sage/xtrem-master-data/pages__supplier__website____title": "Webseite", "@sage/xtrem-master-data/pages__supplier_certificate_panel____title": "Lieferantenzertifikat", "@sage/xtrem-master-data/pages__supplier_certificate_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__supplier_certificate_panel__certificationBody____title": "Zertifizierungsstelle", "@sage/xtrem-master-data/pages__supplier_certificate_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfCertification____title": "Zertifizierungsdatum", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfOriginalCertification____title": "Ursprüngliches Zertifizierungsdatum", "@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title": "Lieferantenzertifikat bearbeiten", "@sage/xtrem-master-data/pages__supplier_certificate_panel__id____title": "Zertifikatreferenz", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainBlock____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title": "Neues Lieferantenzertifikat", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__code": "Code", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__industrySector": "Branche", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__sdo": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__version": "Version", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____lookupDialogTitle": "Standard auswählen", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____title": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__validUntil____title": "Gültigkeitsende", "@sage/xtrem-master-data/pages__supplier_item_panel____title": "Liefer<PERSON><PERSON><PERSON><PERSON> bearbeiten", "@sage/xtrem-master-data/pages__supplier_item_panel___id____title": "ID", "@sage/xtrem-master-data/pages__supplier_item_panel__cancel____title": "Abbrechen", "@sage/xtrem-master-data/pages__supplier_item_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_item_panel__isDefaultItemSupplier____title": "Standardlieferant", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__supplier_item_panel__item____title": "Artikel", "@sage/xtrem-master-data/pages__supplier_item_panel__mainBlock____title": "Artikel-Lieferant", "@sage/xtrem-master-data/pages__supplier_item_panel__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__supplier_item_panel__minimumPurchaseQuantity____title": "Mindesteinkaufsmenge", "@sage/xtrem-master-data/pages__supplier_item_panel__new____title": "Neuer Artikel-Lieferant", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseLeadTime____title": "Einkaufszeit", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____lookupDialogTitle": "Einheit auswählen", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____title": "Einheit", "@sage/xtrem-master-data/pages__supplier_item_panel__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemCode____title": "ID Artikel-Lieferant", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemName____title": "Name <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__team____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__team____objectTypePlural": "Teams", "@sage/xtrem-master-data/pages__team____objectTypeSingular": "Team", "@sage/xtrem-master-data/pages__team____title": "Team", "@sage/xtrem-master-data/pages__team__createAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__team__deleteAction____title": "Löschen", "@sage/xtrem-master-data/pages__team__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__team__id____title": "ID", "@sage/xtrem-master-data/pages__team__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__team__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__team__name____title": "Name", "@sage/xtrem-master-data/pages__team__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__team__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__team__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeFrom__title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeTo__title": "Aktiv bis", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__title": "Effizienz", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__location__title": "Lagerplatz", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__resourceGroup__title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__weeklyShift__title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-master-data/pages__tool_resource____objectTypePlural": "Werkzeugressourcen", "@sage/xtrem-master-data/pages__tool_resource____objectTypeSingular": "Werkzeugressource", "@sage/xtrem-master-data/pages__tool_resource____title": "Werkzeugressource", "@sage/xtrem-master-data/pages__tool_resource___id____title": "ID", "@sage/xtrem-master-data/pages__tool_resource__activeFrom____title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__tool_resource__activeTo____title": "Aktiv bis", "@sage/xtrem-master-data/pages__tool_resource__addCostCategory____title": "Kostenkategorie hinzufügen", "@sage/xtrem-master-data/pages__tool_resource__blockDetails____title": "Einstellungen", "@sage/xtrem-master-data/pages__tool_resource__blockWeekly____title": "Details wöchentliche Schicht", "@sage/xtrem-master-data/pages__tool_resource__cancelAction____title": "Abbrechen", "@sage/xtrem-master-data/pages__tool_resource__cancelSidePanel____title": "Abbrechen", "@sage/xtrem-master-data/pages__tool_resource__consumptionMode____title": "Verbrauchsart", "@sage/xtrem-master-data/pages__tool_resource__costBlock____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__costSection____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__tool_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__tool_resource__efficiency____title": "Effizienz", "@sage/xtrem-master-data/pages__tool_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__tool_resource__hoursTracked____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__id____title": "ID", "@sage/xtrem-master-data/pages__tool_resource__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__tool_resource__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-master-data/pages__tool_resource__item____title": "Artikel", "@sage/xtrem-master-data/pages__tool_resource__location____columns__title__locationType__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__location____lookupDialogTitle": "Lagerplatz auswählen", "@sage/xtrem-master-data/pages__tool_resource__location____title": "Lagerplatz", "@sage/xtrem-master-data/pages__tool_resource__name____title": "Name", "@sage/xtrem-master-data/pages__tool_resource__quantity____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCapacity____title": "Wöchentliche Kapazität", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Typ Kostenkategorie", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costCategory__name": "Kostenkategorie", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costUnit__name": "Kosteneinheit", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirekte Kosten", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__runCost": "Bearbeitung", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____title": "Ressourcenkostenkategorien", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____lookupDialogTitle": "Ressourcengruppe auswählen", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____title": "Ressourcengruppe", "@sage/xtrem-master-data/pages__tool_resource__resourceImage____title": "Bild", "@sage/xtrem-master-data/pages__tool_resource__saveAction____title": "Speichern", "@sage/xtrem-master-data/pages__tool_resource__saveSidePanel____title": "Speichern", "@sage/xtrem-master-data/pages__tool_resource__section____title": "Allgemein", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__title": "Symbol", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__id": "Unternehmen", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-master-data/pages__tool_resource__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-master-data/pages__tool_resource__toolDetails____title": "Verwaltung", "@sage/xtrem-master-data/pages__tool_resource__unitProduced____title": "Gefertigte Einheiten", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__capacity": "Kapazität", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__dailyShift": "Tägliche Schicht", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__day": "Tag", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift1": "Schicht 1", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift2": "Schicht 2", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift3": "Schicht 3", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift4": "Schicht 4", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift5": "Schicht 5", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____title": "Wochendetails", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____columns__title__formattedCapacity": "Kapazität", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____lookupDialogTitle": "Wöchentliche Schicht auswählen", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line_4__title": "Dezimalstellen", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line3__title": "Einheitstyp", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line4__title": "Dezimalstellen", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypePlural": "Maßeinheiten", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypeSingular": "Maßeinheit", "@sage/xtrem-master-data/pages__unit_of_measure____title": "Maßeinheit", "@sage/xtrem-master-data/pages__unit_of_measure__addConversion____title": "Hinzufügen", "@sage/xtrem-master-data/pages__unit_of_measure__conversionBlock____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title": "Name", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title__2": "ID", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__item__name__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__lookupDialogTitle__item__name": "Artikel auswählen", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__2": "Zieleinheit", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__3": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__4": "Umkehrfaktor", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__coefficient": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__customer__businessEntity__name": "Kunde", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__fromUnit__name": "Basiseinheit", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__isStandard": "Standard", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__item__name": "Artikel", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__type": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____dropdownActions__title": "Löschen", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__decimalDigits____title": "Dezimalstellen", "@sage/xtrem-master-data/pages__unit_of_measure__description____title": "Bezeichnung", "@sage/xtrem-master-data/pages__unit_of_measure__generalSection____title": "Allgemein", "@sage/xtrem-master-data/pages__unit_of_measure__id____title": "ID", "@sage/xtrem-master-data/pages__unit_of_measure__isActive____title": "Aktiv", "@sage/xtrem-master-data/pages__unit_of_measure__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__unit_of_measure__name____title": "Name", "@sage/xtrem-master-data/pages__unit_of_measure__save____title": "Speichern", "@sage/xtrem-master-data/pages__unit_of_measure__symbol____title": "Symbol", "@sage/xtrem-master-data/pages__unit_of_measure__type____title": "Einheitstyp", "@sage/xtrem-master-data/pages__utils__notification__custom_validation_error": "Freigabefehler:\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__formattedCapacity__title": "Kapazität", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__isFullWeek__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-master-data/pages__weekly_shift____objectTypePlural": "Wöchentliche Schichten", "@sage/xtrem-master-data/pages__weekly_shift____objectTypeSingular": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__weekly_shift____title": "Wöchentliche Schicht", "@sage/xtrem-master-data/pages__weekly_shift___id____title": "ID", "@sage/xtrem-master-data/pages__weekly_shift__detailsBlock____title": "Details", "@sage/xtrem-master-data/pages__weekly_shift__formattedCapacity____title": "Kapazität", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____lookupDialogTitle": "Schicht Freitag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____title": "Schicht Freitag", "@sage/xtrem-master-data/pages__weekly_shift__id____title": "ID", "@sage/xtrem-master-data/pages__weekly_shift__isFullWeek____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__mainBlock____title": "Allgemein", "@sage/xtrem-master-data/pages__weekly_shift__mainSection____title": "Allgemein", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____lookupDialogTitle": "Schicht Montag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__name____title": "Name", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____lookupDialogTitle": "Schicht Samstag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____title": "Schicht Samstag", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____lookupDialogTitle": "Schicht Sonntag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____title": "<PERSON><PERSON><PERSON> Sonntag", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____lookupDialogTitle": "Schicht Donnerstag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____title": "Schicht Donnerstag", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____lookupDialogTitle": "Schicht Dienstag auswählen", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____title": "Schicht Dienstag", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____lookupDialogTitle": "Schicht Mittwoch auswählen", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content": "Der Umkehrkurs ist für das angegebene Datum bereits vorhanden. Möchten Sie den Umkehrkurs beibehalten oder löschen?", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-master-data/pages_sidebar_block_title_definition": "Definition", "@sage/xtrem-master-data/pages_sidebar_block_title_price": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages_sidebar_block_title_ranges": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages_sidebar_tab_title_definition": "Definition", "@sage/xtrem-master-data/pages_sidebar_tab_title_information": "Informationen", "@sage/xtrem-master-data/pages_sidebar_tab_title_prices": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages_sidebar_tab_title_ranges": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages_site__address_mandatory": "<PERSON>sen Si<PERSON> dem Standort mindestens eine Adresse zu.", "@sage/xtrem-master-data/pages_supplier__address_mandatory": "<PERSON>sen Sie dem Lieferanten mindestens eine Adresse zu.", "@sage/xtrem-master-data/pages-cancel-keep": "Behalten", "@sage/xtrem-master-data/pages-confirm-apply": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-apply-new": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-master-data/pages-confirm-continue": "Fortfahren", "@sage/xtrem-master-data/pages-confirm-delete": "Löschen", "@sage/xtrem-master-data/pages-confirm-no": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-send": "Senden", "@sage/xtrem-master-data/pages-confirm-yes": "<PERSON>a", "@sage/xtrem-master-data/permission__convert_from_to__name": "<PERSON><PERSON><PERSON><PERSON> von nach", "@sage/xtrem-master-data/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__create_bulk_license_plate_numbers__name": "Massen-Behälter-IDs erstellen", "@sage/xtrem-master-data/permission__create_bulk_locations__name": "Massenlagerplätze erstellen", "@sage/xtrem-master-data/permission__delete__name": "Löschen", "@sage/xtrem-master-data/permission__get_item_site_cost__name": "Artikel-Standort-Kosten abrufen", "@sage/xtrem-master-data/permission__get_locations__name": "Lagerplätze abrufen", "@sage/xtrem-master-data/permission__get_purchase_unit__name": "Einkaufseinheit abrufen", "@sage/xtrem-master-data/permission__get_unit_conversion_factor__name": "Einheitenumrechnungsfaktor abrufen", "@sage/xtrem-master-data/permission__get_valued_item_site__name": "Bewerteten Artikel-Standort abrufen", "@sage/xtrem-master-data/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/permission__update__name": "Aktualisieren", "@sage/xtrem-master-data/sales-to-stock-unit-must-be-one": "Setzen Sie den Umrechnungsfaktor auf 1, wenn die Verkaufseinheit und die Lagereinheit gleich sind.", "@sage/xtrem-master-data/service_options__allocation_transfer_option__name": "Option Reservierungsanforderung", "@sage/xtrem-master-data/service_options__bill_of_material_revision_service_option__name": "Dienstoption Stücklistenänderung", "@sage/xtrem-master-data/service_options__customer_360_view_option__name": "Option 360°-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/service_options__datev_option__name": "DATEV-Option", "@sage/xtrem-master-data/service_options__fifo_valuation_method_option__name": "Option FIFO-Bewertungsmethode", "@sage/xtrem-master-data/service_options__intersite_stock_transfer_option__name": "Option Intersite-Bestandstransfer", "@sage/xtrem-master-data/service_options__landed_cost_option__name": "Option Einstandskosten", "@sage/xtrem-master-data/service_options__landed_cost_order_option__name": "Option Bestellung Einstandskosten", "@sage/xtrem-master-data/service_options__landed_cost_stock_transfer_option__name": "Option Bestandstransfer Einstandskosten", "@sage/xtrem-master-data/service_options__order_to_order_option__name": "Option Auftrag-zu-Auftrag", "@sage/xtrem-master-data/service_options__phantom_item_option__name": "Option Phantomartikel", "@sage/xtrem-master-data/service_options__serial_number_option__name": "Option Seriennummern", "@sage/xtrem-master-data/site-etension-financial-currency-not-defined": "Der Buchhaltungsstandort ist nicht definiert.", "@sage/xtrem-master-data/site-extension-financial-currency-not-defined": "Die Finanzwährung ist nicht definiert.", "@sage/xtrem-master-data/telephone-validation-error": "Ungültige Telefonnummer", "@sage/xtrem-master-data/update-confirmation": "Datensatz aktualisiert", "@sage/xtrem-master-data/use-existing-business-entity": "Geschäftsentität gefunden: Name {{beName}}, ID {{beId}} und Steuer-ID {{beTaxId}}. Diese Geschäftsentität verwenden?", "@sage/xtrem-master-data/value-must-be-greater-than-current-sequence": "Der neue Wert ist kleiner als der aktuelle Wert.", "@sage/xtrem-master-data/value-must-be-positive": "Wert {{value}} muss positiv sein.", "@sage/xtrem-master-data/value-must-not-exceed-the-length-of-sequence-number": "Der neue Wert muss kleiner als die Länge des Nummernkreises sein.", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__addresses__title": "<PERSON><PERSON><PERSON> anzeigen", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__contacts__title": "Kontakte anzeigen", "@sage/xtrem-master-data/widgets__customer_contact_list____title": "Kundenkontakte", "@sage/xtrem-master-data/widgets__delete_note_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-master-data/widgets__delete_note_dialog_title": "Löschen bestätigen", "@sage/xtrem-master-data/widgets__system_version____title": "Systemversion", "@sage/xtrem-master-data/widgets-confirm-cancel": "Abbrechen", "@sage/xtrem-master-data/widgets-confirm-delete": "Löschen"}