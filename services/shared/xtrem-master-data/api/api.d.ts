declare module '@sage/xtrem-master-data-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        ChartOfAccount,
        Country,
        CountryInput,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        CompanyInput,
        Package as SageXtremSystem$Package,
        Site,
        SiteBinding,
        SiteInput,
        SysNoteAssociation,
        SysNoteAssociationInput,
        SysVendor,
        User,
        UserInput,
    } from '@sage/xtrem-system-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface AddressEntityType$Enum {
        businessEntity: 1;
        company: 2;
        site: 3;
        customer: 4;
        supplier: 5;
    }
    export type AddressEntityType = keyof AddressEntityType$Enum;
    export interface ApprovalStatus$Enum {
        draft: 0;
        pendingApproval: 1;
        approved: 2;
        confirmed: 3;
        rejected: 4;
        changeRequested: 5;
    }
    export type ApprovalStatus = keyof ApprovalStatus$Enum;
    export interface BaseDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        changeRequested: 5;
        confirmed: 6;
        readyToProcess: 7;
        readyToShip: 8;
        partiallyShipped: 9;
        shipped: 10;
        received: 11;
        partiallyInvoiced: 12;
        invoiced: 13;
        closed: 14;
        postingInProgress: 15;
        error: 16;
        partiallyReceived: 17;
        partiallyReturned: 18;
        partiallyOrdered: 19;
        taxCalculationFailed: 20;
        postingError: 21;
        posted: 22;
        stockError: 23;
        partiallyCredited: 24;
        credited: 25;
        noVariance: 26;
        variance: 27;
        varianceApproved: 28;
        returned: 29;
        ordered: 30;
        pending: 31;
        paid: 32;
        partiallyPaid: 33;
        quote: 34;
    }
    export type BaseDisplayStatus = keyof BaseDisplayStatus$Enum;
    export interface BaseOrigin$Enum {
        direct: 0;
        purchaseRequisition: 1;
        purchaseOrder: 2;
        purchaseSuggestion: 3;
        purchaseReceipt: 4;
        purchaseReturn: 5;
        purchaseInvoice: 6;
        purchaseCreditMemo: 7;
        shipment: 8;
        order: 9;
        invoice: 10;
        return: 11;
    }
    export type BaseOrigin = keyof BaseOrigin$Enum;
    export interface BaseSequenceNumberComponentType$Enum {
        constant: 1;
        year: 2;
        month: 3;
        week: 4;
        day: 5;
        company: 6;
        site: 7;
        sequenceNumber: 8;
        sequenceAlpha: 9;
    }
    export type BaseSequenceNumberComponentType = keyof BaseSequenceNumberComponentType$Enum;
    export interface BaseStatus$Enum {
        draft: 1;
        pending: 2;
        inProgress: 3;
        closed: 4;
        error: 5;
        taxCalculationFailed: 6;
        postingInProgress: 7;
        postingError: 8;
        posted: 9;
        stockError: 10;
        partiallyCredited: 11;
        credited: 12;
        noVariance: 13;
        variance: 14;
        varianceApproved: 15;
        pendingApproval: 16;
        approved: 17;
        rejected: 18;
        received: 19;
        partiallyInvoiced: 20;
        invoiced: 21;
        partiallyReturned: 22;
        returned: 23;
        partiallyReceived: 24;
        confirmed: 25;
        readyToProcess: 26;
        readyToShip: 27;
        shipped: 28;
        changeRequested: 29;
        partiallyShipped: 30;
        quote: 31;
    }
    export type BaseStatus = keyof BaseStatus$Enum;
    export interface BusinessEntityType$Enum {
        customer: 1;
        supplier: 2;
        all: 3;
    }
    export type BusinessEntityType = keyof BusinessEntityType$Enum;
    export interface BusinessRelationType$Enum {
        customer: 0;
        supplier: 1;
    }
    export type BusinessRelationType = keyof BusinessRelationType$Enum;
    export interface ConsumptionMode$Enum {
        none: 1;
        time: 2;
        quantity: 3;
    }
    export type ConsumptionMode = keyof ConsumptionMode$Enum;
    export interface ContactRole$Enum {
        mainContact: 0;
        commercialContact: 1;
        financialContact: 2;
    }
    export type ContactRole = keyof ContactRole$Enum;
    export interface ContainerType$Enum {
        container: 1;
        pallet: 2;
        pack: 3;
        bigBag: 4;
        box: 5;
        barrel: 6;
        other: 9;
    }
    export type ContainerType = keyof ContainerType$Enum;
    export interface CostCalculationMethod$Enum {
        cumulate: 1;
        compound: 2;
    }
    export type CostCalculationMethod = keyof CostCalculationMethod$Enum;
    export interface CostCategoryType$Enum {
        standard: 1;
        budgeted: 2;
        simulated: 3;
        other: 4;
    }
    export type CostCategoryType = keyof CostCategoryType$Enum;
    export interface CostValuationMethod$Enum {
        standardCost: 0;
        averageCost: 1;
        fifoCost: 2;
    }
    export type CostValuationMethod = keyof CostValuationMethod$Enum;
    export interface CustomerDisplayStatus$Enum {
        inactive: 0;
        onHold: 1;
        active: 2;
    }
    export type CustomerDisplayStatus = keyof CustomerDisplayStatus$Enum;
    export interface CustomerOnHoldType$Enum {
        blocking: 0;
        warning: 1;
        none: 2;
    }
    export type CustomerOnHoldType = keyof CustomerOnHoldType$Enum;
    export interface DiscountChargeCalculationBasis$Enum {
        grossPrice: 1;
        grossPriceAndCompound: 2;
    }
    export type DiscountChargeCalculationBasis = keyof DiscountChargeCalculationBasis$Enum;
    export interface DiscountChargeCalculationRule$Enum {
        byUnit: 1;
        byLine: 2;
    }
    export type DiscountChargeCalculationRule = keyof DiscountChargeCalculationRule$Enum;
    export interface DiscountChargeSign$Enum {
        increase: 1;
        decrease: 2;
    }
    export type DiscountChargeSign = keyof DiscountChargeSign$Enum;
    export interface DiscountChargeValueType$Enum {
        percentage: 1;
        amount: 2;
    }
    export type DiscountChargeValueType = keyof DiscountChargeValueType$Enum;
    export interface DiscountOrPenaltyType$Enum {
        percentage: 1;
        amount: 2;
    }
    export type DiscountOrPenaltyType = keyof DiscountOrPenaltyType$Enum;
    export interface DueDateType$Enum {
        afterInvoiceDate: 0;
        afterTheEndOfTheMonthOfInvoiceDate: 1;
        afterInvoiceDateAndExtendedToEndOfMonth: 2;
    }
    export type DueDateType = keyof DueDateType$Enum;
    export interface EmailActionType$Enum {
        approved: 0;
        rejected: 1;
    }
    export type EmailActionType = keyof EmailActionType$Enum;
    export interface ItemCategoryType$Enum {
        allergen: 0;
        ghsClassification: 1;
        none: 2;
    }
    export type ItemCategoryType = keyof ItemCategoryType$Enum;
    export interface ItemFlowType$Enum {
        purchased: 1;
        manufactured: 2;
        sold: 3;
        subcontracted: 4;
    }
    export type ItemFlowType = keyof ItemFlowType$Enum;
    export interface ItemPriceType$Enum {
        normal: 1;
        specialOffer: 2;
        discount: 3;
    }
    export type ItemPriceType = keyof ItemPriceType$Enum;
    export interface ItemStatus$Enum {
        active: 1;
        inDevelopment: 2;
        notRenewed: 3;
        obsolete: 4;
        notUsable: 5;
    }
    export type ItemStatus = keyof ItemStatus$Enum;
    export interface ItemType$Enum {
        service: 1;
        good: 2;
        landedCost: 3;
    }
    export type ItemType = keyof ItemType$Enum;
    export interface LegalEntity$Enum {
        corporation: 1;
        physicalPerson: 2;
    }
    export type LegalEntity = keyof LegalEntity$Enum;
    export interface LocationCategory$Enum {
        internal: 0;
        dock: 1;
        customer: 2;
        subcontract: 3;
        virtual: 4;
    }
    export type LocationCategory = keyof LocationCategory$Enum;
    export interface LotManagement$Enum {
        notManaged: 1;
        lotManagement: 2;
        lotSublotManagement: 3;
    }
    export type LotManagement = keyof LotManagement$Enum;
    export interface OrderType$Enum {
        firm: 1;
        planned: 2;
        suggested: 3;
        closed: 4;
    }
    export type OrderType = keyof OrderType$Enum;
    export interface PaymentMethod$Enum {
        printedCheck: 0;
        cash: 1;
        EFT: 2;
        creditCard: 3;
        ACH: 4;
        none: 5;
    }
    export type PaymentMethod = keyof PaymentMethod$Enum;
    export interface PaymentTermDiscountOrPenaltyType$Enum {
        percentage: 1;
        amount: 2;
    }
    export type PaymentTermDiscountOrPenaltyType = keyof PaymentTermDiscountOrPenaltyType$Enum;
    export interface PeriodType$Enum {
        day: 0;
        week: 1;
        month: 2;
        year: 3;
    }
    export type PeriodType = keyof PeriodType$Enum;
    export interface PreferredProcess$Enum {
        purchasing: 1;
        production: 2;
    }
    export type PreferredProcess = keyof PreferredProcess$Enum;
    export interface ReplenishmentMethod$Enum {
        notManaged: 1;
        byReorderPoint: 2;
        byMRP: 3;
    }
    export type ReplenishmentMethod = keyof ReplenishmentMethod$Enum;
    export interface ResourceGroupType$Enum {
        labor: 1;
        machine: 2;
        subcontract: 3;
        tool: 4;
    }
    export type ResourceGroupType = keyof ResourceGroupType$Enum;
    export interface SequenceCounterDefinitionLevel$Enum {
        application: 1;
        company: 2;
        site: 3;
    }
    export type SequenceCounterDefinitionLevel = keyof SequenceCounterDefinitionLevel$Enum;
    export interface SequenceNumberResetFrequency$Enum {
        noReset: 1;
        yearly: 2;
        monthly: 3;
    }
    export type SequenceNumberResetFrequency = keyof SequenceNumberResetFrequency$Enum;
    export interface SequenceNumberType$Enum {
        alphanumeric: 1;
        numeric: 2;
    }
    export type SequenceNumberType = keyof SequenceNumberType$Enum;
    export interface SerialNumberManagement$Enum {
        notManaged: 1;
        managed: 2;
    }
    export type SerialNumberManagement = keyof SerialNumberManagement$Enum;
    export interface SerialNumberUsage$Enum {
        issueOnly: 0;
        issueAndReceipt: 1;
    }
    export type SerialNumberUsage = keyof SerialNumberUsage$Enum;
    export interface StockManagementMode$Enum {
        onStock: 1;
        byOrder: 2;
        byProject: 3;
    }
    export type StockManagementMode = keyof StockManagementMode$Enum;
    export interface SupplierType$Enum {
        chemical: 1;
        foodAndBeverage: 2;
        other: 3;
    }
    export type SupplierType = keyof SupplierType$Enum;
    export interface TaxCalculationStatus$Enum {
        notDone: 1;
        inProgress: 2;
        done: 3;
        failed: 4;
    }
    export type TaxCalculationStatus = keyof TaxCalculationStatus$Enum;
    export interface Title$Enum {
        ms: 1;
        mr: 2;
        dr: 3;
        mrs: 4;
        family: 5;
        prof: 6;
        master: 7;
        miss: 8;
    }
    export type Title = keyof Title$Enum;
    export interface UnitConversionType$Enum {
        sales: 1;
        purchase: 2;
        other: 3;
    }
    export type UnitConversionType = keyof UnitConversionType$Enum;
    export interface UnitType$Enum {
        each: 1;
        length: 2;
        area: 3;
        volume: 4;
        weight: 5;
        time: 6;
        temperature: 7;
    }
    export type UnitType = keyof UnitType$Enum;
    export interface WeekDays$Enum {
        monday: 1;
        tuesday: 2;
        wednesday: 3;
        thursday: 4;
        friday: 5;
        saturday: 6;
        sunday: 7;
    }
    export type WeekDays = keyof WeekDays$Enum;
    export interface WorkInProgressDocumentType$Enum {
        workOrder: 0;
        materialNeed: 1;
        purchaseOrder: 2;
        purchaseReceipt: 3;
        purchaseReturn: 4;
        salesOrder: 5;
        stockTransferOrder: 6;
        stockTransferReceipt: 7;
    }
    export type WorkInProgressDocumentType = keyof WorkInProgressDocumentType$Enum;
    export interface ZoneType$Enum {
        frozen: 0;
        sensitive: 1;
        secured: 2;
        restricted: 3;
        hazard: 4;
        chemical: 5;
        magnetic: 6;
        virtual: 7;
    }
    export type ZoneType = keyof ZoneType$Enum;
    export interface Address extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
    }
    export interface AddressInput extends ClientNodeInput {
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        region?: string;
        postcode?: string;
        country?: integer | string;
        locationPhoneNumber?: string;
    }
    export interface AddressBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
    }
    export interface Address$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Address$Lookups {
        country: QueryOperation<Country>;
    }
    export interface Address$Operations {
        query: QueryOperation<Address>;
        read: ReadOperation<Address>;
        aggregate: {
            read: AggregateReadOperation<Address>;
            query: AggregateQueryOperation<Address>;
        };
        create: CreateOperation<AddressInput, Address>;
        getDuplicate: GetDuplicateOperation<Address>;
        update: UpdateOperation<AddressInput, Address>;
        updateById: UpdateByIdOperation<AddressInput, Address>;
        asyncOperations: Address$AsyncOperations;
        lookups(dataOrId: string | { data: AddressInput }): Address$Lookups;
        getDefaults: GetDefaultsOperation<Address>;
    }
    export interface AddressBase extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
    }
    export interface AddressBaseInput extends ClientNodeInput {
        _constructor?: string;
        isActive?: boolean | string;
        address?: integer | string;
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        region?: string;
        postcode?: string;
        country?: integer | string;
        locationPhoneNumber?: string;
    }
    export interface AddressBaseBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
    }
    export interface AddressBase$Lookups {
        address: QueryOperation<Address>;
        country: QueryOperation<Country>;
    }
    export interface AddressBase$Operations {
        query: QueryOperation<AddressBase>;
        read: ReadOperation<AddressBase>;
        aggregate: {
            read: AggregateReadOperation<AddressBase>;
            query: AggregateQueryOperation<AddressBase>;
        };
        create: CreateOperation<AddressBaseInput, AddressBase>;
        getDuplicate: GetDuplicateOperation<AddressBase>;
        update: UpdateOperation<AddressBaseInput, AddressBase>;
        updateById: UpdateByIdOperation<AddressBaseInput, AddressBase>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: AddressBaseInput }): AddressBase$Lookups;
        getDefaults: GetDefaultsOperation<AddressBase>;
    }
    export interface Allergen extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        pictogram: BinaryStream;
    }
    export interface AllergenInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
        pictogram?: BinaryStream;
    }
    export interface AllergenBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        pictogram: BinaryStream;
    }
    export interface Allergen$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Allergen$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Allergen$Operations {
        query: QueryOperation<Allergen>;
        read: ReadOperation<Allergen>;
        aggregate: {
            read: AggregateReadOperation<Allergen>;
            query: AggregateQueryOperation<Allergen>;
        };
        create: CreateOperation<AllergenInput, Allergen>;
        getDuplicate: GetDuplicateOperation<Allergen>;
        duplicate: DuplicateOperation<string, AllergenInput, Allergen>;
        update: UpdateOperation<AllergenInput, Allergen>;
        updateById: UpdateByIdOperation<AllergenInput, Allergen>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Allergen$AsyncOperations;
        lookups(dataOrId: string | { data: AllergenInput }): Allergen$Lookups;
        getDefaults: GetDefaultsOperation<Allergen>;
    }
    export interface BaseBusinessRelation extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
    }
    export interface BaseBusinessRelationInput extends VitalClientNodeInput {
        _constructor?: string;
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
    }
    export interface BaseBusinessRelationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
    }
    export interface BaseBusinessRelation$Lookups {
        primaryAddress: QueryOperation<BusinessEntityAddress>;
        paymentTerm: QueryOperation<PaymentTerm>;
        category: QueryOperation<CustomerSupplierCategory>;
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
    }
    export interface BaseBusinessRelation$Operations {
        query: QueryOperation<BaseBusinessRelation>;
        read: ReadOperation<BaseBusinessRelation>;
        aggregate: {
            read: AggregateReadOperation<BaseBusinessRelation>;
            query: AggregateQueryOperation<BaseBusinessRelation>;
        };
        update: UpdateOperation<BaseBusinessRelationInput, BaseBusinessRelation>;
        updateById: UpdateByIdOperation<BaseBusinessRelationInput, BaseBusinessRelation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: BaseBusinessRelationInput }): BaseBusinessRelation$Lookups;
        getDefaults: GetDefaultsOperation<BaseBusinessRelation>;
    }
    export interface BaseCapability extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        dateRangeValidity: string;
        dateStartValid: string;
        dateEndValid: string;
        capabilityLevel: CapabilityLevel;
    }
    export interface BaseCapabilityInput extends ClientNodeInput {
        _constructor?: string;
        id?: string;
        name?: string;
        dateRangeValidity?: string;
        capabilityLevel?: integer | string;
    }
    export interface BaseCapabilityBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        dateRangeValidity: string;
        dateStartValid: string;
        dateEndValid: string;
        capabilityLevel: CapabilityLevel;
    }
    export interface BaseCapability$Lookups {
        capabilityLevel: QueryOperation<CapabilityLevel>;
    }
    export interface BaseCapability$Operations {
        query: QueryOperation<BaseCapability>;
        read: ReadOperation<BaseCapability>;
        aggregate: {
            read: AggregateReadOperation<BaseCapability>;
            query: AggregateQueryOperation<BaseCapability>;
        };
        lookups(dataOrId: string | { data: BaseCapabilityInput }): BaseCapability$Lookups;
        getDefaults: GetDefaultsOperation<BaseCapability>;
    }
    export interface BaseCertificate extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        standard: Standard;
        id: string;
        dateOfCertification: string;
        validUntil: string;
        certificationBody: string;
        dateOfOriginalCertification: string;
    }
    export interface BaseCertificateInput extends ClientNodeInput {
        _constructor?: string;
        standard?: integer | string;
        id?: string;
        dateOfCertification?: string;
        validUntil?: string;
        certificationBody?: string;
        dateOfOriginalCertification?: string;
    }
    export interface BaseCertificateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        standard: Standard;
        id: string;
        dateOfCertification: string;
        validUntil: string;
        certificationBody: string;
        dateOfOriginalCertification: string;
    }
    export interface BaseCertificate$Lookups {
        standard: QueryOperation<Standard>;
    }
    export interface BaseCertificate$Operations {
        lookups(dataOrId: string | { data: BaseCertificateInput }): BaseCertificate$Lookups;
    }
    export interface BaseDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        anyLines: ClientCollection<BaseDocumentItemLine>;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
    }
    export interface BaseDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        lines?: Partial<BaseDocumentItemLineInput>[];
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
    }
    export interface BaseDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        lines: ClientCollection<BaseDocumentItemLineBinding>;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
    }
    export interface BaseDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
    }
    export interface BaseDocument$Operations {
        query: QueryOperation<BaseDocument>;
        read: ReadOperation<BaseDocument>;
        aggregate: {
            read: AggregateReadOperation<BaseDocument>;
            query: AggregateQueryOperation<BaseDocument>;
        };
        create: CreateOperation<BaseDocumentInput, BaseDocument>;
        getDuplicate: GetDuplicateOperation<BaseDocument>;
        duplicate: DuplicateOperation<string, BaseDocumentInput, BaseDocument>;
        update: UpdateOperation<BaseDocumentInput, BaseDocument>;
        updateById: UpdateByIdOperation<BaseDocumentInput, BaseDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseDocument$Mutations;
        asyncOperations: BaseDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseDocumentInput }): BaseDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseDocument>;
    }
    export interface BaseDocumentLine extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
    }
    export interface BaseDocumentLineInput extends ClientNodeInput {
        _constructor?: string;
    }
    export interface BaseDocumentLineBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
    }
    export interface BaseDocumentLine$Operations {}
    export interface BaseDocumentLineInquiry extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        anyLines: ClientCollection<BaseDocumentItemLine>;
    }
    export interface BaseDocumentLineInquiryInput extends ClientNodeInput {
        _constructor?: string;
        user?: integer | string;
        company?: integer | string;
        site?: integer | string;
        itemCategory?: integer | string;
        commodityCode?: string;
        fromItem?: integer | string;
        toItem?: integer | string;
        date?: string;
    }
    export interface BaseDocumentLineInquiryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        lines: ClientCollection<BaseDocumentItemLine>;
    }
    export interface BaseDocumentLineInquiry$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        site: QueryOperation<Site>;
        itemCategory: QueryOperation<ItemCategory>;
        fromItem: QueryOperation<Item>;
        toItem: QueryOperation<Item>;
    }
    export interface BaseDocumentLineInquiry$Operations {
        query: QueryOperation<BaseDocumentLineInquiry>;
        read: ReadOperation<BaseDocumentLineInquiry>;
        aggregate: {
            read: AggregateReadOperation<BaseDocumentLineInquiry>;
            query: AggregateQueryOperation<BaseDocumentLineInquiry>;
        };
        create: CreateOperation<BaseDocumentLineInquiryInput, BaseDocumentLineInquiry>;
        getDuplicate: GetDuplicateOperation<BaseDocumentLineInquiry>;
        duplicate: DuplicateOperation<string, BaseDocumentLineInquiryInput, BaseDocumentLineInquiry>;
        update: UpdateOperation<BaseDocumentLineInquiryInput, BaseDocumentLineInquiry>;
        updateById: UpdateByIdOperation<BaseDocumentLineInquiryInput, BaseDocumentLineInquiry>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: BaseDocumentLineInquiryInput }): BaseDocumentLineInquiry$Lookups;
        getDefaults: GetDefaultsOperation<BaseDocumentLineInquiry>;
    }
    export interface BaseLineDiscountCharge extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basis: string;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        amount: string;
    }
    export interface BaseLineDiscountChargeInput extends ClientNodeInput {
        _constructor?: string;
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basis?: decimal | string;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        amount?: decimal | string;
    }
    export interface BaseLineDiscountChargeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basis: string;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        amount: string;
    }
    export interface BaseLineDiscountCharge$Operations {}
    export interface BaseLineToLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        from: BaseDocumentItemLine;
        to: BaseDocumentItemLine;
    }
    export interface BaseLineToLineInput extends VitalClientNodeInput {
        _constructor?: string;
        to?: integer | string;
    }
    export interface BaseLineToLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        from: BaseDocumentItemLine;
        to: BaseDocumentItemLine;
    }
    export interface BaseLineToLine$Lookups {
        to: QueryOperation<BaseDocumentItemLine>;
    }
    export interface BaseLineToLine$Operations {
        query: QueryOperation<BaseLineToLine>;
        read: ReadOperation<BaseLineToLine>;
        aggregate: {
            read: AggregateReadOperation<BaseLineToLine>;
            query: AggregateQueryOperation<BaseLineToLine>;
        };
        lookups(dataOrId: string | { data: BaseLineToLineInput }): BaseLineToLine$Lookups;
        getDefaults: GetDefaultsOperation<BaseLineToLine>;
    }
    export interface BaseResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
        _syncTick: string;
    }
    export interface BaseResourceInput extends ClientNodeInput {
        _constructor?: string;
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
        _syncTick?: decimal | string;
    }
    export interface BaseResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
        _syncTick: string;
    }
    export interface BaseResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        location: QueryOperation<Location>;
    }
    export interface BaseResource$Operations {
        query: QueryOperation<BaseResource>;
        read: ReadOperation<BaseResource>;
        aggregate: {
            read: AggregateReadOperation<BaseResource>;
            query: AggregateQueryOperation<BaseResource>;
        };
        create: CreateOperation<BaseResourceInput, BaseResource>;
        getDuplicate: GetDuplicateOperation<BaseResource>;
        duplicate: DuplicateOperation<string, BaseResourceInput, BaseResource>;
        lookups(dataOrId: string | { data: BaseResourceInput }): BaseResource$Lookups;
        getDefaults: GetDefaultsOperation<BaseResource>;
    }
    export interface BaseSequenceNumber extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        anyComponents: ClientCollection<BaseSequenceNumberComponent>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
    }
    export interface BaseSequenceNumberInput extends ClientNodeInput {
        _vendor?: integer | string;
        _constructor?: string;
        id?: string;
        name?: string;
        definitionLevel?: SequenceCounterDefinitionLevel;
        rtzLevel?: SequenceNumberResetFrequency;
        isClearedByReset?: boolean | string;
        type?: SequenceNumberType;
        components?: Partial<BaseSequenceNumberComponentInput>[];
        isChronological?: boolean | string;
        legislation?: integer | string;
    }
    export interface BaseSequenceNumberBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<BaseSequenceNumberComponentBinding>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
    }
    export interface BaseSequenceNumber$Queries {
        getDocumentNodeNames: Node$Operation<{}, string[]>;
    }
    export interface BaseSequenceNumber$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface BaseSequenceNumber$Operations {
        query: QueryOperation<BaseSequenceNumber>;
        read: ReadOperation<BaseSequenceNumber>;
        aggregate: {
            read: AggregateReadOperation<BaseSequenceNumber>;
            query: AggregateQueryOperation<BaseSequenceNumber>;
        };
        queries: BaseSequenceNumber$Queries;
        create: CreateOperation<BaseSequenceNumberInput, BaseSequenceNumber>;
        getDuplicate: GetDuplicateOperation<BaseSequenceNumber>;
        duplicate: DuplicateOperation<string, BaseSequenceNumberInput, BaseSequenceNumber>;
        update: UpdateOperation<BaseSequenceNumberInput, BaseSequenceNumber>;
        updateById: UpdateByIdOperation<BaseSequenceNumberInput, BaseSequenceNumber>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: BaseSequenceNumberInput }): BaseSequenceNumber$Lookups;
        getDefaults: GetDefaultsOperation<BaseSequenceNumber>;
    }
    export interface BaseSequenceNumberComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sequenceNumber: BaseSequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
    }
    export interface BaseSequenceNumberComponentInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        _constructor?: string;
        type?: BaseSequenceNumberComponentType;
        constant?: string;
        length?: integer | string;
    }
    export interface BaseSequenceNumberComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sequenceNumber: BaseSequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
    }
    export interface BaseSequenceNumberComponent$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface BaseSequenceNumberComponent$Operations {
        query: QueryOperation<BaseSequenceNumberComponent>;
        read: ReadOperation<BaseSequenceNumberComponent>;
        aggregate: {
            read: AggregateReadOperation<BaseSequenceNumberComponent>;
            query: AggregateQueryOperation<BaseSequenceNumberComponent>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: BaseSequenceNumberComponentInput }): BaseSequenceNumberComponent$Lookups;
        getDefaults: GetDefaultsOperation<BaseSequenceNumberComponent>;
    }
    export interface BusinessEntity extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddress>;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: Site;
        customer: Customer;
        supplier: Supplier;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface BusinessEntityInput extends ClientNodeInput {
        id?: string;
        isActive?: boolean | string;
        legalEntity?: LegalEntity;
        name?: string;
        country?: integer | string;
        currency?: integer | string;
        taxIdNumber?: string;
        siret?: string;
        addresses?: Partial<BusinessEntityAddressInput>[];
        contacts?: Partial<BusinessEntityContactInput>[];
        image?: BinaryStream;
        website?: string;
        parent?: integer | string;
        site?: SiteInput;
        customer?: CustomerInput;
        supplier?: SupplierInput;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface BusinessEntityBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddressBinding>;
        contacts: ClientCollection<BusinessEntityContactBinding>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: SiteBinding;
        customer: CustomerBinding;
        supplier: SupplierBinding;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface BusinessEntity$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BusinessEntity$Lookups {
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
        primaryAddress: QueryOperation<BusinessEntityAddress>;
        primaryContact: QueryOperation<BusinessEntityContact>;
        parent: QueryOperation<BusinessEntity>;
    }
    export interface BusinessEntity$Operations {
        query: QueryOperation<BusinessEntity>;
        read: ReadOperation<BusinessEntity>;
        aggregate: {
            read: AggregateReadOperation<BusinessEntity>;
            query: AggregateQueryOperation<BusinessEntity>;
        };
        create: CreateOperation<BusinessEntityInput, BusinessEntity>;
        getDuplicate: GetDuplicateOperation<BusinessEntity>;
        update: UpdateOperation<BusinessEntityInput, BusinessEntity>;
        updateById: UpdateByIdOperation<BusinessEntityInput, BusinessEntity>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BusinessEntity$AsyncOperations;
        lookups(dataOrId: string | { data: BusinessEntityInput }): BusinessEntity$Lookups;
        getDefaults: GetDefaultsOperation<BusinessEntity>;
    }
    export interface CapabilityLevel extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        level: integer;
    }
    export interface CapabilityLevelInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        level?: integer | string;
    }
    export interface CapabilityLevelBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        level: integer;
    }
    export interface CapabilityLevel$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CapabilityLevel$Operations {
        query: QueryOperation<CapabilityLevel>;
        read: ReadOperation<CapabilityLevel>;
        aggregate: {
            read: AggregateReadOperation<CapabilityLevel>;
            query: AggregateQueryOperation<CapabilityLevel>;
        };
        create: CreateOperation<CapabilityLevelInput, CapabilityLevel>;
        getDuplicate: GetDuplicateOperation<CapabilityLevel>;
        duplicate: DuplicateOperation<string, CapabilityLevelInput, CapabilityLevel>;
        update: UpdateOperation<CapabilityLevelInput, CapabilityLevel>;
        updateById: UpdateByIdOperation<CapabilityLevelInput, CapabilityLevel>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CapabilityLevel$AsyncOperations;
        getDefaults: GetDefaultsOperation<CapabilityLevel>;
    }
    export interface Contact extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        title: Title;
        firstName: string;
        lastName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
    }
    export interface ContactInput extends ClientNodeInput {
        title?: Title;
        firstName?: string;
        lastName?: string;
        preferredName?: string;
        role?: ContactRole;
        position?: string;
        locationPhoneNumber?: string;
        email?: string;
        image?: BinaryStream;
    }
    export interface ContactBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        title: Title;
        firstName: string;
        lastName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
    }
    export interface Contact$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Contact$Operations {
        query: QueryOperation<Contact>;
        read: ReadOperation<Contact>;
        aggregate: {
            read: AggregateReadOperation<Contact>;
            query: AggregateQueryOperation<Contact>;
        };
        create: CreateOperation<ContactInput, Contact>;
        getDuplicate: GetDuplicateOperation<Contact>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Contact$AsyncOperations;
        getDefaults: GetDefaultsOperation<Contact>;
    }
    export interface ContactBase extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
    }
    export interface ContactBaseInput extends ClientNodeInput {
        _constructor?: string;
        isActive?: boolean | string;
        contact?: integer | string;
        title?: Title;
        firstName?: string;
        lastName?: string;
        preferredName?: string;
        role?: ContactRole;
        position?: string;
        locationPhoneNumber?: string;
        email?: string;
        image?: BinaryStream;
    }
    export interface ContactBaseBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
    }
    export interface ContactBase$Lookups {
        contact: QueryOperation<Contact>;
    }
    export interface ContactBase$Operations {
        query: QueryOperation<ContactBase>;
        read: ReadOperation<ContactBase>;
        aggregate: {
            read: AggregateReadOperation<ContactBase>;
            query: AggregateQueryOperation<ContactBase>;
        };
        create: CreateOperation<ContactBaseInput, ContactBase>;
        getDuplicate: GetDuplicateOperation<ContactBase>;
        update: UpdateOperation<ContactBaseInput, ContactBase>;
        updateById: UpdateByIdOperation<ContactBaseInput, ContactBase>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: ContactBaseInput }): ContactBase$Lookups;
        getDefaults: GetDefaultsOperation<ContactBase>;
    }
    export interface Container extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        type: ContainerType;
        isInternal: boolean;
        isSingleItem: boolean;
        isSingleLot: boolean;
        sequenceNumber: SequenceNumber;
        labelFormat: string;
        storageCapacity: integer;
        consumedLocationCapacity: integer;
    }
    export interface ContainerInput extends ClientNodeInput {
        id?: string;
        name?: string;
        isActive?: boolean | string;
        type?: ContainerType;
        isInternal?: boolean | string;
        isSingleItem?: boolean | string;
        isSingleLot?: boolean | string;
        sequenceNumber?: integer | string;
        labelFormat?: string;
        storageCapacity?: integer | string;
        consumedLocationCapacity?: integer | string;
    }
    export interface ContainerBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        type: ContainerType;
        isInternal: boolean;
        isSingleItem: boolean;
        isSingleLot: boolean;
        sequenceNumber: SequenceNumber;
        labelFormat: string;
        storageCapacity: integer;
        consumedLocationCapacity: integer;
    }
    export interface Container$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Container$Lookups {
        sequenceNumber: QueryOperation<SequenceNumber>;
    }
    export interface Container$Operations {
        query: QueryOperation<Container>;
        read: ReadOperation<Container>;
        aggregate: {
            read: AggregateReadOperation<Container>;
            query: AggregateQueryOperation<Container>;
        };
        create: CreateOperation<ContainerInput, Container>;
        getDuplicate: GetDuplicateOperation<Container>;
        duplicate: DuplicateOperation<string, ContainerInput, Container>;
        update: UpdateOperation<ContainerInput, Container>;
        updateById: UpdateByIdOperation<ContainerInput, Container>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Container$AsyncOperations;
        lookups(dataOrId: string | { data: ContainerInput }): Container$Lookups;
        getDefaults: GetDefaultsOperation<Container>;
    }
    export interface CostCategory extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        costCategoryType: CostCategoryType;
        isMandatory: boolean;
    }
    export interface CostCategoryInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        costCategoryType?: CostCategoryType;
        isMandatory?: boolean | string;
    }
    export interface CostCategoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        costCategoryType: CostCategoryType;
        isMandatory: boolean;
    }
    export interface CostCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CostCategory$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface CostCategory$Operations {
        query: QueryOperation<CostCategory>;
        read: ReadOperation<CostCategory>;
        aggregate: {
            read: AggregateReadOperation<CostCategory>;
            query: AggregateQueryOperation<CostCategory>;
        };
        create: CreateOperation<CostCategoryInput, CostCategory>;
        getDuplicate: GetDuplicateOperation<CostCategory>;
        duplicate: DuplicateOperation<string, CostCategoryInput, CostCategory>;
        update: UpdateOperation<CostCategoryInput, CostCategory>;
        updateById: UpdateByIdOperation<CostCategoryInput, CostCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CostCategory$AsyncOperations;
        lookups(dataOrId: string | { data: CostCategoryInput }): CostCategory$Lookups;
        getDefaults: GetDefaultsOperation<CostCategory>;
    }
    export interface Currency extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        id: string;
        symbol: string;
        decimalDigits: integer;
        rounding: integer;
        lastUpdate: string;
        icon: BinaryStream;
        isActive: boolean;
        exchangeRates: ClientCollection<ExchangeRate>;
        currentExchangeRates: ClientCollection<ExchangeRate>;
        exchangeRatesDestinationInverse: ClientCollection<ExchangeRate>;
    }
    export interface CurrencyInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        id?: string;
        symbol?: string;
        decimalDigits?: integer | string;
        rounding?: integer | string;
        icon?: BinaryStream;
        isActive?: boolean | string;
    }
    export interface CurrencyBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        id: string;
        symbol: string;
        decimalDigits: integer;
        rounding: integer;
        lastUpdate: string;
        icon: BinaryStream;
        isActive: boolean;
        exchangeRates: ClientCollection<ExchangeRate>;
        currentExchangeRates: ClientCollection<ExchangeRate>;
        exchangeRatesDestinationInverse: ClientCollection<ExchangeRate>;
    }
    export interface Currency$Mutations {
        saveExchangeRate: Node$Operation<
            {
                base: string;
                destination: string;
                dateRate: string;
                rate: decimal | string;
            },
            boolean
        >;
    }
    export interface Currency$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Currency$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Currency$Operations {
        query: QueryOperation<Currency>;
        read: ReadOperation<Currency>;
        aggregate: {
            read: AggregateReadOperation<Currency>;
            query: AggregateQueryOperation<Currency>;
        };
        create: CreateOperation<CurrencyInput, Currency>;
        getDuplicate: GetDuplicateOperation<Currency>;
        duplicate: DuplicateOperation<string, CurrencyInput, Currency>;
        update: UpdateOperation<CurrencyInput, Currency>;
        updateById: UpdateByIdOperation<CurrencyInput, Currency>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Currency$Mutations;
        asyncOperations: Currency$AsyncOperations;
        lookups(dataOrId: string | { data: CurrencyInput }): Currency$Lookups;
        getDefaults: GetDefaultsOperation<Currency>;
    }
    export interface CustomerPriceReason extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
        priority: integer;
    }
    export interface CustomerPriceReasonInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
        priority?: integer | string;
    }
    export interface CustomerPriceReasonBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
        priority: integer;
    }
    export interface CustomerPriceReason$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CustomerPriceReason$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface CustomerPriceReason$Operations {
        query: QueryOperation<CustomerPriceReason>;
        read: ReadOperation<CustomerPriceReason>;
        aggregate: {
            read: AggregateReadOperation<CustomerPriceReason>;
            query: AggregateQueryOperation<CustomerPriceReason>;
        };
        create: CreateOperation<CustomerPriceReasonInput, CustomerPriceReason>;
        getDuplicate: GetDuplicateOperation<CustomerPriceReason>;
        duplicate: DuplicateOperation<string, CustomerPriceReasonInput, CustomerPriceReason>;
        update: UpdateOperation<CustomerPriceReasonInput, CustomerPriceReason>;
        updateById: UpdateByIdOperation<CustomerPriceReasonInput, CustomerPriceReason>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CustomerPriceReason$AsyncOperations;
        lookups(dataOrId: string | { data: CustomerPriceReasonInput }): CustomerPriceReason$Lookups;
        getDefaults: GetDefaultsOperation<CustomerPriceReason>;
    }
    export interface CustomerSupplierCategory extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isCustomer: boolean;
        isSupplier: boolean;
        isSequenceNumberManagement: boolean;
        sequenceNumber: SequenceNumber;
    }
    export interface CustomerSupplierCategoryInput extends ClientNodeInput {
        id?: string;
        name?: string;
        isCustomer?: boolean | string;
        isSupplier?: boolean | string;
        isSequenceNumberManagement?: boolean | string;
        sequenceNumber?: integer | string;
    }
    export interface CustomerSupplierCategoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isCustomer: boolean;
        isSupplier: boolean;
        isSequenceNumberManagement: boolean;
        sequenceNumber: SequenceNumber;
    }
    export interface CustomerSupplierCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CustomerSupplierCategory$Lookups {
        sequenceNumber: QueryOperation<SequenceNumber>;
    }
    export interface CustomerSupplierCategory$Operations {
        query: QueryOperation<CustomerSupplierCategory>;
        read: ReadOperation<CustomerSupplierCategory>;
        aggregate: {
            read: AggregateReadOperation<CustomerSupplierCategory>;
            query: AggregateQueryOperation<CustomerSupplierCategory>;
        };
        create: CreateOperation<CustomerSupplierCategoryInput, CustomerSupplierCategory>;
        getDuplicate: GetDuplicateOperation<CustomerSupplierCategory>;
        duplicate: DuplicateOperation<string, CustomerSupplierCategoryInput, CustomerSupplierCategory>;
        update: UpdateOperation<CustomerSupplierCategoryInput, CustomerSupplierCategory>;
        updateById: UpdateByIdOperation<CustomerSupplierCategoryInput, CustomerSupplierCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CustomerSupplierCategory$AsyncOperations;
        lookups(dataOrId: string | { data: CustomerSupplierCategoryInput }): CustomerSupplierCategory$Lookups;
        getDefaults: GetDefaultsOperation<CustomerSupplierCategory>;
    }
    export interface DailyShift extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isFullDay: boolean;
        capacity: string;
        formattedCapacity: string;
        shiftDetails: ClientCollection<DailyShiftDetail>;
    }
    export interface DailyShiftInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isFullDay?: boolean | string;
        shiftDetails?: Partial<DailyShiftDetailInput>[];
    }
    export interface DailyShiftBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isFullDay: boolean;
        capacity: string;
        formattedCapacity: string;
        shiftDetails: ClientCollection<DailyShiftDetailBinding>;
    }
    export interface DailyShift$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DailyShift$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface DailyShift$Operations {
        query: QueryOperation<DailyShift>;
        read: ReadOperation<DailyShift>;
        aggregate: {
            read: AggregateReadOperation<DailyShift>;
            query: AggregateQueryOperation<DailyShift>;
        };
        create: CreateOperation<DailyShiftInput, DailyShift>;
        getDuplicate: GetDuplicateOperation<DailyShift>;
        duplicate: DuplicateOperation<string, DailyShiftInput, DailyShift>;
        update: UpdateOperation<DailyShiftInput, DailyShift>;
        updateById: UpdateByIdOperation<DailyShiftInput, DailyShift>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: DailyShift$AsyncOperations;
        lookups(dataOrId: string | { data: DailyShiftInput }): DailyShift$Lookups;
        getDefaults: GetDefaultsOperation<DailyShift>;
    }
    export interface DailyShiftDetail extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        dailyShift: DailyShift;
        shiftDetail: ShiftDetail;
    }
    export interface DailyShiftDetailInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        shiftDetail?: integer | string;
    }
    export interface DailyShiftDetailBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        dailyShift: DailyShift;
        shiftDetail: ShiftDetail;
    }
    export interface DailyShiftDetail$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DailyShiftDetail$Lookups {
        _vendor: QueryOperation<SysVendor>;
        shiftDetail: QueryOperation<ShiftDetail>;
    }
    export interface DailyShiftDetail$Operations {
        query: QueryOperation<DailyShiftDetail>;
        read: ReadOperation<DailyShiftDetail>;
        aggregate: {
            read: AggregateReadOperation<DailyShiftDetail>;
            query: AggregateQueryOperation<DailyShiftDetail>;
        };
        asyncOperations: DailyShiftDetail$AsyncOperations;
        lookups(dataOrId: string | { data: DailyShiftDetailInput }): DailyShiftDetail$Lookups;
        getDefaults: GetDefaultsOperation<DailyShiftDetail>;
    }
    export interface DeliveryDetail extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        address: BusinessEntityAddress;
        isActive: boolean;
        isPrimary: boolean;
        shipmentSite: Site;
        mode: DeliveryMode;
        leadTime: integer;
        incoterm: Incoterm;
        isMondayWorkDay: boolean;
        isTuesdayWorkDay: boolean;
        isWednesdayWorkDay: boolean;
        isThursdayWorkDay: boolean;
        isFridayWorkDay: boolean;
        isSaturdayWorkDay: boolean;
        isSundayWorkDay: boolean;
        workDaysSelection: WeekDays[];
    }
    export interface DeliveryDetailInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        isPrimary?: boolean | string;
        shipmentSite?: integer | string;
        mode?: integer | string;
        leadTime?: integer | string;
        incoterm?: integer | string;
        isMondayWorkDay?: boolean | string;
        isTuesdayWorkDay?: boolean | string;
        isWednesdayWorkDay?: boolean | string;
        isThursdayWorkDay?: boolean | string;
        isFridayWorkDay?: boolean | string;
        isSaturdayWorkDay?: boolean | string;
        isSundayWorkDay?: boolean | string;
    }
    export interface DeliveryDetailBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        address: BusinessEntityAddress;
        isActive: boolean;
        isPrimary: boolean;
        shipmentSite: Site;
        mode: DeliveryMode;
        leadTime: integer;
        incoterm: Incoterm;
        isMondayWorkDay: boolean;
        isTuesdayWorkDay: boolean;
        isWednesdayWorkDay: boolean;
        isThursdayWorkDay: boolean;
        isFridayWorkDay: boolean;
        isSaturdayWorkDay: boolean;
        isSundayWorkDay: boolean;
        workDaysSelection: WeekDays[];
    }
    export interface DeliveryDetail$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DeliveryDetail$Lookups {
        shipmentSite: QueryOperation<Site>;
        mode: QueryOperation<DeliveryMode>;
        incoterm: QueryOperation<Incoterm>;
    }
    export interface DeliveryDetail$Operations {
        query: QueryOperation<DeliveryDetail>;
        read: ReadOperation<DeliveryDetail>;
        aggregate: {
            read: AggregateReadOperation<DeliveryDetail>;
            query: AggregateQueryOperation<DeliveryDetail>;
        };
        create: CreateOperation<DeliveryDetailInput, DeliveryDetail>;
        getDuplicate: GetDuplicateOperation<DeliveryDetail>;
        update: UpdateOperation<DeliveryDetailInput, DeliveryDetail>;
        updateById: UpdateByIdOperation<DeliveryDetailInput, DeliveryDetail>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: DeliveryDetail$AsyncOperations;
        lookups(dataOrId: string | { data: DeliveryDetailInput }): DeliveryDetail$Lookups;
        getDefaults: GetDefaultsOperation<DeliveryDetail>;
    }
    export interface DeliveryMode extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface DeliveryModeInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
    }
    export interface DeliveryModeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface DeliveryMode$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DeliveryMode$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface DeliveryMode$Operations {
        query: QueryOperation<DeliveryMode>;
        read: ReadOperation<DeliveryMode>;
        aggregate: {
            read: AggregateReadOperation<DeliveryMode>;
            query: AggregateQueryOperation<DeliveryMode>;
        };
        create: CreateOperation<DeliveryModeInput, DeliveryMode>;
        getDuplicate: GetDuplicateOperation<DeliveryMode>;
        duplicate: DuplicateOperation<string, DeliveryModeInput, DeliveryMode>;
        update: UpdateOperation<DeliveryModeInput, DeliveryMode>;
        updateById: UpdateByIdOperation<DeliveryModeInput, DeliveryMode>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: DeliveryMode$AsyncOperations;
        lookups(dataOrId: string | { data: DeliveryModeInput }): DeliveryMode$Lookups;
        getDefaults: GetDefaultsOperation<DeliveryMode>;
    }
    export interface DevTools extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
    }
    export interface DevToolsInput extends ClientNodeInput {
        id?: string;
    }
    export interface DevToolsBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
    }
    export interface DevTools$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DevTools$Operations {
        query: QueryOperation<DevTools>;
        read: ReadOperation<DevTools>;
        aggregate: {
            read: AggregateReadOperation<DevTools>;
            query: AggregateQueryOperation<DevTools>;
        };
        asyncOperations: DevTools$AsyncOperations;
        getDefaults: GetDefaultsOperation<DevTools>;
    }
    export interface Employee extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        firstName: string;
        lastName: string;
        name: string;
        site: Site;
        image: BinaryStream;
        _syncTick: string;
    }
    export interface EmployeeInput extends ClientNodeInput {
        id?: string;
        firstName?: string;
        lastName?: string;
        site?: integer | string;
        image?: BinaryStream;
        _syncTick?: decimal | string;
    }
    export interface EmployeeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        firstName: string;
        lastName: string;
        name: string;
        site: Site;
        image: BinaryStream;
        _syncTick: string;
    }
    export interface Employee$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Employee$Lookups {
        site: QueryOperation<Site>;
    }
    export interface Employee$Operations {
        query: QueryOperation<Employee>;
        read: ReadOperation<Employee>;
        aggregate: {
            read: AggregateReadOperation<Employee>;
            query: AggregateQueryOperation<Employee>;
        };
        create: CreateOperation<EmployeeInput, Employee>;
        getDuplicate: GetDuplicateOperation<Employee>;
        update: UpdateOperation<EmployeeInput, Employee>;
        updateById: UpdateByIdOperation<EmployeeInput, Employee>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Employee$AsyncOperations;
        lookups(dataOrId: string | { data: EmployeeInput }): Employee$Lookups;
        getDefaults: GetDefaultsOperation<Employee>;
    }
    export interface ExchangeRate extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        base: Currency;
        destination: Currency;
        dateRate: string;
        rate: string;
        divisor: string;
        shortDescription: string;
    }
    export interface ExchangeRateInput extends ClientNodeInput {
        base?: integer | string;
        destination?: integer | string;
        dateRate?: string;
        rate?: decimal | string;
        divisor?: decimal | string;
    }
    export interface ExchangeRateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        base: Currency;
        destination: Currency;
        dateRate: string;
        rate: string;
        divisor: string;
        shortDescription: string;
    }
    export interface ExchangeRate$Queries {
        convertRate: Node$Operation<
            {
                base?: string;
                destination?: string;
                amount?: decimal | string;
                rateDate?: string | null;
            },
            decimal
        >;
    }
    export interface ExchangeRate$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ExchangeRate$Lookups {
        base: QueryOperation<Currency>;
        destination: QueryOperation<Currency>;
    }
    export interface ExchangeRate$Operations {
        query: QueryOperation<ExchangeRate>;
        read: ReadOperation<ExchangeRate>;
        aggregate: {
            read: AggregateReadOperation<ExchangeRate>;
            query: AggregateQueryOperation<ExchangeRate>;
        };
        queries: ExchangeRate$Queries;
        create: CreateOperation<ExchangeRateInput, ExchangeRate>;
        getDuplicate: GetDuplicateOperation<ExchangeRate>;
        update: UpdateOperation<ExchangeRateInput, ExchangeRate>;
        updateById: UpdateByIdOperation<ExchangeRateInput, ExchangeRate>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ExchangeRate$AsyncOperations;
        lookups(dataOrId: string | { data: ExchangeRateInput }): ExchangeRate$Lookups;
        getDefaults: GetDefaultsOperation<ExchangeRate>;
    }
    export interface GhsClassification extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        hazard: string;
        pictogram: BinaryStream;
    }
    export interface GhsClassificationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        hazard?: string;
        pictogram?: BinaryStream;
    }
    export interface GhsClassificationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        hazard: string;
        pictogram: BinaryStream;
    }
    export interface GhsClassification$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface GhsClassification$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface GhsClassification$Operations {
        query: QueryOperation<GhsClassification>;
        read: ReadOperation<GhsClassification>;
        aggregate: {
            read: AggregateReadOperation<GhsClassification>;
            query: AggregateQueryOperation<GhsClassification>;
        };
        create: CreateOperation<GhsClassificationInput, GhsClassification>;
        getDuplicate: GetDuplicateOperation<GhsClassification>;
        update: UpdateOperation<GhsClassificationInput, GhsClassification>;
        updateById: UpdateByIdOperation<GhsClassificationInput, GhsClassification>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: GhsClassification$AsyncOperations;
        lookups(dataOrId: string | { data: GhsClassificationInput }): GhsClassification$Lookups;
        getDefaults: GetDefaultsOperation<GhsClassification>;
    }
    export interface Incoterm extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        description: string;
    }
    export interface IncotermInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
    }
    export interface IncotermBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        description: string;
    }
    export interface Incoterm$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Incoterm$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Incoterm$Operations {
        query: QueryOperation<Incoterm>;
        read: ReadOperation<Incoterm>;
        aggregate: {
            read: AggregateReadOperation<Incoterm>;
            query: AggregateQueryOperation<Incoterm>;
        };
        create: CreateOperation<IncotermInput, Incoterm>;
        getDuplicate: GetDuplicateOperation<Incoterm>;
        update: UpdateOperation<IncotermInput, Incoterm>;
        updateById: UpdateByIdOperation<IncotermInput, Incoterm>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Incoterm$AsyncOperations;
        lookups(dataOrId: string | { data: IncotermInput }): Incoterm$Lookups;
        getDefaults: GetDefaultsOperation<Incoterm>;
    }
    export interface IndirectCostOrigin extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
    }
    export interface IndirectCostOriginInput extends ClientNodeInput {
        id?: string;
        name?: string;
    }
    export interface IndirectCostOriginBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
    }
    export interface IndirectCostOrigin$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IndirectCostOrigin$Operations {
        query: QueryOperation<IndirectCostOrigin>;
        read: ReadOperation<IndirectCostOrigin>;
        aggregate: {
            read: AggregateReadOperation<IndirectCostOrigin>;
            query: AggregateQueryOperation<IndirectCostOrigin>;
        };
        create: CreateOperation<IndirectCostOriginInput, IndirectCostOrigin>;
        getDuplicate: GetDuplicateOperation<IndirectCostOrigin>;
        duplicate: DuplicateOperation<string, IndirectCostOriginInput, IndirectCostOrigin>;
        update: UpdateOperation<IndirectCostOriginInput, IndirectCostOrigin>;
        updateById: UpdateByIdOperation<IndirectCostOriginInput, IndirectCostOrigin>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IndirectCostOrigin$AsyncOperations;
        getDefaults: GetDefaultsOperation<IndirectCostOrigin>;
    }
    export interface IndirectCostSection extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        calculationMethod: CostCalculationMethod;
        lines: ClientCollection<IndirectCostSectionLine>;
    }
    export interface IndirectCostSectionInput extends ClientNodeInput {
        id?: string;
        name?: string;
        calculationMethod?: CostCalculationMethod;
        lines?: Partial<IndirectCostSectionLineInput>[];
    }
    export interface IndirectCostSectionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        calculationMethod: CostCalculationMethod;
        lines: ClientCollection<IndirectCostSectionLineBinding>;
    }
    export interface IndirectCostSection$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IndirectCostSection$Operations {
        query: QueryOperation<IndirectCostSection>;
        read: ReadOperation<IndirectCostSection>;
        aggregate: {
            read: AggregateReadOperation<IndirectCostSection>;
            query: AggregateQueryOperation<IndirectCostSection>;
        };
        create: CreateOperation<IndirectCostSectionInput, IndirectCostSection>;
        getDuplicate: GetDuplicateOperation<IndirectCostSection>;
        duplicate: DuplicateOperation<string, IndirectCostSectionInput, IndirectCostSection>;
        update: UpdateOperation<IndirectCostSectionInput, IndirectCostSection>;
        updateById: UpdateByIdOperation<IndirectCostSectionInput, IndirectCostSection>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IndirectCostSection$AsyncOperations;
        getDefaults: GetDefaultsOperation<IndirectCostSection>;
    }
    export interface IndirectCostSectionLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        indirectCostSection: IndirectCostSection;
        indirectCostOrigin: IndirectCostOrigin;
        percentage: string;
    }
    export interface IndirectCostSectionLineInput extends VitalClientNodeInput {
        indirectCostOrigin?: integer | string;
        percentage?: decimal | string;
    }
    export interface IndirectCostSectionLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        indirectCostSection: IndirectCostSection;
        indirectCostOrigin: IndirectCostOrigin;
        percentage: string;
    }
    export interface IndirectCostSectionLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IndirectCostSectionLine$Lookups {
        indirectCostOrigin: QueryOperation<IndirectCostOrigin>;
    }
    export interface IndirectCostSectionLine$Operations {
        query: QueryOperation<IndirectCostSectionLine>;
        read: ReadOperation<IndirectCostSectionLine>;
        aggregate: {
            read: AggregateReadOperation<IndirectCostSectionLine>;
            query: AggregateQueryOperation<IndirectCostSectionLine>;
        };
        create: CreateOperation<IndirectCostSectionLineInput, IndirectCostSectionLine>;
        getDuplicate: GetDuplicateOperation<IndirectCostSectionLine>;
        duplicate: DuplicateOperation<string, IndirectCostSectionLineInput, IndirectCostSectionLine>;
        update: UpdateOperation<IndirectCostSectionLineInput, IndirectCostSectionLine>;
        updateById: UpdateByIdOperation<IndirectCostSectionLineInput, IndirectCostSectionLine>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IndirectCostSectionLine$AsyncOperations;
        lookups(dataOrId: string | { data: IndirectCostSectionLineInput }): IndirectCostSectionLine$Lookups;
        getDefaults: GetDefaultsOperation<IndirectCostSectionLine>;
    }
    export interface Item extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
    }
    export interface ItemInput extends ClientNodeInput {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
    }
    export interface ItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
    }
    export interface Item$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Item$Lookups {
        stockUnit: QueryOperation<UnitOfMeasure>;
        volumeUnit: QueryOperation<UnitOfMeasure>;
        weightUnit: QueryOperation<UnitOfMeasure>;
        category: QueryOperation<ItemCategory>;
        bomRevisionSequenceNumber: QueryOperation<BomRevisionSequence>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        serialNumberSequenceNumber: QueryOperation<SequenceNumber>;
        salesUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        lotSequenceNumber: QueryOperation<SequenceNumber>;
    }
    export interface Item$Operations {
        query: QueryOperation<Item>;
        read: ReadOperation<Item>;
        aggregate: {
            read: AggregateReadOperation<Item>;
            query: AggregateQueryOperation<Item>;
        };
        create: CreateOperation<ItemInput, Item>;
        getDuplicate: GetDuplicateOperation<Item>;
        duplicate: DuplicateOperation<string, ItemInput, Item>;
        update: UpdateOperation<ItemInput, Item>;
        updateById: UpdateByIdOperation<ItemInput, Item>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Item$AsyncOperations;
        lookups(dataOrId: string | { data: ItemInput }): Item$Lookups;
        getDefaults: GetDefaultsOperation<Item>;
    }
    export interface ItemAllergen extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        allergen: Allergen;
    }
    export interface ItemAllergenInput extends VitalClientNodeInput {
        allergen?: integer | string;
    }
    export interface ItemAllergenBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        allergen: Allergen;
    }
    export interface ItemAllergen$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemAllergen$Lookups {
        allergen: QueryOperation<Allergen>;
    }
    export interface ItemAllergen$Operations {
        query: QueryOperation<ItemAllergen>;
        read: ReadOperation<ItemAllergen>;
        aggregate: {
            read: AggregateReadOperation<ItemAllergen>;
            query: AggregateQueryOperation<ItemAllergen>;
        };
        create: CreateOperation<ItemAllergenInput, ItemAllergen>;
        getDuplicate: GetDuplicateOperation<ItemAllergen>;
        duplicate: DuplicateOperation<string, ItemAllergenInput, ItemAllergen>;
        update: UpdateOperation<ItemAllergenInput, ItemAllergen>;
        updateById: UpdateByIdOperation<ItemAllergenInput, ItemAllergen>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemAllergen$AsyncOperations;
        lookups(dataOrId: string | { data: ItemAllergenInput }): ItemAllergen$Lookups;
        getDefaults: GetDefaultsOperation<ItemAllergen>;
    }
    export interface ItemCategory extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isSequenceNumberManagement: boolean;
        sequenceNumber: SequenceNumber;
        type: ItemCategoryType;
    }
    export interface ItemCategoryInput extends ClientNodeInput {
        id?: string;
        name?: string;
        isSequenceNumberManagement?: boolean | string;
        sequenceNumber?: integer | string;
        type?: ItemCategoryType;
    }
    export interface ItemCategoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isSequenceNumberManagement: boolean;
        sequenceNumber: SequenceNumber;
        type: ItemCategoryType;
    }
    export interface ItemCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemCategory$Lookups {
        sequenceNumber: QueryOperation<SequenceNumber>;
    }
    export interface ItemCategory$Operations {
        query: QueryOperation<ItemCategory>;
        read: ReadOperation<ItemCategory>;
        aggregate: {
            read: AggregateReadOperation<ItemCategory>;
            query: AggregateQueryOperation<ItemCategory>;
        };
        create: CreateOperation<ItemCategoryInput, ItemCategory>;
        getDuplicate: GetDuplicateOperation<ItemCategory>;
        update: UpdateOperation<ItemCategoryInput, ItemCategory>;
        updateById: UpdateByIdOperation<ItemCategoryInput, ItemCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemCategory$AsyncOperations;
        lookups(dataOrId: string | { data: ItemCategoryInput }): ItemCategory$Lookups;
        getDefaults: GetDefaultsOperation<ItemCategory>;
    }
    export interface ItemClassifications extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        classification: GhsClassification;
    }
    export interface ItemClassificationsInput extends VitalClientNodeInput {
        classification?: integer | string;
    }
    export interface ItemClassificationsBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        classification: GhsClassification;
    }
    export interface ItemClassifications$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemClassifications$Lookups {
        classification: QueryOperation<GhsClassification>;
    }
    export interface ItemClassifications$Operations {
        query: QueryOperation<ItemClassifications>;
        read: ReadOperation<ItemClassifications>;
        aggregate: {
            read: AggregateReadOperation<ItemClassifications>;
            query: AggregateQueryOperation<ItemClassifications>;
        };
        create: CreateOperation<ItemClassificationsInput, ItemClassifications>;
        getDuplicate: GetDuplicateOperation<ItemClassifications>;
        duplicate: DuplicateOperation<string, ItemClassificationsInput, ItemClassifications>;
        update: UpdateOperation<ItemClassificationsInput, ItemClassifications>;
        updateById: UpdateByIdOperation<ItemClassificationsInput, ItemClassifications>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemClassifications$AsyncOperations;
        lookups(dataOrId: string | { data: ItemClassificationsInput }): ItemClassifications$Lookups;
        getDefaults: GetDefaultsOperation<ItemClassifications>;
    }
    export interface ItemCustomer extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: Customer;
        isActive: boolean;
        item: Item;
        id: string;
        name: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
    }
    export interface ItemCustomerInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        item?: integer | string;
        id?: string;
        name?: string;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
    }
    export interface ItemCustomerBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: Customer;
        isActive: boolean;
        item: Item;
        id: string;
        name: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
    }
    export interface ItemCustomer$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemCustomer$Lookups {
        item: QueryOperation<Item>;
        salesUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface ItemCustomer$Operations {
        query: QueryOperation<ItemCustomer>;
        read: ReadOperation<ItemCustomer>;
        aggregate: {
            read: AggregateReadOperation<ItemCustomer>;
            query: AggregateQueryOperation<ItemCustomer>;
        };
        create: CreateOperation<ItemCustomerInput, ItemCustomer>;
        getDuplicate: GetDuplicateOperation<ItemCustomer>;
        update: UpdateOperation<ItemCustomerInput, ItemCustomer>;
        updateById: UpdateByIdOperation<ItemCustomerInput, ItemCustomer>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemCustomer$AsyncOperations;
        lookups(dataOrId: string | { data: ItemCustomerInput }): ItemCustomer$Lookups;
        getDefaults: GetDefaultsOperation<ItemCustomer>;
    }
    export interface ItemCustomerPrice extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: Customer;
        item: Item;
        isActive: boolean;
        priceReason: CustomerPriceReason;
        fromQuantity: string;
        toQuantity: string;
        endDate: string;
        salesSite: Site;
        stockSite: Site;
        validUnits: string[];
        unit: UnitOfMeasure;
        price: string;
        currency: Currency;
        discount: string;
        charge: string;
        startDate: string;
    }
    export interface ItemCustomerPriceInput extends VitalClientNodeInput {
        customer?: integer | string;
        isActive?: boolean | string;
        priceReason?: integer | string;
        fromQuantity?: decimal | string;
        toQuantity?: decimal | string;
        endDate?: string;
        salesSite?: integer | string;
        stockSite?: integer | string;
        unit?: integer | string;
        price?: decimal | string;
        currency?: integer | string;
        discount?: decimal | string;
        charge?: decimal | string;
        startDate?: string;
    }
    export interface ItemCustomerPriceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: Customer;
        item: Item;
        isActive: boolean;
        priceReason: CustomerPriceReason;
        fromQuantity: string;
        toQuantity: string;
        endDate: string;
        salesSite: Site;
        stockSite: Site;
        validUnits: string[];
        unit: UnitOfMeasure;
        price: string;
        currency: Currency;
        discount: string;
        charge: string;
        startDate: string;
    }
    export interface ItemCustomerPrice$Queries {
        getSalesPrice: Node$Operation<
            {
                priceParameters: {
                    site?: integer | string;
                    stockSite?: integer | string;
                    customer?: integer | string;
                    item: integer | string;
                    currency: integer | string;
                    quantity?: decimal | string;
                    unit: integer | string;
                    date?: string;
                };
            },
            {
                grossPrice: string;
                discount: string;
                charge: string;
                priceReason: CustomerPriceReason;
                error: string;
            }
        >;
    }
    export interface ItemCustomerPrice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemCustomerPrice$Lookups {
        customer: QueryOperation<Customer>;
        priceReason: QueryOperation<CustomerPriceReason>;
        salesSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface ItemCustomerPrice$Operations {
        query: QueryOperation<ItemCustomerPrice>;
        read: ReadOperation<ItemCustomerPrice>;
        aggregate: {
            read: AggregateReadOperation<ItemCustomerPrice>;
            query: AggregateQueryOperation<ItemCustomerPrice>;
        };
        queries: ItemCustomerPrice$Queries;
        create: CreateOperation<ItemCustomerPriceInput, ItemCustomerPrice>;
        getDuplicate: GetDuplicateOperation<ItemCustomerPrice>;
        duplicate: DuplicateOperation<string, ItemCustomerPriceInput, ItemCustomerPrice>;
        update: UpdateOperation<ItemCustomerPriceInput, ItemCustomerPrice>;
        updateById: UpdateByIdOperation<ItemCustomerPriceInput, ItemCustomerPrice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemCustomerPrice$AsyncOperations;
        lookups(dataOrId: string | { data: ItemCustomerPriceInput }): ItemCustomerPrice$Lookups;
        getDefaults: GetDefaultsOperation<ItemCustomerPrice>;
    }
    export interface ItemSite extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        economicOrderQuantity: string;
    }
    export interface ItemSiteInput extends VitalClientNodeInput {
        site?: integer | string;
        prodLeadTime?: integer | string;
        safetyStock?: decimal | string;
        batchQuantity?: decimal | string;
        purchaseLeadTime?: integer | string;
        isOrderToOrder?: boolean | string;
        replenishmentMethod?: ReplenishmentMethod;
        reorderPoint?: decimal | string;
        preferredProcess?: PreferredProcess;
        indirectCostSection?: integer | string;
        valuationMethod?: CostValuationMethod;
        outboundDefaultLocation?: integer | string;
        inboundDefaultLocation?: integer | string;
        completedProductDefaultLocation?: integer | string;
        economicOrderQuantity?: decimal | string;
    }
    export interface ItemSiteBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        economicOrderQuantity: string;
    }
    export interface ItemSite$Queries {
        getValuedItemSite: Node$Operation<
            {
                searchCriteria?: {
                    company?: integer | string;
                    stockSiteList?: string[];
                    itemRange?: {
                        start?: string;
                        end?: string;
                    };
                    statusList?: ItemStatus[];
                    valuationMethods?: CostValuationMethod[];
                    preferredProcesses?: PreferredProcess[];
                    replenishmentMethods?: ReplenishmentMethod[];
                    itemCategory?: integer | string;
                    commodityCode?: string;
                };
            },
            ItemSite[]
        >;
    }
    export interface ItemSite$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemSite$Lookups {
        site: QueryOperation<Site>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        defaultSupplier: QueryOperation<Supplier>;
        indirectCostSection: QueryOperation<IndirectCostSection>;
        outboundDefaultLocation: QueryOperation<Location>;
        inboundDefaultLocation: QueryOperation<Location>;
        completedProductDefaultLocation: QueryOperation<Location>;
    }
    export interface ItemSite$Operations {
        query: QueryOperation<ItemSite>;
        read: ReadOperation<ItemSite>;
        aggregate: {
            read: AggregateReadOperation<ItemSite>;
            query: AggregateQueryOperation<ItemSite>;
        };
        queries: ItemSite$Queries;
        create: CreateOperation<ItemSiteInput, ItemSite>;
        getDuplicate: GetDuplicateOperation<ItemSite>;
        duplicate: DuplicateOperation<string, ItemSiteInput, ItemSite>;
        update: UpdateOperation<ItemSiteInput, ItemSite>;
        updateById: UpdateByIdOperation<ItemSiteInput, ItemSite>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemSite$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSiteInput }): ItemSite$Lookups;
        getDefaults: GetDefaultsOperation<ItemSite>;
    }
    export interface ItemSiteCost extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
    }
    export interface ItemSiteCostInput extends ClientNodeInput {
        itemSite?: integer | string;
        costCategory?: integer | string;
        fromDate?: string;
        toDate?: string;
        version?: integer | string;
        forQuantity?: decimal | string;
        isCalculated?: boolean | string;
        materialCost?: decimal | string;
        machineCost?: decimal | string;
        laborCost?: decimal | string;
        toolCost?: decimal | string;
        indirectCost?: decimal | string;
    }
    export interface ItemSiteCostBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
    }
    export interface ItemSiteCost$Queries {
        getItemSiteCost: Node$Operation<
            {
                effectiveDate: string;
                item: string;
                site: string;
            },
            string
        >;
    }
    export interface ItemSiteCost$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemSiteCost$Lookups {
        itemSite: QueryOperation<ItemSite>;
        costCategory: QueryOperation<CostCategory>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface ItemSiteCost$Operations {
        query: QueryOperation<ItemSiteCost>;
        read: ReadOperation<ItemSiteCost>;
        aggregate: {
            read: AggregateReadOperation<ItemSiteCost>;
            query: AggregateQueryOperation<ItemSiteCost>;
        };
        queries: ItemSiteCost$Queries;
        create: CreateOperation<ItemSiteCostInput, ItemSiteCost>;
        getDuplicate: GetDuplicateOperation<ItemSiteCost>;
        update: UpdateOperation<ItemSiteCostInput, ItemSiteCost>;
        updateById: UpdateByIdOperation<ItemSiteCostInput, ItemSiteCost>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemSiteCost$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSiteCostInput }): ItemSiteCost$Lookups;
        getDefaults: GetDefaultsOperation<ItemSiteCost>;
    }
    export interface ItemSiteSupplier extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        supplier: Supplier;
        isDefaultItemSupplier: boolean;
        purchaseLeadTime: integer;
        item: Item;
        site: Site;
        purchaseUnit: UnitOfMeasure;
        itemSupplier: ItemSupplier;
        priority: integer;
        minimumPurchaseOrderQuantity: string;
    }
    export interface ItemSiteSupplierInput extends ClientNodeInput {
        itemSite?: integer | string;
        supplier?: integer | string;
        isDefaultItemSupplier?: boolean | string;
        purchaseLeadTime?: integer | string;
        itemSupplier?: integer | string;
        priority?: integer | string;
        minimumPurchaseOrderQuantity?: decimal | string;
    }
    export interface ItemSiteSupplierBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        supplier: Supplier;
        isDefaultItemSupplier: boolean;
        purchaseLeadTime: integer;
        item: Item;
        site: Site;
        purchaseUnit: UnitOfMeasure;
        itemSupplier: ItemSupplier;
        priority: integer;
        minimumPurchaseOrderQuantity: string;
    }
    export interface ItemSiteSupplier$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemSiteSupplier$Lookups {
        itemSite: QueryOperation<ItemSite>;
        supplier: QueryOperation<Supplier>;
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        itemSupplier: QueryOperation<ItemSupplier>;
    }
    export interface ItemSiteSupplier$Operations {
        query: QueryOperation<ItemSiteSupplier>;
        read: ReadOperation<ItemSiteSupplier>;
        aggregate: {
            read: AggregateReadOperation<ItemSiteSupplier>;
            query: AggregateQueryOperation<ItemSiteSupplier>;
        };
        create: CreateOperation<ItemSiteSupplierInput, ItemSiteSupplier>;
        getDuplicate: GetDuplicateOperation<ItemSiteSupplier>;
        update: UpdateOperation<ItemSiteSupplierInput, ItemSiteSupplier>;
        updateById: UpdateByIdOperation<ItemSiteSupplierInput, ItemSiteSupplier>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemSiteSupplier$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSiteSupplierInput }): ItemSiteSupplier$Lookups;
        getDefaults: GetDefaultsOperation<ItemSiteSupplier>;
    }
    export interface ItemSupplier extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        item: Item;
        supplier: Supplier;
        supplierItemCode: string;
        supplierItemName: string;
        supplierPriority: integer;
        isDefaultItemSupplier: boolean;
        purchaseUnitOfMeasure: UnitOfMeasure;
        minimumPurchaseQuantity: string;
        purchaseLeadTime: integer;
        id: string;
    }
    export interface ItemSupplierInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        item?: integer | string;
        supplierItemCode?: string;
        supplierItemName?: string;
        supplierPriority?: integer | string;
        isDefaultItemSupplier?: boolean | string;
        purchaseUnitOfMeasure?: integer | string;
        minimumPurchaseQuantity?: decimal | string;
        purchaseLeadTime?: integer | string;
    }
    export interface ItemSupplierBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        item: Item;
        supplier: Supplier;
        supplierItemCode: string;
        supplierItemName: string;
        supplierPriority: integer;
        isDefaultItemSupplier: boolean;
        purchaseUnitOfMeasure: UnitOfMeasure;
        minimumPurchaseQuantity: string;
        purchaseLeadTime: integer;
        id: string;
    }
    export interface ItemSupplier$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemSupplier$Lookups {
        item: QueryOperation<Item>;
        purchaseUnitOfMeasure: QueryOperation<UnitOfMeasure>;
    }
    export interface ItemSupplier$Operations {
        query: QueryOperation<ItemSupplier>;
        read: ReadOperation<ItemSupplier>;
        aggregate: {
            read: AggregateReadOperation<ItemSupplier>;
            query: AggregateQueryOperation<ItemSupplier>;
        };
        create: CreateOperation<ItemSupplierInput, ItemSupplier>;
        getDuplicate: GetDuplicateOperation<ItemSupplier>;
        update: UpdateOperation<ItemSupplierInput, ItemSupplier>;
        updateById: UpdateByIdOperation<ItemSupplierInput, ItemSupplier>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemSupplier$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSupplierInput }): ItemSupplier$Lookups;
        getDefaults: GetDefaultsOperation<ItemSupplier>;
    }
    export interface ItemSupplierPrice extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        item: Item;
        supplier: Supplier;
        itemSupplier: ItemSupplier;
        unit: UnitOfMeasure;
        dateValid: string;
        dateValidFrom: string;
        dateValidTo: string;
        currency: Currency;
        price: string;
        type: ItemPriceType;
        priority: integer;
        fromQuantity: string;
        toQuantity: string;
    }
    export interface ItemSupplierPriceInput extends VitalClientNodeInput {
        site?: integer | string;
        supplier?: integer | string;
        unit?: integer | string;
        dateValid?: string;
        currency?: integer | string;
        price?: decimal | string;
        type?: ItemPriceType;
        priority?: integer | string;
        fromQuantity?: decimal | string;
        toQuantity?: decimal | string;
    }
    export interface ItemSupplierPriceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        item: Item;
        supplier: Supplier;
        itemSupplier: ItemSupplier;
        unit: UnitOfMeasure;
        dateValid: string;
        dateValidFrom: string;
        dateValidTo: string;
        currency: Currency;
        price: string;
        type: ItemPriceType;
        priority: integer;
        fromQuantity: string;
        toQuantity: string;
    }
    export interface ItemSupplierPrice$Queries {
        getPurchasePrice: Node$Operation<
            {
                priceParameters: {
                    site?: integer | string;
                    supplier?: integer | string;
                    item: integer | string;
                    currency: integer | string;
                    quantity?: decimal | string;
                    unit: integer | string;
                    date?: string;
                    convertUnit?: boolean | string;
                };
            },
            string
        >;
    }
    export interface ItemSupplierPrice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemSupplierPrice$Lookups {
        site: QueryOperation<Site>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface ItemSupplierPrice$Operations {
        query: QueryOperation<ItemSupplierPrice>;
        read: ReadOperation<ItemSupplierPrice>;
        aggregate: {
            read: AggregateReadOperation<ItemSupplierPrice>;
            query: AggregateQueryOperation<ItemSupplierPrice>;
        };
        queries: ItemSupplierPrice$Queries;
        create: CreateOperation<ItemSupplierPriceInput, ItemSupplierPrice>;
        getDuplicate: GetDuplicateOperation<ItemSupplierPrice>;
        duplicate: DuplicateOperation<string, ItemSupplierPriceInput, ItemSupplierPrice>;
        update: UpdateOperation<ItemSupplierPriceInput, ItemSupplierPrice>;
        updateById: UpdateByIdOperation<ItemSupplierPriceInput, ItemSupplierPrice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemSupplierPrice$AsyncOperations;
        lookups(dataOrId: string | { data: ItemSupplierPriceInput }): ItemSupplierPrice$Lookups;
        getDefaults: GetDefaultsOperation<ItemSupplierPrice>;
    }
    export interface LicensePlateNumber extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        container: Container;
        consumedCapacity: integer;
        location: Location;
        owner: Supplier;
        isSingleItem: boolean;
        isSingleLot: boolean;
    }
    export interface LicensePlateNumberInput extends ClientNodeInput {
        number?: string;
        container?: integer | string;
        location?: integer | string;
        owner?: integer | string;
        isSingleItem?: boolean | string;
        isSingleLot?: boolean | string;
    }
    export interface LicensePlateNumberBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        container: Container;
        consumedCapacity: integer;
        location: Location;
        owner: Supplier;
        isSingleItem: boolean;
        isSingleLot: boolean;
    }
    export interface LicensePlateNumber$Mutations {
        createBulkLicensePlateNumbers: Node$Operation<
            {
                containerId: string;
                isSingleItem: boolean | string;
                isSingleLot: boolean | string;
                numberToCreate: integer | string;
                locationId?: string;
            },
            {
                number: string;
                _id: string;
            }[]
        >;
    }
    export interface LicensePlateNumber$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LicensePlateNumber$Lookups {
        container: QueryOperation<Container>;
        location: QueryOperation<Location>;
        owner: QueryOperation<Supplier>;
    }
    export interface LicensePlateNumber$Operations {
        query: QueryOperation<LicensePlateNumber>;
        read: ReadOperation<LicensePlateNumber>;
        aggregate: {
            read: AggregateReadOperation<LicensePlateNumber>;
            query: AggregateQueryOperation<LicensePlateNumber>;
        };
        create: CreateOperation<LicensePlateNumberInput, LicensePlateNumber>;
        getDuplicate: GetDuplicateOperation<LicensePlateNumber>;
        duplicate: DuplicateOperation<string, LicensePlateNumberInput, LicensePlateNumber>;
        update: UpdateOperation<LicensePlateNumberInput, LicensePlateNumber>;
        updateById: UpdateByIdOperation<LicensePlateNumberInput, LicensePlateNumber>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: LicensePlateNumber$Mutations;
        asyncOperations: LicensePlateNumber$AsyncOperations;
        lookups(dataOrId: string | { data: LicensePlateNumberInput }): LicensePlateNumber$Lookups;
        getDefaults: GetDefaultsOperation<LicensePlateNumber>;
    }
    export interface Location extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        dangerousGoodAllowed: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        locationZone: LocationZone;
        locationType: LocationType;
        site: Site;
    }
    export interface LocationInput extends ClientNodeInput {
        id?: string;
        name?: string;
        isActive?: boolean | string;
        dangerousGoodAllowed?: boolean | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        locationZone?: integer | string;
        locationType?: integer | string;
    }
    export interface LocationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        dangerousGoodAllowed: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        locationZone: LocationZone;
        locationType: LocationType;
        site: Site;
    }
    export interface Location$Mutations {
        getLocations: Node$Operation<
            {
                locationSequence: string;
                requiredCombinations: integer | string;
            },
            {
                id: string;
                name: string;
            }[]
        >;
        createBulkLocations: Node$Operation<
            {
                locations?: {
                    id?: string;
                    name?: string;
                    locationType?: integer | string;
                    locationZone?: integer | string;
                }[];
                locationSequence: string;
            },
            integer
        >;
    }
    export interface Location$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Location$Lookups {
        locationZone: QueryOperation<LocationZone>;
        locationType: QueryOperation<LocationType>;
        site: QueryOperation<Site>;
    }
    export interface Location$Operations {
        query: QueryOperation<Location>;
        read: ReadOperation<Location>;
        aggregate: {
            read: AggregateReadOperation<Location>;
            query: AggregateQueryOperation<Location>;
        };
        create: CreateOperation<LocationInput, Location>;
        getDuplicate: GetDuplicateOperation<Location>;
        duplicate: DuplicateOperation<string, LocationInput, Location>;
        update: UpdateOperation<LocationInput, Location>;
        updateById: UpdateByIdOperation<LocationInput, Location>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Location$Mutations;
        asyncOperations: Location$AsyncOperations;
        lookups(dataOrId: string | { data: LocationInput }): Location$Lookups;
        getDefaults: GetDefaultsOperation<Location>;
    }
    export interface LocationType extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        description: string;
        name: string;
        locationCategory: LocationCategory;
    }
    export interface LocationTypeInput extends ClientNodeInput {
        id?: string;
        description?: string;
        name?: string;
        locationCategory?: LocationCategory;
    }
    export interface LocationTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        description: string;
        name: string;
        locationCategory: LocationCategory;
    }
    export interface LocationType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LocationType$Operations {
        query: QueryOperation<LocationType>;
        read: ReadOperation<LocationType>;
        aggregate: {
            read: AggregateReadOperation<LocationType>;
            query: AggregateQueryOperation<LocationType>;
        };
        create: CreateOperation<LocationTypeInput, LocationType>;
        getDuplicate: GetDuplicateOperation<LocationType>;
        duplicate: DuplicateOperation<string, LocationTypeInput, LocationType>;
        update: UpdateOperation<LocationTypeInput, LocationType>;
        updateById: UpdateByIdOperation<LocationTypeInput, LocationType>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LocationType$AsyncOperations;
        getDefaults: GetDefaultsOperation<LocationType>;
    }
    export interface LocationZone extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        id: string;
        name: string;
        locations: ClientCollection<Location>;
        zoneType: ZoneType;
    }
    export interface LocationZoneInput extends ClientNodeInput {
        site?: integer | string;
        id?: string;
        name?: string;
        zoneType?: ZoneType;
    }
    export interface LocationZoneBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        id: string;
        name: string;
        locations: ClientCollection<Location>;
        zoneType: ZoneType;
    }
    export interface LocationZone$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LocationZone$Lookups {
        site: QueryOperation<Site>;
    }
    export interface LocationZone$Operations {
        query: QueryOperation<LocationZone>;
        read: ReadOperation<LocationZone>;
        aggregate: {
            read: AggregateReadOperation<LocationZone>;
            query: AggregateQueryOperation<LocationZone>;
        };
        create: CreateOperation<LocationZoneInput, LocationZone>;
        getDuplicate: GetDuplicateOperation<LocationZone>;
        duplicate: DuplicateOperation<string, LocationZoneInput, LocationZone>;
        update: UpdateOperation<LocationZoneInput, LocationZone>;
        updateById: UpdateByIdOperation<LocationZoneInput, LocationZone>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LocationZone$AsyncOperations;
        lookups(dataOrId: string | { data: LocationZoneInput }): LocationZone$Lookups;
        getDefaults: GetDefaultsOperation<LocationZone>;
    }
    export interface PaymentTerm extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        businessEntityType: BusinessEntityType;
        description: string;
        dueDateType: DueDateType;
        days: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        penaltyType: PaymentTermDiscountOrPenaltyType;
        penaltyAmount: string;
    }
    export interface PaymentTermInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        businessEntityType?: BusinessEntityType;
        description?: string;
        dueDateType?: DueDateType;
        days?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        penaltyType?: PaymentTermDiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
    }
    export interface PaymentTermBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        businessEntityType: BusinessEntityType;
        description: string;
        dueDateType: DueDateType;
        days: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        penaltyType: PaymentTermDiscountOrPenaltyType;
        penaltyAmount: string;
    }
    export interface PaymentTerm$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PaymentTerm$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface PaymentTerm$Operations {
        query: QueryOperation<PaymentTerm>;
        read: ReadOperation<PaymentTerm>;
        aggregate: {
            read: AggregateReadOperation<PaymentTerm>;
            query: AggregateQueryOperation<PaymentTerm>;
        };
        create: CreateOperation<PaymentTermInput, PaymentTerm>;
        getDuplicate: GetDuplicateOperation<PaymentTerm>;
        duplicate: DuplicateOperation<string, PaymentTermInput, PaymentTerm>;
        update: UpdateOperation<PaymentTermInput, PaymentTerm>;
        updateById: UpdateByIdOperation<PaymentTermInput, PaymentTerm>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: PaymentTerm$AsyncOperations;
        lookups(dataOrId: string | { data: PaymentTermInput }): PaymentTerm$Lookups;
        getDefaults: GetDefaultsOperation<PaymentTerm>;
    }
    export interface ReasonCode extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface ReasonCodeInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
    }
    export interface ReasonCodeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface ReasonCode$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReasonCode$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ReasonCode$Operations {
        query: QueryOperation<ReasonCode>;
        read: ReadOperation<ReasonCode>;
        aggregate: {
            read: AggregateReadOperation<ReasonCode>;
            query: AggregateQueryOperation<ReasonCode>;
        };
        create: CreateOperation<ReasonCodeInput, ReasonCode>;
        getDuplicate: GetDuplicateOperation<ReasonCode>;
        duplicate: DuplicateOperation<string, ReasonCodeInput, ReasonCode>;
        update: UpdateOperation<ReasonCodeInput, ReasonCode>;
        updateById: UpdateByIdOperation<ReasonCodeInput, ReasonCode>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReasonCode$AsyncOperations;
        lookups(dataOrId: string | { data: ReasonCodeInput }): ReasonCode$Lookups;
        getDefaults: GetDefaultsOperation<ReasonCode>;
    }
    export interface ResourceCostCategory extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resource: BaseResource;
        costCategory: CostCategory;
        setupCost: string;
        runCost: string;
        indirectCostSection: IndirectCostSection;
        costUnit: UnitOfMeasure;
    }
    export interface ResourceCostCategoryInput extends VitalClientNodeInput {
        costCategory?: integer | string;
        setupCost?: decimal | string;
        runCost?: decimal | string;
        indirectCostSection?: integer | string;
        costUnit?: integer | string;
    }
    export interface ResourceCostCategoryBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resource: BaseResource;
        costCategory: CostCategory;
        setupCost: string;
        runCost: string;
        indirectCostSection: IndirectCostSection;
        costUnit: UnitOfMeasure;
    }
    export interface ResourceCostCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ResourceCostCategory$Lookups {
        costCategory: QueryOperation<CostCategory>;
        indirectCostSection: QueryOperation<IndirectCostSection>;
        costUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface ResourceCostCategory$Operations {
        query: QueryOperation<ResourceCostCategory>;
        read: ReadOperation<ResourceCostCategory>;
        aggregate: {
            read: AggregateReadOperation<ResourceCostCategory>;
            query: AggregateQueryOperation<ResourceCostCategory>;
        };
        create: CreateOperation<ResourceCostCategoryInput, ResourceCostCategory>;
        getDuplicate: GetDuplicateOperation<ResourceCostCategory>;
        duplicate: DuplicateOperation<string, ResourceCostCategoryInput, ResourceCostCategory>;
        update: UpdateOperation<ResourceCostCategoryInput, ResourceCostCategory>;
        updateById: UpdateByIdOperation<ResourceCostCategoryInput, ResourceCostCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ResourceCostCategory$AsyncOperations;
        lookups(dataOrId: string | { data: ResourceCostCategoryInput }): ResourceCostCategory$Lookups;
        getDefaults: GetDefaultsOperation<ResourceCostCategory>;
    }
    export interface ResourceGroupReplacement extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resourceGroup: GroupResource;
        replacement: GroupResource;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface ResourceGroupReplacementInput extends VitalClientNodeInput {
        replacement?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface ResourceGroupReplacementBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        resourceGroup: GroupResource;
        replacement: GroupResource;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface ResourceGroupReplacement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ResourceGroupReplacement$Lookups {
        replacement: QueryOperation<GroupResource>;
    }
    export interface ResourceGroupReplacement$Operations {
        query: QueryOperation<ResourceGroupReplacement>;
        read: ReadOperation<ResourceGroupReplacement>;
        aggregate: {
            read: AggregateReadOperation<ResourceGroupReplacement>;
            query: AggregateQueryOperation<ResourceGroupReplacement>;
        };
        create: CreateOperation<ResourceGroupReplacementInput, ResourceGroupReplacement>;
        getDuplicate: GetDuplicateOperation<ResourceGroupReplacement>;
        duplicate: DuplicateOperation<string, ResourceGroupReplacementInput, ResourceGroupReplacement>;
        update: UpdateOperation<ResourceGroupReplacementInput, ResourceGroupReplacement>;
        updateById: UpdateByIdOperation<ResourceGroupReplacementInput, ResourceGroupReplacement>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ResourceGroupReplacement$AsyncOperations;
        lookups(dataOrId: string | { data: ResourceGroupReplacementInput }): ResourceGroupReplacement$Lookups;
        getDefaults: GetDefaultsOperation<ResourceGroupReplacement>;
    }
    export interface SequenceNumberAssignment extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        site: Site;
        company: Company;
        legislation: Legislation;
        sequenceNumber: SequenceNumber;
        sequenceNumberAssignmentDocumentType: SequenceNumberAssignmentDocumentType;
        isDefaultAssignment: boolean;
        isAssignOnPosting: boolean;
        isUsed: boolean;
        isActive: boolean;
    }
    export interface SequenceNumberAssignmentInput extends ClientNodeInput {
        _vendor?: integer | string;
        site?: integer | string;
        company?: integer | string;
        legislation?: integer | string;
        sequenceNumber?: integer | string;
        sequenceNumberAssignmentDocumentType?: integer | string;
        isDefaultAssignment?: boolean | string;
        isAssignOnPosting?: boolean | string;
    }
    export interface SequenceNumberAssignmentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        site: Site;
        company: Company;
        legislation: Legislation;
        sequenceNumber: SequenceNumber;
        sequenceNumberAssignmentDocumentType: SequenceNumberAssignmentDocumentType;
        isDefaultAssignment: boolean;
        isAssignOnPosting: boolean;
        isUsed: boolean;
        isActive: boolean;
    }
    export interface SequenceNumberAssignment$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberAssignment$Lookups {
        _vendor: QueryOperation<SysVendor>;
        site: QueryOperation<Site>;
        company: QueryOperation<Company>;
        legislation: QueryOperation<Legislation>;
        sequenceNumber: QueryOperation<SequenceNumber>;
        sequenceNumberAssignmentDocumentType: QueryOperation<SequenceNumberAssignmentDocumentType>;
    }
    export interface SequenceNumberAssignment$Operations {
        query: QueryOperation<SequenceNumberAssignment>;
        read: ReadOperation<SequenceNumberAssignment>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberAssignment>;
            query: AggregateQueryOperation<SequenceNumberAssignment>;
        };
        create: CreateOperation<SequenceNumberAssignmentInput, SequenceNumberAssignment>;
        getDuplicate: GetDuplicateOperation<SequenceNumberAssignment>;
        update: UpdateOperation<SequenceNumberAssignmentInput, SequenceNumberAssignment>;
        updateById: UpdateByIdOperation<SequenceNumberAssignmentInput, SequenceNumberAssignment>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumberAssignment$AsyncOperations;
        lookups(dataOrId: string | { data: SequenceNumberAssignmentInput }): SequenceNumberAssignment$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumberAssignment>;
    }
    export interface SequenceNumberAssignmentDocumentType extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumberAssignmentModule: SequenceNumberAssignmentModule;
        nodeFactory: MetaNodeFactory;
        nodeValues: string[];
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        displayOrder: integer;
    }
    export interface SequenceNumberAssignmentDocumentTypeInput extends ClientNodeInput {
        _vendor?: integer | string;
        sequenceNumberAssignmentModule?: integer | string;
        nodeFactory?: integer | string;
        displayOrder?: integer | string;
    }
    export interface SequenceNumberAssignmentDocumentTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumberAssignmentModule: SequenceNumberAssignmentModule;
        nodeFactory: MetaNodeFactory;
        nodeValues: string[];
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        displayOrder: integer;
    }
    export interface SequenceNumberAssignmentDocumentType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberAssignmentDocumentType$Lookups {
        _vendor: QueryOperation<SysVendor>;
        sequenceNumberAssignmentModule: QueryOperation<SequenceNumberAssignmentModule>;
        nodeFactory: QueryOperation<MetaNodeFactory>;
    }
    export interface SequenceNumberAssignmentDocumentType$Operations {
        query: QueryOperation<SequenceNumberAssignmentDocumentType>;
        read: ReadOperation<SequenceNumberAssignmentDocumentType>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberAssignmentDocumentType>;
            query: AggregateQueryOperation<SequenceNumberAssignmentDocumentType>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumberAssignmentDocumentType$AsyncOperations;
        lookups(
            dataOrId: string | { data: SequenceNumberAssignmentDocumentTypeInput },
        ): SequenceNumberAssignmentDocumentType$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumberAssignmentDocumentType>;
    }
    export interface SequenceNumberAssignmentModule extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        nodes: ClientCollection<SequenceNumberAssignmentDocumentType>;
    }
    export interface SequenceNumberAssignmentModuleInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
    }
    export interface SequenceNumberAssignmentModuleBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        nodes: ClientCollection<SequenceNumberAssignmentDocumentType>;
    }
    export interface SequenceNumberAssignmentModule$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberAssignmentModule$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SequenceNumberAssignmentModule$Operations {
        query: QueryOperation<SequenceNumberAssignmentModule>;
        read: ReadOperation<SequenceNumberAssignmentModule>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberAssignmentModule>;
            query: AggregateQueryOperation<SequenceNumberAssignmentModule>;
        };
        create: CreateOperation<SequenceNumberAssignmentModuleInput, SequenceNumberAssignmentModule>;
        getDuplicate: GetDuplicateOperation<SequenceNumberAssignmentModule>;
        update: UpdateOperation<SequenceNumberAssignmentModuleInput, SequenceNumberAssignmentModule>;
        updateById: UpdateByIdOperation<SequenceNumberAssignmentModuleInput, SequenceNumberAssignmentModule>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumberAssignmentModule$AsyncOperations;
        lookups(
            dataOrId: string | { data: SequenceNumberAssignmentModuleInput },
        ): SequenceNumberAssignmentModule$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumberAssignmentModule>;
    }
    export interface SequenceNumberAssignmentSetup extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        modules: ClientCollection<SequenceNumberAssignmentModule>;
    }
    export interface SequenceNumberAssignmentSetupInput extends ClientNodeInput {}
    export interface SequenceNumberAssignmentSetupBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        modules: ClientCollection<SequenceNumberAssignmentModule>;
    }
    export interface SequenceNumberAssignmentSetup$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberAssignmentSetup$Operations {
        query: QueryOperation<SequenceNumberAssignmentSetup>;
        read: ReadOperation<SequenceNumberAssignmentSetup>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberAssignmentSetup>;
            query: AggregateQueryOperation<SequenceNumberAssignmentSetup>;
        };
        asyncOperations: SequenceNumberAssignmentSetup$AsyncOperations;
        getDefaults: GetDefaultsOperation<SequenceNumberAssignmentSetup>;
    }
    export interface SequenceNumberValue extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: SequenceNumber;
        company: Company;
        site: Site;
        period: integer;
        additionalInfo: string;
        sequenceValue: integer;
        periodDate: string;
    }
    export interface SequenceNumberValueInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        company?: integer | string;
        site?: integer | string;
        period?: integer | string;
        additionalInfo?: string;
        sequenceValue?: integer | string;
        periodDate?: string;
    }
    export interface SequenceNumberValueBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: SequenceNumber;
        company: Company;
        site: Site;
        period: integer;
        additionalInfo: string;
        sequenceValue: integer;
        periodDate: string;
    }
    export interface SequenceNumberValue$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberValue$Lookups {
        _vendor: QueryOperation<SysVendor>;
        company: QueryOperation<Company>;
        site: QueryOperation<Site>;
    }
    export interface SequenceNumberValue$Operations {
        query: QueryOperation<SequenceNumberValue>;
        read: ReadOperation<SequenceNumberValue>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberValue>;
            query: AggregateQueryOperation<SequenceNumberValue>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumberValue$AsyncOperations;
        lookups(dataOrId: string | { data: SequenceNumberValueInput }): SequenceNumberValue$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumberValue>;
    }
    export interface ShiftDetail extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        duration: string;
        formattedDuration: string;
        shiftStart: string;
        shiftEnd: string;
    }
    export interface ShiftDetailInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        shiftStart?: string;
        shiftEnd?: string;
    }
    export interface ShiftDetailBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        duration: string;
        formattedDuration: string;
        shiftStart: string;
        shiftEnd: string;
    }
    export interface ShiftDetail$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShiftDetail$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ShiftDetail$Operations {
        query: QueryOperation<ShiftDetail>;
        read: ReadOperation<ShiftDetail>;
        aggregate: {
            read: AggregateReadOperation<ShiftDetail>;
            query: AggregateQueryOperation<ShiftDetail>;
        };
        create: CreateOperation<ShiftDetailInput, ShiftDetail>;
        getDuplicate: GetDuplicateOperation<ShiftDetail>;
        duplicate: DuplicateOperation<string, ShiftDetailInput, ShiftDetail>;
        update: UpdateOperation<ShiftDetailInput, ShiftDetail>;
        updateById: UpdateByIdOperation<ShiftDetailInput, ShiftDetail>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShiftDetail$AsyncOperations;
        lookups(dataOrId: string | { data: ShiftDetailInput }): ShiftDetail$Lookups;
        getDefaults: GetDefaultsOperation<ShiftDetail>;
    }
    export interface Standard extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        sdo: string;
        code: string;
        name: string;
        version: string;
        industrySector: string;
    }
    export interface StandardInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        sdo?: string;
        code?: string;
        name?: string;
        version?: string;
        industrySector?: string;
    }
    export interface StandardBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        sdo: string;
        code: string;
        name: string;
        version: string;
        industrySector: string;
    }
    export interface Standard$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Standard$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Standard$Operations {
        query: QueryOperation<Standard>;
        read: ReadOperation<Standard>;
        aggregate: {
            read: AggregateReadOperation<Standard>;
            query: AggregateQueryOperation<Standard>;
        };
        create: CreateOperation<StandardInput, Standard>;
        getDuplicate: GetDuplicateOperation<Standard>;
        duplicate: DuplicateOperation<string, StandardInput, Standard>;
        update: UpdateOperation<StandardInput, Standard>;
        updateById: UpdateByIdOperation<StandardInput, Standard>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Standard$AsyncOperations;
        lookups(dataOrId: string | { data: StandardInput }): Standard$Lookups;
        getDefaults: GetDefaultsOperation<Standard>;
    }
    export interface StandardIndustrialClassification extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        legislation: Legislation;
        sicCode: string;
        sicDescription: string;
    }
    export interface StandardIndustrialClassificationInput extends ClientNodeInput {
        _vendor?: integer | string;
        legislation?: integer | string;
        sicCode?: string;
        sicDescription?: string;
    }
    export interface StandardIndustrialClassificationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        legislation: Legislation;
        sicCode: string;
        sicDescription: string;
    }
    export interface StandardIndustrialClassification$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StandardIndustrialClassification$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface StandardIndustrialClassification$Operations {
        query: QueryOperation<StandardIndustrialClassification>;
        read: ReadOperation<StandardIndustrialClassification>;
        aggregate: {
            read: AggregateReadOperation<StandardIndustrialClassification>;
            query: AggregateQueryOperation<StandardIndustrialClassification>;
        };
        asyncOperations: StandardIndustrialClassification$AsyncOperations;
        lookups(
            dataOrId: string | { data: StandardIndustrialClassificationInput },
        ): StandardIndustrialClassification$Lookups;
        getDefaults: GetDefaultsOperation<StandardIndustrialClassification>;
    }
    export interface Team extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        site: Site;
    }
    export interface TeamInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        site?: integer | string;
    }
    export interface TeamBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        site: Site;
    }
    export interface Team$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Team$Lookups {
        site: QueryOperation<Site>;
    }
    export interface Team$Operations {
        query: QueryOperation<Team>;
        read: ReadOperation<Team>;
        aggregate: {
            read: AggregateReadOperation<Team>;
            query: AggregateQueryOperation<Team>;
        };
        create: CreateOperation<TeamInput, Team>;
        getDuplicate: GetDuplicateOperation<Team>;
        update: UpdateOperation<TeamInput, Team>;
        updateById: UpdateByIdOperation<TeamInput, Team>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Team$AsyncOperations;
        lookups(dataOrId: string | { data: TeamInput }): Team$Lookups;
        getDefaults: GetDefaultsOperation<Team>;
    }
    export interface UnitConversionFactor extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        toUnit: UnitOfMeasure;
        coefficient: string;
        item: Item;
        supplier: Supplier;
        customer: Customer;
        type: UnitConversionType;
        isStandard: boolean;
        fromUnit: UnitOfMeasure;
    }
    export interface UnitConversionFactorInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        coefficient?: decimal | string;
        item?: integer | string;
        supplier?: integer | string;
        customer?: integer | string;
        type?: UnitConversionType;
        isStandard?: boolean | string;
        fromUnit?: integer | string;
    }
    export interface UnitConversionFactorBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        toUnit: UnitOfMeasure;
        coefficient: string;
        item: Item;
        supplier: Supplier;
        customer: Customer;
        type: UnitConversionType;
        isStandard: boolean;
        fromUnit: UnitOfMeasure;
    }
    export interface UnitConversionFactor$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnitConversionFactor$Lookups {
        _vendor: QueryOperation<SysVendor>;
        item: QueryOperation<Item>;
        supplier: QueryOperation<Supplier>;
        customer: QueryOperation<Customer>;
        fromUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface UnitConversionFactor$Operations {
        query: QueryOperation<UnitConversionFactor>;
        read: ReadOperation<UnitConversionFactor>;
        aggregate: {
            read: AggregateReadOperation<UnitConversionFactor>;
            query: AggregateQueryOperation<UnitConversionFactor>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: UnitConversionFactor$AsyncOperations;
        lookups(dataOrId: string | { data: UnitConversionFactorInput }): UnitConversionFactor$Lookups;
        getDefaults: GetDefaultsOperation<UnitConversionFactor>;
    }
    export interface UnitOfMeasure extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        symbol: string;
        decimalDigits: integer;
        type: UnitType;
        isActive: boolean;
        conversionFactor: ClientCollection<UnitConversionFactor>;
        _syncTick: string;
    }
    export interface UnitOfMeasureInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        description?: string;
        symbol?: string;
        decimalDigits?: integer | string;
        type?: UnitType;
        isActive?: boolean | string;
        conversionFactor?: Partial<UnitConversionFactorInput>[];
        _syncTick?: decimal | string;
    }
    export interface UnitOfMeasureBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        symbol: string;
        decimalDigits: integer;
        type: UnitType;
        isActive: boolean;
        conversionFactor: ClientCollection<UnitConversionFactorBinding>;
        _syncTick: string;
    }
    export interface UnitOfMeasure$Queries {
        convertFromTo: Node$Operation<
            {
                fromUnit: string;
                toUnit: string;
                quantity: decimal | string;
                item?: string;
                supplier?: string;
                customer?: string;
                type?: UnitConversionType;
                formatToUnitDecimalDigits?: boolean | string;
                conversionFactor?: decimal | string;
            },
            string
        >;
        getUnitConversionFactor: Node$Operation<
            {
                fromUnit: string;
                toUnit: string;
                type?: UnitConversionType;
                item?: string;
                supplier?: string;
                customer?: string;
            },
            {
                fromUnit: UnitOfMeasure;
                toUnit: UnitOfMeasure;
                coefficient: string;
                type: UnitConversionType;
                item: Item;
                supplier: Supplier;
                customer: Customer;
                isStandard: boolean;
            }
        >;
        getPurchaseUnit: Node$Operation<
            {
                item: string;
                supplier: string;
            },
            UnitOfMeasure
        >;
    }
    export interface UnitOfMeasure$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnitOfMeasure$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface UnitOfMeasure$Operations {
        query: QueryOperation<UnitOfMeasure>;
        read: ReadOperation<UnitOfMeasure>;
        aggregate: {
            read: AggregateReadOperation<UnitOfMeasure>;
            query: AggregateQueryOperation<UnitOfMeasure>;
        };
        queries: UnitOfMeasure$Queries;
        create: CreateOperation<UnitOfMeasureInput, UnitOfMeasure>;
        getDuplicate: GetDuplicateOperation<UnitOfMeasure>;
        duplicate: DuplicateOperation<string, UnitOfMeasureInput, UnitOfMeasure>;
        update: UpdateOperation<UnitOfMeasureInput, UnitOfMeasure>;
        updateById: UpdateByIdOperation<UnitOfMeasureInput, UnitOfMeasure>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: UnitOfMeasure$AsyncOperations;
        lookups(dataOrId: string | { data: UnitOfMeasureInput }): UnitOfMeasure$Lookups;
        getDefaults: GetDefaultsOperation<UnitOfMeasure>;
    }
    export interface VersionInformation extends ClientNode {
        _factory: MetaNodeFactory;
        version: string;
        text: string;
    }
    export interface VersionInformationInput extends ClientNodeInput {
        version?: string;
        text?: string;
    }
    export interface VersionInformationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        version: string;
        text: string;
    }
    export interface VersionInformation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface VersionInformation$Operations {
        query: QueryOperation<VersionInformation>;
        read: ReadOperation<VersionInformation>;
        aggregate: {
            read: AggregateReadOperation<VersionInformation>;
            query: AggregateQueryOperation<VersionInformation>;
        };
        create: CreateOperation<VersionInformationInput, VersionInformation>;
        getDuplicate: GetDuplicateOperation<VersionInformation>;
        asyncOperations: VersionInformation$AsyncOperations;
        getDefaults: GetDefaultsOperation<VersionInformation>;
    }
    export interface WeeklyShift extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isFullWeek: boolean;
        capacity: string;
        formattedCapacity: string;
        mondayShift: DailyShift;
        tuesdayShift: DailyShift;
        wednesdayShift: DailyShift;
        thursdayShift: DailyShift;
        fridayShift: DailyShift;
        saturdayShift: DailyShift;
        sundayShift: DailyShift;
    }
    export interface WeeklyShiftInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isFullWeek?: boolean | string;
        mondayShift?: integer | string;
        tuesdayShift?: integer | string;
        wednesdayShift?: integer | string;
        thursdayShift?: integer | string;
        fridayShift?: integer | string;
        saturdayShift?: integer | string;
        sundayShift?: integer | string;
    }
    export interface WeeklyShiftBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isFullWeek: boolean;
        capacity: string;
        formattedCapacity: string;
        mondayShift: DailyShift;
        tuesdayShift: DailyShift;
        wednesdayShift: DailyShift;
        thursdayShift: DailyShift;
        fridayShift: DailyShift;
        saturdayShift: DailyShift;
        sundayShift: DailyShift;
    }
    export interface WeeklyShift$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WeeklyShift$Lookups {
        _vendor: QueryOperation<SysVendor>;
        mondayShift: QueryOperation<DailyShift>;
        tuesdayShift: QueryOperation<DailyShift>;
        wednesdayShift: QueryOperation<DailyShift>;
        thursdayShift: QueryOperation<DailyShift>;
        fridayShift: QueryOperation<DailyShift>;
        saturdayShift: QueryOperation<DailyShift>;
        sundayShift: QueryOperation<DailyShift>;
    }
    export interface WeeklyShift$Operations {
        query: QueryOperation<WeeklyShift>;
        read: ReadOperation<WeeklyShift>;
        aggregate: {
            read: AggregateReadOperation<WeeklyShift>;
            query: AggregateQueryOperation<WeeklyShift>;
        };
        create: CreateOperation<WeeklyShiftInput, WeeklyShift>;
        getDuplicate: GetDuplicateOperation<WeeklyShift>;
        duplicate: DuplicateOperation<string, WeeklyShiftInput, WeeklyShift>;
        update: UpdateOperation<WeeklyShiftInput, WeeklyShift>;
        updateById: UpdateByIdOperation<WeeklyShiftInput, WeeklyShift>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WeeklyShift$AsyncOperations;
        lookups(dataOrId: string | { data: WeeklyShiftInput }): WeeklyShift$Lookups;
        getDefaults: GetDefaultsOperation<WeeklyShift>;
    }
    export interface WorkInProgress extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentType: WorkInProgressDocumentType;
    }
    export interface WorkInProgressInput extends ClientNodeInput {
        _constructor?: string;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        documentNumber?: string;
        documentLine?: integer | string;
        documentId?: integer | string;
        originDocumentType?: WorkInProgressDocumentType;
    }
    export interface WorkInProgressBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentNumber: string;
        documentLine: integer;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
    }
    export interface WorkInProgress$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgress$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgress$Operations {
        queries: WorkInProgress$Queries;
        lookups(dataOrId: string | { data: WorkInProgressInput }): WorkInProgress$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgress>;
    }
    export interface BaseDocumentItemLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface BaseDocumentItemLineInput extends VitalClientNodeInput {
        _constructor?: string;
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
    }
    export interface BaseDocumentItemLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface BaseDocumentItemLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
    }
    export interface BaseDocumentItemLine$Operations {
        query: QueryOperation<BaseDocumentItemLine>;
        read: ReadOperation<BaseDocumentItemLine>;
        aggregate: {
            read: AggregateReadOperation<BaseDocumentItemLine>;
            query: AggregateQueryOperation<BaseDocumentItemLine>;
        };
        lookups(dataOrId: string | { data: BaseDocumentItemLineInput }): BaseDocumentItemLine$Lookups;
        getDefaults: GetDefaultsOperation<BaseDocumentItemLine>;
    }
    export interface BusinessEntityAddress extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        businessEntity: BusinessEntity;
        isPrimary: boolean;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryContact: BusinessEntityContact;
        deliveryDetail: DeliveryDetail;
    }
    export interface BusinessEntityAddressInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        address?: integer | string;
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        region?: string;
        postcode?: string;
        country?: integer | string;
        locationPhoneNumber?: string;
        isPrimary?: boolean | string;
        deliveryDetail?: DeliveryDetailInput;
    }
    export interface BusinessEntityAddressBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        businessEntity: BusinessEntity;
        isPrimary: boolean;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryContact: BusinessEntityContact;
        deliveryDetail: DeliveryDetailBinding;
    }
    export interface BusinessEntityAddress$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BusinessEntityAddress$Lookups {
        address: QueryOperation<Address>;
        country: QueryOperation<Country>;
        primaryContact: QueryOperation<BusinessEntityContact>;
    }
    export interface BusinessEntityAddress$Operations {
        query: QueryOperation<BusinessEntityAddress>;
        read: ReadOperation<BusinessEntityAddress>;
        aggregate: {
            read: AggregateReadOperation<BusinessEntityAddress>;
            query: AggregateQueryOperation<BusinessEntityAddress>;
        };
        create: CreateOperation<BusinessEntityAddressInput, BusinessEntityAddress>;
        getDuplicate: GetDuplicateOperation<BusinessEntityAddress>;
        update: UpdateOperation<BusinessEntityAddressInput, BusinessEntityAddress>;
        updateById: UpdateByIdOperation<BusinessEntityAddressInput, BusinessEntityAddress>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BusinessEntityAddress$AsyncOperations;
        lookups(dataOrId: string | { data: BusinessEntityAddressInput }): BusinessEntityAddress$Lookups;
        getDefaults: GetDefaultsOperation<BusinessEntityAddress>;
    }
    export interface BusinessEntityContact extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        businessEntity: BusinessEntity;
        address: BusinessEntityAddress;
        isPrimary: boolean;
        _notes: ClientCollection<SysNoteAssociation>;
    }
    export interface BusinessEntityContactInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        contact?: integer | string;
        title?: Title;
        firstName?: string;
        lastName?: string;
        preferredName?: string;
        role?: ContactRole;
        position?: string;
        locationPhoneNumber?: string;
        email?: string;
        image?: BinaryStream;
        address?: integer | string;
        isPrimary?: boolean | string;
        _notes?: Partial<SysNoteAssociationInput>[];
    }
    export interface BusinessEntityContactBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        businessEntity: BusinessEntity;
        address: BusinessEntityAddress;
        isPrimary: boolean;
        _notes: ClientCollection<SysNoteAssociation>;
    }
    export interface BusinessEntityContact$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BusinessEntityContact$Lookups {
        contact: QueryOperation<Contact>;
        address: QueryOperation<BusinessEntityAddress>;
    }
    export interface BusinessEntityContact$Operations {
        query: QueryOperation<BusinessEntityContact>;
        read: ReadOperation<BusinessEntityContact>;
        aggregate: {
            read: AggregateReadOperation<BusinessEntityContact>;
            query: AggregateQueryOperation<BusinessEntityContact>;
        };
        create: CreateOperation<BusinessEntityContactInput, BusinessEntityContact>;
        getDuplicate: GetDuplicateOperation<BusinessEntityContact>;
        update: UpdateOperation<BusinessEntityContactInput, BusinessEntityContact>;
        updateById: UpdateByIdOperation<BusinessEntityContactInput, BusinessEntityContact>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BusinessEntityContact$AsyncOperations;
        lookups(dataOrId: string | { data: BusinessEntityContactInput }): BusinessEntityContact$Lookups;
        getDefaults: GetDefaultsOperation<BusinessEntityContact>;
    }
    export interface CompanyAddress extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        company: Company;
        isPrimary: boolean;
        contacts: ClientCollection<CompanyContact>;
    }
    export interface CompanyAddressInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        address?: integer | string;
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        region?: string;
        postcode?: string;
        country?: integer | string;
        locationPhoneNumber?: string;
        isPrimary?: boolean | string;
    }
    export interface CompanyAddressBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        company: Company;
        isPrimary: boolean;
        contacts: ClientCollection<CompanyContact>;
    }
    export interface CompanyAddress$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyAddress$Lookups {
        address: QueryOperation<Address>;
        country: QueryOperation<Country>;
    }
    export interface CompanyAddress$Operations {
        query: QueryOperation<CompanyAddress>;
        read: ReadOperation<CompanyAddress>;
        aggregate: {
            read: AggregateReadOperation<CompanyAddress>;
            query: AggregateQueryOperation<CompanyAddress>;
        };
        create: CreateOperation<CompanyAddressInput, CompanyAddress>;
        getDuplicate: GetDuplicateOperation<CompanyAddress>;
        update: UpdateOperation<CompanyAddressInput, CompanyAddress>;
        updateById: UpdateByIdOperation<CompanyAddressInput, CompanyAddress>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CompanyAddress$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyAddressInput }): CompanyAddress$Lookups;
        getDefaults: GetDefaultsOperation<CompanyAddress>;
    }
    export interface CompanyContact extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        company: Company;
        address: CompanyAddress;
        isPrimary: boolean;
    }
    export interface CompanyContactInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        contact?: integer | string;
        title?: Title;
        firstName?: string;
        lastName?: string;
        preferredName?: string;
        role?: ContactRole;
        position?: string;
        locationPhoneNumber?: string;
        email?: string;
        image?: BinaryStream;
        address?: integer | string;
        isPrimary?: boolean | string;
    }
    export interface CompanyContactBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        company: Company;
        address: CompanyAddress;
        isPrimary: boolean;
    }
    export interface CompanyContact$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyContact$Lookups {
        contact: QueryOperation<Contact>;
        address: QueryOperation<CompanyAddress>;
    }
    export interface CompanyContact$Operations {
        query: QueryOperation<CompanyContact>;
        read: ReadOperation<CompanyContact>;
        aggregate: {
            read: AggregateReadOperation<CompanyContact>;
            query: AggregateQueryOperation<CompanyContact>;
        };
        create: CreateOperation<CompanyContactInput, CompanyContact>;
        getDuplicate: GetDuplicateOperation<CompanyContact>;
        update: UpdateOperation<CompanyContactInput, CompanyContact>;
        updateById: UpdateByIdOperation<CompanyContactInput, CompanyContact>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CompanyContact$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyContactInput }): CompanyContact$Lookups;
        getDefaults: GetDefaultsOperation<CompanyContact>;
    }
    export interface Customer extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomer>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CustomerInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        billToCustomer?: integer | string;
        billToAddress?: integer | string;
        payByCustomer?: integer | string;
        payByAddress?: integer | string;
        isOnHold?: boolean | string;
        items?: Partial<ItemCustomerInput>[];
        creditLimit?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CustomerBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomerBinding>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface Customer$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Customer$Lookups {
        primaryAddress: QueryOperation<BusinessEntityAddress>;
        paymentTerm: QueryOperation<PaymentTerm>;
        category: QueryOperation<CustomerSupplierCategory>;
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
        billToCustomer: QueryOperation<Customer>;
        billToAddress: QueryOperation<BusinessEntityAddress>;
        payByCustomer: QueryOperation<Customer>;
        payByAddress: QueryOperation<BusinessEntityAddress>;
        primaryShipToAddress: QueryOperation<BusinessEntityAddress>;
    }
    export interface Customer$Operations {
        query: QueryOperation<Customer>;
        read: ReadOperation<Customer>;
        aggregate: {
            read: AggregateReadOperation<Customer>;
            query: AggregateQueryOperation<Customer>;
        };
        create: CreateOperation<CustomerInput, Customer>;
        getDuplicate: GetDuplicateOperation<Customer>;
        update: UpdateOperation<CustomerInput, Customer>;
        updateById: UpdateByIdOperation<CustomerInput, Customer>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Customer$AsyncOperations;
        lookups(dataOrId: string | { data: CustomerInput }): Customer$Lookups;
        getDefaults: GetDefaultsOperation<Customer>;
    }
    export interface DetailedResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        _syncTick: string;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
    }
    export interface DetailedResourceInput extends ClientNodeInput {
        _constructor?: string;
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        resourceGroup?: integer | string;
        _syncTick?: decimal | string;
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
    }
    export interface DetailedResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        _syncTick: string;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
    }
    export interface DetailedResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        resourceGroup: QueryOperation<GroupResource>;
        location: QueryOperation<Location>;
    }
    export interface DetailedResource$Operations {
        create: CreateOperation<DetailedResourceInput, DetailedResource>;
        getDuplicate: GetDuplicateOperation<DetailedResource>;
        duplicate: DuplicateOperation<string, DetailedResourceInput, DetailedResource>;
        lookups(dataOrId: string | { data: DetailedResourceInput }): DetailedResource$Lookups;
        getDefaults: GetDefaultsOperation<DetailedResource>;
    }
    export interface GroupResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
        type: ResourceGroupType;
        minCapabilityLevel: CapabilityLevel;
        resources: ClientCollection<DetailedResource>;
        replacements: ClientCollection<ResourceGroupReplacement>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface GroupResourceInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
        type?: ResourceGroupType;
        minCapabilityLevel?: integer | string;
        replacements?: Partial<ResourceGroupReplacementInput>[];
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface GroupResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
        type: ResourceGroupType;
        minCapabilityLevel: CapabilityLevel;
        resources: ClientCollection<DetailedResource>;
        replacements: ClientCollection<ResourceGroupReplacementBinding>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface GroupResource$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface GroupResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        location: QueryOperation<Location>;
        minCapabilityLevel: QueryOperation<CapabilityLevel>;
    }
    export interface GroupResource$Operations {
        query: QueryOperation<GroupResource>;
        read: ReadOperation<GroupResource>;
        aggregate: {
            read: AggregateReadOperation<GroupResource>;
            query: AggregateQueryOperation<GroupResource>;
        };
        create: CreateOperation<GroupResourceInput, GroupResource>;
        getDuplicate: GetDuplicateOperation<GroupResource>;
        duplicate: DuplicateOperation<string, GroupResourceInput, GroupResource>;
        update: UpdateOperation<GroupResourceInput, GroupResource>;
        updateById: UpdateByIdOperation<GroupResourceInput, GroupResource>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: GroupResource$AsyncOperations;
        lookups(dataOrId: string | { data: GroupResourceInput }): GroupResource$Lookups;
        getDefaults: GetDefaultsOperation<GroupResource>;
    }
    export interface LaborCapability extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        dateRangeValidity: string;
        dateStartValid: string;
        dateEndValid: string;
        capabilityLevel: CapabilityLevel;
        labor: LaborResource;
        machine: MachineResource;
        tool: ToolResource;
        service: Item;
    }
    export interface LaborCapabilityInput extends VitalClientNodeInput {
        id?: string;
        name?: string;
        dateRangeValidity?: string;
        capabilityLevel?: integer | string;
        machine?: integer | string;
        tool?: integer | string;
        service?: integer | string;
    }
    export interface LaborCapabilityBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        dateRangeValidity: string;
        dateStartValid: string;
        dateEndValid: string;
        capabilityLevel: CapabilityLevel;
        labor: LaborResource;
        machine: MachineResource;
        tool: ToolResource;
        service: Item;
    }
    export interface LaborCapability$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LaborCapability$Lookups {
        capabilityLevel: QueryOperation<CapabilityLevel>;
        machine: QueryOperation<MachineResource>;
        tool: QueryOperation<ToolResource>;
        service: QueryOperation<Item>;
    }
    export interface LaborCapability$Operations {
        query: QueryOperation<LaborCapability>;
        read: ReadOperation<LaborCapability>;
        aggregate: {
            read: AggregateReadOperation<LaborCapability>;
            query: AggregateQueryOperation<LaborCapability>;
        };
        create: CreateOperation<LaborCapabilityInput, LaborCapability>;
        getDuplicate: GetDuplicateOperation<LaborCapability>;
        duplicate: DuplicateOperation<string, LaborCapabilityInput, LaborCapability>;
        update: UpdateOperation<LaborCapabilityInput, LaborCapability>;
        updateById: UpdateByIdOperation<LaborCapabilityInput, LaborCapability>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LaborCapability$AsyncOperations;
        lookups(dataOrId: string | { data: LaborCapabilityInput }): LaborCapability$Lookups;
        getDefaults: GetDefaultsOperation<LaborCapability>;
    }
    export interface RangeSequenceComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sequenceNumber: RangeSequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface RangeSequenceComponentInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        _constructor?: string;
        type?: BaseSequenceNumberComponentType;
        constant?: string;
        length?: integer | string;
        startValue?: string;
        endValue?: string;
    }
    export interface RangeSequenceComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        sequenceNumber: RangeSequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface RangeSequenceComponent$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface RangeSequenceComponent$Operations {
        query: QueryOperation<RangeSequenceComponent>;
        read: ReadOperation<RangeSequenceComponent>;
        aggregate: {
            read: AggregateReadOperation<RangeSequenceComponent>;
            query: AggregateQueryOperation<RangeSequenceComponent>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: RangeSequenceComponentInput }): RangeSequenceComponent$Lookups;
        getDefaults: GetDefaultsOperation<RangeSequenceComponent>;
    }
    export interface RangeSequenceNumber extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        anyComponents: ClientCollection<RangeSequenceComponent>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
    }
    export interface RangeSequenceNumberInput extends ClientNodeInput {
        _vendor?: integer | string;
        _constructor?: string;
        id?: string;
        name?: string;
        definitionLevel?: SequenceCounterDefinitionLevel;
        rtzLevel?: SequenceNumberResetFrequency;
        isClearedByReset?: boolean | string;
        type?: SequenceNumberType;
        components?: Partial<RangeSequenceComponentInput>[];
        isChronological?: boolean | string;
        legislation?: integer | string;
    }
    export interface RangeSequenceNumberBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<RangeSequenceComponentBinding>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
    }
    export interface RangeSequenceNumber$Queries {
        getDocumentNodeNames: Node$Operation<{}, string[]>;
    }
    export interface RangeSequenceNumber$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface RangeSequenceNumber$Operations {
        query: QueryOperation<RangeSequenceNumber>;
        read: ReadOperation<RangeSequenceNumber>;
        aggregate: {
            read: AggregateReadOperation<RangeSequenceNumber>;
            query: AggregateQueryOperation<RangeSequenceNumber>;
        };
        queries: RangeSequenceNumber$Queries;
        create: CreateOperation<RangeSequenceNumberInput, RangeSequenceNumber>;
        getDuplicate: GetDuplicateOperation<RangeSequenceNumber>;
        duplicate: DuplicateOperation<string, RangeSequenceNumberInput, RangeSequenceNumber>;
        update: UpdateOperation<RangeSequenceNumberInput, RangeSequenceNumber>;
        updateById: UpdateByIdOperation<RangeSequenceNumberInput, RangeSequenceNumber>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: RangeSequenceNumberInput }): RangeSequenceNumber$Lookups;
        getDefaults: GetDefaultsOperation<RangeSequenceNumber>;
    }
    export interface SequenceNumber extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<SequenceNumberComponent>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        values: ClientCollection<SequenceNumberValue>;
    }
    export interface SequenceNumberInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        definitionLevel?: SequenceCounterDefinitionLevel;
        rtzLevel?: SequenceNumberResetFrequency;
        isClearedByReset?: boolean | string;
        type?: SequenceNumberType;
        components?: Partial<SequenceNumberComponentInput>[];
        isChronological?: boolean | string;
        legislation?: integer | string;
        values?: Partial<SequenceNumberValueInput>[];
    }
    export interface SequenceNumberBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<SequenceNumberComponentBinding>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        values: ClientCollection<SequenceNumberValueBinding>;
    }
    export interface SequenceNumber$Queries {
        getDocumentNodeNames: Node$Operation<{}, string[]>;
    }
    export interface SequenceNumber$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumber$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface SequenceNumber$Operations {
        query: QueryOperation<SequenceNumber>;
        read: ReadOperation<SequenceNumber>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumber>;
            query: AggregateQueryOperation<SequenceNumber>;
        };
        queries: SequenceNumber$Queries;
        create: CreateOperation<SequenceNumberInput, SequenceNumber>;
        getDuplicate: GetDuplicateOperation<SequenceNumber>;
        duplicate: DuplicateOperation<string, SequenceNumberInput, SequenceNumber>;
        update: UpdateOperation<SequenceNumberInput, SequenceNumber>;
        updateById: UpdateByIdOperation<SequenceNumberInput, SequenceNumber>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumber$AsyncOperations;
        lookups(dataOrId: string | { data: SequenceNumberInput }): SequenceNumber$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumber>;
    }
    export interface SequenceNumberComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: SequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
    }
    export interface SequenceNumberComponentInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        type?: BaseSequenceNumberComponentType;
        constant?: string;
        length?: integer | string;
    }
    export interface SequenceNumberComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: SequenceNumber;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
    }
    export interface SequenceNumberComponent$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SequenceNumberComponent$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SequenceNumberComponent$Operations {
        query: QueryOperation<SequenceNumberComponent>;
        read: ReadOperation<SequenceNumberComponent>;
        aggregate: {
            read: AggregateReadOperation<SequenceNumberComponent>;
            query: AggregateQueryOperation<SequenceNumberComponent>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SequenceNumberComponent$AsyncOperations;
        lookups(dataOrId: string | { data: SequenceNumberComponentInput }): SequenceNumberComponent$Lookups;
        getDefaults: GetDefaultsOperation<SequenceNumberComponent>;
    }
    export interface Supplier extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificate>;
        items: ClientCollection<ItemSupplier>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface SupplierInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        supplierType?: SupplierType;
        standardIndustrialClassification?: integer | string;
        paymentMethod?: string;
        parent?: integer | string;
        incoterm?: integer | string;
        certificates?: Partial<SupplierCertificateInput>[];
        items?: Partial<ItemSupplierInput>[];
        billBySupplier?: integer | string;
        billByAddress?: integer | string;
        payToSupplier?: integer | string;
        payToAddress?: integer | string;
        returnToSupplier?: integer | string;
        returnToAddress?: integer | string;
        deliveryMode?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface SupplierBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificateBinding>;
        items: ClientCollection<ItemSupplierBinding>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface Supplier$Queries {
        getDefaultSupplier: Node$Operation<
            {
                item: string;
                site: string;
            },
            Supplier
        >;
    }
    export interface Supplier$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Supplier$Lookups {
        primaryAddress: QueryOperation<BusinessEntityAddress>;
        paymentTerm: QueryOperation<PaymentTerm>;
        category: QueryOperation<CustomerSupplierCategory>;
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
        standardIndustrialClassification: QueryOperation<StandardIndustrialClassification>;
        parent: QueryOperation<Supplier>;
        incoterm: QueryOperation<Incoterm>;
        billBySupplier: QueryOperation<Supplier>;
        billByAddress: QueryOperation<BusinessEntityAddress>;
        payToSupplier: QueryOperation<Supplier>;
        payToAddress: QueryOperation<BusinessEntityAddress>;
        returnToSupplier: QueryOperation<Supplier>;
        returnToAddress: QueryOperation<BusinessEntityAddress>;
        deliveryMode: QueryOperation<DeliveryMode>;
    }
    export interface Supplier$Operations {
        query: QueryOperation<Supplier>;
        read: ReadOperation<Supplier>;
        aggregate: {
            read: AggregateReadOperation<Supplier>;
            query: AggregateQueryOperation<Supplier>;
        };
        queries: Supplier$Queries;
        create: CreateOperation<SupplierInput, Supplier>;
        getDuplicate: GetDuplicateOperation<Supplier>;
        update: UpdateOperation<SupplierInput, Supplier>;
        updateById: UpdateByIdOperation<SupplierInput, Supplier>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Supplier$AsyncOperations;
        lookups(dataOrId: string | { data: SupplierInput }): Supplier$Lookups;
        getDefaults: GetDefaultsOperation<Supplier>;
    }
    export interface SupplierCertificate extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        standard: Standard;
        id: string;
        dateOfCertification: string;
        validUntil: string;
        certificationBody: string;
        supplier: Supplier;
        dateOfOriginalCertification: string;
    }
    export interface SupplierCertificateInput extends VitalClientNodeInput {
        standard?: integer | string;
        id?: string;
        dateOfCertification?: string;
        validUntil?: string;
        certificationBody?: string;
        dateOfOriginalCertification?: string;
    }
    export interface SupplierCertificateBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        standard: Standard;
        id: string;
        dateOfCertification: string;
        validUntil: string;
        certificationBody: string;
        supplier: Supplier;
        dateOfOriginalCertification: string;
    }
    export interface SupplierCertificate$Mutations {
        renewCertificate: Node$Operation<
            {
                certificate: string;
                newCertificateDate: string;
            },
            SupplierCertificate
        >;
    }
    export interface SupplierCertificate$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SupplierCertificate$Lookups {
        standard: QueryOperation<Standard>;
    }
    export interface SupplierCertificate$Operations {
        query: QueryOperation<SupplierCertificate>;
        read: ReadOperation<SupplierCertificate>;
        aggregate: {
            read: AggregateReadOperation<SupplierCertificate>;
            query: AggregateQueryOperation<SupplierCertificate>;
        };
        create: CreateOperation<SupplierCertificateInput, SupplierCertificate>;
        getDuplicate: GetDuplicateOperation<SupplierCertificate>;
        update: UpdateOperation<SupplierCertificateInput, SupplierCertificate>;
        updateById: UpdateByIdOperation<SupplierCertificateInput, SupplierCertificate>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SupplierCertificate$Mutations;
        asyncOperations: SupplierCertificate$AsyncOperations;
        lookups(dataOrId: string | { data: SupplierCertificateInput }): SupplierCertificate$Lookups;
        getDefaults: GetDefaultsOperation<SupplierCertificate>;
    }
    export interface BomRevisionSequence extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<BomRevisionSequenceComponent>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
        isDefault: boolean;
        isSequenceGenerated: boolean;
    }
    export interface BomRevisionSequenceInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        definitionLevel?: SequenceCounterDefinitionLevel;
        rtzLevel?: SequenceNumberResetFrequency;
        isClearedByReset?: boolean | string;
        type?: SequenceNumberType;
        components?: Partial<BomRevisionSequenceComponentInput>[];
        isChronological?: boolean | string;
        legislation?: integer | string;
        isDefault?: boolean | string;
        isSequenceGenerated?: boolean | string;
    }
    export interface BomRevisionSequenceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<BomRevisionSequenceComponentBinding>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
        isDefault: boolean;
        isSequenceGenerated: boolean;
    }
    export interface BomRevisionSequence$Queries {
        getDocumentNodeNames: Node$Operation<{}, string[]>;
    }
    export interface BomRevisionSequence$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BomRevisionSequence$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface BomRevisionSequence$Operations {
        query: QueryOperation<BomRevisionSequence>;
        read: ReadOperation<BomRevisionSequence>;
        aggregate: {
            read: AggregateReadOperation<BomRevisionSequence>;
            query: AggregateQueryOperation<BomRevisionSequence>;
        };
        queries: BomRevisionSequence$Queries;
        create: CreateOperation<BomRevisionSequenceInput, BomRevisionSequence>;
        getDuplicate: GetDuplicateOperation<BomRevisionSequence>;
        duplicate: DuplicateOperation<string, BomRevisionSequenceInput, BomRevisionSequence>;
        update: UpdateOperation<BomRevisionSequenceInput, BomRevisionSequence>;
        updateById: UpdateByIdOperation<BomRevisionSequenceInput, BomRevisionSequence>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BomRevisionSequence$AsyncOperations;
        lookups(dataOrId: string | { data: BomRevisionSequenceInput }): BomRevisionSequence$Lookups;
        getDefaults: GetDefaultsOperation<BomRevisionSequence>;
    }
    export interface BomRevisionSequenceComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: BomRevisionSequence;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface BomRevisionSequenceComponentInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        type?: BaseSequenceNumberComponentType;
        constant?: string;
        length?: integer | string;
        startValue?: string;
        endValue?: string;
    }
    export interface BomRevisionSequenceComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: BomRevisionSequence;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface BomRevisionSequenceComponent$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BomRevisionSequenceComponent$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface BomRevisionSequenceComponent$Operations {
        query: QueryOperation<BomRevisionSequenceComponent>;
        read: ReadOperation<BomRevisionSequenceComponent>;
        aggregate: {
            read: AggregateReadOperation<BomRevisionSequenceComponent>;
            query: AggregateQueryOperation<BomRevisionSequenceComponent>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BomRevisionSequenceComponent$AsyncOperations;
        lookups(dataOrId: string | { data: BomRevisionSequenceComponentInput }): BomRevisionSequenceComponent$Lookups;
        getDefaults: GetDefaultsOperation<BomRevisionSequenceComponent>;
    }
    export interface LaborResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        capabilities: ClientCollection<LaborCapability>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
    }
    export interface LaborResourceInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        resourceGroup?: integer | string;
        capabilities?: Partial<LaborCapabilityInput>[];
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
    }
    export interface LaborResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        capabilities: ClientCollection<LaborCapabilityBinding>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
    }
    export interface LaborResource$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LaborResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        resourceGroup: QueryOperation<GroupResource>;
        location: QueryOperation<Location>;
    }
    export interface LaborResource$Operations {
        query: QueryOperation<LaborResource>;
        read: ReadOperation<LaborResource>;
        aggregate: {
            read: AggregateReadOperation<LaborResource>;
            query: AggregateQueryOperation<LaborResource>;
        };
        create: CreateOperation<LaborResourceInput, LaborResource>;
        getDuplicate: GetDuplicateOperation<LaborResource>;
        duplicate: DuplicateOperation<string, LaborResourceInput, LaborResource>;
        update: UpdateOperation<LaborResourceInput, LaborResource>;
        updateById: UpdateByIdOperation<LaborResourceInput, LaborResource>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LaborResource$AsyncOperations;
        lookups(dataOrId: string | { data: LaborResourceInput }): LaborResource$Lookups;
        getDefaults: GetDefaultsOperation<LaborResource>;
    }
    export interface LocationSequence extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<LocationSequenceComponent>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
        lastSequenceUsed: string;
        numberLocationsUsed: integer;
        numberLocationsRemaining: integer;
    }
    export interface LocationSequenceInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        definitionLevel?: SequenceCounterDefinitionLevel;
        rtzLevel?: SequenceNumberResetFrequency;
        isClearedByReset?: boolean | string;
        type?: SequenceNumberType;
        components?: Partial<LocationSequenceComponentInput>[];
        isChronological?: boolean | string;
        legislation?: integer | string;
        lastSequenceUsed?: string;
        numberLocationsUsed?: integer | string;
    }
    export interface LocationSequenceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        componentLength: integer;
        definitionLevel: SequenceCounterDefinitionLevel;
        rtzLevel: SequenceNumberResetFrequency;
        isClearedByReset: boolean;
        type: SequenceNumberType;
        components: ClientCollection<LocationSequenceComponentBinding>;
        isChronological: boolean;
        isUsed: boolean;
        legislation: Legislation;
        sequenceNumberAssignments: ClientCollection<SequenceNumberAssignment>;
        minimumLength: integer;
        numberOfCombinations: integer;
        lastSequenceUsed: string;
        numberLocationsUsed: integer;
        numberLocationsRemaining: integer;
    }
    export interface LocationSequence$Queries {
        getDocumentNodeNames: Node$Operation<{}, string[]>;
    }
    export interface LocationSequence$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LocationSequence$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface LocationSequence$Operations {
        query: QueryOperation<LocationSequence>;
        read: ReadOperation<LocationSequence>;
        aggregate: {
            read: AggregateReadOperation<LocationSequence>;
            query: AggregateQueryOperation<LocationSequence>;
        };
        queries: LocationSequence$Queries;
        create: CreateOperation<LocationSequenceInput, LocationSequence>;
        getDuplicate: GetDuplicateOperation<LocationSequence>;
        duplicate: DuplicateOperation<string, LocationSequenceInput, LocationSequence>;
        update: UpdateOperation<LocationSequenceInput, LocationSequence>;
        updateById: UpdateByIdOperation<LocationSequenceInput, LocationSequence>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LocationSequence$AsyncOperations;
        lookups(dataOrId: string | { data: LocationSequenceInput }): LocationSequence$Lookups;
        getDefaults: GetDefaultsOperation<LocationSequence>;
    }
    export interface LocationSequenceComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: LocationSequence;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface LocationSequenceComponentInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        type?: BaseSequenceNumberComponentType;
        constant?: string;
        length?: integer | string;
        startValue?: string;
        endValue?: string;
    }
    export interface LocationSequenceComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        sequenceNumber: LocationSequence;
        type: BaseSequenceNumberComponentType;
        constant: string;
        length: integer;
        startValue: string;
        endValue: string;
    }
    export interface LocationSequenceComponent$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LocationSequenceComponent$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface LocationSequenceComponent$Operations {
        query: QueryOperation<LocationSequenceComponent>;
        read: ReadOperation<LocationSequenceComponent>;
        aggregate: {
            read: AggregateReadOperation<LocationSequenceComponent>;
            query: AggregateQueryOperation<LocationSequenceComponent>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: LocationSequenceComponent$AsyncOperations;
        lookups(dataOrId: string | { data: LocationSequenceComponentInput }): LocationSequenceComponent$Lookups;
        getDefaults: GetDefaultsOperation<LocationSequenceComponent>;
    }
    export interface MachineResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        serialNumber: string;
        model: string;
        minCapabilityLevel: CapabilityLevel;
        supplier: Supplier;
        contractId: string;
        contractName: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
    }
    export interface MachineResourceInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        resourceGroup?: integer | string;
        serialNumber?: string;
        model?: string;
        minCapabilityLevel?: integer | string;
        supplier?: integer | string;
        contractId?: string;
        contractName?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
    }
    export interface MachineResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        serialNumber: string;
        model: string;
        minCapabilityLevel: CapabilityLevel;
        supplier: Supplier;
        contractId: string;
        contractName: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
    }
    export interface MachineResource$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MachineResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        resourceGroup: QueryOperation<GroupResource>;
        minCapabilityLevel: QueryOperation<CapabilityLevel>;
        supplier: QueryOperation<Supplier>;
        location: QueryOperation<Location>;
    }
    export interface MachineResource$Operations {
        query: QueryOperation<MachineResource>;
        read: ReadOperation<MachineResource>;
        aggregate: {
            read: AggregateReadOperation<MachineResource>;
            query: AggregateQueryOperation<MachineResource>;
        };
        create: CreateOperation<MachineResourceInput, MachineResource>;
        getDuplicate: GetDuplicateOperation<MachineResource>;
        duplicate: DuplicateOperation<string, MachineResourceInput, MachineResource>;
        update: UpdateOperation<MachineResourceInput, MachineResource>;
        updateById: UpdateByIdOperation<MachineResourceInput, MachineResource>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: MachineResource$AsyncOperations;
        lookups(dataOrId: string | { data: MachineResourceInput }): MachineResource$Lookups;
        getDefaults: GetDefaultsOperation<MachineResource>;
    }
    export interface ToolResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        item: Item;
        quantity: string;
        consumptionMode: ConsumptionMode;
        hoursTracked: string;
        unitProduced: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
    }
    export interface ToolResourceInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        resourceGroup?: integer | string;
        item?: integer | string;
        quantity?: decimal | string;
        consumptionMode?: ConsumptionMode;
        hoursTracked?: decimal | string;
        unitProduced?: decimal | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
    }
    export interface ToolResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        item: Item;
        quantity: string;
        consumptionMode: ConsumptionMode;
        hoursTracked: string;
        unitProduced: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
    }
    export interface ToolResource$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ToolResource$Lookups {
        site: QueryOperation<Site>;
        weeklyShift: QueryOperation<WeeklyShift>;
        resourceGroup: QueryOperation<GroupResource>;
        item: QueryOperation<Item>;
        location: QueryOperation<Location>;
    }
    export interface ToolResource$Operations {
        query: QueryOperation<ToolResource>;
        read: ReadOperation<ToolResource>;
        aggregate: {
            read: AggregateReadOperation<ToolResource>;
            query: AggregateQueryOperation<ToolResource>;
        };
        create: CreateOperation<ToolResourceInput, ToolResource>;
        getDuplicate: GetDuplicateOperation<ToolResource>;
        duplicate: DuplicateOperation<string, ToolResourceInput, ToolResource>;
        update: UpdateOperation<ToolResourceInput, ToolResource>;
        updateById: UpdateByIdOperation<ToolResourceInput, ToolResource>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ToolResource$AsyncOperations;
        lookups(dataOrId: string | { data: ToolResourceInput }): ToolResource$Lookups;
        getDefaults: GetDefaultsOperation<ToolResource>;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyExtension$Queries {
        isAccessibleForCurrentUser: Node$Operation<
            {
                nodeName: string;
                propertyOrOperation: string;
                options?: {
                    authorizationCode?: string;
                };
            },
            boolean
        >;
    }
    export interface CompanyExtension$Lookups {
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
        primaryAddress: QueryOperation<CompanyAddress>;
        primaryContact: QueryOperation<CompanyContact>;
    }
    export interface CompanyExtension$Operations {
        queries: CompanyExtension$Queries;
        lookups(dataOrId: string | { data: CompanyInput }): CompanyExtension$Lookups;
        getDefaults: GetDefaultsOperation<Company>;
    }
    export interface CountryExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        iso31661Alpha3: string;
        continent: Continent;
        language: string;
        flag: BinaryStream;
        timeZone: string;
        countryCallingCode: string;
        regionLabel: RegionLabel;
        zipLabel: ZipLabel;
        isEuMember: boolean;
        legislation: Legislation;
        currency: Currency;
    }
    export interface CountryInputExtension {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        iso31661Alpha3?: string;
        continent?: Continent;
        language?: string;
        flag?: BinaryStream;
        timeZone?: string;
        countryCallingCode?: string;
        regionLabel?: RegionLabel;
        zipLabel?: ZipLabel;
        isEuMember?: boolean | string;
        legislation?: integer | string;
        currency?: integer | string;
    }
    export interface CountryBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        iso31661Alpha3: string;
        continent: Continent;
        language: string;
        flag: BinaryStream;
        timeZone: string;
        countryCallingCode: string;
        regionLabel: RegionLabel;
        zipLabel: ZipLabel;
        isEuMember: boolean;
        legislation: Legislation;
        currency: Currency;
    }
    export interface CountryExtension$Lookups {
        currency: QueryOperation<Currency>;
    }
    export interface CountryExtension$Operations {
        lookups(dataOrId: string | { data: CountryInput }): CountryExtension$Lookups;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        _syncTick: string;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        _syncTick?: decimal | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        _syncTick: string;
    }
    export interface SiteExtension$Queries {
        timezones: Node$Operation<{}, string[]>;
    }
    export interface SiteExtension$Lookups {
        financialCurrency: QueryOperation<Currency>;
        stockSite: QueryOperation<Site>;
        primaryAddress: QueryOperation<BusinessEntityAddress>;
        financialSite: QueryOperation<Site>;
        defaultLocation: QueryOperation<Location>;
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
    }
    export interface SiteExtension$Operations {
        queries: SiteExtension$Queries;
        lookups(dataOrId: string | { data: SiteInput }): SiteExtension$Lookups;
        getDefaults: GetDefaultsOperation<Site>;
    }
    export interface Package {
        '@sage/xtrem-master-data/Address': Address$Operations;
        '@sage/xtrem-master-data/AddressBase': AddressBase$Operations;
        '@sage/xtrem-master-data/Allergen': Allergen$Operations;
        '@sage/xtrem-master-data/BaseBusinessRelation': BaseBusinessRelation$Operations;
        '@sage/xtrem-master-data/BaseCapability': BaseCapability$Operations;
        '@sage/xtrem-master-data/BaseCertificate': BaseCertificate$Operations;
        '@sage/xtrem-master-data/BaseDocument': BaseDocument$Operations;
        '@sage/xtrem-master-data/BaseDocumentLine': BaseDocumentLine$Operations;
        '@sage/xtrem-master-data/BaseDocumentLineInquiry': BaseDocumentLineInquiry$Operations;
        '@sage/xtrem-master-data/BaseLineDiscountCharge': BaseLineDiscountCharge$Operations;
        '@sage/xtrem-master-data/BaseLineToLine': BaseLineToLine$Operations;
        '@sage/xtrem-master-data/BaseResource': BaseResource$Operations;
        '@sage/xtrem-master-data/BaseSequenceNumber': BaseSequenceNumber$Operations;
        '@sage/xtrem-master-data/BaseSequenceNumberComponent': BaseSequenceNumberComponent$Operations;
        '@sage/xtrem-master-data/BusinessEntity': BusinessEntity$Operations;
        '@sage/xtrem-master-data/CapabilityLevel': CapabilityLevel$Operations;
        '@sage/xtrem-master-data/Contact': Contact$Operations;
        '@sage/xtrem-master-data/ContactBase': ContactBase$Operations;
        '@sage/xtrem-master-data/Container': Container$Operations;
        '@sage/xtrem-master-data/CostCategory': CostCategory$Operations;
        '@sage/xtrem-master-data/Currency': Currency$Operations;
        '@sage/xtrem-master-data/CustomerPriceReason': CustomerPriceReason$Operations;
        '@sage/xtrem-master-data/CustomerSupplierCategory': CustomerSupplierCategory$Operations;
        '@sage/xtrem-master-data/DailyShift': DailyShift$Operations;
        '@sage/xtrem-master-data/DailyShiftDetail': DailyShiftDetail$Operations;
        '@sage/xtrem-master-data/DeliveryDetail': DeliveryDetail$Operations;
        '@sage/xtrem-master-data/DeliveryMode': DeliveryMode$Operations;
        '@sage/xtrem-master-data/DevTools': DevTools$Operations;
        '@sage/xtrem-master-data/Employee': Employee$Operations;
        '@sage/xtrem-master-data/ExchangeRate': ExchangeRate$Operations;
        '@sage/xtrem-master-data/GhsClassification': GhsClassification$Operations;
        '@sage/xtrem-master-data/Incoterm': Incoterm$Operations;
        '@sage/xtrem-master-data/IndirectCostOrigin': IndirectCostOrigin$Operations;
        '@sage/xtrem-master-data/IndirectCostSection': IndirectCostSection$Operations;
        '@sage/xtrem-master-data/IndirectCostSectionLine': IndirectCostSectionLine$Operations;
        '@sage/xtrem-master-data/Item': Item$Operations;
        '@sage/xtrem-master-data/ItemAllergen': ItemAllergen$Operations;
        '@sage/xtrem-master-data/ItemCategory': ItemCategory$Operations;
        '@sage/xtrem-master-data/ItemClassifications': ItemClassifications$Operations;
        '@sage/xtrem-master-data/ItemCustomer': ItemCustomer$Operations;
        '@sage/xtrem-master-data/ItemCustomerPrice': ItemCustomerPrice$Operations;
        '@sage/xtrem-master-data/ItemSite': ItemSite$Operations;
        '@sage/xtrem-master-data/ItemSiteCost': ItemSiteCost$Operations;
        '@sage/xtrem-master-data/ItemSiteSupplier': ItemSiteSupplier$Operations;
        '@sage/xtrem-master-data/ItemSupplier': ItemSupplier$Operations;
        '@sage/xtrem-master-data/ItemSupplierPrice': ItemSupplierPrice$Operations;
        '@sage/xtrem-master-data/LicensePlateNumber': LicensePlateNumber$Operations;
        '@sage/xtrem-master-data/Location': Location$Operations;
        '@sage/xtrem-master-data/LocationType': LocationType$Operations;
        '@sage/xtrem-master-data/LocationZone': LocationZone$Operations;
        '@sage/xtrem-master-data/PaymentTerm': PaymentTerm$Operations;
        '@sage/xtrem-master-data/ReasonCode': ReasonCode$Operations;
        '@sage/xtrem-master-data/ResourceCostCategory': ResourceCostCategory$Operations;
        '@sage/xtrem-master-data/ResourceGroupReplacement': ResourceGroupReplacement$Operations;
        '@sage/xtrem-master-data/SequenceNumberAssignment': SequenceNumberAssignment$Operations;
        '@sage/xtrem-master-data/SequenceNumberAssignmentDocumentType': SequenceNumberAssignmentDocumentType$Operations;
        '@sage/xtrem-master-data/SequenceNumberAssignmentModule': SequenceNumberAssignmentModule$Operations;
        '@sage/xtrem-master-data/SequenceNumberAssignmentSetup': SequenceNumberAssignmentSetup$Operations;
        '@sage/xtrem-master-data/SequenceNumberValue': SequenceNumberValue$Operations;
        '@sage/xtrem-master-data/ShiftDetail': ShiftDetail$Operations;
        '@sage/xtrem-master-data/Standard': Standard$Operations;
        '@sage/xtrem-master-data/StandardIndustrialClassification': StandardIndustrialClassification$Operations;
        '@sage/xtrem-master-data/Team': Team$Operations;
        '@sage/xtrem-master-data/UnitConversionFactor': UnitConversionFactor$Operations;
        '@sage/xtrem-master-data/UnitOfMeasure': UnitOfMeasure$Operations;
        '@sage/xtrem-master-data/VersionInformation': VersionInformation$Operations;
        '@sage/xtrem-master-data/WeeklyShift': WeeklyShift$Operations;
        '@sage/xtrem-master-data/WorkInProgress': WorkInProgress$Operations;
        '@sage/xtrem-master-data/BaseDocumentItemLine': BaseDocumentItemLine$Operations;
        '@sage/xtrem-master-data/BusinessEntityAddress': BusinessEntityAddress$Operations;
        '@sage/xtrem-master-data/BusinessEntityContact': BusinessEntityContact$Operations;
        '@sage/xtrem-master-data/CompanyAddress': CompanyAddress$Operations;
        '@sage/xtrem-master-data/CompanyContact': CompanyContact$Operations;
        '@sage/xtrem-master-data/Customer': Customer$Operations;
        '@sage/xtrem-master-data/DetailedResource': DetailedResource$Operations;
        '@sage/xtrem-master-data/GroupResource': GroupResource$Operations;
        '@sage/xtrem-master-data/LaborCapability': LaborCapability$Operations;
        '@sage/xtrem-master-data/RangeSequenceComponent': RangeSequenceComponent$Operations;
        '@sage/xtrem-master-data/RangeSequenceNumber': RangeSequenceNumber$Operations;
        '@sage/xtrem-master-data/SequenceNumber': SequenceNumber$Operations;
        '@sage/xtrem-master-data/SequenceNumberComponent': SequenceNumberComponent$Operations;
        '@sage/xtrem-master-data/Supplier': Supplier$Operations;
        '@sage/xtrem-master-data/SupplierCertificate': SupplierCertificate$Operations;
        '@sage/xtrem-master-data/BomRevisionSequence': BomRevisionSequence$Operations;
        '@sage/xtrem-master-data/BomRevisionSequenceComponent': BomRevisionSequenceComponent$Operations;
        '@sage/xtrem-master-data/LaborResource': LaborResource$Operations;
        '@sage/xtrem-master-data/LocationSequence': LocationSequence$Operations;
        '@sage/xtrem-master-data/LocationSequenceComponent': LocationSequenceComponent$Operations;
        '@sage/xtrem-master-data/MachineResource': MachineResource$Operations;
        '@sage/xtrem-master-data/ToolResource': ToolResource$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremMailer$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-master-data-api' {
    export type * from '@sage/xtrem-master-data-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-master-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyExtension$Lookups,
        CompanyExtension$Operations,
        CompanyExtension$Queries,
        CompanyInputExtension,
        SiteBindingExtension,
        SiteExtension,
        SiteExtension$Lookups,
        SiteExtension$Operations,
        SiteExtension$Queries,
        SiteInputExtension,
    } from '@sage/xtrem-master-data-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface Company$Lookups extends CompanyExtension$Lookups {}
    export interface Company$Queries extends CompanyExtension$Queries {}
    export interface Company$Operations extends CompanyExtension$Operations {}
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface Site$Lookups extends SiteExtension$Lookups {}
    export interface Site$Queries extends SiteExtension$Queries {}
    export interface Site$Operations extends SiteExtension$Operations {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type {
        CountryBindingExtension,
        CountryExtension,
        CountryExtension$Lookups,
        CountryExtension$Operations,
        CountryInputExtension,
    } from '@sage/xtrem-master-data-api';
    export interface Country extends CountryExtension {}
    export interface CountryBinding extends CountryBindingExtension {}
    export interface CountryInput extends CountryInputExtension {}
    export interface Country$Lookups extends CountryExtension$Lookups {}
    export interface Country$Operations extends CountryExtension$Operations {}
}
