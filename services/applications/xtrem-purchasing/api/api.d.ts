declare module '@sage/xtrem-purchasing-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        DocumentLineDiscountCharge,
        DocumentLineDiscountChargeBinding,
        DocumentLineDiscountChargeInput,
        Package as SageXtremDistribution$Package,
    } from '@sage/xtrem-distribution-api';
    import type {
        Account,
        AnalyticalData,
        BaseOpenItem,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PaymentDocumentLine,
        PaymentTracking,
        PaymentTrackingBinding,
        PaymentTrackingInput,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostAllocation,
        LandedCostDocumentLine,
        LandedCostDocumentLineBinding,
        LandedCostDocumentLineInput,
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        LandedCostLine,
        LandedCostLineBinding,
        LandedCostLineInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseDocument,
        BaseDocumentLine,
        BomRevisionSequence,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityInput,
        Contact,
        CostCategory,
        Currency,
        CustomerSupplierCategory,
        DeliveryMode,
        Incoterm,
        IndirectCostSection,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteCost,
        ItemSiteInput,
        ItemSiteSupplier,
        ItemSupplier,
        ItemSupplierBinding,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Location,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        ReasonCode,
        SequenceNumber,
        StandardIndustrialClassification,
        Supplier,
        SupplierCertificate,
        SupplierCertificateBinding,
        SupplierCertificateInput,
        SupplierInput,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        FifoValuationTier,
        OrderAssignment,
        Package as SageXtremStockData$Package,
        Stock,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
        StockJournal,
        StockReceiptDetail,
        StockReceiptDetailBinding,
        StockReceiptDetailInput,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Country, Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        Package as SageXtremSystem$Package,
        Site,
        SiteInput,
        User,
        UserInput,
    } from '@sage/xtrem-system-api';
    import type {
        DocumentLineTax,
        DocumentLineTaxBinding,
        DocumentLineTaxInput,
        DocumentTax,
        DocumentTaxBinding,
        DocumentTaxInput,
        ItemTaxGroup,
        Package as SageXtremTax$Package,
        Tax,
        TaxCategory,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface PriceOrigin$Enum {
        manual: 1;
        supplierPriceList: 2;
    }
    export type PriceOrigin = keyof PriceOrigin$Enum;
    export interface PurchaseCreditMemoDisplayStatus$Enum {
        draft: 1;
        taxCalculationFailed: 20;
        postingInProgress: 15;
        postingError: 21;
        posted: 22;
        stockError: 23;
        paid: 32;
        partiallyPaid: 33;
    }
    export type PurchaseCreditMemoDisplayStatus = keyof PurchaseCreditMemoDisplayStatus$Enum;
    export interface PurchaseCreditMemoStatus$Enum {
        draft: 0;
        posted: 1;
        inProgress: 2;
        error: 3;
    }
    export type PurchaseCreditMemoStatus = keyof PurchaseCreditMemoStatus$Enum;
    export interface PurchaseDocumentApprovalStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        changeRequested: 5;
        confirmed: 6;
    }
    export type PurchaseDocumentApprovalStatus = keyof PurchaseDocumentApprovalStatus$Enum;
    export interface PurchaseDocumentLineOrigin$Enum {
        direct: 1;
        purchaseRequisition: 2;
        purchaseOrder: 3;
        purchaseSuggestion: 4;
        purchaseReceipt: 5;
        purchaseReturn: 6;
        purchaseInvoice: 7;
        purchaseCreditMemo: 8;
    }
    export type PurchaseDocumentLineOrigin = keyof PurchaseDocumentLineOrigin$Enum;
    export interface PurchaseDocumentStatus$Enum {
        draft: 1;
        pending: 2;
        inProgress: 3;
        closed: 4;
        posted: 5;
        error: 6;
    }
    export type PurchaseDocumentStatus = keyof PurchaseDocumentStatus$Enum;
    export interface PurchaseDocumentType$Enum {
        purchaseRequisition: 1;
        purchaseOrder: 2;
        purchaseSuggestion: 3;
        purchaseReceipt: 4;
        purchaseReturn: 5;
        purchaseInvoice: 6;
        purchaseCreditMemo: 7;
    }
    export type PurchaseDocumentType = keyof PurchaseDocumentType$Enum;
    export interface PurchaseInvoiceDisplayStatus$Enum {
        taxCalculationFailed: 20;
        postingInProgress: 15;
        postingError: 21;
        partiallyCredited: 24;
        credited: 25;
        posted: 22;
        stockError: 23;
        noVariance: 26;
        variance: 27;
        varianceApproved: 28;
        paid: 32;
        partiallyPaid: 33;
    }
    export type PurchaseInvoiceDisplayStatus = keyof PurchaseInvoiceDisplayStatus$Enum;
    export interface PurchaseInvoiceMatchingStatus$Enum {
        noVariance: 0;
        variance: 1;
        varianceApproved: 2;
    }
    export type PurchaseInvoiceMatchingStatus = keyof PurchaseInvoiceMatchingStatus$Enum;
    export interface PurchaseInvoiceStatus$Enum {
        draft: 0;
        posted: 1;
        inProgress: 2;
        error: 3;
    }
    export type PurchaseInvoiceStatus = keyof PurchaseInvoiceStatus$Enum;
    export interface PurchaseInvoiceVarianceType$Enum {
        noVariance: 0;
        quantity: 1;
        price: 2;
        quantityAndPrice: 3;
    }
    export type PurchaseInvoiceVarianceType = keyof PurchaseInvoiceVarianceType$Enum;
    export interface PurchaseOrderApprovalStatus$Enum {
        approved: 0;
        rejected: 1;
    }
    export type PurchaseOrderApprovalStatus = keyof PurchaseOrderApprovalStatus$Enum;
    export interface PurchaseOrderConfirmStatus$Enum {
        confirmed: 0;
        rejected: 1;
    }
    export type PurchaseOrderConfirmStatus = keyof PurchaseOrderConfirmStatus$Enum;
    export interface PurchaseOrderDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        partiallyReceived: 17;
        received: 11;
        closed: 14;
        taxCalculationFailed: 20;
        confirmed: 6;
    }
    export type PurchaseOrderDisplayStatus = keyof PurchaseOrderDisplayStatus$Enum;
    export interface PurchaseOrderInvoiceStatus$Enum {
        notInvoiced: 1;
        partiallyInvoiced: 2;
        invoiced: 3;
    }
    export type PurchaseOrderInvoiceStatus = keyof PurchaseOrderInvoiceStatus$Enum;
    export interface PurchaseOrderReceiptStatus$Enum {
        notReceived: 1;
        partiallyReceived: 2;
        received: 3;
    }
    export type PurchaseOrderReceiptStatus = keyof PurchaseOrderReceiptStatus$Enum;
    export interface PurchaseReceiptDisplayStatus$Enum {
        draft: 1;
        taxCalculationFailed: 20;
        postingInProgress: 15;
        error: 16;
        received: 11;
        partiallyInvoiced: 12;
        invoiced: 13;
        partiallyReturned: 18;
        returned: 29;
        closed: 14;
    }
    export type PurchaseReceiptDisplayStatus = keyof PurchaseReceiptDisplayStatus$Enum;
    export interface PurchaseReceiptInvoiceStatus$Enum {
        notInvoiced: 1;
        partiallyInvoiced: 2;
        invoiced: 3;
    }
    export type PurchaseReceiptInvoiceStatus = keyof PurchaseReceiptInvoiceStatus$Enum;
    export interface PurchaseReceiptReturnStatus$Enum {
        notReturned: 1;
        partiallyReturned: 2;
        returned: 3;
    }
    export type PurchaseReceiptReturnStatus = keyof PurchaseReceiptReturnStatus$Enum;
    export interface PurchaseRequisitionDisplayStatus$Enum {
        draft: 0;
        pendingApproval: 1;
        approved: 2;
        confirmed: 3;
        rejected: 4;
        partiallyOrdered: 5;
        ordered: 6;
        closed: 7;
    }
    export type PurchaseRequisitionDisplayStatus = keyof PurchaseRequisitionDisplayStatus$Enum;
    export interface PurchaseRequisitionOrderStatus$Enum {
        notOrdered: 1;
        partiallyOrdered: 2;
        ordered: 3;
    }
    export type PurchaseRequisitionOrderStatus = keyof PurchaseRequisitionOrderStatus$Enum;
    export interface PurchaseReturnCreditStatus$Enum {
        notCredited: 1;
        partiallyCredited: 2;
        credited: 3;
    }
    export type PurchaseReturnCreditStatus = keyof PurchaseReturnCreditStatus$Enum;
    export interface PurchaseReturnDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        closed: 14;
        taxCalculationFailed: 20;
        postingInProgress: 15;
        returned: 29;
        error: 16;
        confirmed: 6;
    }
    export type PurchaseReturnDisplayStatus = keyof PurchaseReturnDisplayStatus$Enum;
    export interface PurchaseReturnInvoiceStatus$Enum {
        notInvoiced: 1;
        partiallyInvoiced: 2;
        invoiced: 3;
    }
    export type PurchaseReturnInvoiceStatus = keyof PurchaseReturnInvoiceStatus$Enum;
    export interface PurchaseReturnShippingStatus$Enum {
        notShipped: 1;
        partiallyShipped: 2;
        shipped: 3;
    }
    export type PurchaseReturnShippingStatus = keyof PurchaseReturnShippingStatus$Enum;
    export interface UnbilledAccountPayableStatus$Enum {
        inProgress: 0;
        completed: 1;
        draft: 2;
        error: 3;
    }
    export type UnbilledAccountPayableStatus = keyof UnbilledAccountPayableStatus$Enum;
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
        unit: UnitOfMeasure;
        creditMemoQuantity: string;
        stockUnit: UnitOfMeasure;
        creditMemoQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        creditMemoUnitPrice: string;
        currency: Currency;
        creditMemoAmount: string;
    }
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLineInput extends VitalClientNodeInput {
        purchaseInvoiceLine?: integer | string;
    }
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
        unit: UnitOfMeasure;
        creditMemoQuantity: string;
        stockUnit: UnitOfMeasure;
        creditMemoQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        creditMemoUnitPrice: string;
        currency: Currency;
        creditMemoAmount: string;
    }
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLine$Lookups {
        purchaseInvoiceLine: QueryOperation<PurchaseInvoiceLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface PurchaseInvoiceLineToPurchaseCreditMemoLine$Operations {
        query: QueryOperation<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
        read: ReadOperation<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
            query: AggregateQueryOperation<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
        };
        asyncOperations: PurchaseInvoiceLineToPurchaseCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseInvoiceLineToPurchaseCreditMemoLineInput },
        ): PurchaseInvoiceLineToPurchaseCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        purchaseOrderLine: PurchaseOrderLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        invoicedQuantityInStockUnit: string;
        invoicedUnitPrice: string;
        currency: Currency;
        invoicedAmount: string;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLineInput extends VitalClientNodeInput {
        purchaseOrderLine?: integer | string;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        purchaseOrderLine: PurchaseOrderLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        invoicedQuantityInStockUnit: string;
        invoicedUnitPrice: string;
        currency: Currency;
        invoicedAmount: string;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLine$Lookups {
        purchaseOrderLine: QueryOperation<PurchaseOrderLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface PurchaseOrderLineToPurchaseInvoiceLine$Operations {
        query: QueryOperation<PurchaseOrderLineToPurchaseInvoiceLine>;
        read: ReadOperation<PurchaseOrderLineToPurchaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseOrderLineToPurchaseInvoiceLine>;
            query: AggregateQueryOperation<PurchaseOrderLineToPurchaseInvoiceLine>;
        };
        asyncOperations: PurchaseOrderLineToPurchaseInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseOrderLineToPurchaseInvoiceLineInput },
        ): PurchaseOrderLineToPurchaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseOrderLineToPurchaseInvoiceLine>;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseOrderLine: PurchaseOrderLine;
        unit: UnitOfMeasure;
        receivedQuantity: string;
        stockUnit: UnitOfMeasure;
        receivedQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLineInput extends VitalClientNodeInput {
        purchaseOrderLine?: integer | string;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseOrderLine: PurchaseOrderLine;
        unit: UnitOfMeasure;
        receivedQuantity: string;
        stockUnit: UnitOfMeasure;
        receivedQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLine$Lookups {
        purchaseOrderLine: QueryOperation<PurchaseOrderLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseOrderLineToPurchaseReceiptLine$Operations {
        query: QueryOperation<PurchaseOrderLineToPurchaseReceiptLine>;
        read: ReadOperation<PurchaseOrderLineToPurchaseReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseOrderLineToPurchaseReceiptLine>;
            query: AggregateQueryOperation<PurchaseOrderLineToPurchaseReceiptLine>;
        };
        asyncOperations: PurchaseOrderLineToPurchaseReceiptLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseOrderLineToPurchaseReceiptLineInput },
        ): PurchaseOrderLineToPurchaseReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseOrderLineToPurchaseReceiptLine>;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        invoicedQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        price: string;
        currency: Currency;
        amountExcludingTax: string;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLineInput extends VitalClientNodeInput {
        purchaseReceiptLine?: integer | string;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        invoicedQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        price: string;
        currency: Currency;
        amountExcludingTax: string;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLine$Lookups {
        purchaseReceiptLine: QueryOperation<PurchaseReceiptLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface PurchaseReceiptLineToPurchaseInvoiceLine$Operations {
        query: QueryOperation<PurchaseReceiptLineToPurchaseInvoiceLine>;
        read: ReadOperation<PurchaseReceiptLineToPurchaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReceiptLineToPurchaseInvoiceLine>;
            query: AggregateQueryOperation<PurchaseReceiptLineToPurchaseInvoiceLine>;
        };
        asyncOperations: PurchaseReceiptLineToPurchaseInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseReceiptLineToPurchaseInvoiceLineInput },
        ): PurchaseReceiptLineToPurchaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReceiptLineToPurchaseInvoiceLine>;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseReceiptLine: PurchaseReceiptLine;
        unit: UnitOfMeasure;
        returnedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        returnedQuantityInStockUnit: string;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLineInput extends VitalClientNodeInput {
        purchaseReceiptLine?: integer | string;
        returnedQuantity?: decimal | string;
        returnedQuantityInStockUnit?: decimal | string;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseReceiptLine: PurchaseReceiptLine;
        unit: UnitOfMeasure;
        returnedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        returnedQuantityInStockUnit: string;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLine$Lookups {
        purchaseReceiptLine: QueryOperation<PurchaseReceiptLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseReceiptLineToPurchaseReturnLine$Operations {
        query: QueryOperation<PurchaseReceiptLineToPurchaseReturnLine>;
        read: ReadOperation<PurchaseReceiptLineToPurchaseReturnLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReceiptLineToPurchaseReturnLine>;
            query: AggregateQueryOperation<PurchaseReceiptLineToPurchaseReturnLine>;
        };
        asyncOperations: PurchaseReceiptLineToPurchaseReturnLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseReceiptLineToPurchaseReturnLineInput },
        ): PurchaseReceiptLineToPurchaseReturnLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReceiptLineToPurchaseReturnLine>;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseRequisitionLine: PurchaseRequisitionLine;
        purchaseOrderLine: PurchaseOrderLine;
        orderedQuantity: string;
        unit: UnitOfMeasure;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        orderedQuantityInStockUnit: string;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLineInput extends VitalClientNodeInput {
        purchaseRequisitionLine?: integer | string;
        orderedQuantity?: decimal | string;
        orderedQuantityInStockUnit?: decimal | string;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseRequisitionLine: PurchaseRequisitionLine;
        purchaseOrderLine: PurchaseOrderLine;
        orderedQuantity: string;
        unit: UnitOfMeasure;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        orderedQuantityInStockUnit: string;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLine$Lookups {
        purchaseRequisitionLine: QueryOperation<PurchaseRequisitionLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseRequisitionLineToPurchaseOrderLine$Operations {
        query: QueryOperation<PurchaseRequisitionLineToPurchaseOrderLine>;
        read: ReadOperation<PurchaseRequisitionLineToPurchaseOrderLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseRequisitionLineToPurchaseOrderLine>;
            query: AggregateQueryOperation<PurchaseRequisitionLineToPurchaseOrderLine>;
        };
        asyncOperations: PurchaseRequisitionLineToPurchaseOrderLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseRequisitionLineToPurchaseOrderLineInput },
        ): PurchaseRequisitionLineToPurchaseOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseRequisitionLineToPurchaseOrderLine>;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
        unit: UnitOfMeasure;
        creditMemoQuantity: string;
        stockUnit: UnitOfMeasure;
        creditMemoQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        creditMemoUnitPrice: string;
        currency: Currency;
        creditMemoAmount: string;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLineInput extends VitalClientNodeInput {
        purchaseReturnLine?: integer | string;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
        unit: UnitOfMeasure;
        creditMemoQuantity: string;
        stockUnit: UnitOfMeasure;
        creditMemoQuantityInStockUnit: string;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        creditMemoUnitPrice: string;
        currency: Currency;
        creditMemoAmount: string;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLine$Lookups {
        purchaseReturnLine: QueryOperation<PurchaseReturnLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface PurchaseReturnLineToPurchaseCreditMemoLine$Operations {
        query: QueryOperation<PurchaseReturnLineToPurchaseCreditMemoLine>;
        read: ReadOperation<PurchaseReturnLineToPurchaseCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReturnLineToPurchaseCreditMemoLine>;
            query: AggregateQueryOperation<PurchaseReturnLineToPurchaseCreditMemoLine>;
        };
        asyncOperations: PurchaseReturnLineToPurchaseCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseReturnLineToPurchaseCreditMemoLineInput },
        ): PurchaseReturnLineToPurchaseCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReturnLineToPurchaseCreditMemoLine>;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        invoicedQuantityInStockUnit: string;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLineInput extends VitalClientNodeInput {
        purchaseReturnLine?: integer | string;
        invoicedQuantity?: decimal | string;
        invoicedQuantityInStockUnit?: decimal | string;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseReturnLine: PurchaseReturnLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
        unit: UnitOfMeasure;
        invoicedQuantity: string;
        stockUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionFactor: string;
        unitToStockUnitConversionFactor: string;
        invoicedQuantityInStockUnit: string;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLine$Lookups {
        purchaseReturnLine: QueryOperation<PurchaseReturnLine>;
        unit: QueryOperation<UnitOfMeasure>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseReturnLineToPurchaseInvoiceLine$Operations {
        query: QueryOperation<PurchaseReturnLineToPurchaseInvoiceLine>;
        read: ReadOperation<PurchaseReturnLineToPurchaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReturnLineToPurchaseInvoiceLine>;
            query: AggregateQueryOperation<PurchaseReturnLineToPurchaseInvoiceLine>;
        };
        asyncOperations: PurchaseReturnLineToPurchaseInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: PurchaseReturnLineToPurchaseInvoiceLineInput },
        ): PurchaseReturnLineToPurchaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReturnLineToPurchaseInvoiceLine>;
    }
    export interface UnbilledAccountPayableInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromSupplier: Supplier;
        toSupplier: Supplier;
        asOfDate: string;
        status: UnbilledAccountPayableStatus;
        executionDate: string;
        lines: ClientCollection<UnbilledAccountPayableResultLine>;
    }
    export interface UnbilledAccountPayableInputSetInput extends ClientNodeInput {
        user?: integer | string;
        company?: integer | string;
        sites?: (integer | string)[];
        fromSupplier?: integer | string;
        toSupplier?: integer | string;
        asOfDate?: string;
        status?: UnbilledAccountPayableStatus;
        executionDate?: string;
        lines?: Partial<UnbilledAccountPayableResultLineInput>[];
    }
    export interface UnbilledAccountPayableInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        company: Company;
        sites: Site[];
        fromSupplier: Supplier;
        toSupplier: Supplier;
        asOfDate: string;
        status: UnbilledAccountPayableStatus;
        executionDate: string;
        lines: ClientCollection<UnbilledAccountPayableResultLineBinding>;
    }
    export interface UnbilledAccountPayableInputSet$AsyncOperations {
        unbilledAccountPayableInquiry: AsyncOperation<
            {
                userId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnbilledAccountPayableInputSet$Lookups {
        user: QueryOperation<User>;
        company: QueryOperation<Company>;
        fromSupplier: QueryOperation<Supplier>;
        toSupplier: QueryOperation<Supplier>;
    }
    export interface UnbilledAccountPayableInputSet$Operations {
        query: QueryOperation<UnbilledAccountPayableInputSet>;
        read: ReadOperation<UnbilledAccountPayableInputSet>;
        aggregate: {
            read: AggregateReadOperation<UnbilledAccountPayableInputSet>;
            query: AggregateQueryOperation<UnbilledAccountPayableInputSet>;
        };
        create: CreateOperation<UnbilledAccountPayableInputSetInput, UnbilledAccountPayableInputSet>;
        getDuplicate: GetDuplicateOperation<UnbilledAccountPayableInputSet>;
        update: UpdateOperation<UnbilledAccountPayableInputSetInput, UnbilledAccountPayableInputSet>;
        updateById: UpdateByIdOperation<UnbilledAccountPayableInputSetInput, UnbilledAccountPayableInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: UnbilledAccountPayableInputSet$AsyncOperations;
        lookups(
            dataOrId: string | { data: UnbilledAccountPayableInputSetInput },
        ): UnbilledAccountPayableInputSet$Lookups;
        getDefaults: GetDefaultsOperation<UnbilledAccountPayableInputSet>;
    }
    export interface UnbilledAccountPayableResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: UnbilledAccountPayableInputSet;
        supplier: Supplier;
        currency: Currency;
        financialSite: Site;
        purchaseUnit: UnitOfMeasure;
        netPrice: string;
        item: Item;
        stockSite: Site;
        quantity: string;
        account: Account;
        accountItem: Account;
        invoicedQuantity: string;
        creditedQuantity: string;
        returnedQuantity: string;
        invoiceReceivableQuantity: string;
        invoiceReceivableAmount: string;
        company: Company;
        receiptNumber: string;
        receiptInternalId: PurchaseReceipt;
        documentDate: string;
        billBySupplier: Supplier;
        invoiceReceivableAmountInCompanyCurrency: string;
        invoiceReceivableAmountInCompanyCurrencyAtAsOfDate: string;
        companyCurrency: Currency;
    }
    export interface UnbilledAccountPayableResultLineInput extends VitalClientNodeInput {
        supplier?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        purchaseUnit?: integer | string;
        netPrice?: decimal | string;
        item?: integer | string;
        stockSite?: integer | string;
        quantity?: decimal | string;
        account?: integer | string;
        accountItem?: integer | string;
        invoicedQuantity?: decimal | string;
        creditedQuantity?: decimal | string;
        returnedQuantity?: decimal | string;
        invoiceReceivableQuantity?: decimal | string;
        invoiceReceivableAmount?: decimal | string;
        receiptNumber?: string;
        receiptInternalId?: integer | string;
        documentDate?: string;
        billBySupplier?: integer | string;
        invoiceReceivableAmountInCompanyCurrency?: decimal | string;
        invoiceReceivableAmountInCompanyCurrencyAtAsOfDate?: decimal | string;
        companyCurrency?: integer | string;
    }
    export interface UnbilledAccountPayableResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: UnbilledAccountPayableInputSet;
        supplier: Supplier;
        currency: Currency;
        financialSite: Site;
        purchaseUnit: UnitOfMeasure;
        netPrice: string;
        item: Item;
        stockSite: Site;
        quantity: string;
        account: Account;
        accountItem: Account;
        invoicedQuantity: string;
        creditedQuantity: string;
        returnedQuantity: string;
        invoiceReceivableQuantity: string;
        invoiceReceivableAmount: string;
        company: Company;
        receiptNumber: string;
        receiptInternalId: PurchaseReceipt;
        documentDate: string;
        billBySupplier: Supplier;
        invoiceReceivableAmountInCompanyCurrency: string;
        invoiceReceivableAmountInCompanyCurrencyAtAsOfDate: string;
        companyCurrency: Currency;
    }
    export interface UnbilledAccountPayableResultLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UnbilledAccountPayableResultLine$Lookups {
        supplier: QueryOperation<Supplier>;
        currency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        account: QueryOperation<Account>;
        accountItem: QueryOperation<Account>;
        company: QueryOperation<Company>;
        receiptInternalId: QueryOperation<PurchaseReceipt>;
        billBySupplier: QueryOperation<Supplier>;
        companyCurrency: QueryOperation<Currency>;
    }
    export interface UnbilledAccountPayableResultLine$Operations {
        query: QueryOperation<UnbilledAccountPayableResultLine>;
        read: ReadOperation<UnbilledAccountPayableResultLine>;
        aggregate: {
            read: AggregateReadOperation<UnbilledAccountPayableResultLine>;
            query: AggregateQueryOperation<UnbilledAccountPayableResultLine>;
        };
        asyncOperations: UnbilledAccountPayableResultLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: UnbilledAccountPayableResultLineInput },
        ): UnbilledAccountPayableResultLine$Lookups;
        getDefaults: GetDefaultsOperation<UnbilledAccountPayableResultLine>;
    }
    export interface PurchaseCreditMemoLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: PurchaseCreditMemoLine;
        basis: string;
        amount: string;
    }
    export interface PurchaseCreditMemoLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface PurchaseCreditMemoLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: PurchaseCreditMemoLine;
        basis: string;
        amount: string;
    }
    export interface PurchaseCreditMemoLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseCreditMemoLineDiscountCharge$Operations {
        query: QueryOperation<PurchaseCreditMemoLineDiscountCharge>;
        read: ReadOperation<PurchaseCreditMemoLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<PurchaseCreditMemoLineDiscountCharge>;
            query: AggregateQueryOperation<PurchaseCreditMemoLineDiscountCharge>;
        };
        asyncOperations: PurchaseCreditMemoLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<PurchaseCreditMemoLineDiscountCharge>;
    }
    export interface PurchaseReceipt extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        supplier: Supplier;
        receiptDate: string;
        supplierDocumentNumber: string;
        purchaseReturns: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        carrier: Supplier;
        receivingAddress: Address;
        isStockDetailRequired: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        returnAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<PurchaseReceiptLine>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        returnStatus: PurchaseReceiptReturnStatus;
        totalAmountIncludingTaxInCompanyCurrency: string;
        jsonAggregateLandedCostTypes: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        taxes: ClientCollection<DocumentTax>;
    }
    export interface PurchaseReceiptInput extends ClientNodeInput {
        number?: string;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        isOverwriteNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        supplier?: integer | string;
        supplierDocumentNumber?: string;
        carrier?: integer | string;
        receivingAddress?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        billBySupplier?: integer | string;
        supplierAddress?: integer | string;
        returnAddress?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        lines?: Partial<PurchaseReceiptLineInput>[];
        invoiceStatus?: InvoiceStatus;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        returnStatus?: PurchaseReceiptReturnStatus;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        taxes?: Partial<DocumentTaxInput>[];
    }
    export interface PurchaseReceiptBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        isOverwriteNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        supplier: Supplier;
        receiptDate: string;
        supplierDocumentNumber: string;
        purchaseReturns: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        carrier: Supplier;
        receivingAddress: Address;
        isStockDetailRequired: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        returnAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<PurchaseReceiptLineBinding>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        returnStatus: PurchaseReceiptReturnStatus;
        totalAmountIncludingTaxInCompanyCurrency: string;
        jsonAggregateLandedCostTypes: any;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        taxes: ClientCollection<DocumentTaxBinding>;
    }
    export interface PurchaseReceipt$Mutations {
        createPurchaseReturns: Node$Operation<
            {
                document: string;
            },
            PurchaseReturn[]
        >;
        repost: Node$Operation<
            {
                purchaseReceipt: string;
                receiptLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
                saveOnly?: boolean | string;
                landedCostControl?: boolean | string;
                financeTransaction?: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                purchaseReceipt: string;
                receiptNumber?: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
                validationMessages: {
                    message: string;
                    lineNumber: integer;
                    type: Common;
                    sourceDocumentNumber: string;
                }[];
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                purchaseReceipt: string;
            },
            boolean
        >;
        beforePrintPurchaseReceipt: Node$Operation<
            {
                receipt: string;
            },
            boolean
        >;
        post: Node$Operation<
            {
                receipt: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseReceipt$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseReceipt$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        carrier: QueryOperation<Supplier>;
        receivingAddress: QueryOperation<Address>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        billBySupplier: QueryOperation<Supplier>;
        supplierAddress: QueryOperation<Address>;
        returnAddress: QueryOperation<Address>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface PurchaseReceipt$Operations {
        query: QueryOperation<PurchaseReceipt>;
        read: ReadOperation<PurchaseReceipt>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReceipt>;
            query: AggregateQueryOperation<PurchaseReceipt>;
        };
        create: CreateOperation<PurchaseReceiptInput, PurchaseReceipt>;
        getDuplicate: GetDuplicateOperation<PurchaseReceipt>;
        duplicate: DuplicateOperation<string, PurchaseReceiptInput, PurchaseReceipt>;
        update: UpdateOperation<PurchaseReceiptInput, PurchaseReceipt>;
        updateById: UpdateByIdOperation<PurchaseReceiptInput, PurchaseReceipt>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseReceipt$Mutations;
        asyncOperations: PurchaseReceipt$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseReceiptInput }): PurchaseReceipt$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReceipt>;
    }
    export interface PurchaseReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReceipt;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransaction>;
        uiTaxes: string;
        purchaseOrderLine: PurchaseOrderLineToPurchaseReceiptLine;
        purchaseReturnLines: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        purchaseInvoiceLines: ClientCollection<PurchaseReceiptLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        workInProgress: WorkInProgressPurchaseReceiptLine;
        financialSite: Site;
        billBySupplier: Supplier;
        returnedQuantity: string;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingReturnQuantity: string;
        returnedQuantityInStockUnit: string;
        invoicedQuantityInStockUnit: string;
        orderCost: string;
        returnAddress: Address;
        stockMovements: ClientCollection<StockJournal>;
        actualLandedCostInCompanyCurrency: string;
        discount: string;
        charge: string;
        lineStatus: PurchaseDocumentStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        lineReturnStatus: PurchaseReceiptReturnStatus;
        lineInvoiceStatus: PurchaseReceiptInvoiceStatus;
        remainingQuantityInStockUnit: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        priceOrigin: BasePriceOrigin;
        valuedCost: string;
        stockDetails: ClientCollection<StockReceiptDetail>;
        completed: boolean;
        stockDetailStatus: StockDetailStatus;
        jsonStockDetails: string;
    }
    export interface PurchaseReceiptLineInput extends VitalClientNodeInput {
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        purchaseUnit?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        uiTaxes?: string;
        transientPurchaseOrderLine?: integer | string;
        purchaseOrderLine?: PurchaseOrderLineToPurchaseReceiptLineInput;
        workInProgress?: WorkInProgressPurchaseReceiptLineInput;
        orderCost?: decimal | string;
        returnAddress?: integer | string;
        discount?: decimal | string;
        charge?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineReturnStatus?: PurchaseReceiptReturnStatus;
        lineInvoiceStatus?: PurchaseReceiptInvoiceStatus;
        status?: BaseStatus;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        priceOrigin?: BasePriceOrigin;
        valuedCost?: decimal | string;
        stockDetails?: Partial<StockReceiptDetailInput>[];
        completed?: boolean | string;
        stockDetailStatus?: StockDetailStatus;
        jsonStockDetails?: string;
    }
    export interface PurchaseReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReceipt;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        uiTaxes: any;
        transientPurchaseOrderLine: integer;
        purchaseOrderLine: PurchaseOrderLineToPurchaseReceiptLineBinding;
        purchaseReturnLines: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        purchaseInvoiceLines: ClientCollection<PurchaseReceiptLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        workInProgress: WorkInProgressPurchaseReceiptLineBinding;
        financialSite: Site;
        billBySupplier: Supplier;
        returnedQuantity: string;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingReturnQuantity: string;
        returnedQuantityInStockUnit: string;
        invoicedQuantityInStockUnit: string;
        orderCost: string;
        returnAddress: Address;
        stockMovements: ClientCollection<StockJournal>;
        actualLandedCostInCompanyCurrency: string;
        discount: string;
        charge: string;
        lineStatus: PurchaseDocumentStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        lineReturnStatus: PurchaseReceiptReturnStatus;
        lineInvoiceStatus: PurchaseReceiptInvoiceStatus;
        remainingQuantityInStockUnit: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        priceOrigin: BasePriceOrigin;
        valuedCost: string;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        completed: boolean;
        stockDetailStatus: StockDetailStatus;
        jsonStockDetails: any;
    }
    export interface PurchaseReceiptLine$Mutations {
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReceiptLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        itemSupplier: QueryOperation<ItemSupplier>;
        supplier: QueryOperation<Supplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        financialSite: QueryOperation<Site>;
        billBySupplier: QueryOperation<Supplier>;
        returnAddress: QueryOperation<Address>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
    }
    export interface PurchaseReceiptLine$Operations {
        query: QueryOperation<PurchaseReceiptLine>;
        read: ReadOperation<PurchaseReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReceiptLine>;
            query: AggregateQueryOperation<PurchaseReceiptLine>;
        };
        mutations: PurchaseReceiptLine$Mutations;
        asyncOperations: PurchaseReceiptLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseReceiptLineInput }): PurchaseReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReceiptLine>;
    }
    export interface PurchaseRequisition extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        requester: User;
        requestDate: string;
        changeRequestedDescription: TextStream;
        isApplyDefaultSupplierHidden: boolean;
        isSetDimensionHidden: boolean;
        isCreateOrderLinesHidden: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        lines: ClientCollection<PurchaseRequisitionLine>;
        orderStatus: PurchaseRequisitionOrderStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface PurchaseRequisitionInput extends ClientNodeInput {
        number?: string;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        requester?: integer | string;
        requestDate?: string;
        changeRequestedDescription?: TextStream;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        lines?: Partial<PurchaseRequisitionLineInput>[];
        orderStatus?: PurchaseRequisitionOrderStatus;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
    }
    export interface PurchaseRequisitionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        requester: User;
        requestDate: string;
        changeRequestedDescription: TextStream;
        isApplyDefaultSupplierHidden: boolean;
        isSetDimensionHidden: boolean;
        isCreateOrderLinesHidden: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        lines: ClientCollection<PurchaseRequisitionLineBinding>;
        orderStatus: PurchaseRequisitionOrderStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
    }
    export interface PurchaseRequisition$Mutations {
        confirm: Node$Operation<
            {
                document: string;
                isSafeToRetry?: boolean | string;
            },
            PurchaseRequisition
        >;
        sendRequestChangesMail: Node$Operation<
            {
                document?: string;
                user?: UserInput;
            },
            boolean
        >;
        approve: Node$Operation<
            {
                document: string;
                approve: boolean | string;
            },
            boolean
        >;
        createPurchaseOrders: Node$Operation<
            {
                document: string;
            },
            PurchaseOrder[]
        >;
        close: Node$Operation<
            {
                purchaseRequisition: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseRequisition$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseRequisition$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        requester: QueryOperation<User>;
    }
    export interface PurchaseRequisition$Operations {
        query: QueryOperation<PurchaseRequisition>;
        read: ReadOperation<PurchaseRequisition>;
        aggregate: {
            read: AggregateReadOperation<PurchaseRequisition>;
            query: AggregateQueryOperation<PurchaseRequisition>;
        };
        create: CreateOperation<PurchaseRequisitionInput, PurchaseRequisition>;
        getDuplicate: GetDuplicateOperation<PurchaseRequisition>;
        duplicate: DuplicateOperation<string, PurchaseRequisitionInput, PurchaseRequisition>;
        update: UpdateOperation<PurchaseRequisitionInput, PurchaseRequisition>;
        updateById: UpdateByIdOperation<PurchaseRequisitionInput, PurchaseRequisition>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseRequisition$Mutations;
        asyncOperations: PurchaseRequisition$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseRequisitionInput }): PurchaseRequisition$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseRequisition>;
    }
    export interface PurchaseRequisitionLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseRequisition;
        origin: BaseOrigin;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        receivingSite: Site;
        approvalStatus: PurchaseDocumentApprovalStatus;
        lineStatus: PurchaseDocumentStatus;
        requestedItemDescription: string;
        needByDate: string;
        supplier: Supplier;
        currency: Currency;
        changeRequestedDescription: string;
        purchaseOrderLines: ClientCollection<PurchaseRequisitionLineToPurchaseOrderLine>;
        orderedQuantity: string;
        orderedQuantityInStockUnit: string;
        orderedPercentage: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unit: UnitOfMeasure;
        quantity: string;
        itemSupplier: ItemSupplier;
        itemSiteSupplier: ItemSiteSupplier;
        grossPrice: string;
        quantityToOrder: string;
        priceOrigin: PriceOrigin;
        discountCharges: ClientCollection<PurchaseRequisitionLineDiscountCharge>;
        discount: string;
        charge: string;
        netPrice: string;
        stockUnit: UnitOfMeasure;
        unitToStockUnitConversionFactor: string;
        totalTaxExcludedAmount: string;
        quantityInStockUnit: string;
        lineOrderStatus: PurchaseRequisitionOrderStatus;
        quantityToOrderInStockUnit: string;
        status: BaseStatus;
    }
    export interface PurchaseRequisitionLineInput extends VitalClientNodeInput {
        origin?: BaseOrigin;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        requestedItemDescription?: string;
        needByDate?: string;
        supplier?: integer | string;
        currency?: integer | string;
        changeRequestedDescription?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        grossPrice?: decimal | string;
        priceOrigin?: PriceOrigin;
        discountCharges?: Partial<PurchaseRequisitionLineDiscountChargeInput>[];
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        stockUnit?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        totalTaxExcludedAmount?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineOrderStatus?: PurchaseRequisitionOrderStatus;
        status?: BaseStatus;
    }
    export interface PurchaseRequisitionLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseRequisition;
        origin: BaseOrigin;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        receivingSite: Site;
        approvalStatus: PurchaseDocumentApprovalStatus;
        lineStatus: PurchaseDocumentStatus;
        requestedItemDescription: string;
        needByDate: string;
        supplier: Supplier;
        currency: Currency;
        changeRequestedDescription: string;
        purchaseOrderLines: ClientCollection<PurchaseRequisitionLineToPurchaseOrderLine>;
        orderedQuantity: string;
        orderedQuantityInStockUnit: string;
        orderedPercentage: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unit: UnitOfMeasure;
        quantity: string;
        itemSupplier: ItemSupplier;
        itemSiteSupplier: ItemSiteSupplier;
        grossPrice: string;
        quantityToOrder: string;
        priceOrigin: PriceOrigin;
        discountCharges: ClientCollection<PurchaseRequisitionLineDiscountChargeBinding>;
        discount: string;
        charge: string;
        netPrice: string;
        stockUnit: UnitOfMeasure;
        unitToStockUnitConversionFactor: string;
        totalTaxExcludedAmount: string;
        quantityInStockUnit: string;
        lineOrderStatus: PurchaseRequisitionOrderStatus;
        quantityToOrderInStockUnit: string;
        status: BaseStatus;
    }
    export interface PurchaseRequisitionLine$Queries {
        getFilteredList: Node$Operation<
            {
                siteId: string;
                supplierId: string;
                currencyId: string;
            },
            PurchaseRequisitionLine[]
        >;
    }
    export interface PurchaseRequisitionLine$Mutations {
        closeLine: Node$Operation<
            {
                purchaseRequisitionLine: string;
            },
            boolean
        >;
        applyDefaultSupplier: Node$Operation<
            {
                purchaseRequisitionLine: string;
                supplier: string;
                quantity?: decimal | string;
                grossPrice?: decimal | string;
                netPrice?: decimal | string;
                totalTaxExcludedAmount?: decimal | string;
                priceOrigin?: PriceOrigin;
            },
            boolean
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseRequisitionLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseRequisitionLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        receivingSite: QueryOperation<Site>;
        supplier: QueryOperation<Supplier>;
        currency: QueryOperation<Currency>;
        unit: QueryOperation<UnitOfMeasure>;
        itemSupplier: QueryOperation<ItemSupplier>;
        itemSiteSupplier: QueryOperation<ItemSiteSupplier>;
        stockUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseRequisitionLine$Operations {
        query: QueryOperation<PurchaseRequisitionLine>;
        read: ReadOperation<PurchaseRequisitionLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseRequisitionLine>;
            query: AggregateQueryOperation<PurchaseRequisitionLine>;
        };
        queries: PurchaseRequisitionLine$Queries;
        mutations: PurchaseRequisitionLine$Mutations;
        asyncOperations: PurchaseRequisitionLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseRequisitionLineInput }): PurchaseRequisitionLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseRequisitionLine>;
    }
    export interface PurchaseRequisitionLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: PurchaseRequisitionLine;
        basis: string;
        amount: string;
    }
    export interface PurchaseRequisitionLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface PurchaseRequisitionLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: PurchaseRequisitionLine;
        basis: string;
        amount: string;
    }
    export interface PurchaseRequisitionLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseRequisitionLineDiscountCharge$Operations {
        query: QueryOperation<PurchaseRequisitionLineDiscountCharge>;
        read: ReadOperation<PurchaseRequisitionLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<PurchaseRequisitionLineDiscountCharge>;
            query: AggregateQueryOperation<PurchaseRequisitionLineDiscountCharge>;
        };
        asyncOperations: PurchaseRequisitionLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<PurchaseRequisitionLineDiscountCharge>;
    }
    export interface WorkInProgressPurchaseOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        purchaseOrderLine: PurchaseOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressPurchaseOrderLineInput extends VitalClientNodeInput {
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressPurchaseOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentLine: integer;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        purchaseOrderLine: PurchaseOrderLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
    }
    export interface WorkInProgressPurchaseOrderLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressPurchaseOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressPurchaseOrderLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressPurchaseOrderLine$Operations {
        query: QueryOperation<WorkInProgressPurchaseOrderLine>;
        read: ReadOperation<WorkInProgressPurchaseOrderLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressPurchaseOrderLine>;
            query: AggregateQueryOperation<WorkInProgressPurchaseOrderLine>;
        };
        queries: WorkInProgressPurchaseOrderLine$Queries;
        asyncOperations: WorkInProgressPurchaseOrderLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressPurchaseOrderLineInput },
        ): WorkInProgressPurchaseOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressPurchaseOrderLine>;
    }
    export interface WorkInProgressPurchaseReceiptLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        purchaseReceiptLine: PurchaseReceiptLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressPurchaseReceiptLineInput extends VitalClientNodeInput {
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressPurchaseReceiptLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentLine: integer;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        purchaseReceiptLine: PurchaseReceiptLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
    }
    export interface WorkInProgressPurchaseReceiptLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressPurchaseReceiptLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressPurchaseReceiptLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressPurchaseReceiptLine$Operations {
        query: QueryOperation<WorkInProgressPurchaseReceiptLine>;
        read: ReadOperation<WorkInProgressPurchaseReceiptLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressPurchaseReceiptLine>;
            query: AggregateQueryOperation<WorkInProgressPurchaseReceiptLine>;
        };
        queries: WorkInProgressPurchaseReceiptLine$Queries;
        asyncOperations: WorkInProgressPurchaseReceiptLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressPurchaseReceiptLineInput },
        ): WorkInProgressPurchaseReceiptLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressPurchaseReceiptLine>;
    }
    export interface WorkInProgressPurchaseReturnLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        purchaseReturnLine: PurchaseReturnLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressPurchaseReturnLineInput extends VitalClientNodeInput {
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressPurchaseReturnLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentLine: integer;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        purchaseReturnLine: PurchaseReturnLine;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
    }
    export interface WorkInProgressPurchaseReturnLine$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressPurchaseReturnLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressPurchaseReturnLine$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressPurchaseReturnLine$Operations {
        query: QueryOperation<WorkInProgressPurchaseReturnLine>;
        read: ReadOperation<WorkInProgressPurchaseReturnLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressPurchaseReturnLine>;
            query: AggregateQueryOperation<WorkInProgressPurchaseReturnLine>;
        };
        queries: WorkInProgressPurchaseReturnLine$Queries;
        asyncOperations: WorkInProgressPurchaseReturnLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressPurchaseReturnLineInput },
        ): WorkInProgressPurchaseReturnLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressPurchaseReturnLine>;
    }
    export interface PurchaseCreditMemo extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        creditMemoDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierCreditMemo: BinaryStream;
        matchingUser: User;
        reason: ReasonCode;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTracking;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseCreditMemoLine>;
        totalTaxAmountAdjusted: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseCreditMemoInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        creditMemoDate?: string;
        billByLinkedAddress?: integer | string;
        billByAddress?: integer | string;
        billByContact?: integer | string;
        payToSupplier?: integer | string;
        payToLinkedAddress?: integer | string;
        payToAddress?: integer | string;
        payToContact?: integer | string;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        pdfSupplierCreditMemo?: BinaryStream;
        matchingUser?: integer | string;
        reason?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        businessRelation?: integer | string;
        fxRateDate?: string;
        paymentTerm?: integer | string;
        paymentTracking?: PaymentTrackingInput;
        dueDate?: string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        lines?: Partial<PurchaseCreditMemoLineInput>[];
        totalTaxAmountAdjusted?: decimal | string;
        displayStatus?: BaseDisplayStatus;
    }
    export interface PurchaseCreditMemoBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        creditMemoDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierCreditMemo: BinaryStream;
        matchingUser: User;
        reason: ReasonCode;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTrackingBinding;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseCreditMemoLineBinding>;
        totalTaxAmountAdjusted: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseCreditMemo$Mutations {
        post: Node$Operation<
            {
                creditMemo: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        repost: Node$Operation<
            {
                purchaseCreditMemo: string;
                documentData: {
                    header?: {
                        supplierDocumentNumber?: string;
                        paymentData?: {
                            supplierDocumentDate?: string;
                            paymentTerm?: integer | string;
                        };
                        totalTaxAmount?: decimal | string;
                        taxes?: DocumentTaxInput[];
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: string;
                    }[];
                };
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                creditMemo: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        sendNotificationToBuyerMail: Node$Operation<
            {
                document: string;
                user?: UserInput;
            },
            boolean
        >;
        resendNotificationForFinance: Node$Operation<
            {
                purchaseCreditMemo: string;
            },
            boolean
        >;
        synchronizeDisplayStatus: Node$Operation<
            {
                purchaseCreditMemo: string;
            },
            boolean
        >;
        enforceStatusPosted: Node$Operation<
            {
                creditMemo: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseCreditMemo$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseCreditMemo$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        billBySupplier: QueryOperation<Supplier>;
        billByLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billByAddress: QueryOperation<Address>;
        billByContact: QueryOperation<Contact>;
        payToSupplier: QueryOperation<Supplier>;
        payToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        payToAddress: QueryOperation<Address>;
        payToContact: QueryOperation<Contact>;
        matchingUser: QueryOperation<User>;
        reason: QueryOperation<ReasonCode>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface PurchaseCreditMemo$Operations {
        query: QueryOperation<PurchaseCreditMemo>;
        read: ReadOperation<PurchaseCreditMemo>;
        aggregate: {
            read: AggregateReadOperation<PurchaseCreditMemo>;
            query: AggregateQueryOperation<PurchaseCreditMemo>;
        };
        create: CreateOperation<PurchaseCreditMemoInput, PurchaseCreditMemo>;
        getDuplicate: GetDuplicateOperation<PurchaseCreditMemo>;
        duplicate: DuplicateOperation<string, PurchaseCreditMemoInput, PurchaseCreditMemo>;
        update: UpdateOperation<PurchaseCreditMemoInput, PurchaseCreditMemo>;
        updateById: UpdateByIdOperation<PurchaseCreditMemoInput, PurchaseCreditMemo>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseCreditMemo$Mutations;
        asyncOperations: PurchaseCreditMemo$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseCreditMemoInput }): PurchaseCreditMemo$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseCreditMemo>;
    }
    export interface PurchaseCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseCreditMemo;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        uPurchaseUnit: UnitOfMeasure;
        uStockUnit: UnitOfMeasure;
        purchaseInvoiceLine: PurchaseInvoiceLineToPurchaseCreditMemoLine;
        purchaseReturnLine: PurchaseReturnLineToPurchaseCreditMemoLine;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        receiptNumber: string;
        receiptSysId: integer;
        providerLinkedAddress: BusinessEntityAddress;
        consumptionLinkedAddress: BusinessEntityAddress;
        uiTaxes: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransaction>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        itemDescription: string;
        unitToStockUnitConversionFactor: string;
        priceOrigin: BasePriceOrigin;
        taxDate: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        recipientSite: Site;
        quantityInStockUnit: string;
    }
    export interface PurchaseCreditMemoLineInput extends VitalClientNodeInput {
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        purchaseInvoiceLine?: PurchaseInvoiceLineToPurchaseCreditMemoLineInput;
        purchaseReturnLine?: PurchaseReturnLineToPurchaseCreditMemoLineInput;
        uiTaxes?: string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        itemDescription?: string;
        unitToStockUnitConversionFactor?: decimal | string;
        priceOrigin?: BasePriceOrigin;
        taxDate?: string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        recipientSite?: integer | string;
        quantityInStockUnit?: decimal | string;
    }
    export interface PurchaseCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseCreditMemo;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        uPurchaseUnit: UnitOfMeasure;
        uStockUnit: UnitOfMeasure;
        purchaseInvoiceLine: PurchaseInvoiceLineToPurchaseCreditMemoLineBinding;
        purchaseReturnLine: PurchaseReturnLineToPurchaseCreditMemoLineBinding;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        receiptNumber: string;
        receiptSysId: integer;
        providerLinkedAddress: BusinessEntityAddress;
        consumptionLinkedAddress: BusinessEntityAddress;
        uiTaxes: any;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        itemDescription: string;
        unitToStockUnitConversionFactor: string;
        priceOrigin: BasePriceOrigin;
        taxDate: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        recipientSite: Site;
        quantityInStockUnit: string;
    }
    export interface PurchaseCreditMemoLine$Mutations {
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseCreditMemoLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        uPurchaseUnit: QueryOperation<UnitOfMeasure>;
        uStockUnit: QueryOperation<UnitOfMeasure>;
        providerLinkedAddress: QueryOperation<BusinessEntityAddress>;
        consumptionLinkedAddress: QueryOperation<BusinessEntityAddress>;
        recipientSite: QueryOperation<Site>;
    }
    export interface PurchaseCreditMemoLine$Operations {
        query: QueryOperation<PurchaseCreditMemoLine>;
        read: ReadOperation<PurchaseCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseCreditMemoLine>;
            query: AggregateQueryOperation<PurchaseCreditMemoLine>;
        };
        mutations: PurchaseCreditMemoLine$Mutations;
        asyncOperations: PurchaseCreditMemoLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseCreditMemoLineInput }): PurchaseCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseCreditMemoLine>;
    }
    export interface PurchaseInvoice extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        invoiceDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierInvoice: BinaryStream;
        returnLinkedAddress: BusinessEntityAddress;
        matchingUser: User;
        financeStatus: PurchaseInvoiceStatus;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTracking;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseInvoiceLine>;
        totalTaxAmountAdjusted: string;
        matchingStatus: PurchaseInvoiceMatchingStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalRemainingQuantityToCredit: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseInvoiceInput extends ClientNodeInput {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        invoiceDate?: string;
        billByLinkedAddress?: integer | string;
        billByAddress?: integer | string;
        billByContact?: integer | string;
        payToSupplier?: integer | string;
        payToLinkedAddress?: integer | string;
        payToAddress?: integer | string;
        payToContact?: integer | string;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        pdfSupplierInvoice?: BinaryStream;
        returnLinkedAddress?: integer | string;
        matchingUser?: integer | string;
        financeStatus?: PurchaseInvoiceStatus;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        businessRelation?: integer | string;
        fxRateDate?: string;
        paymentTerm?: integer | string;
        paymentTracking?: PaymentTrackingInput;
        dueDate?: string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        lines?: Partial<PurchaseInvoiceLineInput>[];
        totalTaxAmountAdjusted?: decimal | string;
        matchingStatus?: PurchaseInvoiceMatchingStatus;
        displayStatus?: BaseDisplayStatus;
    }
    export interface PurchaseInvoiceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        invoiceDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierInvoice: BinaryStream;
        returnLinkedAddress: BusinessEntityAddress;
        matchingUser: User;
        financeStatus: PurchaseInvoiceStatus;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTrackingBinding;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseInvoiceLineBinding>;
        totalTaxAmountAdjusted: string;
        matchingStatus: PurchaseInvoiceMatchingStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalRemainingQuantityToCredit: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseInvoice$Queries {
        isStatusValidForVarianceApproval: Node$Operation<
            {
                document: string;
            },
            boolean
        >;
        getRateOrReverseRate: Node$Operation<
            {
                sourceCurrencyId?: string;
                destinationCurrencyId?: string;
                rateDate?: string;
            },
            decimal
        >;
        getRateOrReverseDivisor: Node$Operation<
            {
                sourceCurrencyId?: string;
                destinationCurrencyId?: string;
                rateDate?: string;
            },
            decimal
        >;
        rateDescription: Node$Operation<
            {
                sourceCurrencyId?: string;
                destinationCurrencyId?: string;
                companyFxRate?: decimal | string;
                companyFxRateDivisor?: decimal | string;
            },
            string
        >;
    }
    export interface PurchaseInvoice$Mutations {
        acceptAllVariances: Node$Operation<
            {
                document: string;
                matchingStatusToUpdate: PurchaseInvoiceMatchingStatus;
                isSafeToRetry?: boolean | string;
            },
            PurchaseInvoice
        >;
        sendNotificationToBuyerMail: Node$Operation<
            {
                document: string;
                user?: UserInput;
            },
            boolean
        >;
        post: Node$Operation<
            {
                purchaseInvoice: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
                validationMessages: {
                    message: string;
                    lineNumber: integer;
                    type: Common;
                    sourceDocumentNumber: string;
                }[];
                stockPostingResult: string;
            }
        >;
        financeIntegrationCheck: Node$Operation<
            {
                invoice: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
                validationMessages: {
                    message: string;
                    lineNumber: integer;
                    type: Common;
                    sourceDocumentNumber: string;
                }[];
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                purchaseInvoice: string;
            },
            boolean
        >;
        createCreditMemoFromInvoice: Node$Operation<
            {
                document: string;
                reasonCode: string;
                totalAmountExcludingTax: decimal | string;
                supplierDocumentDate: string;
            },
            PurchaseCreditMemo
        >;
        repost: Node$Operation<
            {
                purchaseInvoice: string;
                documentData: {
                    header?: {
                        supplierDocumentNumber?: string;
                        paymentData?: {
                            supplierDocumentDate?: string;
                            paymentTerm?: integer | string;
                        };
                        totalTaxAmount?: decimal | string;
                        taxes?: DocumentTaxInput[];
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: string;
                    }[];
                };
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resynchronizeDisplayStatus: Node$Operation<
            {
                purchaseInvoice: string;
            },
            {
                oldDisplayStatus: PurchaseInvoiceDisplayStatus;
                newDisplayStatus: PurchaseInvoiceDisplayStatus;
            }
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseInvoice$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        billBySupplier: QueryOperation<Supplier>;
        billByLinkedAddress: QueryOperation<BusinessEntityAddress>;
        billByAddress: QueryOperation<Address>;
        billByContact: QueryOperation<Contact>;
        payToSupplier: QueryOperation<Supplier>;
        payToLinkedAddress: QueryOperation<BusinessEntityAddress>;
        payToAddress: QueryOperation<Address>;
        payToContact: QueryOperation<Contact>;
        returnLinkedAddress: QueryOperation<BusinessEntityAddress>;
        matchingUser: QueryOperation<User>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface PurchaseInvoice$Operations {
        query: QueryOperation<PurchaseInvoice>;
        read: ReadOperation<PurchaseInvoice>;
        aggregate: {
            read: AggregateReadOperation<PurchaseInvoice>;
            query: AggregateQueryOperation<PurchaseInvoice>;
        };
        queries: PurchaseInvoice$Queries;
        create: CreateOperation<PurchaseInvoiceInput, PurchaseInvoice>;
        getDuplicate: GetDuplicateOperation<PurchaseInvoice>;
        duplicate: DuplicateOperation<string, PurchaseInvoiceInput, PurchaseInvoice>;
        update: UpdateOperation<PurchaseInvoiceInput, PurchaseInvoice>;
        updateById: UpdateByIdOperation<PurchaseInvoiceInput, PurchaseInvoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseInvoice$Mutations;
        asyncOperations: PurchaseInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseInvoiceInput }): PurchaseInvoice$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseInvoice>;
    }
    export interface PurchaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseInvoice;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        taxableAmount: string;
        exemptAmount: string;
        canHaveLandedCost: boolean;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        recipientSite: Site;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseInvoiceLine;
        purchaseReturnLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLine>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        uiTaxes: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransaction>;
        varianceText: TextStream;
        varianceApprover: User;
        purchaseOrderLine: PurchaseOrderLineToPurchaseInvoiceLine;
        purchaseCreditMemoLines: ClientCollection<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
        creditedQuantity: string;
        providerLinkedAddress: BusinessEntityAddress;
        consumptionLinkedAddress: BusinessEntityAddress;
        landedCostCheckResult: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        origin: BaseOrigin;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        landedCost: LandedCostDocumentLine;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        discount: string;
        charge: string;
        remainingQuantityToCredit: string;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        quantityInStockUnit: string;
        varianceType: PurchaseInvoiceVarianceType;
        matchingStatus: PurchaseInvoiceMatchingStatus;
    }
    export interface PurchaseInvoiceLineInput extends VitalClientNodeInput {
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        taxableAmount?: decimal | string;
        exemptAmount?: decimal | string;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversionFactor?: decimal | string;
        recipientSite?: integer | string;
        purchaseReceiptLine?: PurchaseReceiptLineToPurchaseInvoiceLineInput;
        purchaseReturnLines?: Partial<PurchaseReturnLineToPurchaseInvoiceLineInput>[];
        uiTaxes?: string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        stockTransactions?: Partial<StockTransactionInput>[];
        varianceText?: TextStream;
        varianceApprover?: integer | string;
        purchaseOrderLine?: PurchaseOrderLineToPurchaseInvoiceLineInput;
        origin?: BaseOrigin;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        quantityInStockUnit?: decimal | string;
        matchingStatus?: PurchaseInvoiceMatchingStatus;
    }
    export interface PurchaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseInvoice;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        taxableAmount: string;
        exemptAmount: string;
        canHaveLandedCost: boolean;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        recipientSite: Site;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseInvoiceLineBinding;
        purchaseReturnLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLineBinding>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        uiTaxes: any;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        varianceText: TextStream;
        varianceApprover: User;
        purchaseOrderLine: PurchaseOrderLineToPurchaseInvoiceLineBinding;
        purchaseCreditMemoLines: ClientCollection<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
        creditedQuantity: string;
        providerLinkedAddress: BusinessEntityAddress;
        consumptionLinkedAddress: BusinessEntityAddress;
        landedCostCheckResult: any;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        origin: BaseOrigin;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        landedCost: LandedCostDocumentLineBinding;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        discount: string;
        charge: string;
        remainingQuantityToCredit: string;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        unitToStockUnitConversionFactor: string;
        quantityInStockUnit: string;
        varianceType: PurchaseInvoiceVarianceType;
        matchingStatus: PurchaseInvoiceMatchingStatus;
    }
    export interface PurchaseInvoiceLine$Mutations {
        acceptAllVariancesLine: Node$Operation<
            {
                purchaseInvoiceId: integer | string;
                purchaseInvoiceLineId: integer | string;
                approve: boolean | string;
            },
            boolean
        >;
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseInvoiceLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        currency: QueryOperation<Currency>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        recipientSite: QueryOperation<Site>;
        varianceApprover: QueryOperation<User>;
        providerLinkedAddress: QueryOperation<BusinessEntityAddress>;
        consumptionLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
    }
    export interface PurchaseInvoiceLine$Operations {
        query: QueryOperation<PurchaseInvoiceLine>;
        read: ReadOperation<PurchaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseInvoiceLine>;
            query: AggregateQueryOperation<PurchaseInvoiceLine>;
        };
        mutations: PurchaseInvoiceLine$Mutations;
        asyncOperations: PurchaseInvoiceLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseInvoiceLineInput }): PurchaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseInvoiceLine>;
    }
    export interface PurchaseOrder extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        businessRelation: Supplier;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        supplierLinkedAddress: BusinessEntityAddress;
        supplierAddress: Address;
        supplierContact: Contact;
        orderDate: string;
        changeRequestedDescription: TextStream;
        supplierOrderReference: string;
        isOrderAssignmentLinked: boolean;
        deliveryMode: DeliveryMode;
        defaultBuyer: User;
        isApprovalManaged: boolean;
        isGrossPriceMissing: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        paymentTracking: PaymentTracking;
        lines: ClientCollection<PurchaseOrderLine>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTaxInCompanyCurrency: string;
        receiptStatus: PurchaseOrderReceiptStatus;
        earliestExpectedDate: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalQuantityToReceiveInStockUnit: string;
        jsonAggregateLandedCostTypes: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        paymentTerm: PaymentTerm;
        totalAmountExcludingTax: string;
        taxes: ClientCollection<DocumentTax>;
        totalAmountIncludingTax: string;
        isPurchaseOrderSuggestion: boolean;
    }
    export interface PurchaseOrderInput extends ClientNodeInput {
        number?: string;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        isOverwriteNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        businessRelation?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        supplierLinkedAddress?: integer | string;
        supplierAddress?: integer | string;
        supplierContact?: integer | string;
        orderDate?: string;
        changeRequestedDescription?: TextStream;
        supplierOrderReference?: string;
        deliveryMode?: integer | string;
        defaultBuyer?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        currency?: integer | string;
        internalNote?: TextStream;
        fxRateDate?: string;
        companyFxRateDivisor?: decimal | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        paymentTracking?: PaymentTrackingInput;
        lines?: Partial<PurchaseOrderLineInput>[];
        invoiceStatus?: InvoiceStatus;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        receiptStatus?: PurchaseOrderReceiptStatus;
        earliestExpectedDate?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        paymentTerm?: integer | string;
        totalAmountExcludingTax?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        isPurchaseOrderSuggestion?: boolean | string;
    }
    export interface PurchaseOrderBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        isOverwriteNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        businessRelation: Supplier;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        supplierLinkedAddress: BusinessEntityAddress;
        supplierAddress: Address;
        supplierContact: Contact;
        orderDate: string;
        changeRequestedDescription: TextStream;
        supplierOrderReference: string;
        isOrderAssignmentLinked: boolean;
        deliveryMode: DeliveryMode;
        defaultBuyer: User;
        isApprovalManaged: boolean;
        isGrossPriceMissing: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        paymentTracking: PaymentTrackingBinding;
        lines: ClientCollection<PurchaseOrderLineBinding>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTaxInCompanyCurrency: string;
        receiptStatus: PurchaseOrderReceiptStatus;
        earliestExpectedDate: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalQuantityToReceiveInStockUnit: string;
        jsonAggregateLandedCostTypes: any;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        paymentTerm: PaymentTerm;
        totalAmountExcludingTax: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalAmountIncludingTax: string;
        isPurchaseOrderSuggestion: boolean;
    }
    export interface PurchaseOrder$Mutations {
        sendRequestChangesMail: Node$Operation<
            {
                document?: string;
                user?: UserInput;
            },
            boolean
        >;
        approve: Node$Operation<
            {
                document: string;
                approvalStatusToUpdate: PurchaseOrderApprovalStatus;
                isSafeToRetry?: boolean | string;
            },
            PurchaseOrder
        >;
        confirm: Node$Operation<
            {
                document?: string;
                confirmStatusToUpdate: PurchaseOrderConfirmStatus;
                isSafeToRetry?: boolean | string;
            },
            PurchaseOrder
        >;
        createPurchaseReceipt: Node$Operation<
            {
                document: string;
            },
            PurchaseReceipt[]
        >;
        close: Node$Operation<
            {
                purchaseOrder: string;
                isSafeToRetry?: boolean | string;
                controlOrderLinks?: boolean | string;
            },
            PurchaseOrder
        >;
        closeLine: Node$Operation<
            {
                purchaseOrderLine: string;
            },
            boolean
        >;
        setIsSentPurchaseOrder: Node$Operation<
            {
                purchaseOrder: string;
            },
            boolean
        >;
        printPurchaseOrderAndEmail: Node$Operation<
            {
                purchaseOrder: string;
                contactTitle: string;
                contactLastName: string;
                contactFirstName: string;
                contactEmail: string;
            },
            boolean
        >;
        createPurchaseOrderReplenishment: Node$Operation<
            {
                data: {
                    site: integer | string;
                    item: integer | string;
                    supplier: integer | string;
                    grossPrice: decimal | string;
                    unit: integer | string;
                    quantity: decimal | string;
                    priceOrigin?: PriceOrigin;
                    orderDate?: string;
                    expectedReceiptDate?: string;
                    storedDimensions?: string | null;
                    storedAttributes?: string | null;
                    stockSite?: integer | string;
                    currency?: integer | string;
                };
            },
            PurchaseOrder
        >;
        financeIntegrationCheck: Node$Operation<
            {
                purchaseOrder: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        repost: Node$Operation<
            {
                purchaseOrder: string;
                orderLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
                saveOnly?: boolean | string;
                financeTransaction?: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        beforePrintPurchaseOrder: Node$Operation<
            {
                order: string;
            },
            boolean
        >;
        afterPrintPurchaseOrder: Node$Operation<
            {
                order: string;
            },
            boolean
        >;
        open: Node$Operation<
            {
                purchaseOrder: string;
            },
            boolean
        >;
        reopenLine: Node$Operation<
            {
                purchaseOrderLine: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseOrder$AsyncOperations {
        massApproval: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        createTestPurchaseOrders: AsyncOperation<
            {
                supplierId: string;
                itemId: string;
                itemQuantity: integer | string;
                orderQuantity: integer | string;
                numberOfLinesPerOrder: integer | string;
                orderNumberRoot?: string;
                fixedNumberOfLines?: boolean | string;
            },
            string
        >;
        printBulk: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseOrder$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        supplier: QueryOperation<Supplier>;
        billBySupplier: QueryOperation<Supplier>;
        supplierLinkedAddress: QueryOperation<BusinessEntityAddress>;
        supplierAddress: QueryOperation<Address>;
        supplierContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        defaultBuyer: QueryOperation<User>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        paymentTerm: QueryOperation<PaymentTerm>;
    }
    export interface PurchaseOrder$Operations {
        query: QueryOperation<PurchaseOrder>;
        read: ReadOperation<PurchaseOrder>;
        aggregate: {
            read: AggregateReadOperation<PurchaseOrder>;
            query: AggregateQueryOperation<PurchaseOrder>;
        };
        create: CreateOperation<PurchaseOrderInput, PurchaseOrder>;
        getDuplicate: GetDuplicateOperation<PurchaseOrder>;
        duplicate: DuplicateOperation<string, PurchaseOrderInput, PurchaseOrder>;
        update: UpdateOperation<PurchaseOrderInput, PurchaseOrder>;
        updateById: UpdateByIdOperation<PurchaseOrderInput, PurchaseOrder>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseOrder$Mutations;
        asyncOperations: PurchaseOrder$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseOrderInput }): PurchaseOrder$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseOrder>;
    }
    export interface PurchaseOrderLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseOrder;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        isPurchaseOrderSuggestion: boolean;
        expectedReceiptDate: string;
        changeRequestedDescription: string;
        uiTaxes: string;
        purchaseRequisitionLines: ClientCollection<PurchaseRequisitionLineToPurchaseOrderLine>;
        purchaseReceiptLines: ClientCollection<PurchaseOrderLineToPurchaseReceiptLine>;
        numberOfPurchaseReceiptLines: integer;
        workInProgress: WorkInProgressPurchaseOrderLine;
        receivedQuantity: string;
        quantityReceivedInProgress: string;
        quantityToReceive: string;
        receivedQuantityInStockUnit: string;
        postedQuantityReceivedInStockUnit: string;
        receivedQuantityProgress: string;
        purchaseInvoiceLines: ClientCollection<PurchaseOrderLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingQuantityToInvoiceOnOrder: string;
        invoicedQuantityInStockUnit: string;
        assignments: ClientCollection<OrderAssignment>;
        uDemandOrderLine: BaseDocumentLine;
        uDemandOrderLineLink: string;
        uDemandOrderQuantity: string;
        uSupplyOrderQuantity: string;
        remainingAmountToReceiveExcludingTax: string;
        remainingAmountToReceiveExcludingTaxInCompanyCurrency: string;
        actualLandedCostInCompanyCurrency: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        lineReceiptStatus: PurchaseOrderReceiptStatus;
        remainingQuantityToProcessForLandedCost: string;
        quantityToReceiveInStockUnit: string;
        lineInvoiceStatus: PurchaseOrderInvoiceStatus;
        amountForLandedCostAllocation: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockSiteAddress: Address;
        stockSiteContact: Contact;
    }
    export interface PurchaseOrderLineInput extends VitalClientNodeInput {
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        isPurchaseOrderSuggestion?: boolean | string;
        expectedReceiptDate?: string;
        changeRequestedDescription?: string;
        uiTaxes?: string;
        purchaseRequisitionLines?: Partial<PurchaseRequisitionLineToPurchaseOrderLineInput>[];
        workInProgress?: WorkInProgressPurchaseOrderLineInput;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineReceiptStatus?: PurchaseOrderReceiptStatus;
        lineInvoiceStatus?: PurchaseOrderInvoiceStatus;
        status?: BaseStatus;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockSiteAddress?: integer | string;
        stockSiteContact?: integer | string;
    }
    export interface PurchaseOrderLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseOrder;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        isPurchaseOrderSuggestion: boolean;
        expectedReceiptDate: string;
        changeRequestedDescription: string;
        uiTaxes: any;
        purchaseRequisitionLines: ClientCollection<PurchaseRequisitionLineToPurchaseOrderLineBinding>;
        purchaseReceiptLines: ClientCollection<PurchaseOrderLineToPurchaseReceiptLine>;
        numberOfPurchaseReceiptLines: integer;
        workInProgress: WorkInProgressPurchaseOrderLineBinding;
        receivedQuantity: string;
        quantityReceivedInProgress: string;
        quantityToReceive: string;
        receivedQuantityInStockUnit: string;
        postedQuantityReceivedInStockUnit: string;
        receivedQuantityProgress: string;
        purchaseInvoiceLines: ClientCollection<PurchaseOrderLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingQuantityToInvoiceOnOrder: string;
        invoicedQuantityInStockUnit: string;
        assignments: ClientCollection<OrderAssignment>;
        uDemandOrderLine: BaseDocumentLine;
        uDemandOrderLineLink: string;
        uDemandOrderQuantity: string;
        uSupplyOrderQuantity: string;
        remainingAmountToReceiveExcludingTax: string;
        remainingAmountToReceiveExcludingTaxInCompanyCurrency: string;
        actualLandedCostInCompanyCurrency: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        lineReceiptStatus: PurchaseOrderReceiptStatus;
        remainingQuantityToProcessForLandedCost: string;
        quantityToReceiveInStockUnit: string;
        lineInvoiceStatus: PurchaseOrderInvoiceStatus;
        amountForLandedCostAllocation: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockSiteAddress: Address;
        stockSiteContact: Contact;
    }
    export interface PurchaseOrderLine$Queries {
        controlCloseLine: Node$Operation<
            {
                purchaseOrderLine: string;
            },
            boolean
        >;
        getPurchaseLeadTime: Node$Operation<
            {
                item: string;
                site: string;
                supplier: string;
            },
            integer
        >;
        controlDelete: Node$Operation<
            {
                purchaseOrderLine: string;
            },
            string[]
        >;
    }
    export interface PurchaseOrderLine$Mutations {
        calculateLineTaxes: Node$Operation<
            {
                data?: {
                    site: integer | string;
                    businessPartner: integer | string;
                    addresses?: {
                        consumerCountry?: integer | string;
                        consumerPostcode?: string;
                        shipToCustomerAddress?: integer | string;
                    };
                    item: integer | string;
                    currency: integer | string;
                    amountExcludingTax: decimal | string;
                    quantity: decimal | string;
                    taxes?: {
                        taxReference?: integer | string;
                        taxCategoryReference?: integer | string;
                        isTaxMandatory: boolean | string;
                        isSubjectToGlTaxExcludedAmount: boolean | string;
                        _sortValue: integer | string;
                    }[];
                    taxDate?: string;
                };
            },
            {
                taxes: {
                    taxCategoryReference: TaxCategory;
                    taxCategory: string;
                    taxRate: string;
                    taxAmount: string;
                    taxReference: Tax;
                    tax: string;
                    deductibleTaxAmount: string;
                    deductibleTaxRate: string;
                    exemptAmount: string;
                    isReverseCharge: boolean;
                    nonTaxableAmount: string;
                    taxAmountAdjusted: string;
                    taxableAmount: string;
                    currency: Currency;
                    isTaxMandatory: boolean;
                    isSubjectToGlTaxExcludedAmount: boolean;
                    _sortValue: integer;
                }[];
                taxAmount: string;
                taxAmountAdjusted: string;
                amountIncludingTax: string;
            }
        >;
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseOrderLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseOrderLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        uDemandOrderLine: QueryOperation<BaseDocumentLine>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockSiteAddress: QueryOperation<Address>;
        stockSiteContact: QueryOperation<Contact>;
    }
    export interface PurchaseOrderLine$Operations {
        query: QueryOperation<PurchaseOrderLine>;
        read: ReadOperation<PurchaseOrderLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseOrderLine>;
            query: AggregateQueryOperation<PurchaseOrderLine>;
        };
        queries: PurchaseOrderLine$Queries;
        update: UpdateOperation<PurchaseOrderLineInput, PurchaseOrderLine>;
        updateById: UpdateByIdOperation<PurchaseOrderLineInput, PurchaseOrderLine>;
        mutations: PurchaseOrderLine$Mutations;
        asyncOperations: PurchaseOrderLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseOrderLineInput }): PurchaseOrderLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseOrderLine>;
    }
    export interface PurchaseReturn extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        text: TextStream;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        returnSite: Site;
        returnRequestDate: string;
        effectiveDate: string;
        supplierDocumentNumber: string;
        supplierAddress: Address;
        returnToAddress: Address;
        supplierReturnReference: string;
        changeRequestDescription: string;
        isApprovalManaged: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        internalNote: TextStream;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        paymentTracking: PaymentTracking;
        lines: ClientCollection<PurchaseReturnLine>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        totalAmountIncludingTaxInCompanyCurrency: string;
        shippingStatus: PurchaseReturnShippingStatus;
        allocationStatus: StockAllocationStatus;
        creditStatus: PurchaseReturnCreditStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        taxes: ClientCollection<DocumentTax>;
        returnItems: boolean;
    }
    export interface PurchaseReturnInput extends ClientNodeInput {
        number?: string;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        text?: TextStream;
        isOverwriteNote?: boolean | string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        returnSite?: integer | string;
        returnRequestDate?: string;
        supplierDocumentNumber?: string;
        supplierAddress?: integer | string;
        returnToAddress?: integer | string;
        supplierReturnReference?: string;
        changeRequestDescription?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        site?: integer | string;
        stockSite?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        internalNote?: TextStream;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        fxRateDate?: string;
        companyFxRateDivisor?: decimal | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        paymentTracking?: PaymentTrackingInput;
        lines?: Partial<PurchaseReturnLineInput>[];
        invoiceStatus?: InvoiceStatus;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        shippingStatus?: PurchaseReturnShippingStatus;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        taxes?: Partial<DocumentTaxInput>[];
        returnItems?: boolean | string;
    }
    export interface PurchaseReturnBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        text: TextStream;
        isOverwriteNote: boolean;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        returnSite: Site;
        returnRequestDate: string;
        effectiveDate: string;
        supplierDocumentNumber: string;
        supplierAddress: Address;
        returnToAddress: Address;
        supplierReturnReference: string;
        changeRequestDescription: string;
        isApprovalManaged: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        internalNote: TextStream;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        paymentTracking: PaymentTrackingBinding;
        lines: ClientCollection<PurchaseReturnLineBinding>;
        invoiceStatus: InvoiceStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        totalAmountIncludingTaxInCompanyCurrency: string;
        shippingStatus: PurchaseReturnShippingStatus;
        allocationStatus: StockAllocationStatus;
        creditStatus: PurchaseReturnCreditStatus;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        taxes: ClientCollection<DocumentTaxBinding>;
        returnItems: boolean;
    }
    export interface PurchaseReturn$Mutations {
        createPurchaseInvoice: Node$Operation<
            {
                returnDoc: string;
            },
            PurchaseInvoice[]
        >;
        post: Node$Operation<
            {
                purchaseReturn: string;
            },
            boolean
        >;
        repost: Node$Operation<
            {
                purchaseReturn: string;
                documentLines?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        close: Node$Operation<
            {
                returnDoc: string;
            },
            boolean
        >;
        confirm: Node$Operation<
            {
                document: string;
            },
            boolean
        >;
        approve: Node$Operation<
            {
                document: string;
                toBeApproved?: boolean | string;
            },
            boolean
        >;
        submitForApproval: Node$Operation<
            {
                document: string;
            },
            boolean
        >;
        financeIntegrationCheck: Node$Operation<
            {
                purchaseReturn: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        resendNotificationForFinance: Node$Operation<
            {
                purchaseReturn: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface PurchaseReturn$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface PurchaseReturn$Lookups {
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        supplier: QueryOperation<Supplier>;
        billBySupplier: QueryOperation<Supplier>;
        returnSite: QueryOperation<Site>;
        supplierAddress: QueryOperation<Address>;
        returnToAddress: QueryOperation<Address>;
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
    }
    export interface PurchaseReturn$Operations {
        query: QueryOperation<PurchaseReturn>;
        read: ReadOperation<PurchaseReturn>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReturn>;
            query: AggregateQueryOperation<PurchaseReturn>;
        };
        create: CreateOperation<PurchaseReturnInput, PurchaseReturn>;
        getDuplicate: GetDuplicateOperation<PurchaseReturn>;
        duplicate: DuplicateOperation<string, PurchaseReturnInput, PurchaseReturn>;
        update: UpdateOperation<PurchaseReturnInput, PurchaseReturn>;
        updateById: UpdateByIdOperation<PurchaseReturnInput, PurchaseReturn>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: PurchaseReturn$Mutations;
        asyncOperations: PurchaseReturn$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseReturnInput }): PurchaseReturn$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReturn>;
    }
    export interface PurchaseReturnLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReturn;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        uiTaxes: string;
        expectedReturnDate: string;
        totalTaxExcludedAmount: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseReturnLine;
        purchaseInvoiceLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLine>;
        workInProgress: WorkInProgressPurchaseReturnLine;
        invoicedQuantity: string;
        invoicedQuantityInStockUnit: string;
        quantityToInvoice: string;
        shippedStatus: PurchaseReturnShippingStatus;
        approvalStatus: PurchaseDocumentApprovalStatus;
        reason: ReasonCode;
        creditedQuantity: string;
        lineCreditStatus: PurchaseReturnCreditStatus;
        purchaseCreditMemoLines: ClientCollection<PurchaseReturnLineToPurchaseCreditMemoLine>;
        remainingQuantityToCredit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        stockTransactions: ClientCollection<StockTransaction>;
        stockMovements: ClientCollection<StockJournal>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        creditedQuantityInStockUnit: string;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        lineInvoiceStatus: PurchaseReturnInvoiceStatus;
        quantityToInvoiceInStockUnit: string;
        remainingQuantityToAllocate: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        allocationStatus: StockAllocationStatus;
        stockDetails: ClientCollection<StockIssueDetail>;
    }
    export interface PurchaseReturnLineInput extends VitalClientNodeInput {
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        uiTaxes?: string;
        expectedReturnDate?: string;
        totalTaxExcludedAmount?: decimal | string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        purchaseReceiptLine?: PurchaseReceiptLineToPurchaseReturnLineInput;
        workInProgress?: WorkInProgressPurchaseReturnLineInput;
        shippedStatus?: PurchaseReturnShippingStatus;
        approvalStatus?: PurchaseDocumentApprovalStatus;
        reason?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineInvoiceStatus?: PurchaseReturnInvoiceStatus;
        status?: BaseStatus;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockDetails?: Partial<StockIssueDetailInput>[];
    }
    export interface PurchaseReturnLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReturn;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        uiTaxes: any;
        expectedReturnDate: string;
        totalTaxExcludedAmount: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseReturnLineBinding;
        purchaseInvoiceLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLine>;
        workInProgress: WorkInProgressPurchaseReturnLineBinding;
        invoicedQuantity: string;
        invoicedQuantityInStockUnit: string;
        quantityToInvoice: string;
        shippedStatus: PurchaseReturnShippingStatus;
        approvalStatus: PurchaseDocumentApprovalStatus;
        reason: ReasonCode;
        creditedQuantity: string;
        lineCreditStatus: PurchaseReturnCreditStatus;
        purchaseCreditMemoLines: ClientCollection<PurchaseReturnLineToPurchaseCreditMemoLine>;
        remainingQuantityToCredit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockMovements: ClientCollection<StockJournal>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        creditedQuantityInStockUnit: string;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        lineInvoiceStatus: PurchaseReturnInvoiceStatus;
        quantityToInvoiceInStockUnit: string;
        remainingQuantityToAllocate: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        allocationStatus: StockAllocationStatus;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
    }
    export interface PurchaseReturnLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface PurchaseReturnLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PurchaseReturnLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        supplier: QueryOperation<Supplier>;
        itemSupplier: QueryOperation<ItemSupplier>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
        company: QueryOperation<Company>;
        reason: QueryOperation<ReasonCode>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
    }
    export interface PurchaseReturnLine$Operations {
        query: QueryOperation<PurchaseReturnLine>;
        read: ReadOperation<PurchaseReturnLine>;
        aggregate: {
            read: AggregateReadOperation<PurchaseReturnLine>;
            query: AggregateQueryOperation<PurchaseReturnLine>;
        };
        mutations: PurchaseReturnLine$Mutations;
        asyncOperations: PurchaseReturnLine$AsyncOperations;
        lookups(dataOrId: string | { data: PurchaseReturnLineInput }): PurchaseReturnLine$Lookups;
        getDefaults: GetDefaultsOperation<PurchaseReturnLine>;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemSiteCostExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
    }
    export interface ItemSiteCostInputExtension {
        itemSite?: integer | string;
        costCategory?: integer | string;
        fromDate?: string;
        toDate?: string;
        version?: integer | string;
        forQuantity?: decimal | string;
        isCalculated?: boolean | string;
        materialCost?: decimal | string;
        machineCost?: decimal | string;
        laborCost?: decimal | string;
        toolCost?: decimal | string;
        indirectCost?: decimal | string;
    }
    export interface ItemSiteCostBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        itemSite: ItemSite;
        costCategory: CostCategory;
        fromDate: string;
        toDate: string;
        version: integer;
        stockUnit: UnitOfMeasure;
        forQuantity: string;
        isCalculated: boolean;
        materialCost: string;
        machineCost: string;
        laborCost: string;
        toolCost: string;
        indirectCost: string;
        totalCost: string;
        unitCost: string;
        areStockTransactionsAvailable: boolean;
    }
    export interface ItemSiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface ItemSiteInputExtension {
        site?: integer | string;
        prodLeadTime?: integer | string;
        safetyStock?: decimal | string;
        batchQuantity?: decimal | string;
        purchaseLeadTime?: integer | string;
        isOrderToOrder?: boolean | string;
        replenishmentMethod?: ReplenishmentMethod;
        reorderPoint?: decimal | string;
        preferredProcess?: PreferredProcess;
        indirectCostSection?: integer | string;
        valuationMethod?: CostValuationMethod;
        outboundDefaultLocation?: integer | string;
        inboundDefaultLocation?: integer | string;
        completedProductDefaultLocation?: integer | string;
        inboundDefaultQualityValue?: integer | string;
        completedProductDefaultQualityValue?: integer | string;
        isSpecificItemTaxGroup?: boolean | string;
        itemTaxGroup?: integer | string;
        economicOrderQuantity?: decimal | string;
        stockValuationAtAverageCost?: decimal | string;
    }
    export interface ItemSiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        site: Site;
        id: string;
        prodLeadTime: integer;
        stockUnit: UnitOfMeasure;
        safetyStock: string;
        batchQuantity: string;
        itemSiteCost: ClientCollection<ItemSiteCost>;
        stdCostValue: string;
        purchaseLeadTime: integer;
        isOrderToOrder: boolean;
        expectedQuantity: string;
        requiredQuantity: string;
        replenishmentMethod: ReplenishmentMethod;
        reorderPoint: string;
        preferredProcess: PreferredProcess;
        suppliers: ClientCollection<ItemSiteSupplier>;
        defaultSupplier: Supplier;
        indirectCostSection: IndirectCostSection;
        valuationMethod: CostValuationMethod;
        costs: ClientCollection<ItemSiteCost>;
        outboundDefaultLocation: Location;
        inboundDefaultLocation: Location;
        completedProductDefaultLocation: Location;
        stockRecords: ClientCollection<Stock>;
        inStockQuantity: string;
        allocatedQuantity: string;
        acceptedStockQuantity: string;
        rejectedStockQuantity: string;
        onQualityControlStockQuantity: string;
        inTransitStockQuantity: string;
        fifoCosts: ClientCollection<FifoValuationTier>;
        fifoCostValue: string;
        fifoCostSum: string;
        currentCost: string;
        stockValueAtStandardCost: string;
        stockValue: string;
        countingInProgress: boolean;
        areStockTransactionsAvailable: boolean;
        allocations: ClientCollection<StockAllocation>;
        allocationRecords: ClientCollection<StockAllocation>;
        inboundDefaultQualityValue: StockStatus;
        completedProductDefaultQualityValue: StockStatus;
        isSpecificItemTaxGroup: boolean;
        itemTaxGroup: ItemTaxGroup;
        economicOrderQuantity: string;
        stockValuationAtAverageCost: string;
        averageCostValue: string;
    }
    export interface LandedCostAllocationExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostAllocationInputExtension {
        allocatedDocumentLine?: integer | string;
        costAmount?: decimal | string;
        costAmountInCompanyCurrency?: decimal | string;
        sourceAllocationLine?: integer | string;
        creditedCostAmount?: decimal | string;
        creditedCostAmountInCompanyCurrency?: decimal | string;
    }
    export interface LandedCostAllocationBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface PaymentTrackingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        document: BaseDocument;
        paymentTerm: PaymentTerm;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        penaltyPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate: string;
        openItems: ClientCollection<BaseOpenItem>;
        status: OpenItemStatus;
        currency: Currency;
        amountPaid: string;
        forcedAmountPaid: string;
        paymentLines: ClientCollection<PaymentDocumentLine>;
    }
    export interface PaymentTrackingInputExtension {
        paymentTerm?: integer | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        penaltyPaymentType?: PaymentTermDiscountOrPenaltyType;
        discountPaymentType?: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate?: string;
    }
    export interface PaymentTrackingBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        document: BaseDocument;
        paymentTerm: PaymentTerm;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        penaltyPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate: string;
        openItems: ClientCollection<BaseOpenItem>;
        status: OpenItemStatus;
        currency: Currency;
        amountPaid: string;
        forcedAmountPaid: string;
        paymentLines: ClientCollection<PaymentDocumentLine>;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: string;
        storedAttributes: string;
        isPurchaseRequisitionApprovalManaged: boolean;
        purchaseRequisitionDefaultApprover: User;
        purchaseRequisitionSubstituteApprover: User;
        isPurchaseOrderApprovalManaged: boolean;
        isPurchaseReturnApprovalManaged: boolean;
        purchaseOrderDefaultApprover: User;
        purchaseOrderSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        defaultStockStatus?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        isPurchaseRequisitionApprovalManaged?: boolean | string;
        purchaseRequisitionDefaultApprover?: integer | string;
        purchaseRequisitionSubstituteApprover?: integer | string;
        isPurchaseOrderApprovalManaged?: boolean | string;
        isPurchaseReturnApprovalManaged?: boolean | string;
        purchaseOrderDefaultApprover?: integer | string;
        purchaseOrderSubstituteApprover?: integer | string;
        _syncTick?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: any;
        storedAttributes: any;
        isPurchaseRequisitionApprovalManaged: boolean;
        purchaseRequisitionDefaultApprover: User;
        purchaseRequisitionSubstituteApprover: User;
        isPurchaseOrderApprovalManaged: boolean;
        isPurchaseReturnApprovalManaged: boolean;
        purchaseOrderDefaultApprover: User;
        purchaseOrderSubstituteApprover: User;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteExtension$Lookups {
        purchaseRequisitionDefaultApprover: QueryOperation<User>;
        purchaseRequisitionSubstituteApprover: QueryOperation<User>;
        purchaseOrderDefaultApprover: QueryOperation<User>;
        purchaseOrderSubstituteApprover: QueryOperation<User>;
    }
    export interface SiteExtension$Operations {
        lookups(dataOrId: string | { data: SiteInput }): SiteExtension$Lookups;
    }
    export interface SupplierExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificate>;
        items: ClientCollection<ItemSupplier>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        purchaseOrders: ClientCollection<PurchaseOrder>;
        defaultBuyer: User;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface SupplierInputExtension {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        supplierType?: SupplierType;
        standardIndustrialClassification?: integer | string;
        paymentMethod?: string;
        parent?: integer | string;
        incoterm?: integer | string;
        certificates?: Partial<SupplierCertificateInput>[];
        items?: Partial<ItemSupplierInput>[];
        billBySupplier?: integer | string;
        billByAddress?: integer | string;
        payToSupplier?: integer | string;
        payToAddress?: integer | string;
        returnToSupplier?: integer | string;
        returnToAddress?: integer | string;
        deliveryMode?: integer | string;
        datevId?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        defaultBuyer?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        analyticalData?: integer | string;
    }
    export interface SupplierBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificateBinding>;
        items: ClientCollection<ItemSupplierBinding>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        purchaseOrders: ClientCollection<PurchaseOrder>;
        defaultBuyer: User;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface SupplierExtension$Lookups {
        defaultBuyer: QueryOperation<User>;
    }
    export interface SupplierExtension$Operations {
        lookups(dataOrId: string | { data: SupplierInput }): SupplierExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine': PurchaseInvoiceLineToPurchaseCreditMemoLine$Operations;
        '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine': PurchaseOrderLineToPurchaseInvoiceLine$Operations;
        '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine': PurchaseOrderLineToPurchaseReceiptLine$Operations;
        '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine': PurchaseReceiptLineToPurchaseInvoiceLine$Operations;
        '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine': PurchaseReceiptLineToPurchaseReturnLine$Operations;
        '@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine': PurchaseRequisitionLineToPurchaseOrderLine$Operations;
        '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine': PurchaseReturnLineToPurchaseCreditMemoLine$Operations;
        '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseInvoiceLine': PurchaseReturnLineToPurchaseInvoiceLine$Operations;
        '@sage/xtrem-purchasing/UnbilledAccountPayableInputSet': UnbilledAccountPayableInputSet$Operations;
        '@sage/xtrem-purchasing/UnbilledAccountPayableResultLine': UnbilledAccountPayableResultLine$Operations;
        '@sage/xtrem-purchasing/PurchaseCreditMemoLineDiscountCharge': PurchaseCreditMemoLineDiscountCharge$Operations;
        '@sage/xtrem-purchasing/PurchaseReceipt': PurchaseReceipt$Operations;
        '@sage/xtrem-purchasing/PurchaseReceiptLine': PurchaseReceiptLine$Operations;
        '@sage/xtrem-purchasing/PurchaseRequisition': PurchaseRequisition$Operations;
        '@sage/xtrem-purchasing/PurchaseRequisitionLine': PurchaseRequisitionLine$Operations;
        '@sage/xtrem-purchasing/PurchaseRequisitionLineDiscountCharge': PurchaseRequisitionLineDiscountCharge$Operations;
        '@sage/xtrem-purchasing/WorkInProgressPurchaseOrderLine': WorkInProgressPurchaseOrderLine$Operations;
        '@sage/xtrem-purchasing/WorkInProgressPurchaseReceiptLine': WorkInProgressPurchaseReceiptLine$Operations;
        '@sage/xtrem-purchasing/WorkInProgressPurchaseReturnLine': WorkInProgressPurchaseReturnLine$Operations;
        '@sage/xtrem-purchasing/PurchaseCreditMemo': PurchaseCreditMemo$Operations;
        '@sage/xtrem-purchasing/PurchaseCreditMemoLine': PurchaseCreditMemoLine$Operations;
        '@sage/xtrem-purchasing/PurchaseInvoice': PurchaseInvoice$Operations;
        '@sage/xtrem-purchasing/PurchaseInvoiceLine': PurchaseInvoiceLine$Operations;
        '@sage/xtrem-purchasing/PurchaseOrder': PurchaseOrder$Operations;
        '@sage/xtrem-purchasing/PurchaseOrderLine': PurchaseOrderLine$Operations;
        '@sage/xtrem-purchasing/PurchaseReturn': PurchaseReturn$Operations;
        '@sage/xtrem-purchasing/PurchaseReturnLine': PurchaseReturnLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-purchasing-api' {
    export type * from '@sage/xtrem-purchasing-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-purchasing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        ItemBindingExtension,
        ItemExtension,
        ItemInputExtension,
        ItemSiteBindingExtension,
        ItemSiteCostBindingExtension,
        ItemSiteCostExtension,
        ItemSiteCostInputExtension,
        ItemSiteExtension,
        ItemSiteInputExtension,
        SupplierBindingExtension,
        SupplierExtension,
        SupplierExtension$Lookups,
        SupplierExtension$Operations,
        SupplierInputExtension,
    } from '@sage/xtrem-purchasing-api';
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface ItemSiteCost extends ItemSiteCostExtension {}
    export interface ItemSiteCostBinding extends ItemSiteCostBindingExtension {}
    export interface ItemSiteCostInput extends ItemSiteCostInputExtension {}
    export interface ItemSite extends ItemSiteExtension {}
    export interface ItemSiteBinding extends ItemSiteBindingExtension {}
    export interface ItemSiteInput extends ItemSiteInputExtension {}
    export interface Supplier extends SupplierExtension {}
    export interface SupplierBinding extends SupplierBindingExtension {}
    export interface SupplierInput extends SupplierInputExtension {}
    export interface Supplier$Lookups extends SupplierExtension$Lookups {}
    export interface Supplier$Operations extends SupplierExtension$Operations {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type {
        LandedCostAllocationBindingExtension,
        LandedCostAllocationExtension,
        LandedCostAllocationInputExtension,
    } from '@sage/xtrem-purchasing-api';
    export interface LandedCostAllocation extends LandedCostAllocationExtension {}
    export interface LandedCostAllocationBinding extends LandedCostAllocationBindingExtension {}
    export interface LandedCostAllocationInput extends LandedCostAllocationInputExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type {
        PaymentTrackingBindingExtension,
        PaymentTrackingExtension,
        PaymentTrackingInputExtension,
    } from '@sage/xtrem-purchasing-api';
    export interface PaymentTracking extends PaymentTrackingExtension {}
    export interface PaymentTrackingBinding extends PaymentTrackingBindingExtension {}
    export interface PaymentTrackingInput extends PaymentTrackingInputExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        SiteBindingExtension,
        SiteExtension,
        SiteExtension$Lookups,
        SiteExtension$Operations,
        SiteInputExtension,
    } from '@sage/xtrem-purchasing-api';
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface Site$Lookups extends SiteExtension$Lookups {}
    export interface Site$Operations extends SiteExtension$Operations {}
}
