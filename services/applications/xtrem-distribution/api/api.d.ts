declare module '@sage/xtrem-distribution-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        AnalyticalData,
        CloseReason,
        Package as SageXtremFinanceData$Package,
        PaymentTracking,
        PaymentTrackingBinding,
        PaymentTrackingInput,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostDocumentLine,
        LandedCostDocumentLineBinding,
        LandedCostDocumentLineInput,
        LandedCostLine,
        LandedCostLineBinding,
        LandedCostLineInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseBusinessRelation,
        BusinessEntity,
        BusinessEntityAddress,
        Contact,
        Currency,
        Customer,
        DeliveryMode,
        Incoterm,
        Item,
        ItemSite,
        ItemSupplier,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        Supplier,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package } from '@sage/xtrem-stock-data-api';
    import type { Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Package as SageXtremSystem$Package, Site, User, UserInput } from '@sage/xtrem-system-api';
    import type {
        DocumentLineTax,
        DocumentLineTaxBinding,
        DocumentLineTaxInput,
        DocumentTax,
        DocumentTaxBinding,
        DocumentTaxInput,
        Package as SageXtremTax$Package,
    } from '@sage/xtrem-tax-api';
    import type { Package as SageXtremUpload$Package } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface BasePriceOrigin$Enum {
        manual: 0;
        supplierPriceList: 1;
        customerPriceList: 2;
        basePrice: 3;
        priceList: 4;
    }
    export type BasePriceOrigin = keyof BasePriceOrigin$Enum;
    export interface DocumentCloseStatusMethodReturn$Enum {
        parametersAreIncorrect: 0;
        isAlreadyClosed: 1;
        isNowClosed: 2;
    }
    export type DocumentCloseStatusMethodReturn = keyof DocumentCloseStatusMethodReturn$Enum;
    export interface DocumentConfirmStatusMethodReturn$Enum {
        parametersAreIncorrect: 0;
        isNotDraft: 1;
        isConfirmed: 2;
    }
    export type DocumentConfirmStatusMethodReturn = keyof DocumentConfirmStatusMethodReturn$Enum;
    export interface DocumentOpenStatusMethodReturn$Enum {
        parametersAreIncorrect: 0;
        isShipped: 1;
        isAlreadyOpen: 2;
        isNowOpen: 3;
    }
    export type DocumentOpenStatusMethodReturn = keyof DocumentOpenStatusMethodReturn$Enum;
    export interface InboundDisplayStatus$Enum {
        draft: 1;
        pendingApproval: 2;
        approved: 3;
        rejected: 4;
        confirmed: 6;
        received: 11;
        partiallyInvoiced: 12;
        invoiced: 13;
        closed: 14;
        postingInProgress: 15;
        error: 16;
        partiallyReceived: 17;
        partiallyReturned: 18;
        partiallyOrdered: 19;
        taxCalculationFailed: 20;
        postingError: 21;
        posted: 22;
        stockError: 23;
        partiallyCredited: 24;
        credited: 25;
        noVariance: 26;
        variance: 27;
        varianceApproved: 28;
        returned: 29;
        ordered: 30;
        paid: 32;
        partiallyPaid: 33;
    }
    export type InboundDisplayStatus = keyof InboundDisplayStatus$Enum;
    export interface InvoiceStatus$Enum {
        notInvoiced: 0;
        partiallyInvoiced: 1;
        invoiced: 2;
    }
    export type InvoiceStatus = keyof InvoiceStatus$Enum;
    export interface ReceivingStatus$Enum {
        notReceived: 0;
        partiallyReceived: 1;
        received: 2;
    }
    export type ReceivingStatus = keyof ReceivingStatus$Enum;
    export interface ShippingStatus$Enum {
        notShipped: 0;
        partiallyShipped: 1;
        shipped: 2;
    }
    export type ShippingStatus = keyof ShippingStatus$Enum;
    export interface BaseDistributionDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        businessRelation: BaseBusinessRelation;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        anyLines: ClientCollection<BaseDistributionDocumentLine>;
        totalAmountIncludingTax: string;
    }
    export interface BaseDistributionDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        businessRelation?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        paymentTerm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        lines?: Partial<BaseDistributionDocumentLineInput>[];
    }
    export interface BaseDistributionDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        businessRelation: BaseBusinessRelation;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<BaseDistributionDocumentLineBinding>;
        totalAmountIncludingTax: string;
    }
    export interface BaseDistributionDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseDistributionDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseDistributionDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BaseDistributionDocument$Operations {
        create: CreateOperation<BaseDistributionDocumentInput, BaseDistributionDocument>;
        getDuplicate: GetDuplicateOperation<BaseDistributionDocument>;
        duplicate: DuplicateOperation<string, BaseDistributionDocumentInput, BaseDistributionDocument>;
        update: UpdateOperation<BaseDistributionDocumentInput, BaseDistributionDocument>;
        updateById: UpdateByIdOperation<BaseDistributionDocumentInput, BaseDistributionDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseDistributionDocument$Mutations;
        asyncOperations: BaseDistributionDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseDistributionDocumentInput }): BaseDistributionDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseDistributionDocument>;
    }
    export interface BaseDistributionDocumentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDistributionDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
    }
    export interface BaseDistributionDocumentLineInput extends VitalClientNodeInput {
        _constructor?: string;
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface BaseDistributionDocumentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDistributionDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
    }
    export interface BaseDistributionDocumentLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface BaseDistributionDocumentLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
    }
    export interface BaseDistributionDocumentLine$Operations {
        mutations: BaseDistributionDocumentLine$Mutations;
        lookups(dataOrId: string | { data: BaseDistributionDocumentLineInput }): BaseDistributionDocumentLine$Lookups;
        getDefaults: GetDefaultsOperation<BaseDistributionDocumentLine>;
    }
    export interface DocumentLineDiscountCharge extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: BaseDistributionDocumentLine;
        basis: string;
        amount: string;
    }
    export interface DocumentLineDiscountChargeInput extends VitalClientNodeInput {
        sign?: DiscountChargeSign;
        valueType?: DiscountChargeValueType;
        calculationBasis?: DiscountChargeCalculationBasis;
        calculationRule?: DiscountChargeCalculationRule;
        basisDeterminated?: decimal | string;
        value?: decimal | string;
        valueDeterminated?: decimal | string;
        basis?: decimal | string;
        amount?: decimal | string;
    }
    export interface DocumentLineDiscountChargeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sign: DiscountChargeSign;
        valueType: DiscountChargeValueType;
        calculationBasis: DiscountChargeCalculationBasis;
        calculationRule: DiscountChargeCalculationRule;
        basisDeterminated: string;
        value: string;
        valueDeterminated: string;
        document: BaseDistributionDocumentLine;
        basis: string;
        amount: string;
    }
    export interface DocumentLineDiscountCharge$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DocumentLineDiscountCharge$Operations {
        query: QueryOperation<DocumentLineDiscountCharge>;
        read: ReadOperation<DocumentLineDiscountCharge>;
        aggregate: {
            read: AggregateReadOperation<DocumentLineDiscountCharge>;
            query: AggregateQueryOperation<DocumentLineDiscountCharge>;
        };
        asyncOperations: DocumentLineDiscountCharge$AsyncOperations;
        getDefaults: GetDefaultsOperation<DocumentLineDiscountCharge>;
    }
    export interface BaseInboundDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        supplier: Supplier;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        anyLines: ClientCollection<BaseInboundDocumentLine>;
        totalAmountIncludingTax: string;
    }
    export interface BaseInboundDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        supplier?: integer | string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        billBySupplier?: integer | string;
        supplierAddress?: integer | string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        lines?: Partial<BaseInboundDocumentLineInput>[];
    }
    export interface BaseInboundDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        supplier: Supplier;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        lines: ClientCollection<BaseInboundDocumentLineBinding>;
        totalAmountIncludingTax: string;
    }
    export interface BaseInboundDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseInboundDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseInboundDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        billBySupplier: QueryOperation<Supplier>;
        supplierAddress: QueryOperation<Address>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BaseInboundDocument$Operations {
        create: CreateOperation<BaseInboundDocumentInput, BaseInboundDocument>;
        getDuplicate: GetDuplicateOperation<BaseInboundDocument>;
        duplicate: DuplicateOperation<string, BaseInboundDocumentInput, BaseInboundDocument>;
        update: UpdateOperation<BaseInboundDocumentInput, BaseInboundDocument>;
        updateById: UpdateByIdOperation<BaseInboundDocumentInput, BaseInboundDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseInboundDocument$Mutations;
        asyncOperations: BaseInboundDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseInboundDocumentInput }): BaseInboundDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseInboundDocument>;
    }
    export interface BaseInboundDocumentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseInboundDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
    }
    export interface BaseInboundDocumentLineInput extends VitalClientNodeInput {
        _constructor?: string;
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface BaseInboundDocumentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseInboundDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
    }
    export interface BaseInboundDocumentLine$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface BaseInboundDocumentLine$Lookups {
        site: QueryOperation<Site>;
        siteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        item: QueryOperation<Item>;
        stockSite: QueryOperation<Site>;
        stockSiteLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemSite: QueryOperation<ItemSite>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        itemSupplier: QueryOperation<ItemSupplier>;
    }
    export interface BaseInboundDocumentLine$Operations {
        mutations: BaseInboundDocumentLine$Mutations;
        lookups(dataOrId: string | { data: BaseInboundDocumentLineInput }): BaseInboundDocumentLine$Lookups;
        getDefaults: GetDefaultsOperation<BaseInboundDocumentLine>;
    }
    export interface BaseOutboundDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        incoterm: Incoterm;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
    }
    export interface BaseOutboundDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        customer?: integer | string;
        customerNumber?: string;
        incoterm?: integer | string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        shipToCustomer?: integer | string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
    }
    export interface BaseOutboundDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        incoterm: Incoterm;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
    }
    export interface BaseOutboundDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseOutboundDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseOutboundDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        customer: QueryOperation<Customer>;
        incoterm: QueryOperation<Incoterm>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        shipToCustomer: QueryOperation<Customer>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BaseOutboundDocument$Operations {
        create: CreateOperation<BaseOutboundDocumentInput, BaseOutboundDocument>;
        getDuplicate: GetDuplicateOperation<BaseOutboundDocument>;
        duplicate: DuplicateOperation<string, BaseOutboundDocumentInput, BaseOutboundDocument>;
        update: UpdateOperation<BaseOutboundDocumentInput, BaseOutboundDocument>;
        updateById: UpdateByIdOperation<BaseOutboundDocumentInput, BaseOutboundDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseOutboundDocument$Mutations;
        asyncOperations: BaseOutboundDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseOutboundDocumentInput }): BaseOutboundDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseOutboundDocument>;
    }
    export interface BaseInboundReceiptDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        supplier: Supplier;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface BaseInboundReceiptDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        supplier?: integer | string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        billBySupplier?: integer | string;
        supplierAddress?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
    }
    export interface BaseInboundReceiptDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        supplier: Supplier;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Supplier;
        paymentTerm: PaymentTerm;
        billBySupplier: Supplier;
        supplierAddress: Address;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface BaseInboundReceiptDocument$Mutations {
        post: Node$Operation<
            {
                receipt: string;
            },
            boolean
        >;
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseInboundReceiptDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseInboundReceiptDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        supplier: QueryOperation<Supplier>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        billBySupplier: QueryOperation<Supplier>;
        supplierAddress: QueryOperation<Address>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BaseInboundReceiptDocument$Operations {
        create: CreateOperation<BaseInboundReceiptDocumentInput, BaseInboundReceiptDocument>;
        getDuplicate: GetDuplicateOperation<BaseInboundReceiptDocument>;
        duplicate: DuplicateOperation<string, BaseInboundReceiptDocumentInput, BaseInboundReceiptDocument>;
        update: UpdateOperation<BaseInboundReceiptDocumentInput, BaseInboundReceiptDocument>;
        updateById: UpdateByIdOperation<BaseInboundReceiptDocumentInput, BaseInboundReceiptDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseInboundReceiptDocument$Mutations;
        asyncOperations: BaseInboundReceiptDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseInboundReceiptDocumentInput }): BaseInboundReceiptDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseInboundReceiptDocument>;
    }
    export interface BaseOutboundOrderDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        incoterm: Incoterm;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        requestedDeliveryDate: string;
        shippingStatus: ShippingStatus;
        shippingDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        expectedDeliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
    }
    export interface BaseOutboundOrderDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        customer?: integer | string;
        customerNumber?: string;
        incoterm?: integer | string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        requestedDeliveryDate?: string;
        shippingStatus?: ShippingStatus;
        shippingDate?: string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        expectedDeliveryDate?: string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
    }
    export interface BaseOutboundOrderDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        incoterm: Incoterm;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        requestedDeliveryDate: string;
        shippingStatus: ShippingStatus;
        shippingDate: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        expectedDeliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        totalAmountIncludingTax: string;
    }
    export interface BaseOutboundOrderDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseOutboundOrderDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseOutboundOrderDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        customer: QueryOperation<Customer>;
        incoterm: QueryOperation<Incoterm>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BaseOutboundOrderDocument$Operations {
        create: CreateOperation<BaseOutboundOrderDocumentInput, BaseOutboundOrderDocument>;
        getDuplicate: GetDuplicateOperation<BaseOutboundOrderDocument>;
        duplicate: DuplicateOperation<string, BaseOutboundOrderDocumentInput, BaseOutboundOrderDocument>;
        update: UpdateOperation<BaseOutboundOrderDocumentInput, BaseOutboundOrderDocument>;
        updateById: UpdateByIdOperation<BaseOutboundOrderDocumentInput, BaseOutboundOrderDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseOutboundOrderDocument$Mutations;
        asyncOperations: BaseOutboundOrderDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseOutboundOrderDocumentInput }): BaseOutboundOrderDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseOutboundOrderDocument>;
    }
    export interface BaseOutboundShipmentDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTracking;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        trackingNumber: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        deliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface BaseOutboundShipmentDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        paymentTracking?: PaymentTrackingInput;
        customer?: integer | string;
        customerNumber?: string;
        trackingNumber?: string;
        businessRelation?: integer | string;
        paymentTerm?: integer | string;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        deliveryDate?: string;
        currency?: integer | string;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        incoterm?: integer | string;
    }
    export interface BaseOutboundShipmentDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        fxRateDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        paymentTracking: PaymentTrackingBinding;
        netBalance: string;
        customer: Customer;
        customerNumber: string;
        trackingNumber: string;
        financeIntegrationStatus: FinanceIntegrationStatus;
        businessRelation: Customer;
        paymentTerm: PaymentTerm;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        deliveryDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        incoterm: Incoterm;
        totalAmountIncludingTax: string;
        allocationStatus: StockAllocationStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface BaseOutboundShipmentDocument$Mutations {
        sendApprovalRequestMail: Node$Operation<
            {
                document?: string;
                user?: UserInput | null;
            },
            boolean
        >;
        setIsPrintedIsSent: Node$Operation<
            {
                document: string;
                send?: boolean | string;
            },
            boolean
        >;
    }
    export interface BaseOutboundShipmentDocument$AsyncOperations {
        bulkResync: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface BaseOutboundShipmentDocument$Lookups {
        site: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        companyCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        siteAddress: QueryOperation<Address>;
        businessEntityAddress: QueryOperation<BusinessEntityAddress>;
        customer: QueryOperation<Customer>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        paymentTerm: QueryOperation<PaymentTerm>;
        shipToCustomer: QueryOperation<Customer>;
        shipToCustomerAddress: QueryOperation<BusinessEntityAddress>;
        shipToAddress: QueryOperation<Address>;
        shipToContact: QueryOperation<Contact>;
        deliveryMode: QueryOperation<DeliveryMode>;
        currency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        incoterm: QueryOperation<Incoterm>;
    }
    export interface BaseOutboundShipmentDocument$Operations {
        create: CreateOperation<BaseOutboundShipmentDocumentInput, BaseOutboundShipmentDocument>;
        getDuplicate: GetDuplicateOperation<BaseOutboundShipmentDocument>;
        duplicate: DuplicateOperation<string, BaseOutboundShipmentDocumentInput, BaseOutboundShipmentDocument>;
        update: UpdateOperation<BaseOutboundShipmentDocumentInput, BaseOutboundShipmentDocument>;
        updateById: UpdateByIdOperation<BaseOutboundShipmentDocumentInput, BaseOutboundShipmentDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: BaseOutboundShipmentDocument$Mutations;
        asyncOperations: BaseOutboundShipmentDocument$AsyncOperations;
        lookups(dataOrId: string | { data: BaseOutboundShipmentDocumentInput }): BaseOutboundShipmentDocument$Lookups;
        getDefaults: GetDefaultsOperation<BaseOutboundShipmentDocument>;
    }
    export interface BaseOpenItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface BaseOpenItemInputExtension {
        _constructor?: string;
        dueDate?: string;
        businessEntity?: integer | string;
        businessEntityPayment?: integer | string;
        type?: BusinessEntityType;
        currency?: integer | string;
        transactionAmountDue?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountDue?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountDue?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        documentSysId?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        discountPaymentBeforeDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        closeReason?: integer | string;
        closeText?: string;
        forcedAmountPaid?: decimal | string;
    }
    export interface BaseOpenItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface Package {
        '@sage/xtrem-distribution/BaseDistributionDocument': BaseDistributionDocument$Operations;
        '@sage/xtrem-distribution/BaseDistributionDocumentLine': BaseDistributionDocumentLine$Operations;
        '@sage/xtrem-distribution/DocumentLineDiscountCharge': DocumentLineDiscountCharge$Operations;
        '@sage/xtrem-distribution/BaseInboundDocument': BaseInboundDocument$Operations;
        '@sage/xtrem-distribution/BaseInboundDocumentLine': BaseInboundDocumentLine$Operations;
        '@sage/xtrem-distribution/BaseOutboundDocument': BaseOutboundDocument$Operations;
        '@sage/xtrem-distribution/BaseInboundReceiptDocument': BaseInboundReceiptDocument$Operations;
        '@sage/xtrem-distribution/BaseOutboundOrderDocument': BaseOutboundOrderDocument$Operations;
        '@sage/xtrem-distribution/BaseOutboundShipmentDocument': BaseOutboundShipmentDocument$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-distribution-api' {
    export type * from '@sage/xtrem-distribution-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-distribution-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type {
        BaseOpenItemBindingExtension,
        BaseOpenItemExtension,
        BaseOpenItemInputExtension,
    } from '@sage/xtrem-distribution-api';
    export interface BaseOpenItem extends BaseOpenItemExtension {}
    export interface BaseOpenItemBinding extends BaseOpenItemBindingExtension {}
    export interface BaseOpenItemInput extends BaseOpenItemInputExtension {}
}
