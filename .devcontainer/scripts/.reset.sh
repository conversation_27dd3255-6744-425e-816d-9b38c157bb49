#!/usr/bin/env bash

set -e

# prepare var on init
source /opt/scripts/.var.sh
init_vars

cd ${WORKSPACE_FOLDER}

# ============ reset NPM

export NPMRC_LOCATION="$(npm config get userconfig)"
rm -f ${NPMRC_LOCATION}
rm -rf ${HOME}/.npm*


# =========== reset AWS

rm -f ${HOME}/.aws/credentials


# ========== reset Docker

docker logout ghcr.io

rm -f ${HOME}/.docker/config.json


# ========= rest Misc.

rm -f ${HOME}/xtreem-credetial-setup-done
rm -f ${HOME}/.bash_history
