#!/usr/bin/env bash

set -e

# prepare var on init
source /opt/scripts/.var.sh
init_vars

mv /opt/scripts/.*rc /home/<USER>/
mv /opt/scripts/sim.zsh-theme /home/<USER>/.oh-my-zsh/custom/themes/

chown node:node /home/<USER>/.*shrc

chmod +x /home/<USER>/.*shrc

# setup xtrem developpment environement
cd ${WORKSPACE_FOLDER}

curl -fsSL https://fnm.vercel.app/install | bash -s -- --skip-shell
eval "`${FNM_PATH}/fnm env`"
${FNM_PATH}/fnm use --install-if-missing --silent-if-unchanged --corepack-enabled

# setup private repo
export NPMRC_LOCATION="$(npm config get userconfig)"
echo "Writing to path: ${NPMRC_LOCATION}"
echo "@sage:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/" >> ${NPMRC_LOCATION}
echo "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken=${AZURE_TOKEN}" >> ${NPMRC_LOCATION}
echo "always-auth=true" >> ${NPMRC_LOCATION}
echo "progress=false" >> ${NPMRC_LOCATION}

if [ "${S3_KEY}" != "" -a "${S3_SECRET}" != "" ]; then
    echo "Writing to path: ${HOME}/.aws/credentials"
    mkdir -p "${HOME}/.aws"

    echo "[default]" > "${HOME}/.aws/credentials"
    echo "aws_access_key_id = ${S3_KEY}" >> "${HOME}/.aws/credentials"
    echo "aws_secret_access_key = ${S3_SECRET}" >> "${HOME}/.aws/credentials"
fi

if [ ! -f xtrem-config.yml ] && [ ! -L xtrem-config.yml  ]; then
    echo "Writing to path: ${WORKSPACE_FOLDER}/xtrem-config.yml"
    cp xtrem-config-template.yml xtrem-config.yml
fi

git rev-parse HEAD > /workspace_cache/xtrem-last-build-sha
