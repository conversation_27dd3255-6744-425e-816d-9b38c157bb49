#!/usr/bin/env bash

set -e

RECEIVED_NEW_CODE=""

# ================================================================ Logging functions

log_section_separator() {
    echo ""

    echo "------------------------------------------"

    echo ""
    echo ""

    sleep 2
}


# ================================================================ Setup functions

setup_fnm() {
    echo "Setting up FNM"

    eval "`${FNM_PATH}/fnm env`"
    ${FNM_PATH}/fnm use --install-if-missing --silent-if-unchanged --corepack-enabled

    corepack install

    log_section_separator
}

setup_npm() {
    echo "Setting up NPM"


    npm config set "@sage:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "@sageai:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken" "${AZURE_TOKEN}"
    npm config set progress false

    # Just to verify the authentication configuration
    npm view @sage/xtrem-cli > /dev/null
    npm view @sageai/gms-chat-ui-react > /dev/null

    log_section_separator
}

setup_s3() {
    echo "Setting up S3"

    mkdir -p "${HOME}/.aws"

    {
        echo "[default]"
        echo "aws_access_key_id = ${S3_KEY}"
        echo "aws_secret_access_key = ${S3_SECRET}"
    } > "${HOME}/.aws/credentials"

    {
        echo "[default]"
        echo "region = eu-west-1"
    } > "${HOME}/.aws/config"

    if [[ $(aws s3 ls s3://xtrem-developers-utility/cache) ]]; then
        echo "S3 configured"
    else
        finish 1
    fi

    log_section_separator
}

setup_docker() {
    set +e

    echo "Setting up Docker"

    echo "${DOCKER_PASSWORD}" | docker login --username "${DOCKER_USERNAME}" --password-stdin ghcr.io

    docker manifest inspect -v ghcr.io/sage-erp-x3/xtrem-test-image-azure

    echo "Docker configured"

    log_section_separator

    set -e
}

pull_if_required() {
    CURRENT_BRANCH_NAME="$(git branch --show-current)"

    RECEIVED_NEW_CODE="false"

    echo "checking git for remote changes"

    git fetch origin "${CURRENT_BRANCH_NAME}"

    if [ "${CURRENT_BRANCH_NAME}" != "master" ]; then
        RECEIVED_NEW_CODE="true"
    elif [ "$(git status --porcelain=v2)" == "" ]; then
        REMOTE_SHA="$(git rev-parse "origin/${CURRENT_BRANCH_NAME}")"

        echo "changes found"

        if [ -n "${REMOTE_SHA}" ] && [ "$(cat /workspace_cache/xtrem-last-build-sha)" != "${REMOTE_SHA}" ]; then
            echo "branch is master without local changes so pulling"

            git pull

            RECEIVED_NEW_CODE="true"

            git rev-parse HEAD > /workspace_cache/xtrem-last-build-sha
        else
            echo "branch isn't master or with local changes so no pull"
        fi
    else
        echo "nothing changed"
    fi
}

update_if_required() {

    if [ "${RECEIVED_NEW_CODE}" == "false" ] && [ ! -d "${WORKSPACE_FOLDER}/node_modules" ]; then
        UPDATE_REQUIRED="true"
    fi

    if [ "${RECEIVED_NEW_CODE}" == "false" ] && [[ ! $(pnpm ls --depth=0 | grep -q "@sage/xdev") ]]; then
        RECEIVED_NEW_CODE="true"
    fi

    if [ "${RECEIVED_NEW_CODE}" == "false" ] && [[ ! $(pnpm --filter="@sage/xtrem-services-main" ls --depth=0 | grep -q "@sage/xtrem-cli") ]]; then
        RECEIVED_NEW_CODE="true"
    fi

    if [ "${RECEIVED_NEW_CODE}" == "true" ]; then
        echo "install node dependencies"

        # quick install then build
        pnpm install

        echo "building after update"
        if [ "${DEV_FOLDER}" != "" ]; then
            cd "${WORKSPACE_FOLDER}/${DEV_FOLDER}"
            echo "building in dev folder ${DEV_FOLDER}"
        fi

        pnpm build

    else
        echo "no update required"
    fi
}

finish() {
    if [ "$1" -gt 0 ]; then
        echo "Start script ran into an error."
    fi

    exit "$1"
}


# ================================================================ Main script
# the part that always runs

# try to prevent concurrent execution of this script
PIDs=$(pgrep -f ".start.sh")
NBoPIDs=$(echo "${PIDs}" | wc -l)

if [ "${NBoPIDs}" -gt 1 ]; then
    OLDER_PID=$(echo "${PIDs}" | head -1)
    if [ "${OLDER_PID}" != "$$" ]; then
        echo "already in progress in a conccurent shell"
        finish 0
    fi
fi

# prepare var on init
source /opt/scripts/.var.sh
init_vars

cd "${WORKSPACE_FOLDER}"

git config --global pull.rebase false

# run the credential setup only the 1st time
if [ -f "${HOME}/xtreem-credetial-setup-done" ]; then
    finish 0
fi

# ============================== Setup apps
# the part that run only the first time

setup_fnm

setup_npm

setup_s3

setup_docker

touch "${HOME}/xtreem-credetial-setup-done"

set +e

# remove the minimal config and replace it by the user templated one.
/opt/scripts/.config.sh

log_section_separator

pull_if_required

log_section_separator

update_if_required

log_section_separator

if [ "${DEV_FOLDER}" != "" ]; then
    cd "${WORKSPACE_FOLDER}/${DEV_FOLDER}"

    if [ "${SETUP_DOCKER_CONTAINERS}" == "true" ]; then
        CONTAINER_LIST=$(docker ps --format "{{.Names}}")

        echo "setting main docker containers"

        if [ "$(echo "${CONTAINER_LIST}" | tr " " "\n" | grep xtrem_postgres)" != "xtrem_postgres" ]; then
            pnpm postgres:setup
        fi

        if [ "$(echo "${CONTAINER_LIST}" | tr " " "\n" | grep xtrem_sqs)" != "xtrem_sqs" ]; then
            pnpm sqs:setup
        fi

        if [ "$(echo "${CONTAINER_LIST}" | tr " " "\n" | grep cloudbeaver)" != "cloudbeaver" ]; then
            docker network create cloudbeaver
            docker run \
                -d \
                --name cloudbeaver \
                -p 8978:8978 \
                -e CB_ADMIN_NAME="cbadmin" \
                -e CB_ADMIN_PASSWORD="myPassword1" \
                -e CB_SERVER_NAME="CloudBeaver" \
                -e CB_SERVER_URL="http://localhost:8978/" \
                -v /var/cloudbeaver/workspace:/opt/cloudbeaver/workspace \
                --network="cloudbeaver" \
                dbeaver/cloudbeaver:latest
            docker network connect cloudbeaver xtrem_postgres

            docker container stop cloudbeaver
        fi

        log_section_separator
    fi

    if [ "${LOAD_DATA_PACKAGE}" != "" ]; then
        echo "pre-loading database"

        LAYER_TO_LOAD=${LAYER_TO_LOAD:='test'}

        pnpm --filter "${LOAD_DATA_PACKAGE}" xtrem layers --load setup,${LAYER_TO_LOAD}

        log_section_separator
    fi
fi

cd "${WORKSPACE_FOLDER}"

# setup graphana / prometeus performance metric reporting tools (allow errors)
echo "setting perf metrics docker containers"
platform/performance-tests/install.sh
log_section_separator

set -e

finish 0
