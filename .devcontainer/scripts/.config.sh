#!/usr/bin/env bash

set -e

# prepare var on init
source /opt/scripts/.var.sh
init_vars

KEY_LIST=("XTREM_INTACCT_SENDER_PASSWORD" "XTREM_SERVICE_FABRIC_SIGNING_KEY" "XTREM_MAILER_API_KEY" "S3_KEY" "S3_SECRET" "XTREM_SHOPIFY_PRIVATE_API_KEY")

CONFIGFILE_PATH="${WORKSPACE_FOLDER}/xtrem-config.yml"
TEMPLATE_CONFIGFILE_PATH=$(eval "echo $DEFAULT_CONFIG_TEMPLATE_PATH")
DOTFILES_PATH="/workspaces/.codespaces/.persistedshare/dotfiles"
DOTFILES_CONFIG_PATH="${DOTFILES_PATH}/configs/"
API_KEYS_PATH="${WORKSPACE_FOLDER}/docker/test-image/api-keys"

if [ ! -f "${TEMPLATE_CONFIGFILE_PATH}" ]; then
  echo "Will use default the config template"

  TEMPLATE_CONFIGFILE_PATH="${WORKSPACE_FOLDER}/xtrem-config-template.yml"
fi

replaceKeyInTemplate() {
  filePath=${1}

  if [ -f "${filePath}" ]; then
    key=${2}

    # parse the key from the screts
    PARSED_KEY=$(echo "${!key//\//\\\/}")

    set +e
    
    # replace the template key by the value from the config
    sed -i 's/{'"${key}"'}/'"${PARSED_KEY}"'/' ${filePath}

    set -e

    unset PARSED_KEY
    unset key
  fi

  unset filePath
}

# make sure we are in the right directory
cd ${WORKSPACE_FOLDER}

if [ -f "${CONFIGFILE_PATH}" -o -L "${CONFIGFILE_PATH}" ]; then
  echo "Updating the config file"
  
  unlink ${CONFIGFILE_PATH}
fi

# create the api file
echo "Generating api-keys"

echo "XTREM_INTACCT_SENDER_PASSWORD=${XTREM_INTACCT_SENDER_PASSWORD}" > ${API_KEYS_PATH}
echo "XTREM_SERVICE_FABRIC_SIGNING_KEY=${XTREM_SERVICE_FABRIC_SIGNING_KEY}" >> ${API_KEYS_PATH}
echo "XTREM_MAILER_API_KEY=${XTREM_MAILER_API_KEY}" >> ${API_KEYS_PATH}
echo "XTREM_SHOPIFY_PRIVATE_API_KEY=${XTREM_SHOPIFY_PRIVATE_API_KEY}" >> ${API_KEYS_PATH}

if [ ! -d "${DOTFILES_PATH}" -o ! -d "${DOTFILES_CONFIG_PATH}" ]; then
  echo "Creating tmp dir (dotfile/configs) for key replacement"

  mkdir -p ${DOTFILES_CONFIG_PATH}
fi

if [ -d "${DOTFILES_CONFIG_PATH}" -a -f "${DOTFILES_CONFIG_PATH}/xtrem-config-template.yml" ]; then
  echo "Using your templated config file"

  TEMPLATE_CONFIGFILE_PATH="${DOTFILES_CONFIG_PATH}/xtrem-config-template.yml"
else
  echo "Using the repo templated config file"
fi

cp ${TEMPLATE_CONFIGFILE_PATH} ${DOTFILES_CONFIG_PATH}/xtrem-config.yml

echo "Replacing the templated string"
# parse all key for the KEY_LIST in the env and replace the template key by the PARSED_KEY data
for i in "${KEY_LIST[@]}"; do
  if [ -f "${DOTFILES_CONFIG_PATH}/xtrem-config.yml" ]; then
    replaceKeyInTemplate ${DOTFILES_CONFIG_PATH}/xtrem-config.yml ${i}
  fi
done

if [ -f ${DOTFILES_CONFIG_PATH}/xtrem-config.yml ]; then
  echo "Symlinking the config"

  set +e

  cd ${WORKSPACE_FOLDER}

  ln -s ${DOTFILES_CONFIG_PATH}/xtrem-config.yml .
else
  echo "Using the config template as is"
  echo "because something went wrong with the replacing of the templated string"

  cp ${TEMPLATE_CONFIGFILE_PATH} xtrem-config.yml
fi
