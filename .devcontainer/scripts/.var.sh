#!/usr/bin/env bash

set -e

function init_vars() {
    TEMP_WORKSPACE_FOLDER="${LOCAL_WORKSPACE_FOLDER}"
    CACHED_FOLDER_PATH="$(find /workspace_cache/ -name 'foldername_*')"
    
    if [ -z "${LOCAL_WORKSPACE_FOLDER}" -a -z "${CACHED_FOLDER_PATH}" ]; then
        TEMP_WORKSPACE_FOLDER="$(pwd)"
    fi
    
    if [ -z "${CACHED_FOLDER_PATH}" ]; then
        WORKSPACE_FOLDER_BASENAME="$(basename ${TEMP_WORKSPACE_FOLDER})"

        TEMP_WORKSPACE_FOLDER="/workspaces/${WORKSPACE_FOLDER_BASENAME}"
        touch /workspace_cache/foldername_${WORKSPACE_FOLDER_BASENAME}
    else
        TEMP_WORKSPACE_FOLDER="/workspaces/${CACHED_FOLDER_PATH#/workspace_cache/foldername_}"
    fi

    export WORKSPACE_FOLDER="${TEMP_WORKSPACE_FOLDER}"
}
