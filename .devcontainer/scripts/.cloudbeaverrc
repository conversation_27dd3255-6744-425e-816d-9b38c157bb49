#!/usr/bin/env bash

alias dockerbkp="docker"
alias pnpmbkp="pnpm"
alias npmbkp="npm"

update_postgres_networking() {
    echo "creation of a postgres container detected"
    echo "handling cloudbeaver networking"

    dockerbkp network connect cloudbeaver xtrem_postgres
}

docker_hook() {
    dockerbkp $*

    if [ $? -eq 0 ] && [ "$1" = "run" ] && [[ "$*" == *"xtrem_postgres"* ]]; then
        update_postgres_networking
    fi
}

npm_hook() {
    if [ "$INVOKED_ALIAS" = "pnpm" ]; then
        pnpmbkp $*
    elif [ "$INVOKED_ALIAS" = "npm" ]; then
        npmbkp $*
    fi

    if [ $? -eq 0 ] && ([[ "$*" == *"postgres:reset"* ]] || [[ "$*" == *"postgres:setup"* ]]); then
        update_postgres_networking
    fi
}

alias docker="docker_hook"
alias pnpm="INVOKED_ALIAS=pnpm npm_hook"
alias npm="INVOKED_ALIAS=npm npm_hook"
