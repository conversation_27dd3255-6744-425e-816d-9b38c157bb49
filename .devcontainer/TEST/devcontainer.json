{"name": "Test (DO NOT USE)", "remoteUser": "node", "runArgs": ["--privileged"], "image": "ghcr.io/sage-erp-x3/xtrem/base-devcontainer:test", "postAttachCommand": "/opt/scripts/.start.sh", "hostRequirements": {"cpus": 8, "memory": "16gb", "storage": "64gb"}, "portsAttributes": {"5000": {"label": "Allure reports [web]"}, "5432": {"label": "Postgres"}, "8080": {"label": "Auth server (ProdUI) [web]"}, "8240": {"label": "XTreM [web]"}, "8978": {"label": "CloudBeaver [web]"}, "9324": {"label": "ElasticMQ (SQS)"}, "9325": {"label": "ElasticMQ (SQS) [web]"}}, "forwardPorts": [5000, 5432, 8080, 8240, 8978, 9324, 9325], "customizations": {"vscode": {"extensions": ["alexkrechik.cucumberautocomplete", "dbaeumer.vscode-eslint", "donjayamanne.githistory", "eamodio.gitlens", "esbenp.prettier-vscode", "GitHub.codespaces", "GitHub.copilot-chat", "GitHub.copilot", "janisdd.vscode-edit-csv", "mechatroner.rainbow-csv", "medo64.render-crlf", "ms-azuretools.vscode-docker", "ms-vscode.live-server", "mtxr.sqltools", "mtxr.sqltools-driver-pg", "redhat.vscode-yaml", "SonarSource.sonarlint-vscode", "streetsidesoftware.code-spell-checker", "wayou.vscode-todo-highlight"], "settings": {"typescript.tsdk": "./node_modules/typescript/lib"}}}, "containerEnv": {"PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "true", "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/google-chrome", "CHROME_PATH": "/usr/bin/google-chrome", "FNM_PATH": "/home/<USER>/.local/share/fnm", "WORKSPACE_FOLDER": "${localWorkspaceFolder}", "DEV_FOLDER": "services", "SETUP_DOCKER_CONTAINERS": "true", "LOAD_DATA_PACKAGE": "@sage/xtrem-services-main", "DEFAULT_CONFIG_TEMPLATE_PATH": "${WORKSPACE_FOLDER}/${DEV_FOLDER}/xtrem-config-applicatives-template.yml", "NODE_ENV": "development", "DO_NOT_TRACK": "1"}}