FROM mcr.microsoft.com/devcontainers/base:ubuntu

ENV DOCKER_BUILDKIT=1\
    LANG="en_US.UTF-8" \
    LANGUAGE="en_US.UTF-8"

# Install Xtreem dependencies.
RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list' \
    && curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg \
    && apt update \
    && apt upgrade -y \
    && DEBIAN_FRONTEND=noninteractive apt install -y \
        ca-certificates \
        curl \
        gcc \
        g++ \
        make \
        build-essential \
        unzip \
        postgresql-client \
        jq \
        libcairo2-dev \
        libpango1.0-dev \
        libjpeg-dev \
        libgif-dev \
        librsvg2-dev \
        netcat-openbsd \
        default-jre \
        xauth \
    && mkdir /opt/scripts  \
    ## Install AWS cli
    && curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm awscliv2.zip \
    ## Install Chrome
    && curl "https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb" -o "google-chrome-stable_current_amd64.deb" \
    && apt install -y ./google-chrome-stable_current_amd64.deb \
    && rm ./google-chrome-stable_current_amd64.deb \
	## Disables chrome default browser prompt
    && mkdir -p /etc/opt/chrome/policies/managed \
    && echo '{ "SuppressFirstRunDefaultBrowserPrompt": true, "HomepageIsNewTabPage": true }' > /etc/opt/chrome/policies/managed/managed_policies.json \
    ## Creates misc config files
    && touch /.dockerenv \
    && touch ~/.Xauthority \
    ## Clean install artifacts
    && apt autoremove -y \
    && apt clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

COPY .devcontainer/scripts/* /opt/scripts/
COPY .devcontainer/src/cloudbeaver-settings.tar.gz /

RUN adduser node --quiet --disabled-password --gecos "Node,,," \
    && mkdir /workspace_cache \
    && chown -R node:node /workspace_cache \
    && chown -R node:node /opt/scripts\
    && chmod +x /opt/scripts/.* \
    && tar -xvf cloudbeaver-settings.tar.gz -C /var \
    && rm cloudbeaver-settings.tar.gz
