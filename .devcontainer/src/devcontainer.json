{
    "name": "base-devcontainer (DO NOT USE)",
    "remoteUser": "node",
    "runArgs": ["--privileged"],
    "build": {
        "dockerfile": "../Dockerfile",
        "context": "../..",
        "args": {
            "VARIANT": "18"
        }
    },
    "onCreateCommand": "/opt/scripts/.setup.sh",
    "postCreateCommand": "/opt/scripts/.reset.sh",
    "hostRequirements": {
        "cpus": 8,
        "memory": "16gb",
        "storage": "64gb"
    },
    "features": {
        "ghcr.io/devcontainers/features/common-utils:latest": {
            "installZsh": true,
            "installOhMyZsh": true,
            "configureZshAsDefaultShell": true,
            "username": "node",
            "upgradePackages": true,
        },
        "ghcr.io/devcontainers/features/docker-in-docker:latest": {},
        "ghcr.io/devcontainers/features/github-cli:latest": {},
        "ghcr.io/devcontainers/features/node:latest": {
            "version": "lts",
            "nodeGypDependencies": true,
            "pnpmVersion": "latest",
        },
        "ghcr.io/devcontainers/features/sshd:latest": {}
    },
    "containerEnv": {
        "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "true",
        "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/google-chrome",
        "CHROME_PATH": "/usr/bin/google-chrome",
        "FNM_PATH": "/home/<USER>/.local/share/fnm",
        "WORKSPACE_FOLDER": "${localWorkspaceFolder}",
        "NODE_ENV": "development",
        "DO_NOT_TRACK": "1"
    }
}
