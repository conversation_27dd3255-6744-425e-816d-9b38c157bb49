All the scripts and config files in this folder are managing the build and the start of a devcontainer to run the project with all the dependencies installed and configured:

- build a docker image from a javascript template provided by microsoft
- setup the different system dependencies and tools
- supports GitHub's Codespaces (with pre-builds) so you can be ready in a minute

# Codespace usage:

A Codespace is a VM running Docker and VSCode in the cloud to provides you with a clean and powerful environnement for you devs.

The principle is to create your branch and dev in the Codespace, once merged you can delete it and start from a fresh one.

## Prerequisites:

- Be sure to be part of the list of authorized users on the organization on GitHub.
- Create the following Secrets on GitHub (https://github.com/settings/codespaces) :
    - DOCKER_PASSWORD (your personal access token for this site GitHub)
    - DOCKER_USERNAME (your username for this site GitHub)
    - NPM_EMAIL (your sage email)
    - AZURE_TOKEN (your PAT for this site https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/)

## Create a new Codespace:

- Open the link https://github.com/codespaces/new?hide_repo_select=true&repo=340174868&machine=premiumLinux
- On the page select your branch
- click "Create codespace"

# Local Devcontainer usage:

TBD
