# Workspace Overview

This workspace is a monorepo that contains a GraphQL application framework and various web applications. It is built using React and Node.js, and uses PostgreSQL for data storage or MSSQL or Oracle for the X3 applications.

## Folder Structure

- `/platform/back-end`: Contains the source code for the Node.js backend.
- `/platform/cli`: Contains the source code for the xtrem CLI tools.
- `/platform/front-end`: Contains the source code for the frontend.
- `/services`: Contains the source code for the Node.js backend.
- `/shopfloor`: Contains the source code for the Shopfloor application.
- `tools/glossary`: Contains the source code for the Glossary application.
- `/x3-services`: Contains the source code for the X3 services, including the CLI and sync services.
- `/x3-services/cli`: Contains the CLI tools for interacting with X3 services.
- `/x3-services/platform`: Contains the platform-specific code for X3 services.
- `/x3-services/platform/xtrem-x3-sync`: Contains the sync service for X3 services.
- `/documentation`: Contains documentation for the project, including API specifications and user guides.
- `pipelines`: Contains CI/CD pipeline configurations.

## Libraries and Frameworks

- React for the frontend.
- Node.js and Express for the backend.
- PostgreSQL for data storage and MSSQL or Oracle for X3 applications.

## Coding Standards

- Use semicolons at the end of each statement.
- Use single quotes for strings except to avoid escape.
- Use arrow functions for callbacks.
- Use `const` for variables that are not reassigned, and `let` for those that are.
- Use `async/await` for asynchronous code.
- Use `camelCase` for variable, constant and function names.
- Use `PascalCase` for class names.
- Use `kebab-case` for file and directory names except for well known files like `Dockerfile`, etc.
