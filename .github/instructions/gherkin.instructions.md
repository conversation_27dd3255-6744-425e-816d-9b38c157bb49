---
applyTo: '*.feature'
---

# Gherkin Feature File Generation Guide

## Overview

This document provides guidelines for generating Cucumber Gherkin feature files for the XTREM application testing. Follow these instructions to create consistent and effective feature files.

## Feature File Structure

### 1. Header

Each feature file should start with:

- A tag indicating the module/application being tested (e.g., `@distribution`, `@manufacturing`)
- A `Feature:` declaration with a descriptive name
- A brief comment explaining the purpose of the test

### 2. Scenarios

- Each scenario should have a clear, numbered title that describes the action being tested
- Start simple scenarios with clear preconditions using `Given` steps
- Use `When` for actions being performed
- Use `Then` for expected outcomes/verifications
- Use `And` for additional steps of the same type

### 3. Steps Conventions

#### Navigation Steps

- For opening applications:
    ```gherkin
    Given the user opens the application on a [device] using the following link: "[link]"
    ```
    where `[device]` is either `desktop` or `mobile`

#### UI Interaction Steps

- For clicking buttons:

    ```gherkin
    When the user clicks the "[button-name]" labelled business action button on the [location]
    ```

    where `[location]` can be `main page`, `navigation panel`, etc.

- For selecting fields:

    ```gherkin
    And the user selects the "[field-name]" [field-type] field on the [location]
    ```

    where `[field-type]` can be `labelled`, `bound`, etc.

- For entering text:

    ```gherkin
    And the user writes "[text]" in the [field-type] field
    ```

- For selecting from dropdown/reference fields:
    ```gherkin
    And the user selects "[value]" in the [field-type] field
    ```

#### Verification Steps

- For verifying page titles:

    ```gherkin
    Then the "[title-text]" titled page is displayed
    ```

- For verifying field values:

    ```gherkin
    Then the value of the [field-type] field is "[expected-value]"
    ```

- For verifying toast messages:
    ```gherkin
    Then the value of the toast is "[message]"
    ```
    or
    ```gherkin
    Then a toast containing text "[message]" is displayed
    ```

### 4. Scenario Outlines

For repetitive tests with different data inputs, use Scenario Outlines with Examples:

```gherkin
Scenario Outline: [description] \ <Parameter> \ [more description]
    [steps with <Parameter> placeholders]

    Examples:
        | Parameter1 | Parameter2 | ... |
        | value1     | value2     | ... |
        | value3     | value4     | ... |
```

## Common Scenarios

### Page Loading Test

```gherkin
Scenario: Check page loads correctly
    Given the user opens the application on a [device] using the following link: "[link]"
    Then the "[title]" titled page is displayed
```

### CRUD Operations

#### Create

```gherkin
Scenario: [Entity] creation
    Given [navigation steps]
    When [steps to fill in entity details]
    And the user clicks the "Save" labelled business action button on the main page
    Then the value of the toast is "Record created"
```

#### Read/Search

```gherkin
Scenario: [Entity] search
    Given [navigation steps]
    When the user filters the "[column]" bound column in the table field with value "[search-term]"
    Then the value of the "[column]" bound nested text field of row 1 in the table field is "[expected-value]"
```

#### Delete

```gherkin
Scenario: [Entity] deletion
    Given [navigation steps to entity details]
    When the user clicks the "Delete" labelled more actions button in the header
    And the user clicks the "Delete" button of the Confirm dialog
    Then a toast containing text "Record deleted" is displayed
```

## Best Practices

1. Keep scenarios focused on a single behavior or functionality
2. Use clear and consistent step definitions
3. Organize tests in logical order (setup → action → verification)
4. For complex flows, break into multiple numbered scenarios
5. Use parameters for repetitive tests with different inputs
6. Include proper verification steps after each significant action
7. Comment important steps for clarity (use # for comments)
8. Use bound fields (with internal identifiers) when possible for stability

## Examples

See these example feature files for reference:

- `/showcase-stock/shared/showcase-stock-master-data/test/cucumber/smoke-test-static.feature`
- `/showcase-stock/shared/showcase-stock-master-data/test/cucumber/smoke-test-pr-cd-item.feature`
- `/x3-services/functional-tests/x3-distribution-test/test/distribution-flow-stock-change-by-identifier-04.feature`
