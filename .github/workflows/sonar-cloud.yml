on:
  # Trigger analysis when pushing in master or pull requests, and when creating
  # a pull request.
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened]
name: Main Workflow
jobs:
  sonarcloud:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
        with:
          # # use blobless clone to improve speed without altering the history depth
          # filter: 'blob:none'
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
      - name: SonarCloud Scan
        uses: SonarSource/sonarqube-scan-action@v5.3.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          # blobless clone requires to use native git, see: https://community.sonarsource.com/t/scan-on-git-blobless-clone-failed/106038/8
          # SONAR_SCANNER_OPTS: '-Dsonar.scm.use.blame.algorithm=GIT_NATIVE_BLAME'
          SONAR_SCANNER_JAVA_OPTS: '-Xmx4g'
