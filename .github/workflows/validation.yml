# Automatic validation checks on every push or pull-request to remote main
name: Semantic pull request
on:
  pull_request:
    types: [opened, reopened, synchronize, edited]

jobs:
  static_checks:
    name: Semantic pull request
    runs-on: ubuntu-latest
    steps:
      - name: semantic-pull-request
        uses: amannn/action-semantic-pull-request@v6.1.1
        with:
          types: |
            fix
            feat
            ci
            refactor
            chore
            docs
            test
            perf
          #subjectPattern: (XT|X3)\-([0-9]){1,7}
          validateSingleCommit: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
