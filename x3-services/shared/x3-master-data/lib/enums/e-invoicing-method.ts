import { X3EnumDataType } from '@sage/xtrem-x3-gateway';

export enum EInvoicingMethodEnum {
    sageConnect = 1,
    email = 2,
    xmlStoring = 3,
}

export interface EInvoicingMethod$EnumInterface {
    sageConnect: 1;
    email: 2;
    xmlStoring: 3;
}

export type EInvoicingMethod = keyof EInvoicingMethod$EnumInterface;

export const eInvoicingMethodDatatype = new X3EnumDataType<EInvoicingMethod>({
    enum: EInvoicingMethodEnum,
    filename: __filename,
    localMenuNumber: 2434,
});
