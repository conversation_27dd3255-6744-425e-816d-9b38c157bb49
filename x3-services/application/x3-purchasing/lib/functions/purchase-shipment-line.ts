import { decimal, ExtensionMembers } from '@sage/xtrem-core';
import * as x3Purchasing from '..';

export async function getQuantityInPurchaseUnitExpected(
    purchaseShipmentLine: ExtensionMembers<x3Purchasing.nodes.PurchaseShipmentLine>,
): Promise<decimal> {
    const quantityInPurchaseUnitShipped = Number(await purchaseShipmentLine.quantityInPurchaseUnitShipped);
    const quantityInPurchaseUnitReceived = Number(await purchaseShipmentLine.quantityInPurchaseUnitReceived);
    const quantityInPurchaseUnitPreReceived = Number(await purchaseShipmentLine.quantityInPurchaseUnitPreReceived);
    return Math.max(
        (quantityInPurchaseUnitShipped - quantityInPurchaseUnitReceived) *
            Number(quantityInPurchaseUnitPreReceived === 0),
        (quantityInPurchaseUnitPreReceived - quantityInPurchaseUnitReceived) *
            Number(quantityInPurchaseUnitPreReceived !== 0),
    );
}
