import * as sageX3MasterData from '@sage/x3-master-data';
import { asyncArray, BusinessRuleError, Context, DateValue, decimal, integer } from '@sage/xtrem-core';
import * as sageX3Finance from '..';
import { AnalyticalBalanceDimensionTypes } from '../nodes';
import { computeAmounts } from './balance-query-balance';

export async function calculateAnalyticalBalance(
    context: Context,
    parameters: {
        in_ReferencePeriod: number;
        in_LedgerType: sageX3MasterData.enums.GeneralLedgerTypes;
        in_Company: string;
        in_FiscalYear: number;
        in_Currency: string;
        in_Site: string;
        in_Account?: string;
        in_Dimension1?: string;
        in_Dimension2?: string;
        in_Dimension3?: string;
        in_Dimension4?: string;
        in_Dimension5?: string;
        in_Dimension6?: string;
        in_Dimension7?: string;
        in_Dimension8?: string;
        in_Dimension9?: string;
        in_BusinessPartner?: string;
    },
): Promise<{
    output?: {
        referencePeriod?: number;
        referencePeriodStartDate?: DateValue;
        referencePeriodEndDate?: DateValue;
        ledgerType: sageX3MasterData.enums.GeneralLedgerTypes;
        company?: string;
        companyName?: string;
        site?: string;
        siteName?: string;
        fiscalYear?: number;
        fiscalYearStartDate?: DateValue;
        fiscalYearEndDate?: DateValue;
        fiscalYearNumberOfPeriods?: number;
        account?: string;
        accountDescription?: string;
        businessPartner?: string;
        businessPartnerName?: string;
        currency?: string;
        ledgerCurrency?: string;
        dimensions?: {
            dimension?: string;
            dimensionType?: string;
            dimensionName?: string;
        }[];
        carryForwardCreditAmountInCurrency?: decimal;
        carryForwardCreditAmountInLedgerCurrency?: decimal;
        carryForwardDebitAmountInCurrency?: decimal;
        carryForwardDebitAmountInLedgerCurrency?: decimal;
        periodCreditAmountInCurrency?: decimal;
        periodCreditAmountInLedgerCurrency?: decimal;
        periodDebitAmountInCurrency?: decimal;
        periodDebitAmountInLedgerCurrency?: decimal;
        previousPeriodsCreditAmountInCurrency?: decimal;
        previousPeriodsCreditAmountInLedgerCurrency?: decimal;
        previousPeriodsDebitAmountInCurrency?: decimal;
        previousPeriodsDebitAmountInLedgerCurrency?: decimal;
        closingCreditAmountInCurrency?: decimal;
        closingCreditAmountInLedgerCurrency?: decimal;
        closingDebitAmountInCurrency?: decimal;
        closingDebitAmountInLedgerCurrency?: decimal;
    }[];
}> {
    const {
        in_ReferencePeriod,
        in_LedgerType,
        in_Company,
        in_FiscalYear,
        in_Account,
        in_Currency,
        in_Dimension1,
        in_Dimension2,
        in_Dimension3,
        in_Dimension4,
        in_Dimension5,
        in_Dimension6,
        in_Dimension7,
        in_Dimension8,
        in_Dimension9,
        in_Site,
        in_BusinessPartner,
    } = parameters ?? {};

    const getAmounts = (amounts: sageX3Finance.nodes.AnalyticalBalanceAmounts[], closingPeriod: integer) =>
        asyncArray(amounts).reduce(
            async (totalLines, amountLine) => ({
                ...(await computeAmounts(
                    amountLine,
                    'debitAmountInCurrency',
                    in_ReferencePeriod,
                    closingPeriod,
                    totalLines,
                )),
                ...(await computeAmounts(
                    amountLine,
                    'creditAmountInCurrency',
                    in_ReferencePeriod,
                    closingPeriod,
                    totalLines,
                )),
                ...(await computeAmounts(
                    amountLine,
                    'debitAmountInLedgerCurrency',
                    in_ReferencePeriod,
                    closingPeriod,
                    totalLines,
                )),
                ...(await computeAmounts(
                    amountLine,
                    'creditAmountInLedgerCurrency',
                    in_ReferencePeriod,
                    closingPeriod,
                    totalLines,
                )),
            }),
            {},
        );

    // ctl of mandatory parameters
    if (in_ReferencePeriod === undefined || in_ReferencePeriod === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_ReferencePeriod-parameter',
                "Missing parameter: 'in_ReferencePeriod'",
            ),
        );
    }
    if (in_LedgerType === undefined) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_LedgerType-parameter',
                "Missing parameter: 'in_LedgerType'",
            ),
        );
    }
    if (in_Company === undefined || in_Company === '') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_Company-parameter',
                "Missing parameter: 'in_Company'",
            ),
        );
    }
    if (in_FiscalYear === undefined || in_FiscalYear === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_FiscalYear-parameter',
                "Missing parameter: 'in_FiscalYear'",
            ),
        );
    }
    if (in_Site === undefined || in_Site === '') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_Site-parameter',
                "Missing parameter: 'in_Site'",
            ),
        );
    }
    // According to EDITBLA function accumulation levels, either account, dimension type or business partner must be entered to filter
    if (in_Account === undefined || in_Account === '') {
        if (
            (in_Dimension1 === undefined || in_Dimension1 === '') &&
            (in_Dimension2 === undefined || in_Dimension2 === '') &&
            (in_Dimension3 === undefined || in_Dimension3 === '') &&
            (in_Dimension4 === undefined || in_Dimension4 === '') &&
            (in_Dimension5 === undefined || in_Dimension5 === '') &&
            (in_Dimension6 === undefined || in_Dimension6 === '') &&
            (in_Dimension7 === undefined || in_Dimension7 === '') &&
            (in_Dimension8 === undefined || in_Dimension8 === '') &&
            (in_Dimension9 === undefined || in_Dimension9 === '')
        ) {
            if (in_BusinessPartner === undefined || in_BusinessPartner === '') {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-input-filter-parameter',
                        "Missing parameters: 'in_Account' or 'in_Dimension' or 'in_BusinessPartner'",
                    ),
                );
            }
        }
    }
    if (in_Currency === undefined || in_Currency === '') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/x3-finance/analytical-balance-query-balance-error-message-missing-in_Currency-parameter',
                "Missing parameter: 'in_Currency'",
            ),
        );
    }
    const queryData = context.query(sageX3Finance.nodes.AnalyticalBalance, {
        filter: {
            ...(in_LedgerType && { ledgerType: in_LedgerType }),
            ...(in_Company && { company: in_Company }),
            ...(in_FiscalYear && { fiscalYear: in_FiscalYear }),
            ...(in_Site && { site: in_Site }),
            ...((in_Account || in_Account === '') && { account: in_Account }),
            ...((in_Dimension1 || in_Dimension1 === '') && { dimension1: in_Dimension1 }),
            ...((in_Dimension2 || in_Dimension2 === '') && { dimension2: in_Dimension2 }),
            ...((in_Dimension3 || in_Dimension3 === '') && { dimension3: in_Dimension3 }),
            ...((in_Dimension4 || in_Dimension4 === '') && { dimension4: in_Dimension4 }),
            ...((in_Dimension5 || in_Dimension5 === '') && { dimension5: in_Dimension5 }),
            ...((in_Dimension6 || in_Dimension6 === '') && { dimension6: in_Dimension6 }),
            ...((in_Dimension7 || in_Dimension7 === '') && { dimension7: in_Dimension7 }),
            ...((in_Dimension8 || in_Dimension8 === '') && { dimension8: in_Dimension8 }),
            ...((in_Dimension9 || in_Dimension9 === '') && { dimension9: in_Dimension9 }),
            ...((in_BusinessPartner || in_BusinessPartner === '') && {
                businessPartner: in_BusinessPartner,
            }),
            ...(in_Currency && { currency: in_Currency }),
        },
        orderBy: {
            ledgerType: 1,
            company: 1,
            site: 1,
            fiscalYear: 1,
            account: 1,
            businessPartner: 1,
            currency: 1,
        },
    });

    // Process returned data
    const processedData = await queryData
        .map(async balance => {
            const fiscalYear = await balance.fiscalYear;
            const closingPeriod = (await fiscalYear?.periods.length) ?? 0;
            // ctl referencePeriod parameter vrs. closingPeriod of the cpy/ledgerType/fiscalYear
            const referencePeriodInstance = await fiscalYear?.periods.at(in_ReferencePeriod - 1);
            if (
                !referencePeriodInstance ||
                in_ReferencePeriod < 1 ||
                (closingPeriod !== undefined && in_ReferencePeriod > closingPeriod)
            ) {
                throw new Error(
                    context.localize(
                        '@sage/x3-finance/analytical-balance-query-balance-error-message-incorrect-referencePeriod-parameter',
                        "Incorrect 'in_ReferencePeriod' parameter",
                    ),
                );
            }
            // filtering balance amounts including carry forward (0), amounts of periods until the asked period and
            // the closing amount (number of periods + 1)
            const amounts = await balance.amounts
                .filter(
                    async amount =>
                        ((await amount.denormalizedIndex) >= 1 &&
                            (await amount.denormalizedIndex) <= in_ReferencePeriod + 1) ||
                        (closingPeriod !== undefined && (await amount.denormalizedIndex) === closingPeriod + 2),
                )
                .toArray();
            return {
                referencePeriod: in_ReferencePeriod,
                referencePeriodStartDate: (await referencePeriodInstance?.startDate) || undefined,
                referencePeriodEndDate: (await referencePeriodInstance?.endDate) || undefined, // new DateValue(********),
                ledgerType: await balance.ledgerType,
                company: await (await balance.company).code,
                companyName: await (await balance.company).name,
                site: (await (await balance.site)?.code) || undefined,
                siteName: (await (await balance.site)?.name) || undefined,
                fiscalYear: (await (await balance.fiscalYear)?.code) || undefined,
                fiscalYearStartDate: (await (await balance.fiscalYear)?.startDate) || undefined, // new DateValue(********),
                fiscalYearEndDate: (await (await balance.fiscalYear)?.endDate) || undefined, // new DateValue(********),
                fiscalYearNumberOfPeriods: (await (await balance.fiscalYear)?.periods?.length) || undefined,
                account: await (await balance.account)?.code,
                accountDescription: await (await balance.account)?.localizedDescription,
                businessPartner: (await (await balance.businessPartner)?.code) || undefined,
                businessPartnerName: (await balance.businessPartner)
                    ? (await (await balance.businessPartner)?.companyName1) || undefined
                    : '',
                currency: await (await balance.currency).code,
                ledgerCurrency: (await (await balance.ledgerCurrency)?.code) || undefined,
                dimensions: await balance.dimensionTypes
                    .map(async dimensionType => {
                        return {
                            dimensionType: await (await dimensionType.dimensionType)?.dimensionType,
                            dimensionName: await (await dimensionType.dimensionType)?.localizedDescription,
                            dimension: (await dimensionType[
                                `dimension${await dimensionType.denormalizedIndex}` as keyof AnalyticalBalanceDimensionTypes
                            ]) as string,
                        };
                    })
                    .toArray(),
                carryForwardCreditAmountInCurrency: 0,
                periodCreditAmountInCurrency: 0,
                previousPeriodsCreditAmountInCurrency: 0,
                closingCreditAmountInCurrency: 0,
                carryForwardDebitAmountInCurrency: 0,
                periodDebitAmountInCurrency: 0,
                previousPeriodsDebitAmountInCurrency: 0,
                closingDebitAmountInCurrency: 0,
                carryForwardCreditAmountInLedgerCurrency: 0,
                periodCreditAmountInLedgerCurrency: 0,
                previousPeriodsCreditAmountInLedgerCurrency: 0,
                closingCreditAmountInLedgerCurrency: 0,
                carryForwardDebitAmountInLedgerCurrency: 0,
                periodDebitAmountInLedgerCurrency: 0,
                previousPeriodsDebitAmountInLedgerCurrency: 0,
                closingDebitAmountInLedgerCurrency: 0,
                // getting amounts computed
                ...(await getAmounts(amounts, closingPeriod)),
            };
        })
        .toArray();

    const result = Promise.resolve({ output: processedData });
    return result;
}
