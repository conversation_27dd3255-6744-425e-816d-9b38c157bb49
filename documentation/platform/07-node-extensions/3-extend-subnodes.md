PATH: XTREEM/07+Node+extensions/3+Extend+subNodes

# Extend subNodes

## Extending nodes in a class hierarchy

When working with a class hierarchy you need a bit of extra care when writing an extension;
you have to pick a different base class for the extension at every level of your hierarchy. For example,
assuming that you have an `Animal` -> `Mammal` -> `Dog` hierarchy, you must derive the `Mammal` and `Dog` extension classes from `SubNodeExtension1` and `SubNodeExtension2` respectively, as in the example below:

```ts
// Extending the Animal class
@decorators.nodeExtension<AnimalExtension>({
    extends: () => Animal,
})
export class AnimalExtension extends NodeExtension<Animal> {
    ...
}

declare module '@sage/my-zoo/lib/nodes/animal' {
    interface Animal extends AnimalExtension {}
}

// Extending the Mammal class
// CAUTION HERE: use subNodeExtension1 decorator and define the subclass with SubNodeExtension1
@decorators.subNodeExtension1<MammalExtension>({
    extends: () => Mammal,
})
export class MammalExtension extends SubNodeExtension1<Mammal> {
    ...
}

declare module '@sage/my-zoo/lib/nodes/mammal' {
    interface Mammal extends MammalExtension {}
}

// Extending the Dog class
// CAUTION HERE: use subNodeExtension2 decorator and define the subclass with SubNodeExtension2
@decorators.subNodeExtension2<DogExtension>({
    extends: () => Dog,
})
export class DogExtension extends SubNodeExtension2<Dog> {
    ...
}

declare module '@sage/my-zoo/lib/nodes/dog' {
    interface Dog extends DogExtension {}
}
```

## Gotchas

### Importing a base class from the same package

Base classes which are defined in the same package as the subclass must be imported as:

```ts
import { MyBaseNode } from './my-base-node';
```

rather than with:

```ts
import { MyBaseNode } from './_index';
```

If you use the second form you are likely to get an error like "TypeError: Class extends value undefined is not a constructor or null" when building the package with `pnpm run build`.
So, always use the first form.

### Limitation in inheritance depth

The current API only supports 5 levels of subclassing (up to `SubNodeExtension5`). It can be expanded if this is not sufficient.
