PATH: XTREEM/1+Node+extensions+generalities/4+Loosely+coupled+node+extensions

# Loosely-coupled node extensions

Nodes may be extended in a tightly or loosely coupled manner:

-   A tightly-coupled extension adds columns directly to the node's table.
-   A loosely-coupled extension does not modify the node's table. Instead, it extends it with another table which is joined with the node's table.

Tight coupling is a good solution when the service that we are building will always use the extension and the node together. The service's SQL schema will contain a single table for the node.

Loose coupling is the right solution when the extension is optional, typically, when the extension belongs to an add-on which may be enabled in some but not all tenants.

## Loose-coupling with vital references

Loosely coupled extensions are implemented with vital references. The design uses 3 TypeScript classes:

-   a base class which defines the base node, without any extensions.
-   a vital child node class, which contains the extension properties.
-   a node extension class, which extends the base class with a vital reference to the vital child node class.

The base class is delivered in one of the packages of the core service.
The other two classes are delivered in an add-on package.

## Example

For example, the xtremMasterData package of the core service provides a base `Item` class:

```typescript

@decorators.node<Item>({
    storage: 'sql',
    isPublished: true,
    ...
})
export class Item extends Node<Item> {
    @decorators.stringProperty<MyNodeExtension, 'name'>({
        isStored: true,
        isPublished: true,
        ...
    })
    readonly name: Promise<string>;
}
```

Then a _pharmacy_ add-on can extend this node with the following two classes:

A node extension:

```typescript
@decorators.nodeExtension<ItemPharmacyExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemPharmacyExtension extends NodeExtension<Item> {
    @decorators.referenceProperty<ItemPharmacyExtension, 'pharmacy'>({
        isPublished: true,
        node: () => pharmacy.nodes.ItemPharmacy,
        reverseProperty: 'item',
    })
    readonly pharmacy: Reference<ItemPharmacy>;
}
```

A vital child node class:

```typescript
@decorators.node<ItemPharmacy>({
    storage: 'sql',
    isPublished: true,
    isVitalChild: true,
    ...
})
export class ItemPharmacy extends Node {
    @decorators.referenceProperty<ItemPharmacy, 'item'>({
        isStored: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<Item>;

    @decorators.stringProperty<ItemPharmacy, 'posology'>({
        isStored: true,
        isPublished: true,
        dataType: () => posologyDatatype,
    })
    readonly posology: Promise<string>;

    // more pharmacy properties ...
}
```

The properties of the extension can be accessed as follows in the pharmacy package:

```typescript
const item = await context.read(xtremMassterData.nodes.item, code);
const posology = await(await item.pharamacy).posology;
```

## Using the add-on pattern inside Sage DMO

The add-on pattern should be used inside Sage DMO for extensions that are specific to a Sage DMO service.

We can use the tight-coupling approach to extends nodes in low-level packages (for example between xtrem-structure and xtrem-master-data) but we should use the loose coupling approach for extensions that are specific to a service (specific to stock, to manufacturing, ...)
