PATH: XTREEM/1+Node+extensions+generalities/2+Simple+examples

# Simple examples

## A first simple example

Let's write out the example given in the previous paragraph:

-   We are developing a `pharmacy` package depending on `@sage/xtrem-master-data`.
-   We want to add a `posology` property to the `Item` node.

Then, we need to create a file called `item-extension.ts` in `pharmacy/lib/node-extensions`, containing the following code:

```ts
// Necessary import from the framework to implement the extension mechanism
import { decorators, NodeExtension } from '@sage/xtrem-core';

// Import the node we want to extend
import * as xtremMasterData from '@sage/xtrem-master-data';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<Item> {
    @decorators.stringProperty<MyNodeExtension, 'posology'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => myStringDatatype,
    })
    readonly posology: Promise<string | null>;
})

// Necessary statement at the end of item-extension.ts.
// This notifies typescript that the current class completes the implementation of the Item node.
declare module '@sage/xtrem-master-data/lib/nodes/item' {
    interface Item extends ItemExtension {}
}

```

## A more generic example

Here is a second example, with:

-   A new property added by the extension: `myExtensionProperty`,
-   A property already existing in the original node enhanced by the extension: `existingProperty`,
-   A new static query added by the extension: `myExtensionQuery`.

```ts
@decorators.nodeExtension<MyNodeExtension>({
    extends: () => MyNode,
})
export class MyNodeExtension extends NodeExtension<MyNode> {
    // This property does not exist in MyNode and will be added
    @decorators.booleanProperty<MyNodeExtension, 'myExtensionProperty'>({
        isPublished: true,
    })
    readonly myExtensionProperty: Promise<boolean>;

    // This property exists in MyNode
    @decorators.booleanProperty<MyNodeExtension, 'existingProperty'>({
        // the extension adds a dependency
        dependsOn: ['myExtensionProperty'],
        // the extension adds a control rule
        async control(cx) {
            if (await this.myExtensionProperty) cx.error.if(await this.existingProperty).is.false();
        },
        // the extension overrides the defaultValue rule
        defaultValue() {
            return this.myExtensionProperty;
        },
    })
    readonly existingProperty: Promise<boolean>;

    // This static method does not exist in MyNode and will be added
    @decorators.query<MyNodeExtension, myExtensionQuery>({
        isPublished: true,
        parameters: [{
            name: 'arg',
            type: 'string',
        }],
        return: 'string',
    })
    static myExtensionQuery(context: Context, arg: string) {
        return `got ${arg}`;
    }
})

// Keep TypeScript happy: add the properties and methods defined by MyNodeExtension to the MyNode type.
// Note that declaration may feel backwards but it is not; this is what TypeScript needs to be happy.
declare module '@sage/my-package/lib/nodes/my-node' {
    interface MyNode extends MyNodeExtension {}
}
```

## A quick note on node extension availability

The node extension will only be active in the package that defines it and in packages that depend on it.

-   In the first example above, the `posology` property won't be added within the `@sage/xtrem-master-data` package.
    It will only be added within the `pharmacy` package.
    If an application is launched without the `pharmacy` package, the new column won't be there.

-   In the second example above, the added property (`myExtensionProperty`) will be available in the package where `MyNodeExtension` is defined but it will not be available in `@sage/my-package`, the package where `MyNode` is defined.

Note: node extensions should not be defined in the same package as the node that they extend; they should be defined in packages that depend on the package where the node being extended was defined.
