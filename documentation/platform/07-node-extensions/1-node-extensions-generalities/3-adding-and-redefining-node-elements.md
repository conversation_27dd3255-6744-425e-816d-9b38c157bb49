PATH: XTREEM/1+Node+extensions+generalities/3+Adding+and+redefining+node+elements

# Adding and redefining node elements

A node extension may:

-   add properties to a node
-   add methods to a node
-   add indexes to a persistent node
-   add rules (control, saveBegin, saveEnd) to a node
-   add control rules to the properties of a node.
-   override some rules (defaultValue, getValue) on properties of a node.
-   add dependencies to properties of a node.

In the next section, we present more thoroughly the different possibilities offered to applicative developers regarding node extensions.
