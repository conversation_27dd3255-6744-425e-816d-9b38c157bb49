PATH: XTREEM/1+Node+extensions+generalities/1+Definition

# Definition

## What is a Node Extension

A node extension is a class that extends a node class.

An applicative developer will write a node extension if he or she needs to enhance a node that was defined in a dependency of the package currently being developed.

For instance, let's imagine that:

-   A developer is currently developing a `pharmacy` package depending on `@sage/xtrem-master-data`.

-   He or she needs to add a `posology` property to the `Item` node.

Then, the way to do this is to define a node-extension on the node `Item` in the `pharmacy` package.

Note: it would make no sense to define a node-extension for the `Item` node directly in the package where it was first defined (`@sage/xtrem-master-data`).

## A kind of partial class

The node extension mechanism allows to split the implementation of a single class among several files, belonging to several packages. It is similar in that aspect to C# partial classes.
