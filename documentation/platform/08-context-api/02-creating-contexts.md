PATH: XTREEM/Server+Framework+Documentation/08+Context+API/02+Creating-contexts

# Executing code in a new writable context

A developer may have the need to execute a piece of code with a separate writable context (possibly asynchronously). A use case may be batch processing of a large list of data. The developer would want to split this data into batches and process the batches asynchronously.

To do this, the developer can use the API `context.runInWritableContext`.
This API accepts a callback function that passes a writable context and returns a Promise.
Errors can be caught with a try/catch.
(do **not** use `promise.then(...)` or `promise.catch(...)`, use `await ...`).

```ts
    try {
        await context.runInWritableContext(writableContext => /** business logic **/);
    }
    catch(err) {
        await context.runInWritableContext(writableContext => /** write to log table **/);
    }
```

## options

runInWritableContext has two options:

-   isolationLevel: the transaction isolation level.
-   noCommit: do not commit the transaction. Can be useful for testing purpose such as a Dry-Run.

## Limitations

-   The root context must be a readonly context.

-   The source of the root context needs to be a custom node mutation or message/notification listener, i.e. `context.runInWritableContext` can currently only be used in the static methods of a listener or custom node mutation. Also, note that the listener and node mutation context are implicitly writable, therefore to use `context.runInWritableContext` in the listener or node mutation one would need to ensure that the `startsReadOnly` attribute on the listener or node mutation decorator is `true` or resolves to `true`.

-   Node instances created outside of the writable context cannot be used in the callback body passed to `context.runInWritableContext`. The data set to be processed can be built in the root context as an object or an array of objects, and this can be referenced in `context.runInWritableContext`.
