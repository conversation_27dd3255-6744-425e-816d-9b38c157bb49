PATH: XTREEM/Server+Framework+Documentation/08+Context+API/03+Low-level+select+API

# Low-level Select API

## Context.select method

The `context.select` method is an efficient low-level API to query the SQL database. Here is an example:

```ts
const data = await context.select(
    // node constructor
    xtremSales.nodes.SalesOrder,
    // selector
    {
        number: true,
        status: true,
        orderDate: true,
        soldToAddress: { name: true, addressLine1: true, city: true, country: { name: true } },
        lines: { item: { name: true }, quantity: true },
        totalQuantity() {
            return this.lines.sum(line => line.quantity);
        },
    },
    // options
    {
        first: 100,
        filter: { orderDate: { _gt: '2023-05-01' }, soldToAddress: { country: { id: 'US' } } },
        orderBy: { orderData: +1 },
    },
);
```

The response will be an array of plain TypeScript objects like:

```ts
[
    {
        number: 'S00158',
        status: 'inProgress',
        orderDate: '2023-05-02',
        soldToAddress: { name: '<PERSON>', addressLine1: '1052 Main St.', city: 'New York', country: { name: 'US' } },
        lines: [
            { item: { name: 'Mountain bike' }, quantity: 1 },
            { item: { name: 'Power pedals' }, quantity: 2 },
        ],
        totalQuantity: 3,
    },
    {
        number: 'S00159',
        status: 'closed',
        orderDate: '2023-05-02',
        soldToAddress: { name: 'Alice Smith', addressLine1: '10 Circus Sq.', city: 'Boston', country: { name: 'US' } },
        lines: [{ item: { name: 'Short sleeve jersay ' }, quantity: 2 }],
        totalQuantity: 2,
    },
    // more ....
];
```

### selector parameter

The selector parameter describes the properties that you want to select.
The selector can traverse references and collections, vital or non-vital, at any depth.
The selector may also include functions to compute values that are not stored directly in the database.

In the example above the selector traverses the `lines` collection. It also traverses the `soldToAddress` reference, its `country` reference, and the `item` reference from the `lines`.

The selector of our example also computes `totalQuantity` with a function that sums the quantity of the lines (here we assume that totalQuantity is not stored in the sales_order table). Selector functions must be convertible to SQL. If not a `ConversionError` will be thrown.

### options parameter

The `context.select` method supports the same filtering, ordering and paging options as the `context.query` method.

## Retrieving only record ids

If you set a reference to `true` in a selector, the query will return the `_id` of the referenced record.
Similarly, if you set a collection to `true`, the query will return an array of `_id` values.
For example, a selector like:

```ts
{
    number: true,
    soldToAddress: true,
    lines: true,
}
```

will return a payload like:

```ts
[
    { number: 'S00158', soldToAddress: 18, lines: [2784, 2785] },
    { number: 'S00159', soldToAddress: 49, lines: [2802] },
    // more ...
];
```

You can also pass `true` in the selector parameter of `context.select`, to get only the `_ids` of the selected records:

-   `context.select(SalesOrder, { _id: true }, options)` --> `[{ _id: 504 }, { _id: 507}, ...]`
-   `context.select(SalesOrder, true, options)` --> `[504, 507]`

## Performance

The `context.select` method fetches the entire data, and only the selected columns, with a single query, unlike `context.query` which always fetches complete records and loads the references and collections lazily, with a cache.
Also, `context.select` does not instantiate (and cache) nodes. It returns plain TypeScript objects that are not cached.

So `context.select` should be faster than `context.query`.

## Gotchas

`context.select` cannot select properties that are defined by a `computeValue` rule.
On the other hand, it can handle properties defined by a `getValue` rule.

Be careful if you mix low-level calls that return plain objects and high-level calls that return node instances in the same transaction.
Node instances are cached in the transaction so if you run two queries that return a common node, they will return the same node instance.

Furthermore, if this node instance was modified with a `node.$.set` call but not yet saved with `node.$.save`, the node instance returned by `context.query` will contain these modifications.
On the other hand, the `context.select` call will always fetch from the database and will ignore changes to nodes that haven't been saved.

So you should not mix low-level and high-level calls on the same node in the same transaction (same context).
