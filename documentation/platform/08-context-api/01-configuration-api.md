PATH: XTREEM/Server+Framework+Documentation/08+Context+API/01+Configuration+API

# Context Configuration API

## What is the configuration API?

The _configuration API_ is a class that exposes specific properties of the loaded _config_ from the **_xtrem-config.yml_** file.

The _configuration API_ is exposed via the _context_.

```ts
const myPackage = context.configuration.getPackageConfig<MyPackage>('@foo/myPackage', {});
```

## Properties exposed in configuration service

### Deployment Mode

Allows applicative code to test if code is executing in **_production_** or **_development_** mode.

```ts
if (context.configuration.deploymentMode === 'development') context.logger.verbose(() => `Foo`);
```

In xtrem-config.yml

```yml
deploymentMode: development
```

### Package Config

Returns the configuration data for the supplied packages in the **_xtrem-config.yml_** file.

```ts
const myPackage = context.configuration.getPackageConfig<MyPackage>('@foo/myPackage', {});
```

In xtrem-config.yml

```yml
packages:
    '@foo/myPackage':
        host: bar
        log:
            level: info
```

**Note:**
If the package is not defined in the xtrem-config.yml file, the second parameter value passed to the function `getPackageConfig` will be returned.

### Error monitoring

Allows configuration of the unhandled error monitoring. If more than `errorMonitoringThreshold` errors occurs during the `errorMonitoringInterval` the process exits. Default values as respectively `10` errors and `3600` seconds (1 hour).

```yml
errorMonitoringInterval: 3600
errorMonitoringThreshold: 10
```
