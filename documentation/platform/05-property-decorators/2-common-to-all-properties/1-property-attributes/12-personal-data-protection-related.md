PATH: XTREEM/1+Property+attributes/12+personalDataProtectionRelated

## Personal Data Protection

The purpose of this documentation is to provide information on the decorators used to identify properties containing sensitive information, and to describe the methods used for anonymizing such data.

Application developers should use these decorators to define the data sensitivity level for a specific node property and define a method of anonymizing the data contained in the field. This method will then be used when anonymizing the data, for example, when exporting a tenant with the `--anonymize` flag.

## dataSensitivityLevel

The `dataSensitivityLevel` decorator is used to set the sensitivity level of a property that contains sensitive information. The possible values for this decorator are:

-   `notSensitive`: this is the default value.
-   `businessSensitive`: information not related to individuals but still possibly sensitive or damaging if leaked.
-   `personal`: information allowing to identify persons as defined by the GDPR regulations. Examples: name, address, email, social security no.
-   `personalSensitive`: sensitive information according to GDPR regulations and associated to persons. Examples: religion, health data, sexual or political orientation.
-   `highlySensitive`: highly sensitive information associated to persons as defined by the Californian CPRA regulations, i.e. criminal records.

Functionality required for GDPR is still in development, but will be based on the data sensitivity level per property.

## anonymizeMethod / anonymizeValue

The `anonymizeMethod` and `anonymizeValue` decorators are used together to define the method of anonymizing the data in a sensitive property. The `anonymizeValue` decorator is optional and only required for certain methods. Below are the methods that are currently available, with an example for each:

### `fixed`

Replaces the data with a fixed value, specified in anonymizeValue. NULL source values will be kept on export and not replaced by the fixed value.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'stringFixed'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'fixed',
        anonymizeValue: 'FixedValue',
    })
    readonly stringFixed: Promise<string>;
```

### `random`

Generates a random number. Only applicable to number types. The size of the generated number will be limited by the type or the max value specified for the property. For NULL source values, no random number will be created on export.

```ts
    @decorators.integerProperty<TestAnonymizeExport, 'integerLimitRandom'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'random',
        max: 500,
    })
    readonly integerLimitRandom: Promise<integer>;
```

### `hash`

Generates a SHA256 hash from the value. Only applicable to strings. The length of the string will be limited by the type. For NULL source values, no hash will be created on export.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'stringShortHash'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hash',
    })
    readonly stringShortHash: Promise<string>;
```

### `hashLimit`

Similar to `hash`, but limits the length of the string. The required length is specified in anonymizeValue. For NULL source values, no hash will be created on export.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'stringHashLimit'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hashLimit',
        anonymizeValue: 15,
    })
    readonly stringHashLimit: Promise<string>;
```

### `perCharRandom`

Randomizes each character of the string, while maintaining the same structure as the original data. Only applicable to strings. Uppercase characters, lowercase characters and digits will be replaced with a random character of the same type, while other characters will be left as is.

This is useful for data like names, email addresses, phone numbers etc. For NULL source values, no random value will be created on export.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'stringPerCharacter'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 255 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'perCharRandom',
    })
    readonly stringPerCharacter: Promise<string>;
```

### `url`

Used to anonymize a URL. Preserve the protocol of the URL and randomize the rest using the `perCharRandom` method.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'urlToAnonymize'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 255 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'url',
    })
    readonly urlToAnonymize: Promise<string>;
```

### `custom`

Allows for a custom transformation to be applied to the data. A callback function is used to generate the transformed value, with the original value passed as a parameter.

The developer must cater for the custom transformation in the Anonymize Export Service, which generates a SQL script that can anonymize a database in place. More information regarding this will be provided later.

```ts
    @decorators.stringProperty<TestAnonymizeExport, 'stringCustom'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return value.split('').reverse().join('');
        },
    })
    readonly stringCustom: Promise<string>;
```

### `binary`

Used for binaryStream properties containing images or PDFs. This method will replace the original image or PDF by a dummy file. No other binary types are supported. The relevant type is selected with anonymizeValue. NULL source values will be kept and not replaced on export.

```ts
    @decorators.binaryStreamProperty<TestAnonymizeExport, 'pdf'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'binary',
        anonymizeValue: 'pdf',
    })
    readonly pdf: Promise<BinaryStream> | null;
```
