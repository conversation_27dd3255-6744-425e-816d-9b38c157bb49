PATH: XTREEM/1+Property+attributes/13+lookupAccess

# lookupAccess

## What is lookupAccess

Properties with `lookupAccess` set to true will be available for lookup queries via graphQL.

When access rights for a user is calculated, lookup access is implicitly granted to all nodes in the vital graph of
the nodes to which the user has been granted access.

When a user does a query or read operation on a node to which only lookup access has been granted, the operation will be
treated as a lookup operation if the node has some properties that have `lookupAccess` set to true. In this
case, only the properties flagged with `lookupAccess` will be returned via graphQL.

The following system properties have `lookupAccess` set to true by default:

`_id`, `_custom_data`, `_createStamp`, `_updateStamp` and `_sortValue`

These system properties are ignored when considering if a node is a lookup node. There has to be some properties with
an explicit `lookupAccess` set to true in order for it to be treated as as described above.
