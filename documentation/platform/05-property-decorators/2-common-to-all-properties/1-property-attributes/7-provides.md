PATH: XTREEM/1+Property+attributes/7+provides

# provides

## What attribute provides means to a property

**provides** makes it possible for a property to provide one or more standard attributes for the node. **provides** may contain the following keywords.

### accessCode:

Identifies the property which contains the access code to be used by the Access Rights Manager for determining if the user is allowed to access the node (see Access Rights Manager)

### site:

Identifies the property which contains the site to be used by the Access Rights Manager for determining if the user is allowed to access the node (see Access Rights Manager)

### isActive:

All reference properties are filtered by the isActive field of the target node by default, if the `provides: ['isActive']` attribute is set on the target node (see referenceProperty)

### key:

TO BE DOCUMENTED

### company:

TO BE DOCUMENTED

### title:

TO BE DOCUMENTED

### shortTitle:

TO BE DOCUMENTED
