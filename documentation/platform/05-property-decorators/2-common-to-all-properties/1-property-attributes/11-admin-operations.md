PATH: XTREEM/1+Property+attributes/11+adminOperations

## adminOperations

This property is defined in the `NodeDecorator` and it allows us to set which operations on a node require administrator
rights.

`adminOperations?: 'all' | 'allMutations' | (keyof Node | StandardOperation)[];`

Administrator rights are defined at the User (`User node` and `UserInfo interface`) level using the `isAdministrator`
property.

### All Operations

In this case, `all the operations` on the node will require administrator rights.

```ts
@decorators.node<MyNode>({
    // ...
    adminOperations: 'all',
})
export class MyNode extends Node {}
```

### All Mutations

In this case, `all the mutations` on the node will require administrator rights.

```ts
@decorators.node<MyNode>({
    // ...
    adminOperations: 'all',
})
export class MyNode extends Node {}
```

### Only Specific Operations

In this case, only the specified operations on the node will require administrator rights.

```ts
@decorators.node<MyNode>({
    // ...
    adminOperations: ['create', 'update'],
})
export class MyNode extends Node {}
```
