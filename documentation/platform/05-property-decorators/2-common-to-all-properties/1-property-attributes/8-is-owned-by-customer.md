PATH: XTREEM/1+Property+attributes/8+isOwnedByCustomer

# isOwnedByCustomer

## What attribute isOwnedByCustomer means to a property

When set to true attribute **isOwnedByCustomer** makes it possible to update a property belonging to a record protected by a vendor.

The attribute **isOwnedByCustomer** is only allowed if:

-   the node hasVendorProperty set to true on the node decorator,
-   the node is a setup one,
-   the property is not part of the natural key.

The other option to allow the update of such protected node is to set the `ignoreVendorProtection` top level parameter to `true` in your `xtrem-config.yml` configuration file:

```yml
ignoreVendorProtection: true
```

> ⚠️ **[WARNING]**
> Use this config setting with extreme caution!
