PATH: XTREEM/1+Property+attributes/14+duplicateRequiresPrompt

# duplicateRequiresPrompt

## What is duplicateRequiresPrompt

Nodes with `canDuplicate` set to true can specify custom behavior on a property level.

Properties with `duplicateRequiresPrompt` set to true should prompt the user for a value.

The default duplication behavior is to keep the original value of properties. New values can be
specified using `duplicatedValue`.
(Refer to [duplicatedValue](https://confluence.sage.com/display/XTREEM/7+duplicatedValue) for more details.)
