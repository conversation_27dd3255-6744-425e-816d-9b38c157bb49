PATH: XTREEM/1+Property+attributes/3+dependsOn

# dependsOn

## What attribute dependsOn means to a property

**dependsOn** makes it possible for a property to list properties on which is value is based upon. If, when accessing the property, the frameworks finds out that one of its dependencies was changed, it calls the **updatedValue** event, if it exists, in order to compute the new value. This mechanism ensures that **updatedValue** is called only once regardless of the number of dependsOn properties changed.

If the **updatedValue** rule isn't defined the framework does not update the value. If **updatedValue** is set to **useDefaultValue** then the framework updates the value with the **defaultValue** rule.

> ⚠️ **[WARNING]** > **dependsOn** only applies to properties that are part of the vital graph. **dependsOn** attributes are checked while building xtrem which provides a warning when detecting useless dependencies. These warnings should be fixed.

In the following example, extracted from [TestBase](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/back-end/xtrem-core/test/fixtures/nodes/base.ts), the value of dependsOn depends on the value of the property controlled.

```ts
@decorators.node<TestBase>({
    isPublished: true,
    storage: 'sql',
})
export class TestBase extends Node {
    @decorators.stringProperty<TestBase, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestBase, 'controlled'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        defaultValue() {
            return 'GOOD1';
        },
        async control(cx) {
            cx.error.if(await this.controlled).is.equal.to('BAD1');
        },
    })
    readonly controlled: Promise<string>;

    @decorators.stringProperty<TestBase, 'dependsOn'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        dependsOn: ['controlled'],
        defaultValue() {
            return 'INITIAL1';
        },
        updatedValue() {
            return 'UPDATED1';
        },
    })
    readonly dependsOn: Promise<string>;
}
```
