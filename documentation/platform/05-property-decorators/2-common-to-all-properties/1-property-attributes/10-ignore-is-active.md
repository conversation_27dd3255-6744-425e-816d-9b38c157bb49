PATH: XTREEM/1+Property+attributes/10+ignoreIsActive

# ignoreIsActive

## What is a ignoreIsActive property

A `ignoreIsActive` is a property or computed one(from other properties) that decides whether the item should be have active columns or not
Example: in case of a purchase order, we need to forbid the usage of a non active item reference in case of a new document, but allow it when the purchase order is being updated when it is received/invoiced/returned.

## ignoreIsActive decorator attribute

`ignoreIsActive` is a decorator attribute which influences lookup queries and control rules on reference properties.
This decorator attribute is only relevant if:

-   the property is a reference property
-   the node that it references has a property which indicates whether a record is active or not (a property flagged with `provides: ['isActive']`).
    By default, lookups are filtered on reference properties. They only return _active_ records. The default control rule also checks that reference properties are set to _active_ records when the reference is initialized or modified.
    This default behavior is suitable for most cases because we want to prevent the user from selecting _inactive_ records but there are special situations where we don't want to enforce this and we want to let the user select records regardless of the active/inactive state. This is where the `ignoreIsActive` attributes comes into play. It allows us to flag properties which should not filter nor control the active state of the referenced records.
    The `ignoreIsActive` attribute may be a boolean literal (true/false) or a function which returns a boolean. The function give us the flexibility to use a business rule to determine whether inactive references should be filtered out or not.
    For example, in the case of a purchase order, we can set the `ignoreIsActive` attribute of the item reference to a function which will exclude inactive items if the new purchase order is being created, but which will allow them if the purchase order is being updated after being received/invoiced/returned.

## How to use it

Set the `ignoreIsActive` attribute to true/false, or to a function returning a boolean.

```ts
 @decorators.referenceProperty<PurchaseOrderLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
         //compute it
         ignoreIsActive() {
             return xtremPurchasing.functions.shouldIgnoreIsActive(this);
         },
         // or set it
         ignoreIsActive:true,
 })
 readonly item: Reference<xtremMasterData.nodes.item>
```
