PATH: XTREEM/1+Property+attributes/2+isTransientInput

# isTransientInput

## What is a transient property

A `transient` property, is a property of a `persistent` node that does not persist it's value back to the database. These properties can be set in `create` or `update` mutations and are used in the business logic that performs calculations or validations. `Transient` properties can only be passed as input to a graphql `create`/`update` mutation and cannot form part of the output properties.

## How to declare a transient property

Simply set the `isTransientInput` attribute on the property decorator to `true`. The `isStored` attribute of the property decorator cannot be set to `true` for `transient` properties. If a collection property `isTransientInput` is set to true and it is linked to a persistent node, the values of the transient node will not write to the table of the collection node.

```ts
@decorators.node<MyNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [],
    async controlBegin(cx) {
        if (await this.transientRef) {
            cx.error.if(await (await this.transientRef).code).is.equal.to('REF2');
        }
    },
})
export class MyNode extends Node {
    @decorators.stringProperty<MyNode, 'stringValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly stringValue: Promise<string>;

    @decorators.stringProperty<MyNode, 'transientValue'>({
        istransientInput: true,
        dataType: () => descriptionDataType,
        isPublished: true,
        isNullable: true,
    })
    readonly transientValue: Promise<string | null>;

    @decorators.stringProperty<MyNode, 'derivedValue'>({
        isStored: true,
        dataType: () => descriptionDataType,
        isPublished: true,
        isNullable: true,
        dependsOn: ['transientValue'],
        async defaultValue() {
            if ((await this.transientValue) != null) {
                return `transient set in defaultValue (${await this.transientValue})- ${await this.$.status}`;
            }

            return `transient not set in defaultValue - ${await this.$.status}`;
        },
        // Update the value with the defaultValue rule
        updatedValue: useDefaultValue,
    })
    readonly derivedValue: Promise<string>;

    @decorators.referenceProperty<MyNode, 'transientRef'>({
        isPublished: true,
        istransientInput: true,
        isNullable: true,
        node: () => TestReferred,
    })
    readonly transientRef: Reference<TestReferred | null>;

    @decorators.stringProperty<MyNode, 'derivedFromRef'>({
        isStored: true,
        dataType: () => descriptionDataType,
        isPublished: true,
        isNullable: true,
        dependsOn: ['transientRef'],
        async defaultValue() {
            if ((await this.transientRef) != null) {
                return (await this.transientRef).code;
            }
            return 'transient reference not set';
        },
        // Update the value with the defaultValue rule
        updatedValue: useDefaultValue,

        async control(cx) {
            if (this.$.status === NodeStatus.modified && (await this.transientRef)) {
                cx.error.if(await (await this.transientRef).code).is.equal.to('REF1');
            }
        },
    })
    readonly derivedFromRef: Promise<string>;

    @decorators.collectionProperty<MyNode, 'transientLines'>({
        isPublished: true,
        node: () => MyNodeLines,
        reverseReference: 'parent',
        istransientInput: true,
    })
    readonly transientLines: Collection<MyNodeLines>;

    @decorators.collectionProperty<MyNode, 'derivedCollection'>({
        isPublished: true,
        node: () => MyNodeLines,
        reverseReference: 'parent',
        isVital: true,
        defaultValue() {
            return this.transientLines.nodes;
        },
    })
    readonly derivedCollection: Collection<MyNodeLines>;

    @decorators.integerProperty<MyNode, 'derivedFromCollection'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue() {
            return this.transientLines.length;
        },
    })
    readonly derivedFromCollection: Promise<integer>;
}
```

In the node example above we show that `transient` properties can be normal **scalar** property type (string, integer, etc.), **reference** properties or even **collections**. We are using `transient` properties in `defaultValue` and `control` functions.

## GraphQL request

Simple create mutation passing in a transient property (`transientValue`) and receiving derived value (`derivedValue`) in request response.

```graphql
mutation {
    sage {
        myPackage {
            myNode {
                create(data: { stringValue: "Value 1", transientValue: "transient text" }) {
                    stringValue
                    derivedValue
                }
            }
        }
    }
}
```

```json
{
    "stringValue": "Value 1",
    "derivedValue": "transient set in defaultValue (transient text)- added"
}
```
