PATH: XTREEM/1+Property+attributes/5+Property+attribute+isFrozen

# isFrozen

## What is a frozen property

A `frozen` property is a property that cannot be modified any more.

A node may be partially frozen, in which case only some of its properties cannot be modified, or completely frozen, in which case the entire node cannot be modified.
The `isFrozen` attribute may be placed either in a node decorator, or in a property decorator, to handle these two cases.

There is usually a business rule that defines whether a property is frozen or not.
In the life-cycle of an object, most properties usually start in a non-frozen state, but some of them move to frozen state over time.
For example, the delivery date and delivery address of an order will become frozen once the order has been delivered.

If a property is frozen, any attempt to assign a new value to it will trigger an exception.

The UI reflects the frozen state of a property by disabling fields that are bound to it. This behavior is (will be) automatically provided by the UI framework.

## How to declare the rule for the frozen state of a property

Simply set the `isFrozen` attribute on the property decorator to a function returning a boolean.

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class MyDocument extends Node {
    @decorators.enumProperty<MyNode, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => statusDataType,
    })
    readonly status: Promise<StatusEnum>;

    @decorators.dateProperty<MyNode, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isFrozen() {
            return (await this.status) >= StatusEnum.delivered;
        },
    })
    readonly deliveryDate: Promise<date | null>;
}
```

In this example the `isFrozen` rule will return true as soon as the status of the document is greater than `delivered`.

Note: you can also set `isFrozen` to a constant (`isFrozen: true`), for example for a value that is set when the object is constructed and that should never change afterwards. This is a shortcut for `isFrozen() { return true; }`.
