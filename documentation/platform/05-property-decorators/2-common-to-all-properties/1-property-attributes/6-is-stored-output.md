PATH: XTREEM/1+Property+attributes/6+isStoredOutput

# isStoredOutput

## What is a stored output value?

A `stored output` property, is a property of a `persistent` node that does not allow the property value to be updated by an external input such as node-state transaction or GraphQL mutations.
The value of a `stored output` property will always be the result of the `updateValue` or attribute required on a `stored output` property.
Thus, a `stored output` property is a persistent output-only property.

## How to declare a stored output property

Set the `isStoredOutput` attribute on the property decorator to `true`.
If the `isStoredOutput` property attribute is set to true the `isStored` property attribute can be omitted - the property will automatically be interpreted as a stored property.

## When to use a stored output property

A `stored output` property would be implemented in the case where you want to prevent a value being updated by input when it should be determined by the `updateValue` property attribute. As an example, consider a `status` property on a `StockCountSession` node.
The value of the `status` property must be determined by the `StockCountList` vital collection. If not flagged with `isStoredOutput`, the `status` property forms part of the GraphQL mutation input layer and its `updateValue` can be overridden if an explicit value for status is provided in the GraphQL mutation or if set explicitly via node-state transactions.
This problem can be solved by setting the `isStoredOutput` property attribute on the the `status` property to `true`, converting `status` property to an output-only property, and excluding it from the GraphQL input type.
