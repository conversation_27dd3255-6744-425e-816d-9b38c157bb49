PATH: XTREEM/1+Property+attributes/9+isClearedByReset

# isClearedByReset

An `isClearedByReset` flag intervenes when a tenant is reset:

-   the tables of nodes that are flagged with isClearedByReset will be emptied.
-   otherwise the properties flagged with isClearedByReset will be reset to their default value.

# isClearedByReset flag rules

-   A vital child can not be tagged with a isClearedByReset (only its parent can). The flag can only be set on the root of a vital tree.
-   A property can only be tagged if its node is not tagged (directly or indirectly).
-   A subNode can only be tagged if its base node is not tagged.

## How to declare isClearedByReset flag

Simply set the `isClearedByReset` attribute on the node decorator.

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isClearedByReset: true,
})
export class MyDocument extends Node {
    @decorators.enumProperty<MyNode, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => statusDataType,
    })
    readonly status: Promise<StatusEnum>;

    @decorators.dateProperty<MyNode, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isFrozen() {
            return (await this.status) >= StatusEnum.delivered;
        },
    })
    readonly deliveryDate: Promise<date | null>;
}
```

Or on the property decorator

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class MyDocument extends Node {
    @decorators.dateProperty<MyNode, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly deliveryDate: Promise<date | null>;
}
```

### flag as a function

isClearedByReset flag can also be a **function**. The function should be convertible to SQL.

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class MyDocument extends Node {
    @decorators.integerProperty<MyNode, 'id'>({
        isStored: true,
        isPublished: true,
    })
    readonly id: Promise<number>;

    @decorators.dateProperty<MyNode, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isClearedByReset() {
            return (await this.id) === 1;
        },
    })
    readonly deliveryDate: Promise<date | null>;
}
```

**N.B:** When a property is reset, stored properties that depend on it (in the same node, or related node) will not be invalidated and recalculated. The only defaultValue will be considered.
When a parent is reset all its vital children are reset.
