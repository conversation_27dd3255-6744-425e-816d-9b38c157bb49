PATH: XTREEM/2+Property+events/5+updatedValue

# updatedValue

The `updatedValue` rule is called to update a property after one of its `dependsOn` properties has been modified.

If `updatedValue` is set to `useDefaultValue` then the `defaultValue` rule will be used to update the value. `useDefaultValue` is a special value that you import from `@sage/xtrem-core`.

If `updatedValue` is a literal (a string, a number, true/false) then the property will be updated with the literal value.

If `updatedValue` is a function, the property will be updated with the value returned by the function.

Otherwise, if `updatedValue` is absent (or undefined) the property value will not be updated. The current value will be preserved.

Note: do not duplicate the `defaultValue` rule when you want the value to be updated with the default rule. Use `useDefaultValue` instead.

# Examples

```ts
import { Node, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<Item>({
    isPublished: true,
    storage: 'sql',
})
export class Item extends Node {
    @decorators.stringProperty<Item, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<Item, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.nameDataType,
        dependsOn: ['code'],
        defaultValue() {
            return `item ${code}`;
        },
        // If code changes from C1 to C2, the defaultValue rule will be called and name will be set to 'item C2'.
        updatedValue: useDefaultValue,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Item, 'fancyName'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.nameDataType,
        dependsOn: ['code'],
        defaultValue() {
            return `item ${code}`;
        },
        // If code changes from C1 to C2, fancyName will be set to 'item C2 (updated)'.
        updatedValue() {
            return `item ${code} (updated)`;
        },
    })
    readonly fancyName: Promise<string>;

    @decorators.stringProperty<Item, 'initialName'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.nameDataType,
        dependsOn: ['code'],
        defaultValue() {
            return `item ${code}`;
        },
        // If code changes from C1 to C2, initialName will remain 'item C1' because there is no updatedValue rule.
    })
    readonly initialName: Promise<string>;
}
```
