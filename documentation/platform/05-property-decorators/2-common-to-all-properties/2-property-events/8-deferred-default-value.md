PATH: XTREEM/2+Property+events/8+deferred-default-value

# deferredDefaultValue

The `deferredDefaultValue` rule allows you to _defer_ the computation of a default value till the end of the current transaction.
This rule has been introduced to improve the throughput of concurrent transactions that allocate document numbers.

## Example

Our document node:

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    indexes: [],
    canCreate: true,
    indexes: [{ orderBy: 'number', isUnique: true, isNaturalKey: true }],
})
export class Document extends Node {
    @decorators.stringProperty<Document, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => documentNumberDataType,
        defaultValue() {
            return nanoid();
        },
        deferredDefaultValue() {
            return allocateDocumentNumber(this);
        },
    })
    readonly number: Promise<string>;

    @decorators.decimalProperty<Document, 'amount'>({
        isStored: true,
        isPublished: true,
    })
    readonly amount: Promise<decimal>;

    // more properties ...
}
```

Let us see how these rules are applied:

```ts
const documentId = await application.withCommittedContext(async context => {
    const document = await context.create(Documnent, { amount: 100 });
    // Here, document.number is a nanoid generated by the defaultValue rule.
    await document.$.save();
    // document.number is still the same nanoid because the transaction hasn't been committed yet.
    return document._id;
});
// Here, the transaction has been committed and the deferredDefaultValue rule has been executed.
// document.number has been updated with a document number allocated by `allocateDocumentNumber`.
// Let us read it in a new transaction:
await application.withReadonlyContext(async context => {
    const document = await context.read(Document, { _id: documentId });
    // Here document.number is something like DOC-2023-01-000072
    console.log(`Document number: ${await document.number}`);
});
```

Real applicative code does not create contexts explicitly with calls like `application.withCommittedContext`.
Instead, the context is created by the framework and the associated SQL transaction is committed by the framework before returning a response to the client.
So, the document number will be valid in the response but won't be valid inside the transaction.

## Why do we need a defaultValue rule?

This is because our example document node has a unique index on `document.number`.
If we did not initialize the document number with a nanoid we would not be able to insert several documents in the same transaction; we would get a SQL error (violation of uniqueness constraint).

Note: this could be improved by deferring this SQL constraint (follow-up story?)

## Is this new rule always executed?

No. This rule is only executed if the document is created without any document number, or with an empty document number.
If you include a document number in the `context.create` payload, the `deferredDefaultValue` rule will not be executed, and neither will `defaultValue`.

## What if I need the document number inside the transaction?

You can do it, with a new API: `context.flushDeferredActions()`.
This call will evaluate and assign all the deferred default values.

For example, in a mocha test:

```ts
Test.withContext(async context => {
    const document = await context.create(Documnent, { amount: 100 });
    await document.$.save();
    // document.number has not been allocated yet. It is still a nanoid.

    await context.flushDeferredActions();
    // Now the document number has been allocated
    const documentNumber = await document.number;
    assert.isTrue(documentNumber.startsWith(`DOC-${date.today().year}`));
});
```

You can use `flushDeferredActions` liberally in unit tests but you should avoid it in business rules or mutations.

If you create a document and other objects that reference it in the same transaction, you should establish the references with the `_id` of the newly created document, not with its document number.

If instead, you call `flushDeferredActions` inside a mutation, you may get lower throughput on concurrent executions of the mutation.
