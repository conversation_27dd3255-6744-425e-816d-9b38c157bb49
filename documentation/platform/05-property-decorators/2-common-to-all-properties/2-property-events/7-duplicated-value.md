PATH: XTREEM/2+Property+events/7+duplicatedValue

# duplicatedValue

Nodes with `canDuplicate` set to true or vital nodes that have at least one parent with `canDuplicate` set to true can be duplicated with `node.$.duplicate` or the `getDuplicate` graphQL query.

A duplicate of the node will be returned, but with some modified values to be able to create a new node. Properties where the `duplicatedValue` attribute is set will have the result of duplicatedValue and \_id values will be negative values.

The `duplicatedValue` attribute can be either:

-   a value for the property
-   a function returning a value for the property
-   the `useDefaultValue` special value,

When `duplicatedValue` is set to `useDefaultValue` the `defaultValue` rule of the property will be used.
If the property does not have any `defaultValue` rule, the default value of the property's type will be used.

An error will be thrown if you set `duplicatedValue` to `useDefaultValue` on a non-nullable reference property
that does not have any `defaultValue` rule.

## Example of a node with some duplicatedValue rules

```ts
@decorators.node<TestDuplicatedValue>({
    storage: 'sql',
    isPublished: true,
    indexes: [],
    canCreate: true,
})
export class TestDuplicatedValue extends Node {
    @decorators.booleanProperty<TestDuplicatedValue, 'booleanVal'>({
        isStored: true,
        isPublished: true,
        // No duplicatedValue. The value of the source record will be used.
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.booleanProperty<TestDuplicatedValue, 'booleanDuplicateDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        duplicatedValue: useDefaultValue,
        // The value will be `true`, the default value.
    })
    readonly booleanDuplicateDefault: Promise<boolean>;

    @decorators.booleanProperty<TestDuplicatedValue, 'booleanDuplicateTrue'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: true,
        // The value will be `true`, but the default value is `false`
        // as there is no `defaultValue` rule.
    })
    readonly booleanDuplicateTrue: Promise<boolean>;

    @decorators.stringProperty<TestDuplicatedValue, 'stringPromptUser'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        isNotEmpty: true,
        // The user will be prompted for a non empty string value.
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly stringPromptUser: Promise<string>;

    @decorators.stringProperty<TestDuplicatedValue, 'stringDuplicateFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        // The duplicated value will depend on the value of the `booleanVal` property.
        await duplicatedValue() { return await this.booleanVal ? 'value 1' : 'value 2' },
    })
    readonly stringDuplicateFunction: Promise<string>;
}
```
