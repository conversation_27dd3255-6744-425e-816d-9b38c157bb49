PATH: XTREEM/2+Property+events/3+getValue+and+setValue

# getValue and computeValue

Computed properties are properties that are not stored in a column and are defined by a `getValue` or a `computeValue` rule.

The `getValue` rule is reserved for functions that can be translated into SQL.

The linter generates an error whenever it finds a formula that is too complex to be translated into SQL. Such errors can
be automatically fixed with the command `num run lint -- --fix` which replaces `getValue` with `computeValue` whenever necessary.

The `computeValue` rule is the fallback for functions that cannot be translated to SQL.

Properties defined by a `getValue` rule can be used to filter, sort (order by) or aggregate data, both in GraphQL queries
and in TypeScript calls to `context.query`, `context.select` and `context.queryAggregate`.

See https://confluence.sage.com/display/XTREM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions
for details on TypeScript to SQL conversions
