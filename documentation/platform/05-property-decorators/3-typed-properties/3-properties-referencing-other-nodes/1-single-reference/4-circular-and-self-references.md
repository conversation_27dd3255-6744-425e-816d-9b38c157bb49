PATH: XTREEM/1+Single+reference/4+circular+and+self+references

# Circular and self references

## Circular references

The nodes and their references form a graph which may contain cycles. For example:

> Employee -> department reference -> Department -> manager reference -> Employee

Cycles are allowed but there is an important restriction:

> There must be at least one nullable reference in any cycle.

In the example the `department` and `manager` references cannot be both non-nullable; one of them must be nullable.

So, although you may wish to enforce that every employee belongs to a department and also every department has a manager you cannot enforce this.
You have to relax the constraint on one side; for example by keeping the employee's departement non-nullable but allowing the departement's manager to be `null`.

If this restriction is violated an error will be thrown when the application is loaded.

## Self reference

There is one exception to the previous rule: a node may form a cycle with itself with a non-nullable reference. For example:

> Supplier -> payToSupplier reference -> Supplier

Every supplier has a `payToSupplier` reference which is also a supplier (the same supplier or a different one, but not `null`).

This kind of self referencing cycle is allowed, even with a non-nullable reference, but you have to provide a `defaultValue` rule which has to return `this`. For example:

```ts
    @decorators.referenceProperty<Supplier, 'payToSupplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        defaultValue() {
            return this;
        },
    })
    readonly payToSupplier: Reference<xtremMasterData.nodes.Supplier>;
```

Note: you only need this special `defaultValue` rule if the self-reference is non-nullable. If it is nullable you can omit this rule and the default value will be `null`.
