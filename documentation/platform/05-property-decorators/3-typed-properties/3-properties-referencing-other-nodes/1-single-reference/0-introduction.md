PATH: XTREEM/3+Properties+referencing+other+nodes/1+Single+reference

# Single References

In this section, we will focus on properties that reference a single other node instance.
This can be done several ways:

-   With a simple referenceProperty, when the main node needs to reference an instance of another basic node.

    For instance, a SalesOrder referencing its associated SalesQuote.

    In that case, the identifier of the referenced instance (the SalesQuote) is stored in a column of the referencing node's table (the SalesOrder).

-   With a **vital** referenceProperty, when the instance of the referenced node should only exist if the main node instance exists.

    For instance, consider a node Site having properties stored in another node called SiteCharacteristics. When a site is created, its siteCharacteristics instance must also be created. When the site is deleted, its siteCharacteristics has no meaning anymore and must also be deleted.

    In that case, the Site node has a vital referenceProperty to the SiteCharacteristics node.
    Storage is done the other way around than for simple referenceProperties: the id of the referencing node (the Site) is stored in the referenced node's table (the SiteCharacteristics).

-   With a referenceProperty to a Json node. In that case, the referenced node is actually stored in a Json column in the table of the referencing node. The referenced node has no table and is just used as a node in the code.
