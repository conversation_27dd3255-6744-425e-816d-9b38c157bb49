PATH: XTREEM/1+Single+reference/3+jsonReferenceProperty+and+Json+nodes

# jsonReferenceProperty and Json nodes

A jsonReferenceProperty is a special type of vital referenceProperty.
It is defined as a vital referenceProperty pointing to a json node, but is in fact persisted as a json column in the main node's table.

To define a json column in the database, one can :

1. Create a node with storage: `'json'`
2. In the main node, create a `referenceProperty` attribute referencing the newly created Json node.

The json node will not be stored in its own table in the database, but only as a json object in a column of the main node's table.

In that case, the `isVital` attribute has to be set to `true` in the reference property of the main node, but the `isVitalReferenceChild` and `isVitalParent` attributes can be omitted.

For instance, let's create the following json node:

```ts
@decorators.node<TestJsonAddress>({
    isPublished: true,
    storage: 'json',
    canCreate: true,
    canUpdate: true,
})
export class TestJsonAddress extends Node {
    @decorators.stringProperty<TestJsonAddress, 'streetAddress'>({
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly streetAddress: Promise<string>;

    @decorators.integerProperty<TestJsonAddress, 'postalCode'>({
        isPublished: true,
    })
    readonly postalCode: Promise<integer>;

    @decorators.stringProperty<TestJsonAddress, 'city'>({
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly city: Promise<string>;
}
```

The node `TestJsonAddress` can then be referenced from another node with a `referenceProperty` attribute having `isVital` set to true.

```ts
@decorators.node<TestPerson>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
})
export class TestPerson extends Node {
    @decorators.stringProperty<TestPerson, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestPerson, 'address'>({
        isVital: true,
        isPublished: true,
        isStored: true,
        node: () => TestJsonAddress,
        isNullable: true,
    })
    readonly address: Reference<TestJsonAddress | null>;
}
```
