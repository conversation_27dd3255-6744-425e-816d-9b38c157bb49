PATH: XTREEM/1+Single+reference/2+Vital+references

# Vital references

## What is a vital reference

A vital reference describes a reference to a sub-object that can't be standalone.
This sub-object is created when the main node is created and deleted when the main node is deleted.

Database storage is done the other way around than for simple referenceProperties: the id of the referencing node is stored in the referenced node's table.

## How to declare a reference as vital

To declare a reference as vital, both nodes must reference each other in the code.

We call the referencing node the _parent_ and the referenced node (the one that doesn't exist without its parent) the _child_.

-   The child node must be tagged as a vital child, meaning that it can't exist without its parent. It also needs to have a property referencing its vital parent.
-   The parent must have a referenceProperty pointing to the child. This referenceProperty must have two attributes: `isVital`, set to true, and `reverseReference`, set to the name of the child's property that references its vital parent.

For instance, consider a Site node referencing an instance of the SiteCharacteristics node. When the site is closed, its siteCharacteristics are meaningless and need to be deleted as well.

The parent node is thus the Site, and the child the SiteCharacteristics.

The SiteCharacteristics node needs to be tagged as a vital child, with the isVitalReferenceChild attribute set to true. It also needs to have a referenceProperty designated as the vital parent, referencing the Site node.

```ts
@decorators.node<SiteCharacteristics>({
    // ...
    isVitalReferenceChild: true,
})
export class SiteCharacteristics extends Node {
    @decorators.referenceProperty<SiteCharacteristics, 'site'>({
        // ...
        node: () => Site,
        isVitalParent: true,
    })
    readonly site: Reference<Site>;
}
```

The Site node needs to have a referenceProperty to the SiteCharacteristics node:

-   Tagged as vital,
-   And mentioning which referenceProperty of SiteCharacteristics has the isVitalParent attribute.

```ts
@decorators.node<Site>({
    // ...
})
export class Site extends Node {
    @decorators.referenceProperty<Site, 'siteCharacteristics'>({
        // ...
        node: () => SiteCharacteristics,
        isVital: true,
        reverseReference: 'site',
    })
    readonly siteCharacteristics: Reference<SiteCharacteristics>;
```

## Additional rules

The following additional rules will apply to the decorators:

-   a vital reference can be _nullable_, or not.
-   a node flagged with `isVitalReferenceChild: true` must have one and only one reference property flagged with `isVitalParent: true`. This parent reference must be non nullable.
    Similarly, in the other direction, the parent node will have one and only one vital property pointing to a given child node. But it may have several vital properties pointing to different child nodes.
-   non-vital reference properties do not have any `reverseReference` attribute.

If we take the previous definition strictly, vital child nodes should not be created/updated/deleted individually.
Instead, these operations should always be performed through the vital parent node.
Then, the `canCreate`/`canUpdate`/`canDelete`/`canDeleteMany` flags should not be set on vital child nodes.

The actual rule is a bit more lenient to accommodate vital child nodes that are loosely coupled with their parent node;
it only forbids these flags if the parent node does not have any property with a `dependsOn` link to the child node.

## Natural key

If the parent node has a natural key, then the vital child reference node will implicitly have the natural key of the `[parent reference property]`.

## Create a node instance with vital references

When you create a node instance, you must provide a data payload that contains data for all its _not nullable_ vital references.

```ts
await context.create(Site, {
    code: 'SITE1',
    siteCharacteristics: {
        areaInSquareMeters: '2000',
        numberOfCubicles: 600,
    },
}),
```

This will create not only a `Site` but also a `SiteCharacteristics`.

If you omit data for a not nullable vital reference, you will get an error _`missing data for vital reference`_.

## Updating a vital reference

You can update a vital reference by passing new data in a `$.set(data)` call.

```ts
await site.$.set({
    siteCharacteristics: {
        areaInSquareMeters: '2000',
        numberOfCubicles: 600,
    },
}),
```

This call will update the siteCharacteristics of the site with the provided data.

## Delete a node with vital references

Simply delete the node as usual

```ts
await node.$.delete();
```

All the vital references and collections held by the node will be deleted as well.

## Default value on vital references

You can provide a default value for the vital child on a vital reference:

```ts
    @decorators.referenceProperty<TestDocument, 'vitalReference'>({
        isPublished: true,
        node: () => VitalChild,
        isVital: true,
        reverseReference: 'parent',
        async defaultValue() {
            return {
                description: `child of ${await this.name}`,
            };
        }
    })
    readonly vitalReference: Reference<VitalChild>;
```

The default value is an object literal which provides the initial values for the properties of the child node. In this example, the `description` of the vital child will be a string which includes the parent's `name`.

If the vital reference is non nullable and `defaultValue` is omitted, the vital child will be created as if `defaultValue` returned an empty object `{}`.
If the vital reference is nullable and `defaultValue` is omitted, the reference will be initialized as `null`.

## Indexes and foreign keys

The framework automatically creates indexes and foreign keys to support vital relationships.

It creates a foreign key in cascade delete mode from the child node to its parent.
This ensures that vital children get automatically deleted when their parent is deleted.

It also creates a unique index on the child table, on [\_tenantId, parent reference].

## Case of node extension

In case of `isVitalReferenceChild: true` declared on an extension of a given node, this last one becomes a vital child reference for a parent node with the same rules/constraints applied in the previous case
