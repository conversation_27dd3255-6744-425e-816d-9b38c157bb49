PATH: XTREEM/1+Single+reference/1+Reference+property

# referenceProperty

## Basics

A referenceProperty is used when a node property references another node. For instance, a sales order references its associated sales quote.

```ts
@decorators.node<SalesOrder>({
    isPublished: true,
    storage: 'sql',
})
export class SalesOrder extends Node {
    @decorators.referenceProperty<SalesOrder, 'salesQuote'>({
        isPublished: true,
        isStored: true,
        node: () => SalesQuote,
        isNullable: true,
    })
    readonly reference: Reference<SalesQuote | null>;
}
```

> ⚠️ **[WARNING]**
> If you plan to use a nullable reference in a unique index, you need to add the `allowedInUniqueIndex` attribute to this property. See the [node indexes](https://confluence.sage.com/display/XTREEM/4+Indexes) page for more details.

## Behavior on referenced node's deletion

If the referenceProperty is non-vital, referenced nodes cannot be deleted unless all references to them have been reset.

For instance, in the example above, a SalesQuote cannot be deleted if referenced by at least one SalesOrder.

## Lookups and control filters

When defining a reference property in a given node, it is possible to define two types of filters: lookup and control.

Both lookup and control filters are applied when trying to get the list of eligible references for a given reference property, for instance when populating a selection dialog or combo box when the user selects a reference in a reference field.

Only control filters are applied when validating a node instance before creation and modification.

Both lookup and control filters are optional.

Both can be defined either with hardcoded values, or with functions that have access to the current node's data like in the following example.

**All reference properties are filtered by the isActive field of the target node by default, if the `provides: ['isActive']` attribute is set on the target node. To ignore this filter the ignoreIsActive attribute can be set to true on the reference property.**

### Example

When defining the reference property "customer" of the SalesOrder node, in the below example, eligible customers are those:

-   with customer.isActive flag set to true,
-   with customer.mainCompany set to the same company as the company of the sales order
-   with customer.site set to either the orderSite or the invoiceSite of the sales order

These constraints could be expressed like so:

```ts
@decorators.referenceProperty<SalesOrder, 'customer'>({
        ...
        // The customers that will pass the lookup filter are those with isActive set to true.
        filters: {
            control: {
                // The customers that will pass the control are those:
                // - Which company is the same as the salesOrder company.
                // - And which site is the current salesOrder's orderSite or invoiceSite.
                // - And those with the isActive set to true.
                mainCompany() {
                    return this.company;
                },
                async site() {
                    return !!await this.orderSite || this.invoiceSite;
                }
            },
        },
    })
    readonly reference: Reference<ReferencedDocument>;
```

And the customer (target) node:

```ts
@decorators.booleanProperty<Customer, 'isActive'>({
    ...
    provides: ['isActive']
})
isActive: boolean;
```

The two fields in the control filter above are not written with hardcoded values, but with functions. Inside those functions, the `this` keyword designates the current node, in our example an instance of SalesOrder.

The above example can be modified to include all customers, disregarding the isActive filter, by setting ignoreIsActive to true, or by removing the `provides: ['isActive']` attribute on the target node.

```ts
@decorators.referenceProperty<SalesOrder, 'customer'>({
        ...
        ignoreIsActive: true,

        // All customers will pass the lookup filter.
        filters: {
            control: {
                // The customers that will pass the control are those:
                // - Which company is the same as the salesOrder company
                // - And which site is the current salesOrder's orderSite or invoiceSite.
                mainCompany() {
                    return this.company;
                },
                async site() {
                    return !!await this.orderSite || this.invoiceSite;
                }
            },
        },
    })
    readonly reference: Reference<ReferencedDocument>;
```

**The control rule of the reference property filter will control as follow (given that the `provides: ['isActive']` attribute is set on the target node, and the filter is not ignored):**

-   When creating a node we reject a reference that is inactive

    -   Creating node A (active) - that references B (inactive)

-   When updating a node we reject a change to a reference that is inactive
    -   Update node A (active) - to reference B (inactive)

### Getting the list of all possible candidates for a reference property

Using the lookup method of the Context class, one can retrieve all possible candidates for a given node reference property. The possible candidates are those that satisfy both the lookup and control filters.

The context.lookup method queries the database and retrieves all the instances of the reference node that comply with **both** filters.

Here are the parameters of the context.lookup method:

| Name            | Description                                                     |
| --------------- | --------------------------------------------------------------- |
| nodeConstructor | Node constructor of the node the reference property is found in |
| propertyName    | The name of the reference property                              |
| args            | Arguments required to build the build the lookup filter         |
| queryOptions    | Other query options, additional filters and paging options      |

For the args parameter at least one of the following fields must be populated:

-   **\_id**: the \_id of the node if it already exists. This will be used to read the record and should not be passed if the record does not exist yet.
-   **data**: an object containing a subset of the node properties. This field is used to pass the current values of the node's properties. Where \_id and data are passed together, the current record is read and the data values that are passed are set on the read node.

The resulting node will be passed in as `this` into the `lookup` and `control` fields of the reference property filter field, if they are functions.

```ts
const lookupArgs = {_id: '1';}
context.lookup(nodeConstructor,  propertyName, lookupArgs, queryOptions)
```

For instance, imagine the two following scenarios:

-   The user has created a new SalesOrder, with id 11123, but later realizes that the customer field is incorrect. The user returns to the SalesOrder and wants to choose from a list of possible customers. To this end, the context.lookup method can be called this way:

    ```ts
    context.lookup(SalesOrderConstructor, 'customer', { _id: '11123' });
    ```

    Internally, the framework will apply the filters using the properties of the existing sales order 11123, company = _Sage_, and site = _Site1_, and a list of eligible Customer instances will be returned.

-   The user is currently creating an instance of the SalesOrder node, and wants to choose a customer.
    Let's say the user has already filled only the SalesOrder.company (_Sage_) and SalesOrder.site (_Site1_) fields. To get the list of the 10 first possible customers at that stage, the context.lookup method can be called this way:

    ```ts
    context.lookup(SalesOrderConstructor, 'customer', { data: { company: 'Sage', site: 'Site1' } }, { first: 10 });
    ```

    No SalesOrder id exists yet but the list of eligible customers at that stage will still be retrieved by passing a data object to the method.

    If **\_id** is not passed and the **data** field is passed as empty an empty object `{}`, the `this` passed to the lookup or control function will be an empty node, static filters that don't use `this` will work well but we need to be aware that some filters using `this` may not return results.

### Validating a node instance

When a node is being validated, part of the validation is to check that each of the node's non-empty reference properties match their control filter if one is defined.
If the reference doesn't comply with the filter, a new error diagnose is added to the context.
The lookup filter is not applied during this node validation step.
