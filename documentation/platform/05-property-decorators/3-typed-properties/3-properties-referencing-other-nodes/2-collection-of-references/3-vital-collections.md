PATH: XTREEM/2+Collection+of+references/3+Vital+collections+and+references

# Vital collections

Prerequisite: documentation on vital references.

## What is a vital collection

A vital collection is a collection of sub-objects that can't be standalone.
The sub-objects are created together with the main node and deleted when the main node is deleted.
A typical example is a collection of document lines. They don't exist without their parent document.

## How to declare a collection as vital

Same as for vital referenceProperties.

Consider a SalesOrder node keeping a vital collection of SalesOrderLine instances.

The parent node is the SalesOrder, and the child the SalesOrderLine.

The SalesOrderLine node needs to be tagged as a vital child, with the isVitalCollectionChild attribute set to true.
It also needs to have a referenceProperty designated as the vital parent, referencing the SalesOrder node.

```ts
@decorators.node<SalesOrderLine>({
    // ...
    isVitalCollectionChild: true,
})
export class SalesOrderLine extends Node {
    @decorators.referenceProperty<SalesOrderLine, 'order'>({
        // ...
        node: () => SalesOrder,
        isVitalParent: true,
    })
    readonly order: Reference<SalesOrder>;
}
```

The SalesOrder node needs to have a collectionProperty to the SalesOrderLine node:

-   Tagged as vital,
-   And mentioning which referenceProperty of SalesOrderLine has the isVitalParent attribute.

```ts
@decorators.node<SalesOrder>({
    // ...
})
export class SalesOrder extends Node {
    @decorators.collectionProperty<SalesOrder, 'lines'>({
        // ...
        node: () => SalesOrderLine,
        isVital: true,
        reverseReference: 'order',
    })
    readonly lines: Collection<SalesOrderLine>;
```

## Additional rules

Same as for vital referenceProperties.

## Create a node with vital collections

When you create a node, you _may_ provide data for its vital collections.

```ts
const salesOrder = await context.create(SalesOrder, {
    id: 'ORDER1',
    lines: [
        {
            product: 'P-1',
            quantity: 4,
        },
        {
            product: 'P-2',
            quantity: 1,
        },
    ],
});
```

This will create not only a `SalesOrder` but also its lines (`SalesOrderLine`).

## The \_sortValue property

The vital child nodes of a collection have an additional system property called `_sortValue`.

This property tracks the order of the children in their collection.
The `_sortValue` values are automatically set when a parent node is created with an array of children, and automatically updated when a parent is updated with a different array of children.

By default, unless you specify a sortOrder parameter, the collection of vital children will be sorted in increasing order of `_sortValue` when you read it. So you are guaranteed to read the children in the order in which its was last updated.

The `_sortValue` property holds an integer. It can be accessed as `parent.$.sortValue` in TypeScript. It is also exposed as a `_sortValue` property in the GraphQL API.

This property only exists on vital child nodes. An exception will be thrown if you try to access it from a non vital child node.

## Natural key

If the parent node has a natural key, then the vital child collection node will implicitly have the natural key of the `[parent reference property]`.

## Updating a vital collection

You can update a vital collection by passing a new array of children in a `$.set(data)` call.

```ts
await salesOrder.$.set({
    lines: [{
        product: 'P-1',
        quantity: 5,
    }, {
        product: 'P-2',
        quantity: 1,
    }, {
        product: 'P-3',
        quantity: 6,
    }],
}),
```

This call will update the salesOrder with the 3 lines that you passed.

With this call the previous lines will be deleted and 3 new lines will be created, which is probably not what you want.
You probably want to keep the old lines and update them instead.
To achieve this you need to pass the \_id to the lines that you want to update. For example:

```ts
await salesOrder.$.set({
    lines: [{
        _id: salesOrder.lines.node(0)._id,
        quantity: 5,
    }, {
        product: 'P-3',
        quantity: 6,
    }, {
        _id: salesOrder.lines.node(1)._id,
    }, {
        product: 'P-4',
        quantity: 6,
    }],
}),
```

This call will update the `quantity` of the first line, insert a new `P-3` line before the old second line, and append a `P-4` line at the end.
The second line will not be modified, it will just be moved into third position.

The `$.set` call deletes lines which don't have a matching `_id` in the array.
So the following call will delete all the lines of the document.

```ts
await document.$.set({
    lines: [],
}),
```

You can also reorder the lines with this call, by passing the `_ids` of the lines in a different order.
For example, you can reverse all the lines of the document with:

```ts
await document.$.set({
    lines: (await document.lines.map(line => ({ _id: line._id })).toArray()).reverse(),
}),
```

Note: this API imposes that you pass the complete array of lines in every `$.set` call, as omitted lines will be deleted.
The following section describes a variant of this API to only pass the modified lines.

## Partial update of a vital collection

A collection can also be updated by passing only the modified lines.
In this mode, all the lines must carry an \_action value to indicate if the line must be created, updated or deleted.

Lines may also carry a \_sortValue to indicate where they will be placed in the updated collection.

The \_sortValue property allows you to control where created lines will be inserted, or to reorder existing lines by changing their \_sortValue. If you set \_sortValue on modified lines you must guarantee that all lines have a different \_sortValue after the update actions have been performed. For example, if the collection contained a line with \_sortValue 20 before the update you can only create a line with \_sortValue 20 if you are also deleting the previous line with \_sortValue 20 or if you are moving it to a different place with a different \_sortValue (which does not conflict with another line).

In the absence of sort values, created lines will be appended to the collection and updated lines will remain at their current position.

For example, let us assume we start with a collection containing the following lines:

```ts
[
    { _id: 10, _sortValue: 10, product: 'P-1', quantity: 4 },
    { _id: 11, _sortValue: 20, product: 'P-2', quantity: 1 },
    { _id: 12, _sortValue: 30, product: 'P-3', quantity: 2 },
];
```

and that we execute the following call:

```ts
await document.$.set({
    lines: [{
        // delete first line
        _action: UpdateAction.delete,
        _id: 10,
    }, {
        // insert a new line between sort values 20 and 30
        _action: UpdateAction.create,
        _sortValue: 25,
        product: 'P-A',
        quantity: 8,
    }, {
        // update the quantity on the last line
        _action: UpdateAction.update,
        _id: 12,
        quantity: 3,
    }],
}),
```

We obtain the following collection:

```ts
[
    { _id: 11, _sortValue: 20, product: 'P-2', quantity: 1 },
    { _id: 25, _sortValue: 25, product: 'P-A', quantity: 8 },
    { _id: 12, _sortValue: 30, product: 'P-3', quantity: 3 },
];
```

Note: The \_id of the second line (25) will be allocated so its value may vary.

You must pass an \_id on all lines that you are updating or deleting, but not on created lines.

You could then reverse the last two lines by swapping their \_sortValue with:

```ts
await document.$.set({
    lines: [
        { _action: UpdateAction.update, _id: 25, _sortValue: 30 }
        { _action: UpdateAction.update, _id: 12, _sortValue: 25 }
    ],
}),
```

Note: you can use the `setSortValues` utility function from `@sage/xtrem-shared` to allocate sort values on a range of modified lines.

## Delete a node with vital collections

Same as for vital referenceProperties.

## Default value on vital collections

Default value rules may be specified on collections. For example:

```ts
    @decorators.collectionProperty<TestDocument, 'addresses'>({
        isPublished: true,
        node: () => DocumentAddress,
        isVital: true,
        reverseReference: 'parent',
        async defaultValue() {
            return [
                await this.customer.billingAddress.$.payload(),
                await this.customer.deliveryAddress.$.payload(),
            ];
        },
    })
    readonly addresses: Collection<DocumentAddress>;
```

The `defaultValue` method returns an array of payloads which will be used to initialize the child nodes of the collection.

Collections are empty by default. If this is what you need, just omit the `defaultValue` rule.

## Indexes and foreign keys

Same as for vital referenceProperties.
For collections, the index created on the child table is [\_tenantId, parent reference, \_sortValue].

## How to enforce a non-empty vital collection

To enforce a non-empty vital collection, add the `isRequired` to the collection property and set it to true.

```ts
@decorators.node<SalesOrder>({
    // ...
})
export class SalesOrder extends Node {
    @decorators.collectionProperty<SalesOrder, 'lines'>({
        // ...
        node: () => SalesOrderLine,
        isVital: true,
        reverseReference: 'order',
        isRequired: true,
    })
    readonly lines: Collection<SalesOrderLine>;
```
