PATH: XTREEM/2+Collection+of+references/4+Reference+array+properties

# Reference array properties

A referenceArray property :

- Is used to store an array of references.
- Differs from a collection property in that no foreign keys are created at the database level. A referenceArray property thus cannot be subjected to database level validations when the referenced records are altered. In particular, the referenceArray property doesn't get considered when one of the referenced records it includes is deleted.
- Has the same attributes as a referenceProperty.
- Queries can be filtered using
    - \_eq and \_ne, passing in an array of \_id values
    - the array filter \_contains, passing in an \_id value
    - nested collection filters \_atLeast, \_atMost, \_none, and \_every. See https://confluence.sage.com/display/XTREEM/6+Nested+Collections+and+GraphQL+queries
- Lookup and control filters on reference array will work the same as reference properties. See https://confluence.sage.com/display/XTREEM/1+Reference+property. If a control filter is provided, this will be tested on every value in the reference array.

# Example

Let's consider a Site node storing a list of addresses, past and present, for this site.
When an address is deleted from the _Address_ node, sites that reference it in their _addresses_ property are not updated.
They still reference the deleted address.

If this is not the expected behavior, meaning a validation step is needed, it is recommended to use a collectionProperty instead of a referenceArrayProperty.

```ts
// Let's first define the Address Node.
@decorators.node<Address>({
    isPublished: true,
    storage: 'sql',
})
export class Address extends Node {
    // ...
}

@decorators.node<Site>({
    isPublished: true,
    storage: 'sql',
})
export class Site extends Node {
    // Array referencing all past and present addresses for the site.
    // Addresses are stored as references to the Address node defined above.
    @decorators.referenceArrayProperty<Site, 'addresses'>({
        isPublished: true,
        isStored: true,
        node: () => Address,
        onDelete: 'restrict',
        isNullable: true,
    })
    readonly addresses: Promise<Address[] | null>;
}
```

# Database referencial integrity

Postgres does not support foreign keys on int[] columns (the type of columns used to store reference arrays).
The platform will handle the referencial integrity depending on the value of the **onDelete** attribute of the decorator.

- '_restrict_': a referenced record cannot be deleted. In the sample above, an address can't be deleted if at least on _Site_ references it (through the _addresses_ column).
- '_remove_': if a referenced record is deleted, then it's \_id will be removed from the array stored in the column.
  In the example above (assuming that onDelete would be '_remove_'), if an Address is deleted, then records in the _site_ table will updated (the \_id of the address will be removed from the _addresses_ column).

# Immutability

Reference arrays are immutable, meaning that the property value can be assigned but not changed. For instance, you can set it with `this.addresses = [new Address()]`, but cannot change it using `this.addresses.push(new Address())` or `this.addreses.pop()` functions. Those two calls will result in
respective errors `Cannot add property 2, object is not extensible` and `Cannot delete property '1' of [object Array]`.
