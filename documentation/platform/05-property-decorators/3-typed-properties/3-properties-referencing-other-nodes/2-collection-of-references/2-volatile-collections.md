PATH: XTREEM/2+Collection+of+references/2+Volatile+collections

# Volatile collections

## What is a volatile collection property

A _volatile_ collection property is a property which holds a _volatile_ collection.
A _volatile_ collection differs from a _persistent_ collection in that the framework does not persist its data directly to the database.
Instead, the developer must provide the logic to read and save the collection data.

Volatile collections are typically used to create a facade for a collection which comes from another source.
One example is data which is stored in another, persistent, collection property but which may be transformed or filtered before being exposed to the outside.
Another example is data which is fetched with a Web API call from another service, and may optionally be saved via another Web API call.

## How to declare a volatile collection property?

Simply add a `getValue` event to the _collectionProperty_ decorator, and omit the `join` attribute.
You may also provide a `setValue` event if the collection may be written to.

```ts
    @decorators.collectionProperty<Parent, 'volatileCollection'>({
        ...
        node: () => VolatileChild,
        // no `join` attribute
        getValue() {
            const childData = ...; // array of child data
            return childData;
        },
        // optional setValue event if property is writable.
        setValue(data) {
            // data is an array of data that may be used create children nodes
            // Store the data somewhere so that it can be fetched by `getValue` later.
        }
    })
    readonly volatileCollection: Collection<VolatileChild>;
```

The `VolatileChild` class must be a `basic` node, not a persistent one.
`VolatileChild` instances will be allocated on demand when the volatile collection is queried, and will be automatically garbage collected once the query result has been processed.

The `getValue` event returns an array of `NodeCreateData<VolatileChild>` rather than an array of `VolatileChild`.
So you should not create the `VolatileChild` instances; you just need to provide their initial data.
The framework will create the nodes and the collections.

Similarly, the `data` parameter of the `setValue` event is an array of `NodeCreateData<VolatileChild>`.
If you provide this event, you are responsible for storing this data so that your `getValue` event can retrieve it later.

## Gotchas

The current API assumes that the entire collection data is sufficiently small to fit in memory, as `getValue` must return the entire collection as a single array.
You can use it for collections of a few 1000s items, not more. A more powerful API may be provided later.
