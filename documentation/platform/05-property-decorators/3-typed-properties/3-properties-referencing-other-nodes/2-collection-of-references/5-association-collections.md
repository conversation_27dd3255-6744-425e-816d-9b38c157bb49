PATH: XTREEM/2+Collection+of+references/5+Association+collections

# Association collections

## What is an association collection

An association collection is a collection that associates 2 or more nodes with a many to many relationship. An example
could be how students relate to courses. Each student can have many courses and each course will have many students.

This is similar to how vital relationships work, but in this case with more than one `vital` parent. The association can
consist of a vital parent and association parent, or just association parents.

An association record cannot exist without all of the the referenced records, so when either of the referenced records
are deleted the associated records must be deleted via a cascaded delete defined on the foreign key for the association
parent references.

For the association node the following indexes will be automatically generated:
If no explicit natural key is defined on the node, the natural key will be a unique index consisting of the association
parent properties. Additionally, a non-unique index for each of the association parent properties will be generated.

## How to declare an association collection

Taking the Student / Course example, we will need an association node for the many to many relationship between
Student and Course. This association node will have be flagged with `ts isAssociationCollectionChild: true ` and have reference
properties for Student and Course, each flagged with `isAssociationParent: true`.

Student and Course nodes will each have a collection of the association node, flagged with `isAssociation: true` and
named accordingly (courses on Student and students on Course). The collection also needs a reverse reference from the
association node to the parent node, `student` on Student and `course` on Course.

The following is a simplified example of how that would be implemented:

```ts
@decorators.node<Student>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Student extends Node {
    @decorators.stringProperty<Student, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<Student, 'courses'>({
        isPublished: true,
        isAssociation: true,
        node: () => StudentCourse,
        reverseReference: 'student',
    })
    readonly courses: Collection<StudentCourse>;
}

@decorators.node<Course>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Course extends Node {
    @decorators.stringProperty<Course, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<Course, 'students'>({
        isPublished: true,
        isAssociation: true,
        node: () => StudentCourse,
        reverseReference: 'course',
    })
    readonly students: Collection<StudentCourse>;
}

@decorators.node<StudentCourse>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class StudentCourse extends Node {
    @decorators.referenceProperty<StudentCourse, 'student'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => Student,
    })
    readonly student: Reference<Student>;

    @decorators.referenceProperty<StudentCourse, 'course'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => Course,
    })
    readonly course: Reference<Course>;
}
```

Building on the Student Course example, if we include Campus, a student can have a course at a specific campus, so there
is a 3 way association.

Here is an example of adding this type of association collection on Campus:

```ts
@decorators.node<Campus>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Campus extends Node {
    @decorators.stringProperty<Campus, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<Campus, 'studentCourses'>({
        isPublished: true,
        isAssociation: true,
        node: () => StudentCourseCampus,
        reverseReference: 'campus',
    })
    readonly studentCourses: Collection<StudentCourseCampus>;
}

@decorators.node<StudentCourseCampus>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class StudentCourseCampus extends Node {
    @decorators.referenceProperty<StudentCourseCampus, 'student'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => Student,
    })
    readonly student: Reference<Student>;

    @decorators.referenceProperty<StudentCourseCampus, 'course'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => Course,
    })
    readonly course: Reference<Course>;

    @decorators.referenceProperty<StudentCourseCampus, 'campus'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => Campus,
    })
    readonly campus: Reference<Campus>;
}
```
