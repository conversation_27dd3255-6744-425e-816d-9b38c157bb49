PATH: XTREEM/3+Properties+referencing+other+nodes/2+Collection+of+references

# Collection of references

In this section, we will focus on properties containing a collection of references.

This can be done several ways:

- With a simple collectionProperty, when the main node needs to keep an array of references to instances of another basic node.

- With a **vital** collectionProperty, when the instance of the referenced node should only exist if the main node instance exists.

    For instance, a SalesOrder keeping a collection of references to SalesOrderLine. When a SalesOrder is created, its SalesOrderLines must also be created. When the SalesOrder is deleted, its lines have no meaning anymore and must also be deleted.

    In that case, the SalesOrder node has a vital collectionProperty of SalesOrderLine instances.
    Storage is done the other way around than for a non-vital collectionProperty: the id of the referencing node (the SalesOrder) is stored in the referenced node's table (the SalesOrderLine node).

- With a **volatile** collectionProperty. In that case, the collection is not stored in the database but computed on-the-fly. The developer must provide the logic to read and save the collection data.
