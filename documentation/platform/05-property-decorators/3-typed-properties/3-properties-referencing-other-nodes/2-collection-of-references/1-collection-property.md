PATH: XTREEM/2+Collection+of+references/1+collectionProperty

# collectionProperty

A collectionProperty is used when a node property references a collection of another node's instances.
For instance, a sales order having a collection of lines.

```ts
@decorators.node<SalesOrder>({
    // ...
})
export class SalesOrder extends Node {
    @decorators.collectionProperty<SalesOrder, 'lines'>({
        // ...
        node: () => SalesOrderLine,
    })
    readonly lines: Collection<SalesOrderLine>;
```

If the collectionProperty is not marked as vital, the deletion of the main node (SalesOrder) does not lead to the deletion of the referenced instances (SalesOrderLines).

## Collection property events

### prepareBegin / prepareEnd

Ordinary properties have a single `prepare` rules but collection properties differ. They have a pair of `prepareBegin` / `prepareEnd` rules.

The `prepareBegin` rule is executed before the `prepareBegin` of the first node in the collection.
The `prepareEnd` rule is executed after the `prepareEnd` of the last node in the collection.

These rules are executed even if the collection is empty.
