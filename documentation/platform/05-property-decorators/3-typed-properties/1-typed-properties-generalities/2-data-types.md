PATH: XTREEM/1+Typed+properties+generalities/2+Data+Types

# Data types

Data types allow us to factor out attributes that are common to several properties sharing the same classic type (string, boolean...) in the data model.

For instance, if several string properties have to have the same maximum length control rule, a StringDataType instance should be defined and shared by all of them.

DataType instances are specific to given property types:

-   A DataType instance inheriting from StringDataType can be affected to a stringProperty's dataType method, but not to a booleanProperty's.

-   A DataType instance inheriting from DecimalDataType can be affected to a decimalProperty dataType method, but not to a booleanProperty's nor to a stringProperty's.

## Main principle

When defining node properties, one could wish to specify data type specificities relative to those properties, depending on the general data type of the property.
Those specificities won't be the same for a string property or for a decimal property, for instance:

-   When defining a string property, one could wish to specify the maximum authorized length.
-   When defining a decimal property, one could wish to specify the precision and scale.

To this end, the following DataType objects have been created:

-   A `DataType` abstract class, containing attributes common to all types, such as `defaultValue`.
-   A `StringDataType` inheriting from `DataType` containing the `maxLength` attribute.
-   A `DecimalDataType` inheriting from `DataType` containing `precision`, `scale` and `roundingMode` attributes.
-   An `EnumDataType` inheriting from `DataType` containing `enum` and `filename` attributes.
-   A `TextStreamDataType` inheriting from `DataType` containing `maxLength` and `allowedContentTypes` attributes.
-   A `BinaryStreamDataType` inheriting from `DataType` containing `maxLength` and `allowedContentTypes` attributes.
-   A `JsonDataType` inheriting from `DataType` containing no additional attributes.
-   A `ReferenceDataType` inheriting from `DataType` containing additional attributes to define lookup behavior.

These data types can be inherited to create more specific behaviors.
See further down to get more detailed help on each of those DataTypes.

## Genericity

DataTypes can define three methods:

-   controlValue, to trigger an additional control on the value fed to a property.
-   defaultValue, to provide the property with a default value if one is not already defined in the property itself.
-   adaptValue, to transform the value when it is set to a property.

Those methods may need to have access to some properties of the node itself.
For instance, a CurrencyDataType could rely on the fact that there is a Legislation column in the same node, to be able to control that the provided currency is compatible with the legislation.
To be able to do that, we introduced genericity in the DataType's definition.

DataType takes two generic parameters: `DataType<TVal, T>`.

-   TVal is the primitive type covered by the DataType.
    For StringDataType, it is `string`. For DecimalDataType, `decimal`.
-   T is an interface containing the properties required for a node to have in order to put this DataType on one of its properties.
    For instance, in our CurrencyDataType, T will be an interface containing an only property `legislation`.
    We will then be sure that the controlValue will be able to use `legislation` since it is in the node.

## DataType methods

### controlValue

Signature: `public controlValue(node: T, cx: ValidationContext, val: any): void`.

-   The controlValue method defines an additional control on the property value.
-   This method will be called before the controlValue of the property if any.
-   It is compulsory to call the super() method from within this method to make sure the controlValue of the superclass is run.
-   Errors should be logged in the `ValidationContext` provided with the `addDiagnose` method.

### defaultValue

Signature: `public defaultValue(node: T): any`.

-   This method will only be called if the property does not have its own defaultValue() rule.

### adaptValue

Signature: `public adaptValue(node: T, val: TVal): any`.

-   The adaptValue method transforms the value when it is set to the property.
    For instance, the adaptValue of DecimalDataType applies its internal rounding rule on the value provided.
    If the data type has a scale of 2 and a roundingMode equal to `roundUp`, `1.123` will be transformed into `1.13` by adaptValue.

## Standard data types

In the following example:

-   The `name` string property will be limited to 50 characters and its value will be trimmed (default adaptValue logic from StringDataType).
-   The `text` string property will not be trimmed because it has the `doNotTrim` option set as true.
-   The `price` decimal property will have a 10 digits precision and a 3 digits scale.
    Its rounding mode will be roundUp, meaning that if 1.1234 is provided, the adaptValue method will round it to 1.124.
-   The `category` enum property will have values from the CategoryEnum.

```ts

export enum CategoryEnum {
    cheap,
    normal,
    expensive,
}

export type Category = keyof typeof CategoryEnum;

@decorators.node<TestDependencyDocument>({
    isPublished: true,
    ...
})
class MyNode extends Node {
    @decorators.stringProperty<MyNode, 'name'>({
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MyNode, 'text'>({
        isStored: true,
        dataType: () => new StringDataType({ doNotTrim: true }),
    })
    readonly text: Promise<string>;

    @decorators.decimalProperty<MyNode, 'price'>({
        isStored: true,
        dataType: () => new DecimalDataType(10, 3, RoundingMode.roundUp),
    })
    readonly price: Promise<decimal>;

    @decorators.enumProperty<MyNode, 'category'>({
        isStored: true,
        dataType: () => new EnumDataType<Category>(CategoryEnum, __filename);
    })
    readonly category: Reference<Category>;

    @decorators.binaryStreamProperty<MyNode, 'image'>({
        isPublished: true,
        isStored: true,
        dataType: () =>
            new BinaryStreamDataType({ maxLength: 1234, allowedContentTypes: ['image/*'] }),
    })
    readonly image: Promise<BinaryStream>;

    @decorators.textStreamProperty<MyNode, 'manual'>({
        isPublished: true,
        isStored: true,
        dataType: () =>
            new TextStreamDataType({ maxLength: 1234, allowedContentTypes: ['text/html'] }),
    })
    readonly manual: Promise<TextStream>;

    ...
}
```

### StringDataTpe, DecimalDataType and JsonDataType

The StringDataType, DecimalDataType and JsonDataType don't need either the primitive value (generic parameter `TVal`) or the node generic type (generic parameter `T`).
The reason is that:

-   They don't rely on the node to contain any given properties in their controls, so `T` is not needed.
-   `TVal` will be automatically set to `string` for StringDataType, `decimal` for DecimalDataType and 'object' for JsonDataType.
-   StringDataType contains by default specific adaptValue logic that trims the value when it is set to the property. This effect can be prevented by setting the `doNotTrim` property to false within the StringDataTypeOptions of the property.

```ts
@decorators.stringProperty<TestAdaptValues, 'doNotTrimStringProperty'>({
    isPublished: true,
    isStored: true,
    dataType: () => new StringDataType({ doNotTrim: true })
})
readonly doNotTrimStringProperty: Promise<string>;
```

### EnumDataType

The EnumDataType needs to be passed the actual enum used as a parameter to the constructor. The generic type parameter to the class is a type, which is the `keyof typeof` the enum. So the declaration of the EnumDataType will be:

```ts
import { EnumDataType } from '@sage/xtrem-core';

export enum MyEnum {
    value1 = 1,
    value2 = 2,
    value3 = 3,
    value0 = 4,
}

export type MyEnumType = keyof typeof MyEnum;

export const myEnumDataType = new EnumDataType<MyEnumType>({
    enum: MyEnum,
    filename: __filename,
});
```

Just like for StringDataType and DecimalDataType, the second type T need not be specified, because the EnumDataType does not need the node to contain any specific attributes.

The EnumDataType provides the following utility methods/attributes:

-   `values` - values is an attribute of the EnumDataType that returns a string array of the memberValues of the enum. For myEnumDataType above, this attribute will be ('value1', 'value2', 'value3', 'value0'). Note the list is in the order of the enum declaration.
-   `numberValue(memberValue: string)` - returns the numeric key value for the string `memberValue`. From the example, `myEnumDataType.numberValue('value2')` returns `2`.
-   `stringValue(keyValue: number)` - returns the string member value for the numeric `keyValue`. From the example above, `myEnumDataType.stringValue(2)` returns `value2`.
-   `getLocalizedValue(context: Context, memberValue: string)` - returns the localized value for the memberValue based on the locale in the context.
-   `getLocalizedValues(context: Context)` - returns an array of localized member values of the enum based on the locale of the context.
-   `compareValues(val1: string | number | null, val2: string | number | null)` - compares 2 enum values and returns 1 (val1 is greater than val2), -1 (val1 is less than val2), or 0 (val1 is equal to val2). These values can be either a string or number or null. The comparison is done with the numeric `keyValues` and not the string `memberValues`. So, from the example above `myEnumDataType.compareValues('value0', 'value3')` will return 1 as the key value for value0 (4) is greater than the keyValue for value3(3).

### ReferenceDataTpe

```ts
class ReferenceDataType<ValT extends Node | null, NodeT extends Node = Node> extends DataType<
    ValT,
    NodeT,
    ReferenceDataTypeOptions<ValT, NodeT>
>
```

ReferenceDataType requires the generic type for the **referenced** node, `ValT`. The second generic, `NodeT`, is necessary only when access to the **containing** node's properties is required. However, this binds the declared type to a specific node, limiting its generality. To overcome this limitation, an interface containing only the necessary properties common to similar nodes can be defined. This interface can then be reused as the datatype for `NodeT`, resulting in a more versatile solution. An example of this is demonstrated below with the interface `LikeTestDataTypes`, allowing any node with a `stringVal` property to reuse the `testReferenceDataType` datatype.

The constructor takes `ReferenceDataTypeOptions` as parameter, which includes the following attributes:

-   `reference`: The referenced Node
-   `filters`: Definition of filters for lookup (Refer to [reference property](https://confluence.sage.com/display/XTREEM/1+Reference+property) for more details.)
-   `lookup`: Lookup definition comprising 'valuePath', 'helperTextPath', and an array of 'columnPaths'. All must be strings containing the path to a leaf property. If the leaf property is a text or binary stream, then the path must end with the attribute value, e.g. we have a binary stream property called image, it's path will be `image.value`. A leaf property cannot be a reference or collection property.
-   `isNullable`, `ignoreIsActive`, and `isDefault`: Optional booleans defaulting to false, which can be set to true for the desired behavior.

#### Example of a datatype declaration

```ts
export interface LikeTestDataTypes extends Node {
    readonly stringVal: Promise<string>;
}

export const testReferenceDataType = new ReferenceDataType<TestReferenceDataType, LikeTestDataTypes>({
    reference: () => TestReferenceDataType,
    filters: {
        lookup: {
            code() {
                return this.stringVal;
            },
        },
    },
    lookup: {
        valuePath: 'code',
        helperTextPath: 'name',
        columnPaths: ['code', 'name', 'child.childCode', 'child.grandChild.grandChildCode'],
    },
});
```

#### Example of datatype usage in node definition

```ts
    @decorators.referenceProperty<TestDatatypes, 'referenceDataType'>({
        isPublished: true,
        isStored: true,
        dataType: () => testReferenceDataType,
        node: () => TestReferenceDataType,
    })
    readonly referenceDataType: Reference<TestReferenceDataType>;

    @decorators.referenceProperty<TestDatatypes, 'nullableReferenceDataType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testReferenceDataType,
        node: () => TestReferenceDataType,
    })
    readonly nullableReferenceDataType: Reference<TestReferenceDataType | null>;
```

## Implement a custom data type

Let's define a custom CurrencyDataType extending the StringDataType.
CurrencyDataType will require the node to contain a property called `legislation`, and it will use it in its rules.

```ts
interface HasLegislationColumn {
    legislation: Legislation;
}

// The node has to have a legislation column
export class CurrencyDataType<T extends HasLegislationColumn> extends StringDataType<string, T> {
    constructor(public maxLength?: number) {
        super(maxLength);
    }

    public async defaultValue(node: T): Promise<string | undefined> {
        // Returns the defaultCurrency defined in the node's legislation column.
        return (await node.legislation).defaultCurrency || super.defaultValue(node);
    }

    public async controlValue(node: T, cx: ValidationContext, val?: string): void {
        // Mandatory call to super.controlValue.
        // As super is StringDataType, this will check that val.length <= this.maxLength
        await super.controlValue(node, cx, val);

        // Apply legislation's control.
        if (val && !(await (await node.legislation).isValidCurrency(val))) {
            cx.addDiagnoseAtPath(ValidationSeverity.error, `${val} is not a valid currency for ${node.legislation}.`);
        }
    }
}
```

Here is the code to add the dataType to a currency property:

```ts
@decorators.node<TestDependencyDocument>({
    isPublished: true,
    ...
})
class MyNode extends Node {
    @decorators.referenceProperty<MyNode, 'legislation'>({
        isStored: true,
        node: () => Legislation,
    })
    readonly name: Reference<Legislation>;

    @decorators.decimalProperty<MyNode, 'currency'>({
        isStored: true,
        dataType: () => new CurrencyDataType(10);
    })
    readonly currency: Promise<decimal>;
```

## Utilities

### Find all nodes that use a specific dataType

In the `Application` instance, we have provided a utility function that returns an array of `NodeFactory` class instances. The `NodeFactory` class provides information about a `Node`.

```ts
await asyncArray(node.$.context.application.findFactoriesUsingDatatype(myDataType)).forEach(factory => {
    const properties = factory.properties.filter(property => property.dataType === myDataType && property.isStored);
    if (node instanceof MyNode1 || node instanceof MyNode2)
        filter = {
            _or: properties.map(property => {
                return { [property.name]: { _ne: null } };
            }),
        };

    const result = await node.$.context.query(factory.nodeConstructor, { filter, first: 1 } as any);
    return result.length > 0;
});
```

The example above, gets all `NodeFactory` instances that have properties that use `myDataType` and the property value is not null.

## Good practice: harmonize frequent types

> ⚠️ **[IMPORTANT]**
> DataType classes allow harmonization of very frequent types (`code`, `description`, `shortDescription`, `email`) across the product.
> For instance, a single instance of `StringDataType` should be used for all `code` properties declared in all modules.
