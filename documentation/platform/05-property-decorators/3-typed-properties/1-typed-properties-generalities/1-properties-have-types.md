PATH: XTREEM/1+Typed+properties+generalities/1+Properties+have+types

# Properties have types

Just like the columns of a table in the database, the properties of a node each have a specific type.

Properties may have classic types (string, boolean, decimal...) or more complex types (reference, collection...).

Classic types can be refined and customized via the property's dataType method, presented in the next paragraph.

The possible types for properties are detailed further away in this section.
