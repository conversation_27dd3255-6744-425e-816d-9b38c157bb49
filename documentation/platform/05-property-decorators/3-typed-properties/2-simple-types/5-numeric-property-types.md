PATH: XTREEM/2+Simple+types/5+Numeric+property+types

# Numeric property types

# Integer property

The integer property type is used to store integers. These integers must not exceed the GraphQl integer limits, which is 2^31.

```ts
    @decorators.integerProperty<ShowCaseItem, 'unitPrice'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    reaonly unitPrice: Promise<integer | null>;
```
