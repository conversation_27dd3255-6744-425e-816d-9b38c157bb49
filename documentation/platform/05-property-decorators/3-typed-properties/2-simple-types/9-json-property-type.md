PATH: XTREEM/2+Simple+types/9+Json+property+types

# JSON property type

The JSON property type is used to store an `object`.

## Example

```ts
@decorators.node<MyMessage>({
    isPublished: true,
    storage: 'sql',
})
export class MyMessage extends Node {
    // Array containing the kitten names.
    @decorators.jsonProperty<MyMessage, 'payload'>({
        isStored: true,
        isPublished: true,
        dataType: () => myJsonMessageDataType,
    })
    readonly payload: Promise<object>;
}
```

In the example above we define a Node that has a JSON property called `payload`. The property decorator also has an optional dataType attribute. `jsonProperty` types have to be `typeOf` `object`.
