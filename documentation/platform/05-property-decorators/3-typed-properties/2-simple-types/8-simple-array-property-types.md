PATH: XTREEM/2+Simple+types/8+Simple+array+property+types

# Simple array property types

There are three simple array property types:

-   integerArrayProperty
-   enumArrayProperty
-   stringArrayProperty

All three array property types have the same attributes as their simple counterparts, respectively integerProperty, enumProperty and stringProperty.
The arrays are not limited in size.

# Example

Let's consider a Cat node storing information about the cat's kittens.

```ts
// Let's first define an enumeration for possible kitten fur colors.
export enum FurColorsEnum {
    black = 1,
    white = 2,
    striped = 3,
}

export type FurColors = keyof typeof FurColorsEnum;

export const furColorsDataType = new EnumDataType<FurColors>({ enum: FurColorsEnum, _filename });

// And a string datatype for cat names.
export const catNameDataType = new StringDataType({ maxLength: 250 });

@decorators.node<Cat>({
    isPublished: true,
    storage: 'sql',
})
export class Cat extends Node {
    // Array containing the ages of each of the Cat's kittens.
    @decorators.integerArrayProperty<Cat, 'kittenAges'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly kittenAges: Promise<integer[] | null>;

    // Array containing the fur color of each of the Cat's kittens.
    // Possible fur colors are stored in the FurColors enumeration defined above.
    @decorators.enumArrayProperty<Cat, 'catFurColors'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => furColorsDataType,
    })
    readonly catFurColors: Promise<fixtures.enums.FurColors[] | null>;

    // Array containing the kitten names.
    @decorators.stringArrayProperty<Cat, 'stringArrayVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => catNameDataType,
    })
    readonly stringArrayVal: Promise<string[] | null>;
}
```

# Immutability

All three arrays are immutable, meaning that the property value can be assigned but not changed. For instance, you can set it with `this.kittenAges = [1,2,3]`, but cannot change it using `this.kittenAges.push(4)` or `this.kittenAges.pop()` functions. Those two calls will result in
respective errors `Cannot add property 4, object is not extensible` and `Cannot delete property '1' of [object Array]`.
