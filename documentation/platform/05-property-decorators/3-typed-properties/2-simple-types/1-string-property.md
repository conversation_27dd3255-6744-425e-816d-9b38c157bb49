PATH: XTREEM/2+Simple+types/1+stringProperty

# stringProperty

## Generalities

### What is a localized string property

A _isLocalized_ string property is a property which manages _localized/translated_ texts based on the locale it is stored in.
A _isLocalized_ string property differs from a _string_ property in that it is stored as a JSON value in the database. The
JSON value will have both the locale and locale language as keys, with the provided value:
`{"en": "english value", "en-US": "english value"}`.
In cases where the default locale might differ from the current locale, the default locale language is also populated:
`{"fr": "english value", "en": "english value", "en-US": "english value"}`
where the default locale language is _fr_.

The value is returned to the node as a string value, based on the current locale of the user, or the default locale if the
current locale value is not available, using the postgres _coalesce_ expression. [Read more about coalesce in postgres](https://www.postgresqltutorial.com/postgresql-coalesce/)

(`coalesce (field ->> currentLocale, field ->> currentLocaleLanguage, field ->> defaultLocaleLanguage`)

### How to declare a localized string property

Simply set the `isLocalized: true` option when creating the instance of `StringDataType`. The `isStored` attribute of the _string property_ decorator must be set to `true` for `isLocalized` _string properties_

```ts
    @decorators.stringProperty<Foo, 'text'>({
        isStored: true,
        dataType: () => new StringDataType({ isLocalized: true }),
    })
    readonly text: Promise<string>;
```

## Encrypted string property

To store property in encrypted way, isStoredEncrypted should be added to property decorator. Encrypted value can be used only on string property.

```ts
    @decorators.stringProperty<TestEncryptedValues, 'passwordValue'>({
        isPublished: true,
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly passwordValue: Promise<string>;
```

### Decrypt value

To decrypt a value you should use decryptValue api, it is used like this:

```ts
nodeQuery.$.decryptValue('passwordValue');
```

where 'passwordField' is the name of the field that should decrypted.

### Empty values:

if the property's value is empty then it is not encrypted.

### Graphql query response

Graphql query will always return (\*..\*\*) (6 stars) for security reason, and, we can receive encrypted value throw graphql query.

### Note

Related documentation to review would be on the `Localization Manager`
