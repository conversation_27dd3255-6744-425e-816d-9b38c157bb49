PATH: XTREEM/1+Generalities+on+node+properties/3+System+properties

# System properties

System properties are properties added to all the nodes by the framework.

| Property      | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| \_id          | The `_id` property is a numeric property, that is a component of the primary key of a record. This property will be allocated to each record inserted using an auto-increment.                                                                                                                                                                                                                                                                                                                                        |
| \_tenantId    | The `_tenantId` property identifies the tenant that the record belongs to and is the other component of the primary key of a record. The framework, manages the row level security by filtering records for only tenants set in `context.tenantId`. If a node decorator has `isSharedByAllTenants` attribute set to `true`, then this property will not be present in the node, and no filter will be applied, as the data of this node is shared by all tenants. The primary key for shared nodes will be the `_id`. |
| \_sourceId    | The `_sourceId` property is an internal property, that will be used in the framework to store a value that will link the record to the source system it was extracted from. This could be a nano id, UUID, numeric string, etc. that could link the record.                                                                                                                                                                                                                                                           |
| \_createStamp | The `_createStamp` property is the date and timestamp of when the record was created                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| \_updateStamp | The `_updateStamp` property is the date and timestamp of when the record was last updated                                                                                                                                                                                                                                                                                                                                                                                                                             |
| \_createUser  | The `_createUser` property is the id of the user that created the record.                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| \_updateUser  | The `_updateUser` property is the id of the user that last updated the record.                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| \_factory     | The `_factory` property is a reference to the `MetaNodeFactory` of the node. Through this reference you can get the name of the node factory, its localized title and other metadata like its list of properties, the properties of its natural key, ... This property can be filtered and sorted so you can use it to filter tables or references bound to an abstract base.                                                                                                                                         |
| \_syncTick    | The `_syncTick` property is an internal property used by synchronization. It is set on synchronization _source_ records. It tracks the id the transaction which last updated a source record. The synchronization framework uses this tick to obtain the records which have been updated since the last synchronization pass. Its value may only increase.                                                                                                                                                            |
| \_syncInfo    | The `_syncInfo` property is an internal property used by synchronization. It is set on synchronization _target_ records. Its value is a JSON object with 2 members: `syncTick` and `updateStamp`, holding the `_syncTick` and `_updateStamp` of the source record when it was last synchronized.                                                                                                                                                                                                                      |
| \_updateTick  | The `_updateTick` is an internal property used when a record is being updated, it is used in the check if the current record in context is stale, i.e. the record has been updated in the interim by another user or process.                                                                                                                                                                                                                                                                                         |
