PATH: XTREEM/1+Generalities+on+node+properties/2+Custom+properties

# Custom properties

When creating or extending a node, applicative developers can define one or several custom properties on the node. Those properties can be:

-   Classical sql-stored properties with simple types (string, boolean...), that will each translate into a new column in the node's table in the database.

-   Json-stored properties, that will translate into Json columns in the node's table.

-   Reference properties, that point to another node's record.

-   Collection properties, used to define a header / lines relationship, but that won't affect the header node's table, only the lines' node.

-   Computed properties, that are used only for runtime calculations and won't be stored.

This section aims at describing thoroughly each of those possibilities.
