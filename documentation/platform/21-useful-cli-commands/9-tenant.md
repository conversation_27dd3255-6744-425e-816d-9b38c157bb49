PATH: XTREEM/21+Useful+CLI+commands/9+Tenant

# Tenant

The `tenant` command is used for the management of a tenant.

> This command supersedes the `manage` command.

## Usage

```sh
xtrem tenant [--init {base64} [--layers]] [--update {base64}] [--delete {tenantId}] [--export {tenantSelector} [options...]]
```

### --init:

Will initialize a new tenant, load all the default data for the layers provided and create an admin user of the current application for a given tenant Id. Data must be provided as a JSON object encoded in base64. All data in the JSON object must be provided and will be validated. If no layers are provided only data in the _setup_ layer will be loaded for the tenant.

Data must contain:

-   The tenant's id and name.
-   The name and id of the customer to whom the tenant belongs.
-   The administer's email, first name, last name and locale.

Data may also contain:

-   A list of service options that have to be activated: this information is presently optional.
-   A list of packages that have to be activated: this information is presently optional. By default, packages are activable.

Warning: Be careful that the format of the data will change with the implementation of the licence management.

This information has to be set as in the following example:

```json
{
    "customer": {
        "id": "00000000000000000001",
        "name": "acme"
    },
    "tenant": {
        "id": "000000000000000000000",
        "name": "dev"
    },
    "adminUser": {
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "locale": "en-US"
    },
    "serviceOptions": {
        "@sage/xtrem-show-case/show-case-discount-option": true,
        "@sage/xtrem-show-case/show-case-experimental-option": false,
        "@sage/xtrem-show-case/show-case-option-high-level": true,
        "@sage/xtrem-show-case/show-case-work-in-progress-option": false
    },
    "packs": {
        "@sage/xtrem-show-case": true,
        "@sage/xtrem-system": true
    }
}
```

    Once encoded in baes64 the previous JSON becomes:

```ts
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;
```

    Example:

```sh
xtrem tenant -- --init 'eyJjdXN0b21lciI6eyJpZCI6IjAwMDAwMDAwMDAwMDAwMDAwMDAxIiwibmFtZSI6ImFjbWUifSwidGVuYW50Ijp7ImlkIjoiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIiwibmFtZSI6ImRldiJ9LCJhZG1pblVzZXIiOnsiZW1haWwiOiJqb2huLmRvZUBhY21lLmNvbSIsImZpcnN0TmFtZSI6IkpvaG4iLCJsYXN0TmFtZSI6IkRvZSIsImxvY2FsZSI6ImVuLVVTIn19='
```

### --layers:

A list of layers to load the initial data of the tenant can be provided to the `init` command. If a list of layers are provided, it must be formatted with a comma separator and no space. For instance: --layers=setup,demo-data1,demo-data2.

    Example:

```sh
pnpm run xtrem tenant --init 'eyJjdXN0b21lciI6eyJ...'
```

    Where no layer is provided, the 'setup' layer will be loaded by default.

```sh
pnpm run xtrem tenant --init  'eyJjdXN0b21lciI6eyJ...' --layers=setup,demo-data1,demo-data2
```

    Where a layer is provided.

### --update:

Will update an existing tenant. Data must be provided as a JSON object encoded in base64. All data in the JSON object must be provided and will be validated.

Data must contain:

-   The tenant's id and name.
-   The name and id of the customer to whom the tenant belongs.

Data may also contain:

-   A list of service options that must be activated: this information is presently optional.
-   A list of packages that must be activated: this information is presently optional. By default, packages are activable.

Warning: Be careful that the format of Data will change with the implementation of the licence management.

This information has to be set as in the following example:

    ```JSON
    {
        "tenant": {
            "id": "000000000000000000000"
        },
        "packages": {
            "@sage/xtrem-show-case": true
        },
        "serviceOptions": {
            "@sage/xtrem-show-case/show-case-discount-option": true,
            "@sage/xtrem-show-case/show-case-experimental-option": true,
            "@sage/xtrem-show-case/show-case-option-high-level": true,
            "@sage/xtrem-show-case/show-case-work-in-progress-option": false
        },
        "packs": {
            "@sage/xtrem-show-case": true,
            "@sage/xtrem-system": true
        }
    }
    ```

    Once encoded in base 64 the previous JSON becomes:

    ewoJInRlbmFudCI6IHsKCQkiaWQiOiAiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIgoJfSwKCSJwYWNrYWdlcyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlIjogdHJ1ZQoJfSwKCSJzZXJ2aWNlT3B0aW9ucyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlL3Nob3ctY2FzZS1kaXNjb3VudC1vcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvdy1jYXNlLWV4cGVyaW1lbnRhbC1vcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvdy1jYXNlLW9wdGlvbi1oaWdoLWxldmVsIjogdHJ1ZSwKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlL3Nob3ctY2FzZS13b3JrLWluLXByb2dyZXNzLW9wdGlvbiI6IGZhbHNlCgl9LAoJInBhY2tzIjogewoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2UiOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zeXN0ZW0iOiB0cnVlCgl9Cn0

### --delete:

Will delete the tenant data of the current application for a given tenant Id.
Example:

```sh
xtrem tenant -- --delete "000000000000000000000"
```

### --export

    Export data for all tenants matching a given `tenantSelector`. It can be a tenant id, directory name or directory name glob pattern.
    It will generate 2 CSV files per table, one for the non nullable data and one for nullable, then create zip containing both of them
    in `data/exports/<tenantDirectoryName>/<export-id | directory-name---vv.mm.pp--YYYY.MM.DD.hh.mm.ss>.zip` file (vv.mm.pp is the application version)
    The zip will be uploaded to a S3 bucket if a location is provided and the s3 config is set

### --export-id::

An Id to identify this export. This id is used to generate a zip file with the name `<export-id>.zip`. This option cannot be set if we have multiple tenant exports.

### --location:

The location is in the form `s3://<bucket-name>/[path-prefix]`
If no path-prefix is provided, the default is to put the `<export-id>.zip` file on `s3://<bucket-name>/exports/<tenant-id>`

### --keep-all-values:

If specified, sensitive data flagged on properties with the exportValue decorator set, will export the actual value
contained in the database. If this parameter is not passed, the values defined in the exportValue decorator on the node will be exported.

### --anonymize:

If specified, properties on nodes that have an anonymizeMethod value set, will anonymize the data for the property using the method specified on the property.

    Examples:

```sh
xtrem tenant -- --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name

xtrem tenant -- --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name/my/specific/path

xtrem tenant -- --export "000000000000000000000" --keep-all-values

xtrem tenant -- --export "000000000000000000000" --anonymize

xtrem tenant -- --export 'reference-*' --location s3://my-bucket-name

```

### --import:

Import all tenant data of a given `tenantId` tenant Id from the location provided. If the location is an S3 Uri, the zip file is downloaded, extracted and the extracted CSV files are then imported. If the --location is a path to a local zip, then similarly it is extracted and the extracted CSV files are then imported.

-   If the location is an S3 Uri, the zip file is - downloaded, extracted. The extracted CSV files are then imported.
-   If the location is a path to a local zip, then similarly it is extracted and the extracted CSV files are then imported.
-   If the userAdmin is provided, then an admin user will be created at the end of the import.
-   If the import fails in the middle of an import the tenant must be deleted before attempting the import again.

Data must contain:

-   The tenant's id and name.
-   The customer's id and name the tenant belongs to.
-   The location: S3Uri (looks like `s3://<bucket-name>/exports/<tenant-id>`) or path on the server where the zip of export data is located.
    Data may also contain:
-   The administrator's email, first name, last name and locale.

This information has to be set as in the following example:

        ```json
        {
            "customer": {
                "id": "00000000000000000001",
                "name": "acme"
            },
            "tenant": {
                "id": "000000000000000000000",
                "name": "dev"
            },
            "location": "s3://xtrem-dev-eu-global/tenants/export_id.tgz",
            "adminUser": {
                "email": "<EMAIL>",
                "firstName": "John",
                "lastName": "Doe",
                "locale": "en-US"
            }
        }
        ```

        Once encoded in base 64 the previous JSON becomes:

                    ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    Example:

xtrem tenant --import

```sh
        '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
```

Warning: If the import fails in the middle of an import the tenant must be deleted before attempting the import again.

### Tenant data JSON format

```json
{
    "customer": {
        "id": "customer_nanoid",
        "name": "name of the customer"
    },
    "tenant": {
        "id": "tenant_nanoid",
        "name": "name of the tenant"
    },
    "adminUser": {
        "email": "<EMAIL>",
        "firstName": "FirstName",
        "lastName": "LastName",
        "locale": "en-US"
    },
    "serviceOptions": {
        "@sage/xtrem-show-case/show-case-discount-option": true,
        "@sage/xtrem-show-case/show-case-experimental-option": true,
        "@sage/xtrem-show-case/show-case-option-high-level": true,
        "@sage/xtrem-show-case/show-case-work-in-progress-option": false
    }
}
```

## Notes

-   This command cannot be executed without options.
-   The SQL connection and other application-specific details will be obtained from the xtrem-config.yml file.
-   `--init` and `--delete` are mutually exclusive.
-   There are two support users created when initializing a tenant, that cannot be modified or deleted. These users will be used by the Sage support team to be able to assist customers with support issues.
    -   `<EMAIL>`
    -   `<EMAIL>`
