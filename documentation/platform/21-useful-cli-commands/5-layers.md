PATH: XTREEM/21+Useful+CLI+commands/5+layers

# `xtrem layers` CLI command

The `xtrem layers` command manages the CSV files in the `data/layers` and `data/extension-layers` directories. It provides two sub-commands:

-   `xtrem layers --load {layers}`: Load data from the CSV files of one or more layers and inject it into the SQL database.

-   `xtrem layers --extract {layer}`: Read data from the SQL tables and regenerate the layer's CSV files.

Warning: The `--load` command recreates the `xtrem` SQL schema so any data that you may have in this schema will be lost, unless you extracted it, exported the tenant or backed it up.

## Modifying layer data

You have two options to modify layer data:

-   Edit the CSV files directly. This is a good option if you have to make small changes to existing records. The "Edit csv" vscode extension is a good tool for this.
-   Use the `xtrem layers` CLI command with the workflow described just below. This is the recommended option if you are creating new data or making substantial changes to existing records.

If you choose the second option you should use the following workflow:

1. Load your layers with `xtrem layers --load {layers}`. **You can only modify one layer at a time and it should always be the last layer that you pass to the `--load` command**. For example, use `xtrem layers --load setup,qa` to work on the `qa` layer and `xtrem layers --load setup` to work on the `setup` layer.
2. Start the server with `xtrem start`.
3. Open the application with your browser. Default URL is `http://localhost:8240`.
4. Change the data with the web UI: add, modify or delete records.
5. Extract the layer with `xtrem layers --extract {layer}`, where `{layer}` is the layer chosen at step 1. This will regenerate all the CSV files of the layer.
6. Check the changes. The easiest way is to use the vscode git integration. The `--extract` command should create and update records in the right place but you may have to make some manual adjustments.
   For example, if you created a new item that you will only use for manufacturing unit tests, the `--extract` command will create it in `xtrem-master-data` because it cannot guess your intent. You can leave this item there but you can also move it to `xtrem-manufacturing` if you want to better scope it.
7. Commit the modified CSV files.

If you need to do more changes you can skip steps 1-3 and go directly to step 4; modify the data with the web UI and extract again.

## Usage

```
layers [--load {layers}] [--extract {layer}] [--tenant {tenantId}];
```

-   `--load {layers}`

    Loads the CSV files of the supplied layers into the database.
    A comma separated (without space) list of layers must be provided.
    For instance: `--load setup,test`

-   `--extract {layer}`

    Extracts the CSV layer. All the CSV files of this layer will be regenerated.
    A single layer name must be provided.
    For instance: `--extract test`

-   `--tenant {tenantId}`

    Optional tenant id. If omitted the test tenant, `777...777`, will be used.

Either `--load` or `--extract` must be provided but not both.

## NPM commands

Most packages expose handy aliases for these commands in their package.json files:

| pnpm command                  | xtrem command                    |
| ----------------------------- | -------------------------------- |
| `pnpm run load:setup:data   ` | `xtrem layers --load setup     ` |
| `pnpm run load:test:data    ` | `xtrem layers --load setup,test` |
| `pnpm run load:demo:data    ` | `xtrem layers --load setup,demo` |
| `pnpm run load:qa:data      ` | `xtrem layers --load setup,qa  ` |
| `pnpm run extract:setup:data` | `xtrem layers --extract setup  ` |
| `pnpm run extract:test:data ` | `xtrem layers --extract test   ` |
| `pnpm run extract:demo:data ` | `xtrem layers --extract demo   ` |
| `pnpm run extract:qa:data   ` | `xtrem layers --extract qa     ` |

## A note on --load

The `--load` command can be executed either from the root of the project or from within a given package.

Depending on where it is executed, the loaded database will vary, since the tables will be created in the context of the current package.

More specifically, if the command is run in `xtrem-system`, it won't take into account the extensions and tables introduced by packages that depend on `xtrem-system`:

-   The `company` table will exist because its base package is `xtrem-system`,
    but it will only have the columns defined in `xtrem-system`.

-   The `currency` column won't be there because it is introduced by `xtrem-master-data`.
