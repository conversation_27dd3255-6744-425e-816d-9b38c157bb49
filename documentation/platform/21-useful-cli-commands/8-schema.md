PATH: XTREEM/21+Useful+CLI+commands/8+Schema

# Manage

The `schema` command is used to manage the database schema of a xtrem application.

## Usage

```ts
xtrem schema [--create [--force]] [--upgrade] [--dump-to-s3] [--restore-from-s3 [version]] [--test-upgrade [version] [--force]]
```

-   **--create:** Will create the SQL schema of the current application. This method will iterate over all the persistent nodes and create their tables, indexes and foreign keys.
-   **--force:** Will force reset database if it already exists. It only applies to --create. This flag should only be used by the CI (needs write access to the S3 bucket).

```ts
pnpm run xtrem schema --create
```

or

```ts
pnpm run xtrem schema --create --reset-database
```

-   **--upgrade:** Will upgrade all tables of the current application. This method will iterate over all the persistent nodes and create their tables, indexes and foreign keys. If any version moves backwards the operation will abort.

```ts
pnpm run xtrem schema --upgrade
```

-   **--dump-to-s3:** Dump the current database into a S3 bucket

```ts
pnpm run xtrem schema --dump-to-s3
```

-   **--restore-from-s3:** Restore the provided version of a database from a S3 bucket. If no version is provided, or if the provided version does not exist in the S3 bucket, the command will display all the available versions. The optional argument `s3ConfigType` can be provided to specify S3 bucket, folder and key information to the S3 manager. The possible values are `dbDumps`, `upgradeBundles` and `clusterCuBackup`. In the absence of the `s3ConfigType` argument the default S3 configuration type will be `dbDumps`.

```ts
pnpm run xtrem schema --restore-from-s3 version [--s3ConfigType=[dbDumps | upgradeBundles | clusterCuBackup]]
```

-   **--test-upgrade:** Restore the provided version of a database from a S3 bucket and run the upgrade process. If the version
    is ommitted, the latest available version will be used. If no version is available, an error will be raised.
-   **--force:** Will force reset database if the test-upgrade failed (and backup all the existing S3 dumps).

```
pnpm run xtrem schema --test-upgrade [version] [--force]
```

-   **--rename-to:** Rename the schema configured in the xtrem-config.yml file (or _xtrem_ if not set) to the new name provided.

    ⚠️ This is a dangerous operation and should be used with caution.

Example: `pnpm run xtrem schema --rename-to newSchemaName` will rename the currently used schema (the one configured in the xtrem-config.yml file - or _xtrem_ if not set) to _newSchemaName_

-   **--rename-from:** Rename the provided schema to the one configured in the xtrem-config.yml file (or _xtrem_ if not set).

    ⚠️ This is a dangerous operation and should be used with caution.

Example: `pnpm run xtrem schema --rename-from oldSchemaName` will rename the _oldSchemaName_ schema to the schema name configured in the xtrem-config.yml file (or _xtrem_ if not set)
