PATH: XTREEM/21+Useful+CLI+commands/7+Lint

# Lint

The `lint` command looks for potential errors and code styling issues. It flags issues in the code by running statical code analysis. Some of the issue may be fixed automatically. It ensures that all Xtrem application packages are written with the same coding and styling standards, increasing code quality and reducing code differences in source control.

The Xtrem CLI uses [tslint](https://palantir.github.io/tslint/) and [prettier](https://prettier.io/) behind the scenes for linting.

Alias: `l`

## Usage

```ts
xtrem lint [--fix] [relative path within the working directory]
```

When the command is executed without any parameters, it searches for issues in the current working directory which are displayed in a list at the end of the process. The list contains the location of the issue (the file path and where applicable line and character position) and error code. The error codes can be looked up on [tslint](https://palantir.github.io/tslint/)'s website.

## Parameters

-   **--fix:** Fixes all auto-fixable issues. The path of the files fixed is displayed.
-   **\[a relative path within the package\]:** Restricts the search area of the linting process to the provided path.

## Notes

-   When any issues are discovered, exit code is **1** . On the other hand, when no issues are found, the exit code is **0**.
