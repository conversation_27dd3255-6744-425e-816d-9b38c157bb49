PATH: XTREEM/21+Useful+CLI+commands/3+Start

# Start

The `start` command starts the Xtrem package.

Alias: `s`

## Usage

```ts
xtrem start [--channels <comma seperated list of channels>] [--services <comma seperated list of services>] [--watch-client] [--watch-server] [--watch-all] [--debug] [--cluster];
```

-   **--channels:** This option sets the list of comma separated channels to be active on the server. The allowed channels are graphql, routing and listeners.

    -   routing: Starts the communication routing services.
    -   listeners: Starts the communication listeners.
    -   graphql: Starts the server in interactive mode. Endpoints are active by default.

    If the --channels option is not supplied, then all the channels are active by default.

-   **--services:** This option sets the list of comma separated packages to be started as a service, for example `xtrem start --services=@sage/xtrem-communication` or xtrem `start --services=communication` (the prefix `@sage/xtrem-` can be omitted). In the absence of the --services option xtrem start loads all the packages and run all their startService methods. This allows to easily start all services in development mode.
-   **--watch-server:** Watches the server-side artifacts. When a change is detected it compiles the file changed and automatically redeploys the server.
-   **--watch-client:** When started, it compiles the client-side artifacts and then keeps watching for changes. When a change is detected, it recompiles on the changed file. In case of compilation error, the server will stop listening until the error is not fixed in the code, no restart of the process is needed.
-   **--watch-all:** Combines the two parameters above.
-   **--socket-key="THE_KEY":** Opens a socket on port 4002 where compilation events are broadcasted using the Socket.IO protocol. Clients have to authenticate with the key provided as an argument.
-   **--queues:** Comma separated list of queues to explicitly start.
-   **--cluster:** Cluster mode. Creates a forked process for the number provided in the config cluster->numberOfForkedProcesses, if numberOfForkedProcesses is not provided then a fork is created for each available CPU.

If the --routing, --listeners and --graphql options are not supplied, then all the related services are active by default

**Examples**

-   Start all services and channels

```
pnpm run xtrem start
```

-   Start just the graphql channel

```
pnpm run xtrem start --channels 'graphql'
```

-   Starting just the routing channel, and all communication services

```
pnpm run xtrem start --channels 'routing'
```

-   Start the graphql and listeners channel, and all communication services

```
pnpm run xtrem start --channels 'graphql,listeners'
```

-   Start the graphql and listeners channels and, the finance communication services

```
pnpm run xtrem start --channels 'graphql,listeners' --services 'finance'
```

-   Only start the 'export' and 'sales-order' SQS queues

```
pnpm run xtrem start --queues=export,sales-order
```

## The socket-key attribute

When `start` command is executed with the `--socket-key="<any key>"` parameter, the Xtrem CLI listens on port 4002 for incoming [socket.io](https://github.com/socketio/socket.io) connections.

### Triggered Events

Various events are emitted via this event stream:

-   **COMPILER_STARTED:** Triggered when changes detected and the Xtrem CLI starts incrementally recompiling the package.
-   **COMPILER_SUCCESS:** Triggered when the compilation of the changes finished without any errors. The data field contains whether the compiler is `client` or `server`.
-   **COMPILER_FAILURE:** Triggered when the compilation fails, an array of error objects is dispatched in the data fields using the following format:

```ts
export interface CompilationError {
    code: number;
    file: string;
    line: number;
    character: number;
    message: string;
    source: 'client' | 'server';
}
```

### Available Commands

Clients connecting to the event stream can also trigger basic commands:

-   **AUTH:** Authenticates the connection, the data field must contain the key which was provided in the `--socket-key="<any key>"` command line argument. No events are sent to the client, until it successfully authenticates.
-   **REBOUNCE:** Restarts the Xtrem service.

### Sample client

```ts
const io = require('socket.io-client');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

const socket = io('http://localhost:4002');

socket.on('connect', () => {
    console.log(`CONNECTED: ${socket.id}`);
    socket.emit('AUTH', 'cc72b9e6-2e56-11ea-978f-2e728ce88125');
});

socket.on('COMPILER_FAILURE', data => {
    console.log('COMPILER_FAILURE', data);
});

socket.on('COMPILER_SUCCESS', data => {
    console.log('COMPILER_SUCCESS', data);
});

socket.on('COMPILER_STARTED', data => {
    console.log('COMPILER_STARTED', data);
});

rl.on('line', () => {
    console.log(`Triggered recompiling`);
    socket.emit('REBOUNCE');
});
```

> ⚠️ **[IMPORTANT]**
> When this parameter is declared, the service will not start until someone successfully connects to the event stream.

## Notes

-   Watching the client artefact may stop working after a couple dozens of changes, in this case just simply restart the process.
-   An Xtrem package with a number of Xtrem application package dependencies but without any source code can be also deployed using this command.
