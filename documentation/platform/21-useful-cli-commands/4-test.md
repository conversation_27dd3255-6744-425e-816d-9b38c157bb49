PATH: XTREEM/21+Useful+CLI+commands/4+Test

# Test

The `test` command executes various tests of the Xtrem package.

Alias: `t`

## Usage

```ts
xtrem test [pattern] [--unit] [--integration] [--ci] [-- [otherArgs]]
```

-   **\[pattern\]:** (Optional) Relative path or regular expression pattern which is used to filter the test files. This option is ignored in "ci" mode.
-   **--unit:** Executes the [Mocha](https://mochajs.org/) based server-side unit test files that are located in the `test` folder
-   **--integration:** Executes Cucumber feature files that are located in the `test/cucumber` directory.
-   **--ci:** Optimizes the test execution for a CI environment, enables test coverage. In case of integration tests, it also starts the server while tests are being executed.
-   **--service-options:** A list of service options that shall be active when executing unit tests.
-   **--all-service-options:** All service options have to be active when executing unit tests.
-   **otherArgs:** any other arguments that should be used (see below).

```ts
xtrem test --unit --service-options=showCaseWorkInProgressOption
xtrem test --unit --all-service-options
```

`otherArgs` possible options:

-   **--timeout xxx:** set the timeout to `xxx` ms (default is 60000ms).
-   **--noTimeout:** disable the mocha timeout.

## Notes

-   Watching the client artifact may stop working after a couple dozens of changes, in this case just simply restart the process.
-   An Xtrem package with a number of Xtrem application package dependencies but without any source code can be also deployed using this command.
