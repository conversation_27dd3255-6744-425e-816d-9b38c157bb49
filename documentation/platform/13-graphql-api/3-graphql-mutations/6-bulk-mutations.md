PATH: XTREEM/3+GraphQL+mutations/6+Bulk+mutations

# Bulk mutations

A _bulk_ mutation is an asynchronous mutation that applies an operation to all the nodes returned by a query.
The querying and iteration logic is handled by the framework and the developer only supplies the method that is executed
on each node instance.

Bulk mutations could be implemented directly as async mutations but the _bulk_ mutation saves some boilerplate code (querying and iterating) and it can be bound directly to a table.
When bound to a table, the user can select rows by clicking on checkboxes and then apply the action to all the selected rows.
The framework takes care of all the wiring for this common pattern.

## Bulk mutation decorator

A bulk mutation is a static method of a Node class, decorated with a `bulkMutation` decorator.

```ts
    @decorators.bulkMutation<typeof PurchaseOrder, 'massApproval'>({
        isPublished: true,
    })
    static async massApproval(context: Context, purchaseOrder: PurchaseOrder): Promise<boolean> {
        // ...
    }
```

The main difference with a vanilla mutation is the presence of a second parameter (`purchaseOrder` in our example) which is an instance
of the node class, and which is not described by the decorator.

## GraphQL API for a bulk mutation

A bulk mutation is handled as an asynchronous mutation, with a `start` mutation that initiates the operation and a `track` query that tracks its progress.

The only notable difference is the fact that the start mutation takes a `filter` parameter, which must be a filter on the mutation's node.
The framework uses this filter to build a query, then iterates on the query's results and applies the bulk mutation's method to each instance returned by the query.

For example, the `massApproval` bulk mutation above will be started with:

```graphql
mutation {
    xtremPurchasing {
        PurchaseOrder {
            massApproval {
                start(filter: "{ supplier: { site: { name: 'SITE A' } } }") {
                    trackingId
                }
            }
        }
    }
}
```

This mutation will start the mass approval of all purchase orders that have a supplier for the 'SITE A' site.

This mutation will return the tracking ID of the operation which is running in the background.

You can track the progress of this operation with the `track` query, like any asynchronous mutation.

## Client proxy for bulk mutation

A bulk mutation can be started with the start method of the client proxy, like any other asynchronous mutation. You only
have to pass the `filter` parameter:

```ts
const purchaseOrderNode = page.$.graph.node('@sage/xtrem-purchasing/PurchaseOrder');

// Start the operation and get its tracking id
const { trackingId } = await purchaseOrderNode.asyncOperations.massApproval.start(
    { trackingId: true },
    {
        filter: { supplier: { site: { name: 'SITE A' } } },
    },
);
```

## Additional parameters

Simple bulk mutations only take a `filter` parameter but you can specify additional parameters if you need to.

Just add your parameters after the `filter` parameter of your static method, and add a `parameters` attribute
to the `bulkMutation` decorator with type descriptors for your additional parameters.
