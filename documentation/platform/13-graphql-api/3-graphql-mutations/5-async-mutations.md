PATH: XTREEM/3+GraphQL+mutations/5+Background+mutations

# Background mutations

An _async_ mutation is a mutation that may take a long time to execute.
Unlike _normal_ (sync) mutations, async mutations do not return their result directly to their caller.
Instead, they start the operation and return a tracking id to their caller.
The caller of an async mutation can follow the progress of the operation by polling or by subscribing to a response notification.

## Background mutation decorator

You can add an async mutation to the GraphQL API with
a _decorated_ static method in a _Node_ class. For example:

```ts
    @decorators.asyncMutation<typeof Customer, 'assessSolvability'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [{
                name: 'customer',
                type: 'reference',
                node: () => Customer,
        }],
        return: 'boolean',
    })
    static async assessSolvability(context: Context, customer: Customer): Promise<boolean> {
        // ...
    }
```

The `parameters` array describes the parameters of the async mutation, their name and their type.

The `return` attribute describes the type returned by the async mutation.

The `isSchedulable` attribute indicates whether the async mutation can be scheduled or not by the batch scheduler,
If schedulable, the user who schedules the operation will provide the value of the parameters at the time he creates the schedule entry.

The static method implements the static mutation. Its first parameter (`context`) is an extra parameter that is passed by the framework and not described in the decorator.

The GraphQL API uses the parameter names specified in the decorator. The method parameter names should match the decorator parameter names, although typing won't enforce it.

## GraphQL API for async mutation

An async mutation will be exposed as a set of three operations in the GraphQL Schema:

-   a `start` mutation that starts the operation
-   a `stop` mutation that stops a mutation that is currently running
-   a `track` query that will return the status of the operation, and its result or error message at the end.

For example, the `assessSolvability` mutation above will be started with:

```graphql
mutation {
    xtremMasterData {
        Customer {
            assessSolvability {
                start(customer: 25) {
                    trackingId
                }
            }
        }
    }
}
```

This mutation will return the tracking ID of the operation which is running in the background.

You can track the progress of this operations with the `track` query:

```graphql
query {
    xtremMasterData {
        Customer {
            assessSolvability {
                track(trackingId: 'EDJSvjE6P_7kt-ziK626R') {
                    status
                    result
                    errorMessage
                }
            }
        }
    }
}
```

This query will return the status of the operation.
If the operation completes successfully the `result` field is set in the response.
If the operation completes with an error the `errorMessage` field is set.

The operation can also be stopped with:

```graphql
mutation {
    xtremMasterData {
        Customer {
            assessSolvability {
                stop(trackingId: 'EDJSvjE6P_7kt-ziK626R')
            }
        }
    }
}
```

The stop mutation returns a boolean (which is always true for now)

## Client proxy for async mutation

The client proxy packages (@sage/xtrem-client and @sage/some-package-api) provide a simple, well-typed, API for async mutations.
In this API, the async mutation is exposed as an object with 4 methods:

-   `start`, which calls the `start` GraphQL mutation and returns the tracking id.
-   `stop`, which calls the `stop` GraphQL mutation.
-   `track`, which calls the `track` GraphQL query.
-   `runToCompletion`, which calls the `start` mutation and then executes a polling loop until the operation completes with a `success`, `error` or `stopped` status.

Here is how the accessSolvability async mutations would be called from a UI page:

```ts
const customerNode = page.$.graph.node('@sage/xtrem-master-data/Customer');

// Start the operation and get its tracking id
// This would typically be called from the onClick of a button
const { trackingId } = await customerNode.asyncOperations.assessSolvability.start(
    { trackingId: true },
    { customer: 25 },
);

// Check the status of the operation
// This would typically be called for a component which polls the server to get the status of the operation
const { status, result, errorMessage } = await customerNode.asyncOperations.assessSolvability.track(
    { status: true, result: true, errorMessage: true },
    { trackingId },
);
switch (status) {
    case 'succees':
        await doSomethingWithResult(result);
        break;
    case 'error':
        await handleError(errorMessage);
        break;
    case 'stopped':
        await handleStopped(result, errorMessage);
        break;
    default:
        await showStatus(status);
        break;
}

// Stops the operation.
// The `reason` parameter is a message which will be returned as errorMessage in the 'stopped' case above.
// This would typically be called from the onClick of a link or button
await customerNode.asyncOperations.assessSolvability.stop(true, { trackingId, reason: 'user gave up' });
```

In the case where we just want to start the operation and wait for its result, we can use the `runToCompletion` method and just write:

```ts
const customerNode = page.$.graph.node('@sage/xtrem-master-data/Customer');

// Start the operation and run it to completion
try {
    const result = await customerNode.asyncOperations.assessSolvability.runToCompletion(true, { customer: 25 });
    // Operation completed successfully.
    await doSomethingWithResult(result);
} catch (err) {
    // Operation completed in `error` status, or was stopped
    await handleError(err);
}
```

Note: the proxy API uses `asyncOperations` rather than `asyncMutations` because it groups the mutations and the query altogether
and because `runToCompletion` combines calls to the `start` mutation and the `track` query.

## Use a specific queue

By default, an async mutation will use the SQS queue declared for the package of its node.

The `queue` decorator allows to provide the name of a specific queue to use.

```ts
    @decorators.asyncMutation<typeof ...., 'myMutation'>({
        ...
        queue: 'myQueue',
        ...
    })
    static async myMutation(context: Context, ...): Promise<...> {
        // ...
    }
```

This will allow, for instance, to group the execution of some async mutations on a dedicated cluster (see --queue=... option)
