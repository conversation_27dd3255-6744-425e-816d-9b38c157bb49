PATH: XTREEM/3+GraphQL+mutations/3+Cross+references

# Cross references

## Create

Fake ids, using negative numbers, can be used in mutations create in order to identify elements that are related.
This feature is conditioned to the fact that the reference is defined as nullable.

In the following example a customer is created with a billing address (which is a nullable reference) referencing an address contained in the collection of addresses:

```graphql
mutation {
    xtremMasterData {
        customer {
            create(
                data: {
                    addresses: [
                        { _id: "-3", street: "444 High Street", city: "Palo Alto" }
                        { _id: "-4", street: "156 University Av", city: "Palo Alto" }
                    ]
                    billingAddress: "-4"
                }
            )
        }
    }
}
```

The server will use the ids to identify the records that have been created. In the GraphQL response the records will be identified with their real ids. Something like:

```ts
"xtremMasterData": {
  "customer": {
    "create": {
      "addresses": [
        { "_id": "1", "street": "444 High Street", "city": "Palo Alto" },
        { "_id": "2", "street": "156 University Av", "city": "Palo Alto" },
      ],
      "billingAddress": "1"
    }
  }
}
```

## Update

Fake ids can also be used in mutations update. This feature is conditioned to the fact that the reference is defined as nullable.

In the following example a customer the new billingAddress references an address added to the collection of addresses

```graphql
mutation {
    xtremMasterData {
        customer {
            create(
                data: {
                    _id: 1
                    addresses: [
                        { _id: "1", street: "444 High Street", city: "Palo Alto" }
                        { _id: "2", street: "156 University Av", city: "Palo Alto" }
                        { _id: "-3", street: "City Hall", city: "Palo Alto" }
                    ]
                    billingAddress: "-3"
                }
            )
        }
    }
}
```

In the GraphQL response the records will be identified with their real ids. Something like:

```ts
"xtremMasterData": {
  "customer": {
    "create": {
      "addresses": [
        { "_id": "1", "street": "444 High Street", "city": "Palo Alto" },
        { "_id": "2", "street": "156 University Av", "city": "Palo Alto" },
        { "_id": "3", "street": "City Hall", "city": "Palo Alto" }
      ],
      "billingAddress": "3"
    }
  }
}
```
