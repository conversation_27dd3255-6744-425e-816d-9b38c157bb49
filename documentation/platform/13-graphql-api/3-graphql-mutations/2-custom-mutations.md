PATH: XTREEM/3+GraphQL+mutations/2+Custom+mutations

# Custom mutations

## Mutation

You can add a custom mutation or a custom query to the GraphQL API with
a _decorated_ static method in a _Node_ class. For example:

```ts
    @decorators.mutation<typeof Currency, 'updateRate'>({
        isPublished: true,
        parameters: [
            { name: 'currencyCode', type: 'string' },
            { name: 'rate', type: 'decimal' },
        ],
        return: 'boolean',
        startsReadOnly: true,
    })
    static async updateRate(context: Context, currencyCode: string, rate: decimal): Promise<boolean> {
        // ...
    }
```

The `parameters` array describes the parameters of the mutation, their name and their type.

The `return` attribute describes the type returned by the mutation.

The `startsReadOnly` attribute is used to determine if the context passed to the static method is readonly or not. This attribute can be assigned a boolean or callback function that returns a boolean. If the attribute is true or resolved to true, the method `context.runInWritableContext` can be used to execute code in writable contexts. [See the Context API for further details.](https://confluence.sage.com/display/XTREEM/08+Context+API)

The static method implements the mutation. Its first parameter (`context`) is an extra parameter which is passed by the framework and not described in the decorator.

The GraphQL API uses the parameter names specified in the decorator. The method parameter names should match the decorator parameter names, although typing won't enforce it.

## Note on queries

If an operation does not modify any data and only returns a result it should be decorated with `@decorators.query` instead of `@decorators.mutation`.

The decorators of queries and mutations are similar, however the query decorator has an additional boolean attribute called
`isGrantedByLookup`. If the `isGrantedByLookup` is set to `true`, access to execute this query will be granted if the user is authorized for the query or `lookup` operation.

## Parameters

Parameters are specified as object literals. They have mandatory `name` and `type` attributes, as well as optional decorator attributes that are similar to the attributes of property decorators: `isMandatory`, `isNullable`, `enum` ('enum' type), `node` ('reference' type).

```ts
    { name: 'currencyCode', type: 'string', isMandatory: true },
```

Our example above could be rewritten with a 'reference' type instead of a 'string':

```ts
    @decorators.mutation<typeof Currency, 'updateRate'>({
        isPublished: true,
        parameters: [
            {
                name: 'currency',
                type: 'reference',
                isMandatory: true,
                node: () => xtremMasterData.nodes.Currency,
            },
            { name: 'rate', type: 'decimal' ],
        ],
        return: 'boolean',
    })
    static async updateRate(context: Context, currency: xtremMasterData.nodes.Currency, rate: decimal): Promise<boolean> {
        // ...
    }
```

Note that the type of the `currency` parameter is now `xtremMasterData.nodes.Currency`, instead of `string`. But the parameter is still a string in the GraphQL schema because the _input_ type for references is string in XTreeM GraphQL APIs.

## Return type

The return type is specified either as a type name, for simple types, or as an object literal. For example:

```ts
    @decorators.mutation<typeof Currency, 'setExchangeRate'>({
        isPublished: true,
        parameters: [ ... ],
        // simple return
        return: 'boolean',
    })
    static ...

    @decorators.mutation<typeof Currency>({
        isPublished: true,
        parameters: [ ... ],
        // more complex return (a reference)
        return: {
            type: 'reference',
            node: () => xtremMasterData.nodes.Currency,
        },
    })
    static ...
```

## Passing node instance payloads

In the previous example we passed a reference to the mutation but you can also
pass an _instance payload_, by setting the parameter type to `instance` instead of `reference`.

For example, assuming that `ExchangeRate` is a _node_ with properties `baseCurrencyCode`, `targetCurrencyCode`, `date` and `conversionRate`:

```ts
    @decorators.mutation<typeof Currency, 'setExchangeRate'>({
        isPublished: true,
        parameters: [
            {
                name: 'exchangeRate',
                type: 'instance',
                isMandatory: true,
                node: () => xtremMasterData.nodes.ExchangeRate,
            },
        ],
        return: 'boolean',
    })
    static async setExchangeRate(
        context: Context,
        exchangeRate: NodeCreateData<xtremMasterData.nodes.ExchangeRate>,
    ): Promise<boolean> {
        // ...
    }
```

This mutation will be called as:

```graphql
mutation {
    xtremMasterData {
        currency {
            mutations {
                setEchangeRate(
                    exchangeRate: {
                        baseCurrencyCode: "EUR"
                        targetCurrencyCode: "USD"
                        date: "2020-10-15"
                        rate: "1.17"
                    }
                )
            }
        }
    }
}
```

Note that the type of the method parameter has changed: `NodeCreateData<ExchangeRate>` instead of `ExchangeRate`.

The `node` attribute of the reference parameter must return a _decorated_ Node class. You may use an existing persistent node class (storage: 'sql'), or an ad-hoc node class (storage: 'json').

## Passing arrays and objects

It is also possible to define parameters that receive objects or arrays, and any combination thereof, without having to define _Node classes_ for them.

Syntax:

-   Object parameter: `{ name: '...', type: 'object', properties: { /* the object's properties */ }}`
-   Array parameter: `{ name: '...', type: 'array', item: /* the array's element type */ }`
-   Nested object: `{ type: 'object', properties: { /* the object's properties */ }}`
-   Nested array: `{ type: 'array', item: /* the array's element type: object, instance, string, integer, ... */ }`

For example:

```ts
    @decorators.mutation<typeof MyNode, 'myMutation'>({
        isPublished: true,
        parameters: [
            {
                name: 'quantities',
                type: 'array',
                item: 'decimal',
            },
            {
                name: 'person',
                type: 'object',
                properties: {
                    civility: { type: 'enum', enum: myPackage.enums.Civility },
                    firstName: 'string',
                    lastName: 'string',
                    dateOfBirth: 'date',
                },
            },
            {
                name: 'contacts',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        name: 'string',
                        phone: 'string',
                    },
                },
            },
            {
                name: 'currencies',
                type: 'array',
                item: {
                    type: 'instance',
                    node: () => xtremMasterData.nodes.Currency,
                },
            },
        ],
        return: 'boolean',
    })
    static async myMutation(
        context: Context,
        quantities: decimal[],
        person: { civility: myPackage.enums.Civility; firstName: string; lastName: string; dateOfBirth: date; },
        contacts: { name: string; phone: string; }[],
        currencies: xtremMasterData.nodes.Currency[],
    : Promise<boolean> {
        // ...
    }
```

The parameters of the method must match the `parameters` of the decorator. TypeScript will raise a typing error if they don't match.

## Return stream type

textStream as well as binaryStream are passed as string parameters.

### Return textStream

textStream is returned as follow:

```ts
    @decorators.mutation<typeof TestOperation, 'mutationWithTextStreamAsResult'>({
         isPublished: true,
         parameters: [
             {
                 name: 'textContent',
                 type: 'string',
                 isMandatory: true,
             },
         ],
         return: {
             type: 'textStream',
         },
     })
     static mutationWithTextStreamAsResult(context: Context, textContent: string): TextStream {
         return new TextStream(textContent);
     }
```

### Return binaryStream

binaryStream is returned as follow:

```ts
    @decorators.mutation<typeof TestOperation, 'mutationWithBinaryStreamAsResult'>({
         isPublished: true,
         parameters: [
             {
                 name: 'binaryContent',
                 type: 'string',
                 isMandatory: true,
             },
         ],
         return: {
             type: 'binaryStream',
         },
     })
     static mutationWithBinaryStreamAsResult(context: Context, base64Data: string): BinaryStream {
         const buffer = Buffer.from(base64Data, 'base64');
         return new BinaryStream(buffer);
     }
```
