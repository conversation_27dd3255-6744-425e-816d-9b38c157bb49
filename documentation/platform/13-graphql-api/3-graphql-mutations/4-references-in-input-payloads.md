PATH: XTREEM/3+GraphQL+mutations/4+References+in+input+payloads

# References in Input Payloads

The graphql API accepts several syntaxes for non-vital references in input payloads:

1. An \_id integer value, for example `15`.
2. An \_id integer value passed as a string, for example `"15"`.
3. An \_id value prefixed by `_id:`, for example `"_id:15"`.
4. A _setup id_ prefixed with `#`, for example `#EUR` for a reference to a currency.
5. A key value passed as a JSON5 string, for example `"{ code: 'CUSTOMER-01' }"`.

In the first 3 cases, the reference is passed as its \_id value.
This is the preferred way if the reference was previously read with the GraphQL API.

The '#' syntax is only supported on references to setup data. It is handy in unit tests.

The JSON5 syntax is useful when integrating external data.
Any unique key (simple or composite) can be used to identify the reference.

Notes:

-   Negative \_id values are allowed for cross-references between nodes created in the same payload.
-   Non integer \_id values are only valid in nodes with _external_ storage.
-   JSON5 is a superset of JSON which is more convenient inside GraphQL string literals
    because double quotes `"` can be replaced by simple quotes `'`, and may be omitted around property names.
-   These syntaxes only apply to non-vital references. See the [vital references](https://confluence.sage.com/display/1+Single+reference/2+Vital+references) page for details on vital references.

## Example

```graphql
mutation {
    xtremMasterData {
        item {
            create(
                data: {
                    id: 'ITEM1'
                    name: 'Item 1'
                    volumeUnit: 5               # an _id value
                    weightUnit: "#KILOGRAM"     # a setup id
                    stockUnit: "_id:5"          # an _id value
                    purchaseUnit: "{id:'kg'}"   # a unique key (`id`) in JSON5 syntax
                }) {
                  _id
            }
        }
    }
}
```
