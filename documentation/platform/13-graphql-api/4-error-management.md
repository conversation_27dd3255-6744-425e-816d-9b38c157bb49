PATH: XTREEM/13+GraphQL+API/4+Error+management

# Error management

Graphql API errors don't include technical details.
The only errors displayed with details in the GraphQL API are applicative errors.

## Error classes

Here are the existing error classes:

### BusinessRuleError

-   Thrown when a business rule is broken.
-   The message is localized and intended for the end user.
-   **These messages are fully displayed in the GraphQL API.**

### DataInputError

-   Thrown when the data provided to the API is faulty.
-   The message is localized and intended for the end user.
-   **These messages are fully displayed in the GraphQL API.**

### AuthorizationError

-   Thrown when a query tries to access unauthorized data.
-   The message is localized and intended for the end user.
-   **These messages are fully displayed in the GraphQL API.**

### LogicError

-   Logic error, for instance a failed assertion, or a division by 0. These errors should never happen.
-   Independent of external environment and data.
-   Message not localized, not returned to user.
-   **A generic message is returned to the user instead, mentioning that a logic error has occurred.**

### SystemError

-   Not a logical error nor a business error.
-   For instance, a file that was not found, a connection that failed...
-   Message not localized, not returned to user.
-   **A generic message is returned to the user instead, mentioning that a logic error has occurred.**
-   Normal Typescript errors are considered as system errors.

## The innerError attribute

The classes mentioned above have an innerError attribute. This attribute can be used when catching an error to encapsulate the real error into another one that will be thrown instead.

For instance:

```ts
try {
    // Try read a record in the database
} catch (e) {
    // Rethrow a system error.
    // e is stored in the new error's innerError attribute, so that the original message and stack will be logged on the server.
    throw new SystemError("Couldn't perform database read", e);
}
```

## Information about the error on the client side

Errors thrown by graphQL have an `extensions` attribute, containing two keys: `diagnoses` and `code`.

-   The `code` key is the type of the error: 'business-rule-error', 'data-input-error', 'authorization-error', 'system-error' or 'logic-error'.
-   The `diagnoses` key contains an array of more specific problems that arose server-side.

```json
"errors": [
    {
        "extensions": {
            "code": "business-rule-error",
            "diagnoses": [
                {
                    "message": "The type and amount discount are mandatory when a discount date is entered.",
                    "path": [],
                    "severity": 3
                },
                {
                    "message": "The discount amount must be less than 100%.",
                    "path": [],
                    "severity": 3
                }
            ]
        },
        "locations": [
            {
                "column": 14,
                "line": 4
            }
        ],
        "message": "Validation failed",
        "path": ["xtremMasterData", "paymentTerm", "create"]
    }
]
```
