PATH: XTREEM/2+GraphQL+queries/4+Lookups

# Lookups

The `lookups` GraphQL field returns a list of eligible candidates for a node's reference property.

Let's consider a node `SalesOrder` having a reference property `customer`, of type `Customer`.
The goal of the `lookups` field is to retrieve all eligible instances of `Customer` for `SalesOrder.customer`.
An eligible instance is one that satisfies both the optional `lookup` and `control` filters specified in the definition of `customer` inside `SalesOrder`.

## Lookups syntax

The 10 first eligible candidates for the reference property `customer` of the node `SalesOrder` with id = 43 can be obtained with the following syntax:

```graphql
{
    aPackage {
        salesOrder {
            lookups(id: 43) {
                customer(first: 10) {
                    edges {
                        node {
                            id
                            billingAddress
                        }
                    }
                    pageInfo
                    totalCount
                }
            }
        }
    }
}
```

Instead of providing an id to the lookups field, a data object containing a subset of the node `SalesOrder` properties can also be provided.
The returned list will then contain eligible instances of `Customer` for an instance of `SalesOrder` created with the provided data.
For instance, the following query will return all possible instances of `Customer` for the property `customer` of an instance of `SalesOrder` having `orderCategory` set to 10 and `orderCurrency` to 'EUR':

```graphql
{
    aPackage {
        salesOrder {
            lookups(data: {orderCategory: 10, orderCurrency: 'EUR'}) {
                customer {
                    edges {
                        node {
                            id
                            billingAddress
                        }
                    }
                    pageInfo
                    totalCount
                }
            }
        }
    }
}
```
