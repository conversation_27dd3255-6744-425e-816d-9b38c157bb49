PATH: XTREEM/2+GraphQL+queries/7+getDuplicate

# getDuplicate

The `getDuplicate` GraphQL field returns a copy of a node with \_id values negated in order to create new records
based on the original.

Any properties with a duplicatedValue defined, will return the value returned from duplicatedValue instead of
copying the property value as is.

## getDuplicate syntax

```graphql
query {
    xtremShowCase {
        showCaseCountry {
            getDuplicate(_id: "1") {
                _sourceId
                _id
                code
                phoneCountryCode
                name
                _createStamp
                _updateStamp
            }
        }
    }
}
```

The example above returns something like this:

```graphql
{
    "data": {
        "xtremShowCase": {
            "showCaseCountry": {
                "getDuplicate": {
                    "_sourceId": "",
                    "_id": "-1",
                    "code": "ES",
                    "phoneCountryCode": 34,
                    "name": "Spain",
                    "_createStamp": "2022-06-13T10:41:43.000Z",
                    "_updateStamp": "2022-06-13T10:41:43.000Z"
                }
            }
        }
    },
    "extensions": {
        "diagnoses": []
    }
}
```
