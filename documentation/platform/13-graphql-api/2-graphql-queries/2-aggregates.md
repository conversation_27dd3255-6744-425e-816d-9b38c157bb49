PATH: XTREEM/2+GraphQL+queries/2+Aggregates

# Aggregates

The `readAggregate` node can contain a list of properties to aggregate.

```graphql
{
    x3Sales {
        salesOrder {
            readAggregate(filter: "...") {
                orderDate {
                    min
                    max
                }
                amount {
                    sum
                }
                discount {
                    avg
                }
                customer {
                    code {
                        distinctCount
                    }
                }
            }
        }
    }
}
```

## Aggregators (min, max, avg, sum, distinctCount, hasNull)

The aggregator(s) to apply to the property can be:

-   _min_: returns the `minimum` property value for all the records that match the (optional) filters.
-   _max_: returns the `maximum` property value for all the records that match the (optional) filters.
-   _avg_: returns the `average` property value for all the records that match the (optional) filters.
-   _sum_: `sums` the property values for all the records that match the (optional) filters.
-   _distinctCount_: returns the number of different property values for all the records that match the (optional) filters.

All the aggregations must be declared in the _{ readAggregate { ... } }_ node.

Aggregations can be bound to properties from the node itself (here _orderDate, amount, discount_) or to values from references (here _customer.code_). Many aggregators can be applied to a property (`min`, `max` are applied to _orderDate_ in the example)

The previous query will return a **single** result that will look like :

```json
{
    "x3Sales": {
        "salesOrder": {
            "readAggregate": {
                "orderDate": {
                    "min": "2017-05-07",
                    "max": "2020-02-03"
                },
                "amount": {
                    "sum": 5
                },
                "discount": {
                    "avg": 2
                },
                "customer": {
                    "code": {
                        "distinctCount": 7
                    }
                }
            }
        }
    }
}
```

The list of available aggregators depends on the type of the property:
property type | distinctCount | min | max | sum | avg | hasNull |
|:------------|:-------------:|:---:|:---:|:---:|:---:|:-------:|
|boolean | X | X | X | - | - | X |
|numbers (integer, short, decimal, ...) | X | X | X | X | X | X |
|string | X | X | X | - | - | X |
|date/datetime | X | X | X | - | - | X |
|enums | X | X | X | - | - | X |
|uuid | X | - | - | - | - | X |
|streams (binary/text) | - | - | - | - | - | X |

## GroupBy

### GroupBy syntax

The node `queryAggregate/edges/node/group` can contain a list of properties to group by.

```graphql
{
    x3Sales {
        salesOrder {
            queryAggregate(filter: "...") {
                edges {
                    node {
                        group {
                            customer {
                                code
                            }
                            orderDate(by: month)
                        }
                    }
                    cursor
                }
                pageInfo
            }
        }
    }
}
```

The `by` parameter can have multiple values:

-   _value_: (default) will group by the value of the property.
-   _year_: `for dates/datetime only` will group by the year of the date/datetime (the grouped-by value will be the first day of the year, sth like 2020/01/01).
-   _month_: `for dates/datetime only` will group by the month of the date/datetime (the grouped-by value will be the first day of the month, sth like 2020/08/01)
-   _day_: `for dates/datetime only` will group by the day of the date/datetime.

Aggregations can be bound to properties from the node itself (here _orderDate_) or to values from references (here _customer.code_).

The previous query will return a **list** of results that will look like :

```json
{
   "x3Sales": {
        "salesOrder": {
            "queryAggregate": {
                "edges": [
                    {
                        "node": {
                            "group": {
                                "customer": {
                                    "code": "CUST_1"
                                },
                                "orderDate": "2019-02-01"
                            }
                        },
                        "cursor": "XXXX"
                    },
                    {
                        "node": {
                            "group": {
                                "customer" {
                                    "code": "CUST_1"
                                },
                                "orderDate": "2019-03-01"
                            }
                        },
                        "cursor": "YYYY"
                    },
                    ...
                ],
                "pageInfo" {
                    "startCursor": "XXXX",
                    "endCursor": "ZZZZ",
                    "hasNextPage": false,
                    "hasPreviousPage": false
                }
            }
        }
    }
}
```

### GroupBy and min/max/sum/... aggregators

_min/max/...._ aggregators can be applied to groups:

```graphql
{
    x3Sales {
        salesOrder {
            queryAggregate(filter: "...") {
                edges {
                    node {
                        group {
                            orderDate(by: month)
                        }
                        values {
                            amount {
                                sum
                            }
                        }
                    }
                }
            }
        }
    }
}
```

will return the total amount of _sales orders_, grouped by month :

```json
{
    "x3Sales": {
        "salesOrder": {
            "queryAggregate": {
                "edges": [
                    {
                        "node": {
                            "group": {
                                "orderDate": "2019-02-01"
                            },
                            "values": {
                                "amount": {
                                    "sum": 158230.78
                                }
                            }
                        }
                    },
                    {
                        "node": {
                            "group": {
                                "orderDate": "2019-03-01",
                            },
                            "values": {
                                "amount": {
                                    "sum": 538984.4
                                }
                            }
                        },
                    },
                    ...
                ]
            }
        }
    }
}
```
