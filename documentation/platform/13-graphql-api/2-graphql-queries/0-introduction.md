PATH: XTREEM/13+GraphQL+API/2+GraphQL+queries

# GraphQL queries

This section describes the API provided by the framework to write GraphQL queries.

## Query Filters

You can filter a query with a `filter` option. For example:

```graphql
{
    xtremAuthorization {
        activity {
            query(filter: "{name: 'purchaseOrderManagement'}") {
                edges {
                    node {
                        name
                        description
                        permissions
                    }
                }
            }
        }
    }
}
```

Filters support the following operators:

| Operator  | Description                                           |
| --------- | ----------------------------------------------------- |
| \_eq      | equal to                                              |
| \_ne      | not equal to                                          |
| \_lt      | less than                                             |
| \_lte     | less than or equal                                    |
| \_gt      | greater than                                          |
| \_gte     | less than or equal                                    |
| \_in      | in (an array of values)                               |
| \_nin     | not in (an array of values)                           |
| \_regex   | matches a regular expression                          |
| \_options | regular expression options ('i' for case independent) |
| \_mod     | arithmetic modulo (value % args[0] === args[1])       |
| \_and     | logical and of an array of filters                    |
| \_or      | logical or of an array of filters                     |
| \_not     | logical not of a filter                               |
| \_fn      | function filter                                       |

## Query Sort

You can sort a query with an `orderBy` option. For example:

```graphql
query {
    xtremInventory {
        stockJournal {
            query(orderBy: "{ item: 1, location: 1, lot: 1, sequence: 1 }") {
                edges {
                    node {
                        item {
                            id
                        }
                        location {
                            id
                        }
                        lot {
                            id
                        }
                        sequence
                        quantityInStockUnit
                        activeQuantityInStockUnit
                    }
                }
            }
        }
    }
}
```

The leaf properties of the `orderBy` object can take two values: `+1` (ascending) or `-1` (descending).

For properties that reference another node, you can specify an order on one or more properties of the referenced node.

## Query first

You can limit the number of nodes returned by a query with a `first` option. For example:

```graphql
{
    xtremAuthorization {
        activity {
            query(first: 50, filter: "{name: 'purchaseOrderManagement'}") {
                edges {
                    node {
                        name
                        description
                        permissions
                    }
                }
            }
        }
    }
}
```

- If first is omitted then Xtrem automatically limits the number of nodes to 20.
- If first is set with value -1 Xtrem automatically uses the maximum number of nodes per page as a limitation:
    - By default the maximum number of nodes per page is set to 10000,
    - The maximum number of nodes per page can be configured in xtrem-config.yml using the graphql maxNodesPerPage parameter.

```yml
graphql:
    maxNodesPerPage: 20000
```
