PATH: XTREEM/2+GraphQL+queries/6+Nested+Collections+and+GraphQL+queries

# Nested Collections and GraphQL queries

Filters on nested collections support the following quantifiers:

| Quantifier | Description                                      |
| ---------- | ------------------------------------------------ |
| \_atLeast  | At least N elements have to match the condition  |
| \_atMost   | At most N elements have to match the condition   |
| \_none     | None of the elements have to match the condition |
| \_every    | Every elements have to matche the condition      |

Let's imagine we have the following families:

-   TestGrandParent#1

    -   Parent#1.1 - age: 31

        -   Child#1.1.1 - age: 1
        -   Child#1.1.2 - age: 2
        -   Child#1.1.3 - age: 3

    -   Parent#1.2 - age: 32

        -   Child#1.2.1 - age: 1
        -   Child#1.2.2 - age: 2
        -   Child#1.2.3 - age: 3
        -   Child#1.2.4 - age: 4

    -   Parent#1.3 - age: 33
        -   Child#1.3.1 - age: 1
        -   Child#1.3.2 - age: 2
        -   Child#1.3.3 - age: 3
        -   Child#1.3.4 - age: 4
        -   Child#1.3.5 - age: 5

-   TestGrandParent#2

    -   Parent#2.1 - age: 31

        -   Child#2.1.1 - age: 1
        -   Child#2.1.2 - age: 2
        -   Child#2.1.3 - age: 3
        -   Child#2.1.4 - age: 4

    -   Parent#2.2 - age: 32
        -   Child#2.2.1 - age: 1

-   TestGrandParent#3
    -   Parent#3.1 - age: 31

## \_atLeast

The quantifier \_atLeast makes is possible to specify the minimum number of children which have to match the condition

Imagine that we want to obtain the list of grand parents that have at least 2 parents which are 32 years old or older:

The graphql query will be:

```graphql
{
    testFilterNestedGrandParent {
        query(filter: "{ children: { _atLeast: 2, age: { _gte: 32 } } }") {
            edges {
                node {
                    text
                    children {
                        query {
                            edges {
                                node {
                                    text
                                    children {
                                        query {
                                            edges {
                                                node {
                                                    text
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

The result is:

```ts
[
    {
        text: 'TestGrandParent#1',
        children: [
            {
                text: 'Parent#1.1',
                children: ['Child#1.1.1', 'Child#1.1.2', 'Child#1.1.3'],
            },
            {
                text: 'Parent#1.2',
                children: ['Child#1.2.1', 'Child#1.2.2', 'Child#1.2.3', 'Child#1.2.4'],
            },
            {
                text: 'Parent#1.3',
                children: ['Child#1.3.1', 'Child#1.3.2', 'Child#1.3.3', 'Child#1.3.4', 'Child#1.3.5'],
            },
        ],
    },
];
```

## \_atMost

The quantifier \_atMost makes is possible to specify the maximum number of children which can match the condition

Imagine that we want to obtain the list of grand parents that have at most 1 parent which is 32 years old or older:

The graphql query will be:

```graphql
{
    testFilterNestedGrandParent {
        query(filter: "{ children: { _atMost: 1, age: { _gte: 32 } } }") {
            edges {
                node {
                    text
                    children {
                        query {
                            edges {
                                node {
                                    text
                                    children {
                                        query {
                                            edges {
                                                node {
                                                    text
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

The result is:

```ts
[
    {
        text: 'TestGrandParent#2',
        children: [
            {
                text: 'Parent#2.1',
                children: ['Child#2.1.1', 'Child#2.1.2', 'Child#2.1.3', 'Child#2.1.4'],
            },
            {
                text: 'Parent#2.2',
                children: ['Child#2.2.1'],
            },
        ],
    },
    {
        text: 'TestGrandParent#3',
        children: [
            {
                text: 'Parent#3.1',
                children: [],
            },
        ],
    },
];
```

## \_none

The quantifier \_none makes is possible to specify that no child should match the condition

> ⚠️ **[WARNING]**
> On a logical point of view it is right to say that no element of an empty collection matches the condition set in the filer

Imagine that we want to obtain the list of grand parents that have no parents which are 32 years old:

```graphql
{
    testFilterNestedGrandParent {
        query(filter: "{ children: { _none: true, age: 32 } }") {
            edges {
                node {
                    text
                    children {
                        query {
                            edges {
                                node {
                                    text
                                    children {
                                        query {
                                            edges {
                                                node {
                                                    text
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

The result is:

```ts
[
    {
        text: 'TestGrandParent#3',
        children: [
            {
                text: 'Parent#3.1',
                children: [],
            },
        ],
    },
];
```

## \_every

The quantifier \_none makes is possible to specify that all children should match the condition

> ⚠️ **[WARNING]**
> On a logical point of view it is right to say that all elements of an empty collection match the condition set in the filer

Imagine that we want to obtain the list of grandparents whose children are all 31 years old

```graphql
{
    testFilterNestedGrandParent {
        query(filter: "{ children: { _every: true, age: 31 } }") {
            edges {
                node {
                    text
                    children {
                        query {
                            edges {
                                node {
                                    text
                                    children {
                                        query {
                                            edges {
                                                node {
                                                    text
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

The result is:

```ts
[
    {
        text: 'TestGrandParent#3',
        children: [
            {
                text: 'Parent#3.1',
                children: [],
            },
        ],
    },
];
```
