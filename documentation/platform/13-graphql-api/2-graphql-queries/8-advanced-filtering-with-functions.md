PATH: XTREEM/2+GraphQL+queries/8+Advanced+filtering+with+functions

# Advanced query filtering with functions

## \_fn filter operator

The \_fn filter operator lets you specify advanced filters, like filters that compare the values of several properties.

For example, you can query the sales orders that have their delivery date more than 10 days after their order date with:

```graphql
{
    xtremSales {
        salesOrder {
            query(filter: "{ _fn: 'this.deliveryDate.value > this.orderDate.addDays(10).value' }") {
                edges {
                    node {
                        number
                    }
                }
            }
        }
    }
}
```

The value of the `_fn` filter is a JavaScript expression that can be converted to SQL. It is passed as a string. In this expression, `this` represents the node that is being queried, `salesOrder` in our example.

Note: These expressions are not converted to JavaScript functions, they are converted to SQL. So they can be written without any `await` keywords.

See https://confluence.sage.com/display/XTREM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions
for details on TypeScript to SQL conversions
