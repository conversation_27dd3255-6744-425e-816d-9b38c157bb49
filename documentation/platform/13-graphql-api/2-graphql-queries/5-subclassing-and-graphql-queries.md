PATH: XTREEM/2+GraphQL+queries/5+Subclassing+and+GraphQL+queries

# Subclassing and GraphQL queries

Let's imagine we have the following hierarchy:

```
                +------> Animal <-----+
                |                     |
    +----->  Mammal <-----+          <PERSON>
    |                     |
   Dog                   Cat
```

`Animal` and `Mammal` are abstract.

Every node defines a specific string property:
Node | Property
|:---|:---
Animal | strFromAnimal
Fish | strFromFish
Mammal | strFromMammal
Dog | strFromDog
Cat | strFromCat

## Querying final nodes (Dog/Cat/Fish)

No matter whether a node is deriving from another node, the GraphQL queries will still look the same.

For instance, to query all the _dogs_:

```graphql
{
    dog {
        query {
            totalCount
            edges {
                node {
                    strFromAnimal
                    strFromMammal
                    strFromDog
                }
            }
        }
    }
}
```

`Note`: As you can see, the query can not only invoke properties from `Dog` node but also from `Mammal` and `Animal` nodes.

## Querying abstract nodes (Animal/Mammal)

Querying an abstract node works exactely the same way:

```graphql
{
    mammal {
        query {
            totalCount
            edges {
                node {
                    strFromAnimal
                    strFromMammal
                }
            }
        }
    }
}
```

## Querying abstract nodes (Animal/Mammal) with properties from final nodes

The following query will return all the _animals_:

-   for every _animal_, we will query some basic properties (_\_constructor_, _\_id_, _strFromAnimal_),
-   if the _animal_ is a `Mammal`, we will query the _strFromMammal_ property as well and check the final class:
    -   if it's a `Dog`, then the _strFromDog_ property will be returned
    -   if it's a `Cat`, then the _strFromCat_ property will be returned
-   if the _animal_ is a `Fish`, we will get the _strFromFish_ property.

Note: the _\_constructor_ property will return the name of the final class (here, `Dog`, `Cat` or `Fish`).

```graphql
{
    animal {
        query {
            totalCount
            edges {
                node {
                    _constructor
                    _id
                    strFromAnimal
                    ... on Mammal {
                        strFromMammal
                        ... on Dog {
                            strFromDog
                        }
                        ... on Cat {
                            strFromCat
                        }
                    }
                    ... on Fish {
                        strFromFish
                    }
                }
            }
        }
    }
}
```

The result will look like:

```json
"animal": {
    "query": {
        "totalCount": 15,
        "edges": [
            {
                "node": {
                    "_constructor": "Fish",
                    "_id": "1",
                    "strFromAnimal": "....",
                    "strFromFish": "...."
                }
            },
            {
                "node": {
                    "_constructor": "Dog",
                    "_id": "6",
                    "strFromAnimal": "....",
                    "strFromMammal": "....",
                    "strFromDog": "...."
                }
            },
            {
                "node": {
                    "_constructor": "Cat",
                    "_id": "11",
                    "strFromAnimal": "....",
                    "strFromMammal": "....",
                    "strFromCat": "...."
                }
            }
        ]
    }
}
```
