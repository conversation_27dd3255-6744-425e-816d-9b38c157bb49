PATH: XTREEM/2+GraphQL+queries/3+getDefaults

# getDefaults

The `getDefaults` GraphQL field returns the default values defined on the properties of a node.
It will also default any non-nullable references for which there is only one active record that can be referenced.

## getDefaults syntax

```graphql
{
    xtremSystem {
        user {
            getDefaults {
                email
                firstName
                lastName
                isActive
                photo {
                    value
                }
            }
        }
    }
}
```

You can provide data as an input to the request. This data will be used for any property that might have a dependency on other property value to derive it's own default value. If a value is supplied for a property in the data, it's own default value will not be evaluated, and the request response will have the value of the data passed in.

```graphql
{
    xtremSystem {
        user {
            getDefaults(data: { isActive: false }) {
                email
                firstName
                lastName
                isActive
                photo {
                    value
                }
            }
        }
    }
}
```
