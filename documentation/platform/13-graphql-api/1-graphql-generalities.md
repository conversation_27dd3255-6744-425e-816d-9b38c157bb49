PATH: XTREEM/13+GraphQL+API/1+GraphQL+generalities

# GraphQL generalities

## Introduction

XTReeM uses [GraphQL](https://graphql.org/learn/) as its API standard. The transactional data model of Xtrem is represented by a queryable graph.
GraphQL is an API query language and a server-side runtime for executing queries by using a type system.

### Schemas

In GraphQL the data model is organized into schemas where the entities are interlinked by the edges of the nodes.

The XTReeM framework has two separate schemas:

-   **Transactional data schema:** It stores the transactional business data that can be consumed by external and internal systems. It is exposed to https://_your_address_/api/ (eg: `http://xtrem-xxx.sagex3.com:8124/api/`)

-   **Metadata schema:** This schema exposes data that is not part of the transactional model such as a list of available pages, translation, installed packages, and their versions. It is exposed to https://_your_address_/metadata (eg: `http://xtrem-xxx.sagex3.com:8124/metadata/`)

Both schemas come with [Graph**i**QL](https://github.com/graphql/graphiql) enabled which is a graphical interactive in-browser GraphQL IDE. It allows users to explore the schema, construct queries and execute them. It comes with syntax highlighting, intelligent type ahead of fields, and argument types.

## Transactional data schema

The transactional data schema exposes the queries and mutation required to transact on business data via the `api` endpoint. the `api` endpoint is used by the client side to transact with the server side via graphql requests. The transactional data schema is compiled from the published nodes, node properties, and node operations.

### `_access` field

The `_access` field is available as a selection on the node level of the graphql request. This field will return the restricted properties and operations of the node in the format of an array of access bindings

```json
_access: [
    { name: '$create', status: 'unauthorized' },
    { name: '$update', status: 'unauthorized' },
    { name: '$delete', status: 'unauthorized' },
    { name: '$import', status: 'unauthorized' },
    { name: 'code', status: 'unauthorized' },
],
```

-   name: The name of the property or operation the restriction relates to. Operations could be standard CRUD operations (prefixed by `$`) or published custom node operations (query or mutation). Only properties selected in the graphql request will be included in the list of restrictions
-   status: The restricted status of the property or operation. The potentially restricted statuses are;
    -   `unauthorized`: The property or operation is not authorized for the current user
    -   `unavailable`: The property or operation is unavailable (disabled by a service option or access code)
    -   `readonly`: The property is read-only. (Frozen properties are read-only after the record is created)
    -   `inactive`: The operation is inactive. (Nodes that are frozen cannot be updated, therefore the `$update` operation is disabled for these nodes)

## Metadata schema

The metadata schema exposes non-transactional data about the current Xtrem installation. This schema is mostly used by the framework itself, but some parts might be used by other users or systems, such as the version number information query. Unlike the transactional data schema, this schema is not dynamic, it is the same across all installations, except of course the data it holds.

This page discusses the queries of this schema and how this information is used across the system.

### Installed Packages (installedPackages)

This query returns a list of all XTreeM application packages that are loaded into the current installation with their version numbers. This query can be used for checking for updates and compatibility.

### Strings (strings)

This query holds a list of string literal translations. The language of the strings depends on the `Accept-language` HTTP header. It is mostly used by the client-side rendering engine. For example, it can be used by native mobile applications to retrieve string literals for their screens.
The list returned can be filtered with a `starts-with` filter applied to the string key.

### Pages (pages)

This query holds the following properties related to the pages rendered by the client framework engine:

| property     | description                                                              |
| ------------ | ------------------------------------------------------------------------ |
| key          | Key of the page in the form of `package-name/PageClassName`              |
| title        | Title from the page decorator                                            |
| content      | Javascript source code of the page                                       |
| category     | Category of the page                                                     |
| extensions   | List of javascript source code of the extensions of the page             |
| plugins      | List of plugins used by that page                                        |
| strings      | List of resolved string literals for the current locale                  |
| accessRights | List of `{ activity, permissions }` of the page                          |
| access       | The access status of properties and actions as a list of access bindings |
| pageAccess   | The page access status                                                   |

#### Access status

Access status is a string that defines the availability of the field. It can be:

-   `unavailable`: it belongs to a package that is not active or is controlled by a service option that is not active in the current tenant.
-   `unauthorized`: it is available but the current user is not authorized for the operation.
-   `authorized`: access granted
-   `inactive`: operation is inactive

#### Access bindings

| property | description                                                                                                  |
| -------- | ------------------------------------------------------------------------------------------------------------ |
| node     | Node name for the binding in the form of `package-name/ClassName`                                            |
| bindings | List of object `{ name, status }` where name is a property or operation name and status is its access status |

### Stickers (stickers)

The source code of the stickers which are rendered by the client framework engine.

### Permissions (permissions)

This query returns a list of permissions per activity, for the page passed in.

### About (about)

This query returns the version properties of the application and the root package

application and root implement the following properties:

| property    | description                                                                                                  |
| ----------- | ------------------------------------------------------------------------------------------------------------ |
| name        | The package's name set in the package's package.json file                                                    |
| version     | The package's version set in the package's package.json file. Version is bumped by the release patch process |
| author      | The package's author set in the package's package.json file                                                  |
| description | The package's description set in the package's package.json file                                             |
| license     | The license                                                                                                  |
| buildStamp  | Date the version was created (ISO format). This property is automatically set by Azure pipelines.            |

```graphql
{
    about {
        application {
            name
            version
            author
            description
            license
            buildStamp
        }
        root {
            name
            version
            author
            description
            license
            buildStamp
        }
    }
}
```

The response

```graphql
{
  "data": {
    "about": {
      "application": {
        "name": "@sage/xtrem-intacct-gateway",
        "version": "10.0.4",
        "author": "sage",
        "description": "intacct Gateway",
        "license": "UNLICENSED",
        "buildStamp": "2021-08-04T08:42:27.832Z"
      },
      "root": {
        "name": "@sage/xtrem-root",
        "version": "10.0.4",
        "author": "",
        "description": "Umbrella project for Xtrem development",
        "license": "UNLICENSED",
        "buildStamp": "2021-08-04T08:42:27.828Z"
      }
    }
  }
}
```

### Node localization (getNodeDetails)

This query returns the localization of the node and its properties.

| property   | description                            |
| ---------- | -------------------------------------- |
| name       | The name of the node                   |
| title      | The localization of the node           |
| properties | List of node properties name and title |

```graphql
{
    getNodeDetails(nodeName: "Account") {
        name
        title
        properties {
            name
            title
        }
    }
}
```

The response (according to locale)

```graphql
{
  "data": {
    "getNodeDetails": {
      "name": "Account",
      "title": "Account",
      "properties": [
        {
          "name": "id",
          "title": "ID"
        },
        {
          "name": "chartOfAccount",
          "title": "Chart of accounts"
        },
        {
          "name": "isActive",
          "title": "Is active"
        },
        {
          "name": "name",
          "title": "Name"
        },
        {
          "name": "composedDescription",
          "title": "Composed description"
        },
        {
          "name": "isDirectEntryForbidden",
          "title": "Is Direct entry forbidden"
        },
        {
          "name": "isControl",
          "title": "Is Control"
        },
        {
          "name": "attributeTypes",
          "title": "Attribute types"
        },
        {
          "name": "dimensionTypes",
          "title": "Dimension types"
        },
        {
          "name": "taxManagement",
          "title": "Tax management"
        },
        {
          "name": "intacctId",
          "title": "Sage Intacct ID"
        },
        {
          "name": "recordNo",
          "title": "Record number"
        },
        {
          "name": "statusIntacct",
          "title": "Status Sage Intacct"
        },
        {
          "name": "isRequiredLocation",
          "title": "Is Required location"
        },
        {
          "name": "isRequiredItem",
          "title": "Is Required item"
        },
        {
          "name": "isRequiredCustomer",
          "title": "Is Required customer"
        },
        {
          "name": "isRequiredSupplier",
          "title": "Is Required supplier"
        },
        {
          "name": "isRequiredProject",
          "title": "Is Required project"
        },
        {
          "name": "isRequiredEmploye",
          "title": "Is Required employe"
        },
        {
          "name": "isRequiredDepartement",
          "title": "Is Required departement"
        },
        {
          "name": "isRequiredClass",
          "title": "Is Required class"
        },
        {
          "name": "frpKey",
          "title": "FRP key"
        },
        {
          "name": "frpAccountType",
          "title": "FRP account type"
        }
      ]
    }
  }
}
```

### Request timeout

When executing a graphql mutation or query, a timeout is required for the request, so that it won't block server resources. To control the length of time a request is allowed to run, a `timeLimitInSeconds` attribute was added to the **Context** class, a read-only number representing the amount in `seconds`.

The default timeout is 28 seconds. When the timeout expires, a `Request Timeout` error is thrown.
