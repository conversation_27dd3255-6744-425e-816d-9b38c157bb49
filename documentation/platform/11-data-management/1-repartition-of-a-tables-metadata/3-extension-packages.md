PATH: XTREEM/1+Repartition+of+metadata/3+Extension+packages

# Extension packages

The metadata of the table is extended in possibly several extension packages.

Both the base package and the extension packages contain a file called TABLE.json in their folder data/schema.
In the extension packages, only the new or modified meta-elements are listed there.

In the extension packages, the json file contains a property isExtension set to true.
In the base package, the isExtension flag is either absent from the file or set to false.

## Example

In xtrem-services/@sage/xtrem-inventory/data/schema/, there is no file named country.json.
This means that xtrem-inventory doesn't touch Country's metadata.

In xtrem-services/@sage/xtrem-structure/data/schema/, there is a file country.json. As there is no flag isExtension set to true in this file, xtrem-structure is the base package of Country.

xtrem-master-data/data/schema contains a file country.json file, with isExtension set to true. xtrem-master-data is thus an extension package of the table Country.

xtrem-master-data adds to the Country table just one column named currency.

We can now visualize the state of the metadata of Country in each package in the package dependency graph.
In each package, Country has the columns defined before in the graph, plus the columns added by the current package if any.

![country-columns](assets/images/country-columns.png)

If we now add a custom package my-custom-package that depends on xtrem-master-data, the table Country will be inherited in my-custom-package with all the 4 columns added before in this graph. The new custom package can add its own new columns to Country if necessary.

> ⚠️ **[IMPORTANT]**
> An extension package can only affect a table's metadata by adding indexes or columns to a table.
> It cannot modify indexes or columns defined in another package.
> It can however modify the node and node properties (see Nodes and Node extension sections from this documentation).
