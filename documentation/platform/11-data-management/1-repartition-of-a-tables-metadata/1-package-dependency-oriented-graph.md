PATH: XTREEM/1+Repartition+of+metadata/1+Package+dependency+oriented+graph

# Package dependency oriented graph

The xtrem-services packages dependencies with each other form an oriented graph.

For instance:

-   xtrem-structure depends on xtrem-system.
-   xtrem-master-data depends on xtrem-structure.
-   xtrem-inventory depends on xtrem-master-data.
-   Both xtrem-purchasing and xtrem-technical-data depend on xtrem-inventory.
-   xtrem-manufacturing depends on xtrem-purchasing and xtrem-technical-data.

This leads to the following mini-dependency graph:

![package-dependency-graph](assets/images/package-dependency-graph.png)
