PATH: XTREEM/1+Repartition+of+metadata/2+Base+package+for+a+given+table

# Base package for a given table

Each table has one and only one base package, where its metadata is first defined.

The base package of a table is the first package defining the table in the oriented graph just described.

## Example

The table Country is first defined in its base package xtrem-structure.

In xtrem-structure, let's imagine for the sake of this document that Country is very simple and has 3 columns only.

xtrem-structure.Country:

| id  | name | continent |
| --- | ---- | --------- |

This structure is defined in the file : xtrem-services/@sage/xtrem-structure/data/schema/country.json

As the package xtrem-system comes before strem-structure in the dependency graph, we can consider that in xtrem-system, the table Country doesn't exist.
If someone installs only xtrem-system but not strem-structure, the table Country won't exist at all in their environment.

For all the packages coming after the base package strem-structure in the graph, the table will, however, exist.
For instance, if one tries to install xtrem-inventory alone, they will also install all of its dependencies along with it, including xtrem-structure: the table Country will thus be present in their environment.
