PATH: XTREEM/2+Repartition+of+data/3+Data+distribution+among+layers

# Data distribution among layers

## With layers, allow several versions of the same record

Layers are used to have several possible versions of the same record.

For instance, let's consider an example around the Company table. Company's base package is xtrem-system.

Let's imagine that the following company is introduced in the package xtrem-system, in a layer called layer1 :

| id  | name    | legislation |
| --- | ------- | ----------- |
| 1   | Parsley | FRA         |

This file is called company.csv and stored in the folder xtrem-system/data/layers/layer1.

In the same package, there are two tests: one that can use the exact company "Parsley" as defined above, and one that needs a similar test set, but with legislation set to USA.

We need to create test data for the second test.

-   One solution would be to duplicate the test set and create a second company "Thyme" in the same file with id 2, with the desired characteristics. But sometimes, the test set is more complex than a single table with only 3 columns, and it can get hard to duplicate it.

    | id  | name    | legislation |
    | --- | ------- | ----------- |
    | 1   | Parsley | FRA         |
    | 2   | Thyme   | USA         |

-   Another solution is to use layers: create a new csv file in another layer called layer2. Call it company.csv and store it in the folder xtrem-system/data/layers/**layer2**. In this file, the record with id 1 (Parsley) can be redefined in accordance with the needs of the second test:

    | id  | name    | legislation |
    | --- | ------- | ----------- |
    | 1   | Parsley | **USA**     |

If the first solution is too complex to implement, for instance if the "Parsley" company is referenced by other records in other csv files, the second solution can be implemented.

Then, the only thing left to do is to specify which layer must be used in which test. In test 1, it will be layer1. In test 2, layer2.

> ⚠️ **[IMPORTANT]**
>
> -   Existing records can be completely overriden, but only in another layer than the one where they were initially defined.
> -   A record cannot be created twice in the same layer.
>
> For instance:
>
> -   If the record was defined in package1/data/layers/layer1, it can not be redefined anywhere in layers/layer1.
> -   If a package package2 needs to redefine it, it has to be in another layer layer2, in package2/data/layers/layer2.

## Different layer lists produce different databases

With this test data:

-   A test running with layer **"layer1"** will have the Parsley company's legislation set to **FRA**.
-   A test running with layer **"layer2"** will have the Parsley company's legislation set to **USA**.

And as each layer provided in the list overrides the previous one when building the database:

-   A test running with layers **"layer1,layer2"** will have the Parsley company's legislation set to **USA** because layer2 will override layer1.
-   A test running with layers **"layer2,layer1"** will have the Parsley company's legislation set to **FRA** because layer1 will override layer2.

> ⚠️ **[IMPORTANT]**
> The layers order provided to the framework to load the database matters. Each layer provided in the list overrides the previous one.

## A more complex example

If a given record is defined in data/layers/mylayer of a package, if complemented in an extension package, the complement must be defined in the same layer in the extension package : data/extension-layers/mylayer.

Let's consider, like above, the Company table.

-   Company's base package is xtrem-system.
-   Like for the Country table, xtrem-master-data is an extension package for the Company table, and also complements it with a column called currency.

Let's imagine that we have, in xtrem-system, in layer1 :

| id  | name    | legislation |
| --- | ------- | ----------- |
| 1   | Parsley | FRA         |

And in layer2:

| id  | name    | legislation |
| --- | ------- | ----------- |
| 1   | Parsley | **USA**     |

Then, xtrem-master-data needs to complement both versions of "Parsley" in their respective layers:

**xtrem-master-data, extension-layers/layer1:**

| id  | currency |
| --- | -------- |
| 1   | Euro     |

**xtrem-master-data, extension-layers/layer2:**

| id  | currency |
| --- | -------- |
| 1   | Dollar   |

Then:

-   Tests that need "Parsley" to have legislation set to FRA need to be run on a database generated with data from layer1.
-   Tests that need "Parsley" to have legislation set to USA need to be run on a database generated with layer2.

> ⚠️ **[IMPORTANT]**
>
> -   If a given record is defined in data/layers/mylayer of a package, if complemented in an extension package, the complement must be defined in the same layer in the extension package : data/extension-layers/mylayer.
> -   Note that overriding records is allowed, but not recommended, and should be avoided when possible. A record can be linked to many other records, and changing it could lead to incoherencies in the resulting database.
