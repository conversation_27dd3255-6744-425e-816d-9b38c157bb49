PATH: XTREEM/2+Repartition+of+data/2+Data+distribution+among+packages

# Data distribution among packages

## For a given table, in which packages can new test records be created

New test records for a table can be created in any package where the table exists.
Consider our previous example: the table Country.
No test records can be created in the package xtrem-system, because the table doesn't exist there.
New records can however be created in xtrem-structure and all the packages that come after it in the dependency graph.

## For a given table, when creating a new test record, which columns should be included

When creating a new test record for a given table in a given package, all the columns of the table in that package should be present.
A package cannot anticipate which columns will be added by packages that come after in the package dependency graph.

### Example

If one installs only the package xtrem-structure, and not the following packages, they get a very simple version of the table Country with only 3 columns.
Let's imagine a test is included in the package. Its hardcoded test set is the following country :

**xtrem-structure.Country in xtrem-structure:**

| id  | name   | continent |
| --- | ------ | --------- |
| 1   | France | Europe    |

Now, what if one installs xtrem-master-data ?
Inside xtrem-master-data, the table Country has a new column: currency.
Imagine xtrem-master-data also comes with a test.
This test runs on another hardcoded test set: the country Italy.
Inside xtrem-master-data, there will thus be a csv file containing this new test set, that will look like this:

**xtrem-structure.Country complement in xtrem-master-data:**

| id  | name  | continent | currency |
| --- | ----- | --------- | -------- |
| 2   | Italy | Europe    | Euro     |

As xtrem-master-data depends on xtrem-structure:

-   xtrem-structure will also be installed exactly as if it were alone.
-   Data for the Country table will be the aggregation of data found in xtrem-structure and of data found in xtrem-master-data.

We will thus have several test records for Country in this environment: France, coming from xtrem-structure, and Italy, coming from xtrem-system.

## Where should full new records be stored

The path of csv files containing full new records must be of the form: package/data/layers/mylayer/node-title.csv.

-   **package** : Current package where the new record is created.
-   **data/layers** : Those folders will be present and have the same name in all the packages.
-   **mylayer** : The layer of the new data. Layers are presented later in this document.
-   **node-title.csv** : The file is named after the title of the node.

For instance, in our example:

-   xtrem-structure/data/layers/some-layer/country.csv
-   xtrem-master-data/data/layers/some-layer/country.csv

## How does the framework reconstitute full tables from those csv files

For a given table, test records are distributed among possibly many packages.
To build the database, the framework browses through all those packages and gathers the test data.

In the previous example, the framework will build a database where the Country table will be the following:

| id  | name   | continent | currency |
| --- | ------ | --------- | -------- |
| 1   | France | Europe    | -        |
| 2   | Italy  | Europe    | Euro     |

NB : the column currency is empty for the record created in xtrem-structure, because this column does not exist in this package.

> ⚠️ **[IMPORTANT]**
> New test records can be introduced for a given table exclusively:
>
> -   In the table's base package.
> -   In any package dependent from the table's base package.
> -   New test records must be placed in a csv file in a folder data/layers/mylayer. The file must contain all the columns of the table at that stage of the package's dependency graph, including the columns introduced by the current package if any.

## Can an extension package complement a record that was created in another package

Yes. An extension package can complement a record that was defined in a previous package (previous as in the package dependency graph), in a csv file with only the primary key of the table and the new columns added by the extension.

For instance, instead of creating a new test record (Italy), xtrem-master-data could opt for using the existing record (France) created in xtrem-structure. In that case, the framework needs to complement this record (France) with a value for the new column currency, with the following csv file:

| id  | currency |
| --- | -------- |
| 1   | Euro     |

The path to csv files aimed at complementing existing records must be of the form: package/data/extension-layers/mylayer/node-title.csv.
For instance, in our example: xtrem-master-data/data/extension-layers/some-layer/country.csv.

> ⚠️ **[IMPORTANT]**
>
> -   Extension packages can populate the columns they add for records defined previously in the package dependency graph.
> -   Complemented test records must be placed in a csv file in a folder data/extension-layers/mylayer/base-package/.
> -   The file must contain the id column, in order to identify the record to be complemented, and the columns added by the current extension package.

## A note on the id column

For a given record, the id present in the csv file will not necessarily match the id of the record in the generated table.
For instance, in our previous example:

-   There is no guarantee that France will be created with an id equal to 1
-   There is no guarantee that Italy will be created with an id equal to 2

In the process of record complementation described above:

-   The framework will be able to match the record to complement with the record "France" created in xtrem-structure.
-   So "France" will indeed have a currency in the Country table.
-   But the id of "France" in the actual Country table might not be 1.

> ⚠️ **[IMPORTANT]**
>
> -   There is not guarantee that for a given record, the id present in the csv file will match the id of the record in the generated table.
> -   However, in the csv files themselves:
>     -   The id can be used for the record complementation mechanism described just above.
>     -   The id can also be used to reference another record, present in another csv file, from a reference column.

## A note on the id column for setup nodes

The values of the natural key columns are concatenated by pipes (`|`) and used to generate a 53bit numeric hash for the id (53bit numeric hash will be discontinued).

### Example 1:

Suppose there is a `country` table where the 2 character ISO code is the only column in the natural key properties list. There is another table with a column that references the `country` table. In the CSV of this table the `country` column can be simply set to the 2 character ISO code, e.g. `US`. The value `US` to the 53 bit numeric hash value for the id.

If a table has a column that references another table, and this table has an natural key, the value in the CSV file for the reference column
can be a pipe (`|`) separated list of the natural key column values of the target table.

### Example 2:

Now suppose there is a reference column in the natural key of a table, and that referenced table also has an natural key. The value of the reference to the parent table will be a the pipe (`|`) separated value. This value can be entered in the CSV as the the expanded values of the reference column natural key embedded into the pipe separated natural key of the parent table.

There is a `state` table where the natural key consists 2 columns, country and code. The country column is a reference to the `country` table.

In a CSV with a column that references the `state` table, the value `US|CA` is acceptable and will be resolved to the id in the `state` table.
