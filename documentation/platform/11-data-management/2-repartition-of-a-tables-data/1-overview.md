PATH: XTREEM/2+Repartition+of+data/1+Overview

# Overview

Data is stored in csv files inside packages and layers.
The framework offers the possibility to load a new database with this data in the following cases:

-   Before running tests - the loading step is then performed automatically by the framework
-   While developing a package, before starting the application, so that the database is populated beforehand
-   When setting up a new customer, to add basic data such as a list of countries and currencies.

This data is distributed among packages, and, within a package, among different layers.

## Why distribute data among packages

### Ready-to-go demo data

Having data readily available in a package makes it possible to start demoing the code and the interface right after installation.

### Ensure availability of test data

Each test is stored inside the tested package: this allows for the calculation of an estimate of the code coverage.

Each time a package is installed, it comes with its unit tests. Unit tests run on specific data hardcoded in the test.
In order for those tests to be runnable right after installation, the hardcoded test data must also be available, either inside the same package, or in a dependency (meaning a package coming before the current package in the package dependency graph).

### Ensure availability of setup data

In production mode, when a new tenant is initialized, the application needs some setup data to be loaded into the production database right at the beginning.
For instance: countries, currencies, languages...

Currently, the setup data for tables and columns created by a given package are stored inside the same package.

> ⚠️ **[IMPORTANT]**
> The _setup_ layer can only contain data for nodes with an _isSetupNode_ decorator.
> _setup_ data supplied can be protected from update by providing a value for the `_vendor` column. `_vendor` indicates to which software vendor the data belongs to.

## Why distribute data among layers

Within the folders layers and extension-layers, the csv files containing the table's data are distributed among several subfolders called layers.

The main goal of these layers is to allow more flexibility when building the database.

-   The demo data can all be stored in a layer called _demo_.
-   The setup data necessary to initialize a tenant is stored in a layer called _setup_.
-   When running a test, a list of layers can be given to the test as a parameter.
    The database is then built by visiting all the layers in this list, in the order of the list, and ignoring data stored in other layers.

> ⚠️ **[IMPORTANT]**
> If no layers are provided in the definition of an unit test, the default layers used for the test are setup, master-data, documents and test.
