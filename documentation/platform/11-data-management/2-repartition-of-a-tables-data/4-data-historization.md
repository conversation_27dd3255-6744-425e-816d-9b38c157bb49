PATH: XTREEM/2+Repartition+of+data/4+Data+historization

# Data historization

When a date-setting environmental variable is provided to a unit test, the data loading mechanism only loads records that appear to have been created before that date.

To learn more about environmental variables in tests, please visit the following pages:

-   https://confluence.sage.com/display/XTREEM/2+Environmental+variables
-   https://confluence.sage.com/display/XTREEM/4+Provide+environmental+variables+to+GraphQL+tests
-   https://confluence.sage.com/display/XTREEM/3+Provide+environmental+variables+to+Mocha+tests

To allow for this historization feature, data kept in the csv files is historized, meaning that a given record can actually appear twice in the same file, provided that it has different \_update_stamp dates.

## Columns used for historization

Three columns are used for history management : \_create_stamp, \_update_tamp and \_delete_stamp. They are system columns present in each table.

**\_create_stamp:**

-   Is used to mark the creation datetime of a given record.
-   Is present in /layers/\* files
-   Is absent from extension-layers/\* files.

**\_update_stamp:**

-   Contains, for each record, the timestamp of the last update.
-   Is present in /layers/\* files
-   Is present in /extension-layers/\* files

When reconstituting the data to load the database, the framework will use this column to match a given record with its extension data. For this reason, this column is present in all the data files.

**\_delete_stamp:**

-   Is used to mark the deletion datetime of a given record.
-   Is present in /layers/\* files
-   Is absent from extension-layers/\* files.

## Example

Let's imagine the Company table is:

-   Defined in xtrem-system with two applicative columns: **id**, **name**
-   Extended in xtrem-master-data with an applicative column: **address**.

Consider a company, "Parsley", that was:

-   Created on the 2020/04/01 with an address in Paris
-   Modified on the 2020/04/03 with an address in New-York.
-   Deleted on the 2020/04/05.
-   Recreated on the 2020/04/07 with an address in Milan.

To ensure the dataset reflects historization, the csv files for the Company table need to specify the time-stamp system tables mentioned above.

-   xtrem-system/data/layers/**layer1**/company.csv:

    | id  | name    | \_create_stamp               | \_update_stamp               | \_delete_stamp               |
    | --- | ------- | ---------------------------- | ---------------------------- | ---------------------------- |
    | 1   | Parsley | 2020-04-**01**T10:10:10.000Z | 2020-04-**01**T10:10:10.000Z |                              |
    | 1   | Parsley | 2020-04-**01**T10:10:10.000Z | 2020-04-**03**T10:10:10.000Z | 2020-04-**05**T10:10:10.000Z |
    | 1   | Parsley | 2020-04-**07**T10:10:10.000Z | 2020-04-**07**T10:10:10.000Z |                              |

-   xtrem-master-data/data/extension-layers/**layer1**/company.csv:

    | id  | address  | \_update_stamp               |
    | --- | -------- | ---------------------------- |
    | 1   | Paris    | 2020-04-**01**T10:10:10.000Z |
    | 1   | New-York | 2020-04-**03**T10:10:10.000Z |
    | 1   | Milan    | 2020-04-**07**T10:10:10.000Z |

## Loading the database at a certain timestamp

When the framework loads data into the tables, it loads the data at a given moment in time, that we call t.

Then, for a given record:

-   It reads the version of this record with highest \_update_stamp, but with \_update_stamp < t. This is the most recent version at the considered timestamp.
-   It checks that \_delete_stamp is either empty or after t, meaning the record has not been deleted (yet).
-   It loads the record in the database.

In our example:

-   If t=2020/03/31, Parsley won't exist
-   If t=2020/04/02, Parsley will be loaded with an address in Paris
-   If t=2020/04/04, Parsley will be loaded with an address in New-York
-   If t=2020/04/06, Parsley won't exist
-   If t=2020/04/08, Parsley will be loaded with an address in Milan

The timestamp used by the database is the value returned by datetime.now, either mocked by an environmental variable, or the real value.

For instance, if the parameters.json of a given graphql test contains the following:

```json
"my-test-scenario": {
        "input": { ... },
        "output": { ... },
        "layers": [ ... ],
        "envConfigs": {
            "now": "2020/04/04T10:10:10.000Z"
        }
}
```

Then : the scenario "my-test-scenario" will run on a database generated with t = "2020/04/04T10:10:10.000Z".
In our case, Parsley will be loaded with an address in New-York.

> ⚠️ **[IMPORTANT]**
> If some records don't have time stamps columns specified, they will be loaded no matter what datetime.now returns.
