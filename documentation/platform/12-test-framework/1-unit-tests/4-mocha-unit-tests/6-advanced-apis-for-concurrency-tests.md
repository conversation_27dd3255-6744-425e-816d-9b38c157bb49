PATH: XTREEM/4+Mocha+unit+tests/6+Advanced-APIs+for+concurrency+tests

# Condition Variables

The framework provides a simple `ConditionVariable` class that you may use to implement advanced concurrency tests more
efficiently than with sleep calls.

**IMPORTANT**: This API is reserved to mocha unit tests. **Do not** use it in applicative code.

## Links

-   See https://en.wikipedia.org/wiki/Monitor_(synchronization)#Condition_variables

## Creating a Condition Variable

A condition variable is created with:

```ts
// create a condition variable with name 'condition1'
const condition1 = Test.createConditionVariable('condition1');
```

## Synchronizing with a Condition Variable

Synchronization is done with 2 methods: `wait` and `notifyAll`.

You can wait on a condition variable with:

```ts
condition1.wait();
```

Note: several concurrent tasks can wait on the same variable.

Then you can notify all the tasks that are waiting on the variable.
This will cause all the `wait` calls on that variable to return.

```ts
condition1.notifyAll();
```

## Logging

You can log the `wait` and `notifyAll` calls with the following entry in your `xtrem-config.yml` configuration file:

```yml
logs:
  disabledForTests: false
  domains:
    sage/xtrem-core/test:
      level: debug
    ...
```

## Example

See https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/back-end/xtrem-core/test/runtime/isolation-anomaly-2-test.ts

This test uses 3 condition variables (`afterReceiptReadBatch`, `afterCloseBatchCommit` and `afterReportCommit`) to force the 3 tasks to wait on each other as specified in the diagram (at the top of the test).
Run it with logs enabled (see above) to see the sequence of `wait` and `notifyAll` calls.
