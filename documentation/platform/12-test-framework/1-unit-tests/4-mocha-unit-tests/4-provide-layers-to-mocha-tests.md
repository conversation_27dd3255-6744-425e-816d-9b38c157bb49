PATH: XTREEM/4+Mocha+unit+tests/4+Provide+layers+to+Mocha+tests

# Provide layers to Mocha tests

This section focuses on providing layers to a Mocha test.

## Reminders on layers

Please have a look at this section to get a reminder on the way the framework manages test data distributed among packages and layers:
https://confluence.sage.com/display/XTREEM/09+Data+management

To get a description specifically of the mechanism used by the framework to create a database with a list of layers, visit this page:
https://confluence.sage.com/display/XTREEM/3+Data+distribution+among+layers

A general introduction on providing layers to unit tests is available on this page:
https://confluence.sage.com/display/XTREEM/1+Layers.

## Rule

Custom layers can be provided to a Mocha test through the second parameter of the Test.withContext method.

If no list of layers is provided, the framework will run the test with the default list of layers: `setup, master-data, documents, test`.

## Example

The following Mocha test runs with the specific list of layers ["layer1", "layer2"].

```ts
it('my unit test', function () {
    Test.withContext(
        context => {
            // Beginning of the test.
            const testNodeDb = context.read(x3Test.nodes.TestNodeDB, {
                code: 'C1',
            });
            assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
            // End of the test.
        },
        { config: { layers: ['layer1', 'layer2'] } },
    );
});
```
