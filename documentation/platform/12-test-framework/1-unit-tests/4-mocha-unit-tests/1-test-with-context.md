PATH: XTREEM/4+Mocha+unit+tests/1+Test+withContext

# Test.withContext

Mocha tests should be passed as parameters to the Test.withContext method, as in the following example:

```ts
it('my unit test', function () {
    Test.withContext(context => {
        // Beginning of the test.
        const testNodeDb = context.read(x3Test.nodes.TestNodeDB, {
            code: 'C1',
        });
        assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
        // End of the test.
    });
});
```

Note: Test.readonly, Test.committed and Test.uncommitted are now deprecated.

## active service options

Test.withContext accepts a list of service options that have to be activated for a test

```ts
it('Create a showCaseProduct with the service option showCaseDiscountOption activated', () => {
    Test.withContext(
        context => {
            const node = context.create(nodes.ShowCaseProduct, {});
            assert.equal(node.discount, 20);
        },
        {
            testActiveServiceOptions: [serviceOptions.showCaseDiscountOption],
        },
    );
});
```
