PATH: XTREEM/4+Mocha+unit+tests/5+Provide+mocks+to+Mocha+tests

# Provide mocks to Mocha tests

A general introduction on mocks is available on this page: https://confluence.sage.com/display/XTREEM/3+Mocks.

This section focuses on providing mocks to a Mocha test.

## Rule

-   A list of mocks can be provided to a Mocha test through the second parameter of the Test.withContext method.

-   The applicative developer also needs to create an additional Json file specifying how the libraries will behave when mocked.

## Example

We provide an example to mock the `axios` library.

Three steps are necessary:

-   Create a new file specifying the desired behavior of the mocked library.
-   Fill the my-unit-test.json file you just created.
-   Write the Mocha test.

### Create a new file specifying the desired behavior of the mocked library

For instance:

-   Create a `mocks` folder inside your package's `test` folder
-   Create a `axios` folder inside the new `mocks` folder
-   Create a Json file inside `axios` and name it after your Mocha test, for instance `my-unit-test.json`

You then have the following file structure:

```bash
├── my-package
│   ├── test
│   │   ├── mocks
│   │   │   ├── axios
│   │   │   │   ├── my-unit-test.json
```

### Fill the my-unit-test.json file you just created

The newly created `my-unit-test.json` file should contain:

-   A **request** key. The `my-unit-test` Mocha test will then be expected to call the `axios` library with parameters set to the values specified here. If the test calls `axios` with other parameters, or with those parameters set to different values, the framework will throw an error.
-   A **response** key, with the object returned by the `axios` mock when called with the values given in the request key.

In the following example, `my-unit-test` is expected to call `axios` with:

-   method = "POST"
-   url = "scmx3-dev-abc.sagefr.adinternal.com"
-   ...

The mock will then return the following JSON object:

```json
{
    "hello": "world"
}
```

`my-unit-test.json` file:

```json
{
    "request": {
        "method": "POST",
        "url": "scmx3-dev-abc.sagefr.adinternal.com",
        "headers": {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "SOAPAction",
            "Connection": "Keep-Alive"
        },
        "data": "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:wss=\"http://www.adonix.com/WSS\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n            <soap:Header/>\r\n                <soapenv:Body>\r\n                    <wss:save soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n                        <callContext xsi:type=\"wss:WebServicePoolParameters\">\r\n                            <codeLang xsi:type=\"xsd:string\">FRA</codeLang>\r\n                            <poolAlias xsi:type=\"xsd:string\">TOGRAPH</poolAlias>\r\n                            <poolId xsi:type=\"xsd:string\"></poolId>\r\n                            <requestConfig xsi:type=\"xsd:string\">adxwss.optreturn=JSON&adxwss.beautify=true</requestConfig>\r\n                        </callContext>\r\n                        <publicName xsi:type=\"xsd:string\">CWSSCS</publicName>\r\n\r\n        <objectXml xsi:type=\"xsd:string\">\r\n            <![CDATA[<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n                <PARAM>\r\n                    <GRP ID=\"SCS1_1\"> <FLD NAME=\"STOFCY\">FR012</FLD> <FLD NAME=\"SGHTYP\">CHG</FLD> <FLD NAME=\"BETFCYCOD\">1</FLD> <FLD NAME=\"VCRNUM\">CHGFR0120056</FLD> <FLD NAME=\"IPTDAT\">20200122</FLD> </GRP> <TAB ID=\"SCS1_7\"> <LIN NUM=\"1\"> <FLD NAME=\"VCRLIN\">1</FLD> <FLD NAME=\"ITMREF\">BMS001</FLD> <FLD NAME=\"STA\">A</FLD> <FLD NAME=\"LOCTYP\">ST</FLD> <FLD NAME=\"LOC\">A1C11</FLD> <FLD NAME=\"PCU\">UN</FLD> <FLD NAME=\"QTYPCU\">1</FLD> <FLD NAME=\"PCUSTUCOE\">1</FLD> <FLD NAME=\"QTYSTUDES\">1</FLD> <FLD NAME=\"COEDES\">0</FLD> <FLD NAME=\"QTYPCUDES\">1</FLD> <FLD NAME=\"STADES\">A</FLD> <FLD NAME=\"LOCTYPDES\">STO</FLD> <FLD NAME=\"LOCDES\">A1C12</FLD> <FLD NAME=\"PRI\">0</FLD> </LIN> </TAB>\r\n                </PARAM>\r\n            ]]>\r\n        </objectXml>\r\n                    </wss:save>\r\n                </soapenv:Body>\r\n        </soapenv:Envelope>",
        "timeout": 500
    },
    "response": {
        "hello": "world"
    }
}
```

### Write the Mocha test

The `my-unit-test` test needs to specify that it wants to mock `axios` like specified in the file `my-unit-test.json` located in the directory `test/mocks/axios`.

To do that, it needs to pass in the second argument of Test.context:

-   **mocks**: A one-element list containing the name of the library to be mocked: 'axios'
-   **scenario**: The name of the json file containing the desired behavior of the mocked library, without the file extension.
-   **directory**: The path to the directory containing the file. As the current test file is in `test/mocha`, the absolute path to `test/mocks/axios` is obtained by `__dirname.concat('../mocks/axios')`.

```ts
it('my unit test', function () {
    Test.withContext(
        context => {
            // Beginning of the test.
            // ...
            // Test calling axios with the parameters specified in test/mocks/axios/my-unit-test.json
            // ...
            // End of the test.
        },
        {
            mocks: ['axios'],
            scenario: 'my-unit-test',
            directory: __dirname.concat('../mocks/axios'),
        },
    );
});
```
