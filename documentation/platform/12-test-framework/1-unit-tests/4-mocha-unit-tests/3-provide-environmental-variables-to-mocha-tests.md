PATH: XTREEM/4+Mocha+unit+tests/3+Provide+environmental+variables+to+Mocha+tests

# Provide environmental variables to Mocha tests

A general introduction on environmental variables is available on this page: https://confluence.sage.com/display/XTREEM/2+Environmental+variables.

This section focuses on providing environmental variables to a Mocha test.

## Reminder on data historization

Please have a look at this section to get a reminder on data historization in layers' csv files:
https://confluence.sage.com/display/XTREEM/4+Data+historization

## Rule

Environmental variables can be provided to a Mocha test through the second parameter of the Test.withContext method.

## Example

In the following example:

-   The _date_ and _datetime_ modules would return mocked dates.
-   Legislation would be set to French (FRA).
-   The username would be "TAT<PERSON>".

```ts
it('my unit test', function () {
    Test.withContext(
        context => {
            // Beginning of the test.
            const testNodeDb = context.read(x3Test.nodes.TestNodeDB, {
                code: 'C1',
            });
            assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
            // End of the test.
        },
        {
            today: '2019-10-21',
            now: '2019-10-20T17:23:07Z',
            currentLegislationCode: 'FRA',
            user: { email: '<EMAIL>', userName: 'TATA' },
        },
    );
});
```
