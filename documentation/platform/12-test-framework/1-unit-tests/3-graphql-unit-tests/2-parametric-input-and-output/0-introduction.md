PATH: XTREEM/3+GraphQL+unit+tests/2+Parametric+input+and+output

# Parametric input and output

## Files to create

-   Create a folder under `xtrem-<package-name>/test/graphql` with the name of your "scenario";
-   Create a `request.graphql.hbs` file under the newly created folder containing your query or mutation with placeholders to be completed;
-   Create a `response.json.hbs` file under the newly created folder containing the result you expect from the execution of the previously defined query or mutation with placeholders to be completed;
-   Create a `parameters.json` file under the newly created folder containing variables to be placed in your query/mutation and your expected result placeholders;
-   Run your test by opening either `request.graphql.hbs` or `response.json.hbs` in VS Code and run the **task** named `run currently opened Graphql test`. To run all Graphql tests instead use the xtrem-cli, i.e. `pnpm run xtrem test --graphql`.

## Main idea

Steps followed by the framework to process a parametric GraphQL test:

-   In `request.graphql.hbs` and `response.json.hbs`: replace all placeholders by a set of values from the `parameters.json` file.
-   Pass the request to the GraphQL endpoint.
-   Compare the result returned by the GraphQL endpoint to the expected result found in the `parameters.json` file.

> ⚠️ **[IMPORTANT]**
>
> -   If the framework detects `{{{output}}}` in the `response.json.hbs` file, it will replace the placeholder by the whole content of the output key in `parameters.json` (obtained by applying JSON.stringify to the nested object).
> -   If `{{{output}}}` is not found in `response.json.hbs`, the framework will replace one by one each placeholder found in the file with handlebars standard replacement.

## Two possible ways to create a parametric GraphQL test : with variables / with input

There are two ways to structure the `parameters.json` file:

-   With **variables**
-   With **input**.

These two methods to make a GraphQL test parametric - with input and with variables - **cannot be mixed together**.
The `parameters.json` has thus either one of two following structures :

```json
{
    "Test with variables": {
        "variables": {},
        "output": {}
    }
}
```

```json
{
    "Test with input": {
        "input": {},
        "output": {}
    }
}
```

In this section, we give an example of each of these two methods.
