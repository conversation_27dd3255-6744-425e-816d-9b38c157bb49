PATH: XTREEM/2+Parametric+input+and+output/2+Method+with+input

# Method with input

In this section, we provide an example of a parametric GraphQL test using the _input_ method.

There are 2 runs for the same test. Each run has its own set of inputs and outputs.

Reminder: for a parametric GraphQL test, the three following files must be provided to the framework:

-   `request.graphql.hbs`
-   `response.json.hbs`
-   `parameters.json`

## Request

Path: `@sage/xtrem-sales/test/graphql/sales-read/request.graphql.hbs`

```graphql
{
        xtremSales {
            salesOrder {
                query(filter: "{orderDate: {_gt: '{{orderDate}}'}}", orderBy: "{orderDate : {{orderDateBy}} }",
                            first:{{firstParameter}}) {
                edges {
                    node {
                        id
                        orderDate
                        shipmentDate
                    }
                }
            }
        }
    }
}
```

## Response

Path: `@sage/xtrem-system/test/graphql/site-read/response.json`

```json
{
    "data": {
        "xtremSales": {
            "salesOrder": {
                "edges": [
                    {
                        "node": {
                            "id": "{{salesOrder.id}}",
                            "orderDate": "{{salesOrder.orderDate}}",
                            "shipmentDate": "{{salesOrder.shipmentDate}}"
                        }
                    }
                ]
            }
        }
    }
}
```

## Parameters

Path: `@sage/xtrem-system/test/graphql/site-read/parameters.json`

```json
{
    "test asc 2019-07-27": {
        "input": {
            "orderDate": "2017-05-30",
            "orderDateBy": 1,
            "firstParameter": 1
        },
        "output": {
            "salesOrder": {
                "id": "SITEA1706SON00000001",
                "orderDate": "2017-06-01",
                "shipmentDate": "2017-06-01"
            }
        }
    },
    "test desc order ": {
        "input": {
            "orderDate": "2017-01-10",
            "orderDateBy": -1,
            "firstParameter": 1
        },
        "output": {
            "salesOrder": {
                "id": "US00919UTF/00000139",
                "orderDate": "2019-09-04",
                "shipmentDate": "2019-09-04"
            }
        }
    }
}
```
