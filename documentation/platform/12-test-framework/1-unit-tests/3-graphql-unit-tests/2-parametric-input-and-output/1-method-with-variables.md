PATH: XTREEM/2+Parametric+input+and+output/1+Method+with+variables

# Method with variables

In this section, we provide an example of a parametric GraphQL test using the _variables_ method.

There are 2 runs for the same test. Each run has its own set of variables and outputs.

Reminder: for a parametric GraphQL test, the three following files must be provided to the framework:

-   `request.graphql.hbs`
-   `response.json.hbs`
-   `parameters.json`

## Request

Path: `@sage/xtrem-sales/test/graphql/sales-read/request.graphql.hbs`

```graphql
query salesOrder($filter: String, $orderBy: String, $first: Int) {
    xtremSales {
        salesOrder {
            query(filter: $filter, orderBy: $orderBy, first: $first) {
                edges {
                    node {
                        id
                        orderDate
                        shipmentDate
                    }
                }
            }
        }
    }
}
```

## Response

Path: `@sage/xtrem-system/test/graphql/site-read/response.json`

```json
{{{output}}}
```

Note: There is a `{{{output}}}` placeholder in the response file.
When executing a scenario from the parameters file, the framework will thus replace it with the whole 'output' key of the given scenario.

## Parameters

Note that the keys provided in this file for each scenario are **"variables"** and "output".

Path: `@sage/xtrem-system/test/graphql/site-read/parameters.json`

```json
{
    "test asc 2019-07-27": {
        "variables": {
            "filter": "{orderDate: {_gt: '2017-01-10'}}",
            "orderBy": "{ orderDate: 1 }",
            "first": 3
        },
        "output": {
            "data": {
                "xtremSales": {
                    "salesOrder": {
                        "edges": [
                            {
                                "node": {
                                    "id": "AFG011705SON00000001",
                                    "orderDate": "2017-05-30",
                                    "shipmentDate": "2017-05-30"
                                }
                            },
                            {
                                "node": {
                                    "id": "SITEA1705SON00000001",
                                    "orderDate": "2017-05-30",
                                    "shipmentDate": "2017-05-30"
                                }
                            },
                            {
                                "node": {
                                    "id": "SITEA1706SON00000001",
                                    "orderDate": "2017-06-01",
                                    "shipmentDate": "2017-06-01"
                                }
                            }
                        ]
                    }
                }
            }
        }
    },
    "test desc order ": {
        "variables": {
            "filter": "{orderDate: {_gt: '2017-01-10'}}",
            "orderBy": "{ orderDate: -1 }",
            "first": 3
        },
        "output": {
            "data": {
                "xtremSales": {
                    "salesOrder": {
                        "edges": [
                            {
                                "node": {
                                    "id": "US00919UTF/00000139",
                                    "orderDate": "2019-09-04",
                                    "shipmentDate": "2019-09-04"
                                }
                            },
                            {
                                "node": {
                                    "id": "US00919UTF/00000140",
                                    "orderDate": "2019-09-04",
                                    "shipmentDate": null
                                }
                            },
                            {
                                "node": {
                                    "id": "US00919UTF/00000141",
                                    "orderDate": "2019-09-04",
                                    "shipmentDate": null
                                }
                            }
                        ]
                    }
                }
            }
        }
    }
}
```

## Note

The following `response.json.hbs` and `parameters.json` are also valid, and are just another way to write exactly the same test.

```json
{
    "data": {
            "xtremSales": {{{output}}}
        }
    }
```

```json
{
    "Test 1 ASC Order": {
        "variables": {
            "filter": "{orderDate: {_gt: '2017-01-10'}}",
            "orderBy": "{ orderDate: 1 }",
            "first": 3
        },
        "output": {
            "salesOrder": {
                "edges": [
                    {
                        "node": {
                            "id": "AFG011705SON00000001",
                            "orderDate": "2017-05-30",
                            "shipmentDate": "2017-05-30"
                        }
                    },
                    {
                        "node": {
                            "id": "SITEA1705SON00000001",
                            "orderDate": "2017-05-30",
                            "shipmentDate": "2017-05-30"
                        }
                    },
                    {
                        "node": {
                            "id": "SITEA1706SON00000001",
                            "orderDate": "2017-06-01",
                            "shipmentDate": "2017-06-01"
                        }
                    }
                ]
            }
        }
    },
    "Test 2 DESC order ": {
        "variables": {
            "filter": "{orderDate: {_gt: '2017-01-10'}}",
            "orderBy": "{ orderDate: -1 }",
            "first": 3
        },
        "output": {
            "salesOrder": {
                "edges": [
                    {
                        "node": {
                            "id": "US00919UTF/00000139",
                            "orderDate": "2019-09-04",
                            "shipmentDate": "2019-09-04"
                        }
                    },
                    {
                        "node": {
                            "id": "US00919UTF/00000140",
                            "orderDate": "2019-09-04",
                            "shipmentDate": null
                        }
                    },
                    {
                        "node": {
                            "id": "US00919UTF/00000141",
                            "orderDate": "2019-09-04",
                            "shipmentDate": null
                        }
                    }
                ]
            }
        }
    }
}
```
