PATH: XTREEM/3+GraphQL+unit+tests/5+Provide+layers+to+GraphQL+tests

# Provide layers to GraphQL tests

This section focuses on providing layers to a GraphQL test.

## Reminders on layers

Please have a look at this section to get a reminder on the way the framework manages test data distributed among packages and layers:
https://confluence.sage.com/display/XTREEM/09+Data+management

To get a description specifically of the mechanism used by the framework to create a database with a list of layers, visit this page:
https://confluence.sage.com/display/XTREEM/3+Data+distribution+among+layers

A general introduction on providing layers to unit tests is available on this page:
https://confluence.sage.com/display/XTREEM/1+Layers.

## A few rules

Layers:

-   Can be provided to all static GraphQL tests, but a `parameters.json` file needs to be added to the test folder.
-   Can be passed to all parametric GraphQL tests, those developed with the **variables** key as well as those using **input**, by modifying the existing `parameters.json` file.
-   Are passed to the test with a new key (`layers`) added to each scenario of the `parameters.json` file.

When a scenario of the `parameters.json` file contains a field named `layers`, the framework will load a database with csv files found in the custom list of layers provided in the field.
If no list of layers is provided, the framework will use the default list of layers (`setup, master-data, documents, test`) to load the database.

## Examples

-   We show here how to specify that a GraphQL test runs with the specific list of layers ["layer1", "layer2"].
-   We provide two examples, one for a static GraphQL test, and the other for a parametric test.

### Static test

Create a `parameters.json` in the folder `@sage/xtrem-system/test/graphql/MY-TEST/`, with the following content:

```json
{
    "envConfig": { ... },
    "layers": ["layer1", "layer2"]
}
```

Note: The "layers" key can be the only one in the file or not, like in the example above.

### Parametric test

Modify the existing `parameters.json` file by adding the "layers" key to the relevant scenario(s).
In the following example:

-   "first scenario" will run with the layers ["layer1", "layer2"]
-   "second scenario" will run with the layers ["layer2", "layer1", "layers3"]
-   "third scenario" will run with the standard layers ["setup", "master-data", "documents", "test"]

```json

{
    "first scenario": {
        "input": { ... },
        "output": { ... },
        "layers": ["layer1", "layer2"]
    },
    "second scenario": {
        "input": { ... },
        "output": { ... },
        "layers": ["layer2", "layer1", "layer3"]
    },
    "third scenario": {
        "input": { ... },
        "output": { ... }
    }
}
```
