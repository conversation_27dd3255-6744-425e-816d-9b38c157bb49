PATH: XTREEM/3+GraphQL+unit+tests/3+Execution+mode

# Execution mode

Execution mode is an additional parameter that can be used to skip or run only given GraphQL test scenarios.
It is similar to the Mocha `it.only` and `it.skip` mechanism.

This parameter has to be supplied in the parameters.json file.

The `executionMode` parameter can have a value of `skip`, `only` or `normal`.

-   `skip` - will skip the current test or scenario.
-   `only` - will execute the current and other tests marked with only `executionMode`.
-   `normal` - this is the default execution mode, this and other tests will run as normal (if no other tests `executionMode` is set).

## Example : skipping a test

`parameters.json` file:

```json
{
    "load env variables ": {
        "input": { "countryName": "FRA" },
        "output": {
            "countryName": "FRA",
            "now": "2019-10-20T17:23:07.000Z"
        },
        "executionMode": "skip"
    }
}
```
