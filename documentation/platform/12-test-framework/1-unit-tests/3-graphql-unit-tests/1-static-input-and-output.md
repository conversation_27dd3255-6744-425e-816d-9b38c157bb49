PATH: XTREEM/3+GraphQL+unit+tests/1+Static+input+and+output

# Static input and output

## Files to create

-   Create a folder under `xtrem-<package-name>/test/graphql` with the name of your "scenario";
-   Create a `request.graphql` file under the newly created folder containing your query or mutation;
-   Create a `response.json` file under the newly created folder containing the result you expect from the execution of the previously defined query or mutation;
-   Run your test by opening either `request.graphql` or `response.json` in VsCode and run the **task** named `run currently opened Graphql test`. To run all Graphql tests instead use the xtrem-cli, i.e. `pnpm run xtrem test --graphql`.

## Example

Here is an example of a basic GraphQL test with static input and output.
Both the request and response files need to be created by the applicative developer for the test to work.

### Request

Path: `@sage/xtrem-system/test/graphql/site-read/request.graphql`

```graphql
query {
    xtremSystem {
        site {
            node(code: "US009") {
                code
                legalCompany {
                    code
                }
            }
        }
    }
}
```

### Response

Path: `@sage/xtrem-system/test/graphql/site-read/response.json`

```json
{
    "data": {
        "xtremSystem": {
            "site": {
                "node": {
                    "code": "US009",
                    "legalCompany": {
                        "code": "UTFRA"
                    }
                }
            }
        }
    }
}
```
