PATH: XTREEM/3+GraphQL+unit+tests/6+Provide+mocks+to+GraphQL+tests

# Provide mocks to GraphQL tests

A general introduction on mocks is available on this page: https://confluence.sage.com/display/XTREEM/3+Mocks.

This section focuses on providing mocks to a GraphQL test.

## A few rules

Mocks:

-   Can be provided to all static GraphQL tests, but a `parameters.json` file needs to be added to the test folder.
-   Can be passed to all parametric GraphQL tests, those developed with the **variables** key as well as those using **input**, by modifying the existing `parameters.json` file.
-   Are passed to each test or scenario as a special case of environmental variable.

## Examples

We provide two examples, one for a static GraphQL test, and the other for a parametric test.

In both following examples, we mock the `axios` library.

### Parametric test

Let's mock the `axios` library for the `soap-test-1` scenario.

#### Modify the parameters.json file

Modify the existing `parameters.json` file by adding the "envConfig" key, itself containing a "mocks" key, to the chosen scenario(s):

```json
{
    "soap-test-1": {
        "input": {...},
        "output": {...},
        "envConfigs": {
            "mocks": ["axios"]
        }
    }
}
```

#### Add a new folder named axios in your test folder

-   Create a subfolder named 'axios' in the test folder.
-   Create a json file named after your scenario inside this new folder.

You should get the following file structure:

```bash
├── my-test-folder
│   ├── axios
│   │   ├── soap-test-1.json
│   ├── request.graphql.hbs
│   ├── response.json.hbs
│   ├── parameters.json
```

#### Fill the soap-test-1.json file you just created

The newly created `soap-test-1.json` file should contain:

-   A **request** key. The `soap-test-1` test scenario is expected to call the `axios` library with parameters set to the values specified here. If the test calls `axios` with other parameters, or with those parameters set to different values, the framework will throw an error.
-   A **response** key, with the object returned by the `axios` mock when called with the values given in the request key.

In the following example, the `soap-test-1` test scenario is expected to call `axios` with:

-   method = "POST"
-   url = "scmx3-dev-abc.sagefr.adinternal.com"
-   ...

The mock will then return the following JSON object:

```json
{
    "hello": "world"
}
```

`soap-test-1.json` file:

```json
{
    "request": {
        "method": "POST",
        "url": "scmx3-dev-abc.sagefr.adinternal.com",
        "headers": {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "SOAPAction",
            "Connection": "Keep-Alive"
        },
        "data": "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:wss=\"http://www.adonix.com/WSS\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n            <soap:Header/>\r\n                <soapenv:Body>\r\n                    <wss:save soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n                        <callContext xsi:type=\"wss:WebServicePoolParameters\">\r\n                            <codeLang xsi:type=\"xsd:string\">FRA</codeLang>\r\n                            <poolAlias xsi:type=\"xsd:string\">TOGRAPH</poolAlias>\r\n                            <poolId xsi:type=\"xsd:string\"></poolId>\r\n                            <requestConfig xsi:type=\"xsd:string\">adxwss.optreturn=JSON&adxwss.beautify=true</requestConfig>\r\n                        </callContext>\r\n                        <publicName xsi:type=\"xsd:string\">CWSSCS</publicName>\r\n\r\n        <objectXml xsi:type=\"xsd:string\">\r\n            <![CDATA[<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n                <PARAM>\r\n                    <GRP ID=\"SCS1_1\"> <FLD NAME=\"STOFCY\">FR012</FLD> <FLD NAME=\"SGHTYP\">CHG</FLD> <FLD NAME=\"BETFCYCOD\">1</FLD> <FLD NAME=\"VCRNUM\">CHGFR0120056</FLD> <FLD NAME=\"IPTDAT\">20200122</FLD> </GRP> <TAB ID=\"SCS1_7\"> <LIN NUM=\"1\"> <FLD NAME=\"VCRLIN\">1</FLD> <FLD NAME=\"ITMREF\">BMS001</FLD> <FLD NAME=\"STA\">A</FLD> <FLD NAME=\"LOCTYP\">ST</FLD> <FLD NAME=\"LOC\">A1C11</FLD> <FLD NAME=\"PCU\">UN</FLD> <FLD NAME=\"QTYPCU\">1</FLD> <FLD NAME=\"PCUSTUCOE\">1</FLD> <FLD NAME=\"QTYSTUDES\">1</FLD> <FLD NAME=\"COEDES\">0</FLD> <FLD NAME=\"QTYPCUDES\">1</FLD> <FLD NAME=\"STADES\">A</FLD> <FLD NAME=\"LOCTYPDES\">STO</FLD> <FLD NAME=\"LOCDES\">A1C12</FLD> <FLD NAME=\"PRI\">0</FLD> </LIN> </TAB>\r\n                </PARAM>\r\n            ]]>\r\n        </objectXml>\r\n                    </wss:save>\r\n                </soapenv:Body>\r\n        </soapenv:Envelope>",
        "timeout": 500
    },
    "response": {
        "hello": "world"
    }
}
```

### Static test

In this case, we don't have multiple scenarios but a single test with hardcoded test data.

#### Create and fill the parameters.json file

Create a `parameters.json` in the folder `@sage/xtrem-system/test/graphql/my-test-folder/`, containing the following Json object:

```json
{
    "envConfigs": {
        "mocks": ["axios"]
    }
}
```

#### Add the new axios folder in your test folder

-   Create a subfolder named 'axios' in the test folder.
-   Create a Json file named like your test folder inside the axios folder.

**Note:** for parametric tests, the json file had to be named after a scenario of the test. In a static test, there are no scenarios, so the file is named after the test folder.

File structure:

```bash
├── my-test-folder
│   ├── axios
│   │   ├── my-test-folder.json
│   ├── request.graphql
│   ├── response.json
│   ├── parameters.json
```

The `my-test-folder.json` should have the same contents as the `soap-test-1.json` file described above.
