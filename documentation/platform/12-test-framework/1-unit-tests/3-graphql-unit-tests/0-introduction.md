PATH: XTREEM/1+Unit+tests/3+GraphQL+unit+tests

# GraphQL unit tests

In this section, we describe in detail how to create GraphQL unit tests and the different possibilities they offer.

Our test framework allows for a configuration-based test execution: any **Graphql** query or mutation can be easily tested in a **declarative** way under different "scenarios". Refer to the following paragraphs to see how you can configure input/output, parameters and licenses for any individual test.

We have 2 different ways for creating tests:

-   Hardcoded tests using static input and output configuration
-   Parametric tests to run the same query/mutation with different sets of input/output
