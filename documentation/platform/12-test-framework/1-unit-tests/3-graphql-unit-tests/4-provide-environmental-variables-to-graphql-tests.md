PATH: XTREEM/3+GraphQL+unit+tests/4+Provide+environmental+variables+to+GraphQL+tests

# Provide environmental variables to GraphQL tests

A general introduction on environmental variables is available on this page: https://confluence.sage.com/display/XTREEM/2+Environmental+variables.

This section focuses on providing environmental variables to a GraphQL test.

## Reminder on data historization

Please have a look at this section to get a reminder on data historization in layers' csv files:
https://confluence.sage.com/display/XTREEM/4+Data+historization

## A few rules

Environmental variables:

-   Can be provided to all static GraphQL tests, but a `parameters.json` file needs to be added to the test folder.
-   Can be passed to all parametric GraphQL tests, those developed with the **variables** key as well as those using **input**, by modifying the existing `parameters.json` file.
-   Are passed to the test with a new key (`envConfigs`) added to each scenario of the `parameters.json` file.

When a scenario of the `parameters.json` file contains a field named `envConfigs` with any or all supported variables, the framework will
stub the normal behavior of the provided system functions and force them to return the values specified in `parameters.json`.

-   The mocking of these variables is limited to the execution of a single scenario.
    If the same value is expected in multiple scenarios, the `envConfigs` fields needs to be passed to each scenario separately.

## Examples

We provide two examples, one for a static GraphQL test, and the other for a parametric test.

In both following tests:

-   The _date_ and _datetime_ modules would return mocked dates.
-   Language and legislation would be set to French (FRA).
-   The user code would be "TATA".

### Static test

Create a `parameters.json` in the folder `@sage/xtrem-system/test/graphql/MY-TEST/`, containing the following Json object:

```json
{
    "envConfigs": {
        "now": "2019-10-20T17:23:07Z",
        "currentLanguageCode": "FRA",
        "currentLegislationCode": "FRA",
        "user": { "code": "TATA" }
    }
}
```

### Parametric test

Modify the existing `parameters.json` file by adding the "envConfig" key to the relevant scenario(s):

```json
{
    "load env variables ": {
        "input": { "countryName": "FRA" },
        "output": {
            "countryName": "FRA",
            "now": "2019-10-20T17:23:07.000Z"
        },
        "envConfigs": {
            "now": "2019-10-20T17:23:07Z",
            "currentLanguageCode": "FRA",
            "currentLegislationCode": "FRA",
            "user": { "code": "TATA" }
        }
    }
}
```
