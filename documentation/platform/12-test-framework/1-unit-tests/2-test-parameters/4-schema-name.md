PATH: XTREEM/2+Test+parameters/4+Test+Schema+Name

## Default test schema name

By default, each package has its test schema, which is:

```ts
`${package_name}_test`;
```

## Enforce schema name

It is possible to enforce the schema name by setting the `XTREM_SCHEMA_NAME` environment variable to the desired name.

```ts
process.env.XTREM_SCHEMA_NAME = 'desired_schema_name';
```

or by passing the `schemaName` option when creating a new application.

```ts
const application = await Application.create({
    buildDir,
    ...options,
    schemaName: this.getSchemaName(buildDir, options?.applicationType),
});
```
