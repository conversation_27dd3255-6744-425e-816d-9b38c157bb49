PATH: XTREEM/2+Test+parameters/3+Mocks

# Mocks

The framework provides the possibility for a developer to mock given modules during a test's execution.

## Current available mocks

### axios

```ts
Test.withContext(
    () => {
        const axios = Mocker.get('axios', require);
        const url = 'myWebService.com';
        const data = `<data>
                <name> Joe </name><lastName> Soap </lastName>
             </data> `;
        const header = {
            'Content-Type': 'text/xml;charset=UTF-8',
            SOAPAction: 'SOAPAction',
            Connection: 'Keep-Alive',
        };

        const soapRequest: AxiosRequestConfig = {
            method: 'post',
            url,
            timeout: 500,
            headers: header,
            data,
        };

        assert.deepEqual(wait(axios(soapRequest)), { hello: 'world' });
    },
    {
        mocks: ['axios'],
        scenario: 'soap-test-1',
        directory: __dirname.concat('/../../../xtrem-cli/test/fixtures/x3-test/test/graphql/pass-mocks'),
    },
);
```

In the code above we pass `axios` into the `mocks` attribute of the `Test.withContext` method. This will notify the framework that we will be using the standard axios mock plugin (mock plugins are explained further below).

## Mocker.get

**Important**

```ts
const axios = Mocker.get('axios', require);
```

The Mocker class can be imported from the `@sage/xtrem-core` package.

`Mocker.get` is a function that takes in the module name (`axios`) and the `require` method of current working module, and it will return the mocked version of a module (if it has a mock plugin registered or it will perform the normal require). In our source, all instances where `axios` is being required or imported and mocking of `axios` needs to occur, this function **must** be called, otherwise the mocking will not occur.

## Mock plugins

We have been discussing the standard mock plugins of the framework above, however the framework also provides the feature to pass custom plugins to units.

```ts
class FsPlugin extends Plugin {
    constructor(public readonly path: string = '', public readonly scenario: string = '') {
        super('fs', path, scenario);
    }

    // eslint-disable-next-line class-methods-use-this
    mock(): any {
        return {
            readFileSync: (...args: any) => {
                ...
            },
        };
    }
}
```

The code above shows the implementation of the `Plugin` abstract class that can be imported from `@sage/xtrem-core`. The plugin above provides a mock for the `fs` module. It is important to call the super constructor of the `Plugin` class to set all the necessary attributes to make the mock work. The `mock` function is where the mocking logic needs to be returned. As you see in the code above, when loaded the `fs` module will only have one method, `readFileSync`, which overrides the standard `readFileSync`.

Take note, that the module will only have the functionality you have mocked, so for instance if you try to use the `fs.writeFileSync`, this will not work as it was not part of the mock provided.

```ts
Test.withContext(
    () => {
        const fs = Mocker.get('fs', require);
        assert.equal(fs.readFileSync('hello'), 'world');
    },
    { plugins: [new FsPlugin()] },
);
```

In the code above see how we can use the mock plugin we defined above, passing it into the plugins attribute of the `Test.withContext` method options parameter.

**Important**

```ts
const fs = Mocker.get('fs', require);
```

Take note that we use `Mocker.get` again instead of `require` or `import`.

```ts
Test.withContext(
    () => {
        ...
    },
    { mocks: ['axios'], plugins: [new FsPlugin()] },
);
```

If there is a need, mocks and mock plugins can be passed in together, but take note that the plugins passed cannot be mocking the same module as the mocks passed, for instance you cannot pass axios into mocks and pass a mock plugin for axios at the same time.

```ts
 // THIS WILL NOT WORK AND WILL THROW AN ERROR
 Test.withContext(
    () => {
        ...
    },
    {
        mocks: ['axios'],
        plugins: [new AxiosPlugin()],
        scenario: 'soap-test-1',
        directory: __dirname.concat('/../../../xtrem-cli/test/fixtures/x3-test/test/graphql/pass-mocks'),
    },
);
```

See GraphQL and Mocha sections to get specific examples of tests running with mocked libraries.
