PATH: XTREEM/2+Test+parameters/1+Layers

# Layers

## Main idea

Each unit test runs on a test database created with data found in csv files coming from a given set of layers.

-   The default list of layers for unit tests is : **setup, master-data, documents, test**.
-   But another custom list of layers can be specified for a given test.

## Reminders on layers

Please have a look at this section to get a reminder on the way the framework manages test data distributed among packages and layers:
https://confluence.sage.com/display/XTREEM/09+Data+management

To get a description specifically of the mechanism used by the framework to create a database with a list of layers, visit this page:
https://confluence.sage.com/display/XTREEM/3+Data+distribution+among+layers

## What happens to the database when running tests

Before executing the first test:

-   If it doesn't exist already, a database schema called `xtrem_test` is created in the Postgres database found in the `xtem-config.yml` file of the project.
-   The application tables are created in the schema, without data. Their metadata is built by browsing through the packages in the order induced by the package dependency graph.
-   Those tables are filled with data found in the layers specified in the first test to be executed. If the first test doesn't specify a custom list of layers, standard layers are loaded: `setup, master-data, documents, test`.

While a test is running:

-   Unit tests are all run in **uncommitted** mode: changes made during the test are rolled back at the end of the test. The test execution can only modify the database temporarily.

In between the execution of two tests:

-   If the new test uses a different set of layers than the previous test, data is reloaded into the database.
-   If the two tests use the same layers, the database is not reloaded.

Note: if two tests use different time-setting environmental variables (for instance different `now` values), the database will be reloaded in between the two.
See this page for more details on environmental variables: https://confluence.sage.com/display/XTREEM/2+Environmental+variables.
