PATH: XTREEM/2+Test+parameters/2+Environmental+variables

# Environmental variables

## Main idea

With environmental variables, the framework provides the possibility to mock certain system features during the test's execution.

The supported options are:

-   **today**: Stubs the _date_ module and forces all methods that request today's date to return the specified value.
-   **now**: Stubs _datetime_ module and forces all methods that request the **now** value to return the specified value.
-   **currentLanguageCode**: Enforces the current test to use the specified language code.
-   **currentLegislationCode**: Enforces the current test to use the specified legislation code.
-   **user**: Enforces the current test to use the specified user.

If the developer attempts to mock today's value, `today` must be provided.

Environmental variables need to be provided to each test separately:

-   The provided variables are mocked only during the test's execution. The stubbing soes not propagate between tests.
-   If the same variables need to be mocked by the same value in multiple tests, the applicative developer needs to provide those variables to each of those tests separately.

See GraphQL and Mocha sections to get specific examples of tests running with environmental variables.

## Notes on database reload between two tests

-   When providing a time mock to a test, the data loading mechanism only loads records that appear to have been created before that date. Please visit https://confluence.sage.com/display/XTREEM/4+Data+historization for a thorough description of data historization in layers' csv files.
-   The page https://confluence.sage.com/display/XTREEM/1+Layers provides a description of the reloading of the database between two tests if they run with a different list of layers. Note that if two tests run on different values of **now**, **today**, the database will also be reloaded between them.
