PATH: XTREEM/2+Integration+tests/2+How+to+use+Cucumber

# How to use Cucumber

## Using Cucumber on a WSL system

By default, when running the integration tests on a WSL system the Chrome launcher will start a windows instance of Chrome. The issue is that the remote connection to Chrome fails because of various issues including Windows Defender Firewall rules and the fact that Chrome is listening only on the local network interface.

The workaround is then to trick windows WSL so that the it appears to be linux env. But first, be sure to have Google Chrome installed in your WSL system.

### Installing Google Chrome on WSL

```sh
sudo apt update && sudo apt -y upgrade && sudo apt -y autoremove

wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
sudo apt -y install ./google-chrome-stable_current_amd64.deb

```

Then check that Chrome is installed

```sh
google-chrome --version
```

### Mimic true Linux env

As said above, we need now to force the cucumber stack to run the Linuw instance of Chrome and not the windows one. The magic is to create a `.dockerenv` file at the root dir.

```sh
sudo touch /.dockerenv
```

### Running tests

Running all smoke tests from the xtrem root folder:

```sh
pnpm run test:smoke:ci
```

Running smoke tests from the xtrem-show-case folder:

```sh
pnpm run test:ci:integration
```

Running a single test from the xtrem-show-case folder:

```sh
pnpm run xtrem test "test/cucumber/date.feature" --integration --ci
```

> ⚠️ **[IMPORTANT]** This works only in headless (no UI) with the `--ci` option
> This may change with Windows 11 that have a GUI support for WSL.

> ⚠️ **[SECURITY]** Be careful that using another option like an X11 server on windows may introduce security breaches on your system.
