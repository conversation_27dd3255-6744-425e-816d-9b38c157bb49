PATH: XTREEM/02+Getting+started/6+use+docker+image

# Use docker image

## The general syntax for a command

Inside the image, an xtrem command is in the system path and pnpm is removed from the image to reduce the vulnerability exposure. As a consequence just use `xtrem` and do not `pnpm run xtrem`.

```sh
xtrem schema --create

xtrem tenant --init 'eyJjdXN0b21lciI6eyJpZCI6IjAwMDAwMDAwMDAwMDAwMDAwMDAxIiwibmFtZSI6ImFjbWUifSwidGVuYW50Ijp7ImlkIjoiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIiwibmFtZSI6ImRldiJ9LCJhZG1pblVzZXIiOnsiZW1haWwiOiJqb2huLmRvZUBhY21lLmNvbSIsImZpcnN0TmFtZSI6IkpvaG4iLCJsYXN0TmFtZSI6IkRvZSIsImxvY2FsZSI6ImVuLVVTIn19='
```

## Noticeable environment variables

When running the xtrem command with the docker image you can set environment variables that will change the behavior of node.

-   **NODE_OPTIONS**: The standard node.js environment variable to set options
-   **XTREM_V8_OPTIONS**: Additional V8 engine options that cannot be set by NODE_OPTIONS standard
