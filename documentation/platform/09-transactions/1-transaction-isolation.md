PATH: XTREEM/09+Transactions/1+Transaction+Isolation

# Isolation Levels

PostgreSQL supports 3 isolation levels, from lowest to highest:

-   READ COMMITTED (low): transaction sees a committed snapshot taken at the time the _current statement_ started to execute.
-   REPEATABLE READ (medium): transaction sees a committed snapshot taken at the time the _transaction_ started to execute.
-   SERIALIZABLE (high): same guarantees as REPEATABLE READ but, in addition, conflicts between transactions are detected and handled to guarantee that the result is the same as if transactions had been serialized.

You can control the isolation level with decorator attributes. The levels are defined as:

```ts
export type IsolationLevel = 'low' | 'medium' | 'high';
```

## Links

-   https://www.postgresql.org/docs/current/transaction-iso.html: the PostgreSQL doc page on isolation levels.
-   https://www.postgresql.org/files/developer/transactions.pdf: shallow dive into PostgreSQL transactions.
-   https://drkp.net/papers/ssi-vldb12.pdf: deep dive into the theory and implementation of Serializable Snapshot Isolation (SII) in PostgreSQL.

# Read-only Transactions

Transaction isolation is relevant for read-only transactions.

If multiple queries are necessary to obtain a result, it may be important that the query be run against a consistent
snapshot of the data. The 'medium' and 'high' isolation levels provide this snapshot isolation.

# Resolution of isolation level

The isolation level is 'low' by default and can be overridden in a decorator.

The isolation levels of update and delete mutations can be set in the node decorator. The isolation levels of custom queries and mutations, and listeners, can be set on their decorators.

The isolation level of queries may be overridden by the GraphQL client and passed via an 'isolation' parameter in the JSON payload. This isolation level applies to all the queries contained in the request.

## Hard-coded defaults

The hard-coded defaults are 'low' on all mutations

# Decorators

You should only override the isolation in decorators if you need a higher level of isolation than the default. Note that raising the isolation level may downgrade performance in mutations that have a high level of contention.

## Node Decorator

```ts
@decorators.node<MyNode>({
    storage: 'sql',
    isolationLevels: {
        // increase all the levels
        create: 'medium',
        update: 'medium',
        delete: 'medium',
})
export class MyNode extends Node { ... }
```

## Custom Mutation Decorator

```ts
    @decorators.mutation<typeof MyNode, 'myMutation'>({
        isPublished: true,
        // default is 'low', increase it.
        isolationLevel: 'medium',
         ...
    })
    static async myMutation(context, ...) { ... }
```

## Listener Decorator

```ts
    @decorators.notificationListener<typeof MyNode>({
        queue: () => aQueue),
        topic: 'myTopic',
        // default is 'low', increase it.
        isolationLevel: 'medium',
        ...
    })
    static async myListener(...) { ... }
```

Same for `messageListener`.

# TypeScript API

The XTreeM framework allocates a new context for each query, mutation or listener call.

This context starts a transaction with the requested isolation level unless it is a read-only operation with a 'low' level, in which case there is no real transaction and SQL connections are allocated and returned to the pool after each SQL statement.

The business rules are executed on this transaction and committed at the end of the operation.
The GraphQl response is read from this transaction, after the commit.

The framework takes care of the eventual rollbacks or retries when transactions fail and have conflicts.
