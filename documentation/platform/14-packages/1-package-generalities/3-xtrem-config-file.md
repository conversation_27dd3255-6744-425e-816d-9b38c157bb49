PATH: XTREEM/1+Package+generalities/3+Configuration+file

# xtrem-config.yml file

## Overview

This is a YAML file that contains the configuration details for a cluster. This includes the configuration for the deployment mode, the database, the queues, the log system, the package-specific parameters and more.

The format is:

```yml
deploymentMode: development

storage:
    sql:
        hostname: localhost
        port: 5432
        database: xtrem
        user: '@secret/database-user'
        password: '@secret/database-user-password'
        sysUser: '@secret/database-sysuser'
        sysPassword: '@secret/database-sysuser-password'
        sysDatabase: postgres

logs:
    domains:
        sage/xtrem-core/sql:
            level: verbose
        sage/xtrem-core/graphql:
            level: info
        sage/xtrem-service/http:
            level: verbose

packages:
    '@sage/util-mailer':
        host: sage-smtp.sagefr.adinternal.com

interop:
    heartbeatSeconds: 1000
    messageVisibilitySeconds: 5
    sendRetrySeconds: 3
    queues:
        intacctReceive:
            url: 'http://127.0.0.1:9324/queue/demo-queue.fifo'
        intacctSend:
            url: 'http://127.0.0.1:9324/queue/demo-queue.fifo'
        stock-notifications:
            url: 'http://127.0.0.1:9324/queue/stock-notifications.fifo'
        sales-notifications:
            url: 'http://127.0.0.1:9324/queue/sales-notifications.fifo'
        manufacturing-notifications:
            url: 'http://127.0.0.1:9324/queue/manufacturing-notifications.fifo'
        purchasing-notifications:
            url: 'http://127.0.0.1:9324/queue/purchasing-notifications.fifo'
        finance-notifications:
            url: 'http://127.0.0.1:9324/queue/finance-notifications.fifo'
        avalara-gateway-notifications:
            url: 'http://127.0.0.1:9324/queue/avalara-gateway-notifications.fifo'
        frp-1000-gateway-notifications:
            url: 'http://127.0.0.1:9324/queue/frp-1000-gateway-notifications.fifo'
        inventory-notifications:
            url: 'http://127.0.0.1:9324/queue/inventory-notifications.fifo'
        mrp-notifications:
            url: 'http://127.0.0.1:9324/queue/mrp-notifications.fifo'
        service-fabric-notifications:
            url: 'http://127.0.0.1:9324/queue/demo-queue.fifo'

documentationServiceUrl: https://documentation.dev-sagextrem.com/
```

## Secrets management in a production environment

Any value that starts with '@secret/' will be replaced by the actual value by reading it from the AWS vault configured for the cluster.

**Note**: This feature is not available on the local development system.
