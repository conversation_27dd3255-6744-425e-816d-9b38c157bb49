PATH: XTREEM/1+Package+generalities/5+Umbrella+Package

# Umbrella Package

An umbrella package is a package which does not provide any XTreeM artifacts by itself but which collects all the artifacts from a list of XTreeM packages instead.

Umbrella packages are typically used to build docker images. They allow us to include several XTreeM service packages into a single image. When starting a container, we can select which services to activate with the `--services` option of the `xtrem start` command.

The services packages which are collected by the umbrella package are simply its `@sage/xtrem-*` dependencies.

The umbrella package.json must have an `xtrem` key (with an empty object as value) and must **not** have any `main` key (because it does not have any JavaScript artifacts). Here is an example:

```json
{
    "name": "@sage/xtrem",
    "version": "12.0.14",
    "author": "Sage",
    "license": "UNLICENSED",
    "xtrem": {},
    "dependencies": {
        "@sage/xtrem-avalara-gateway": "^12.0.14",
        "@sage/xtrem-intacct-gateway": "^12.0.14"
    },
    "scripts": {
        "xtrem": "xtrem"
    }
}
```
