PATH: XTREEM/3+Add+Ons/2+Deployment+Of+Add+Ons

# Deployment of Add-ons

## Add-ons path in config

-   Create the folder where the add-ons will be deployed
-   Copy the add-on package into this folder
-   In the xtrem-config.yml add the following

```yaml
addOns:
    folder: /my-add-on-folder
```

-   An example of the folder structure is as follows;

/my-add-on-folder/add-ons/@acme/acmm-add-on-package

## Add-ons path not in config

-   In the root folder of the application, create a folder called `add-ons`
-   Copy the add-on package into this folder
-   An example of the folder structure is as follows;

/xtrem/app/add-ons/@acme/acmm-add-on-package

## Multi-worker services

-   If the multi-workers are used, then the add-ons will be loaded from a sub-folder in the folder described about. The sub-folder, must be name after the id allocated to workers.
-   An example of the folder structure with worker is as follows;

/my-add-on-folder/add-ons/MY-WORKER-ID/@acme/acmm-add-on-package/

## NOTE:

**The add-ons folder structure is add-ons-folder/sub-folder/(package name), if this structure is not followed the add-on will not be loaded into the application.**

All add-ons in this folder will be loaded when the application is started.

# Gotchas

-   The installation or upgrade of the add-on schema objects on Postgres has not been implemented, yet. This feature will not work with add-ons using nodes with `sql` storage.
