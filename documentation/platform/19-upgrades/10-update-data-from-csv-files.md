PATH: XTREEM/19+Upgrades/10+Update+data+from+CSV+files

# Updating of setup data

The `setup` layer can only contain data for nodes with an `isSetupNode` decorator. This data can be protected from updates by providing a value for the `_vendor` column. The `_vendor` value in this column indicates to which software vendor the data belongs to.

## When updating setup data from a CSV file, two scenarios exist:

-   Where the record in the CSV has no `_vendor` value.
    -   If the record does not exist in the database, it will be created.
    -   If it does exist in the database, no changes will be made to the record.
-   Where the record in the CSV has 'sage' as a vendor.
    -   If the record does not exist in the database, it will be created.
    -   If the record does exist in the database, all fields will be updated except where the fields are protected by isOwnedByCustomer property.

## Resetting of the `_vendor` column value:

If a record does not exist in the CSV file or if it does not have a `_vendor` value in the CSV file, the `_vendor` value in the DB will also be reset to NULL.

# Example:

| CSV file                 | Database                          | Action     | Reset                         |
| ------------------------ | --------------------------------- | ---------- | ----------------------------- |
| Record with `_vendor`    | No record in database             | Insert     | Do nothing                    |
| Record without `_vendor` | No record in database             | Insert     | Reset `_vendor` value to NULL |
| Record with `_vendor`    | Existing record with `_vendor`    | Update     | Do nothing                    |
| Record with `_vendor`    | Existing record without `_vendor` | Update     | Update `_vendor`              |
| Record without `_vendor` | Existing record with `_vendor`    | Do nothing | Reset `_vendor` value to NULL |
| Record without `_vendor` | Existing record without `_vendor` | Do nothing | Reset `_vendor` value to NULL |
| No record in the CSV     | Existing record with `_vendor`    | Do nothing | Reset `_vendor` value to NULL |
| No record in the CSV     | Existing record without `_vendor` | Do nothing | Reset `_vendor` value to NULL |
