PATH: XTREEM/19+Upgrades/1+Upgrade+engine+overview

# Upgrade engine overview

## What is an upgrade

An upgrade is a set of actions that must be performed to update a package from a version to another one.

Actions can be:

-   _schema actions_: actions that will manipulate the database schema.
-   _data actions_: actions that will manipulate the data.

All the actions (data and/or schema) are grouped into `upgrade suites`.

## Cold vs hot upgrades

Cold upgrades interrupt the service. Hot upgrades don't. Upgrades are for now implemented as cold upgrades.

## Folder structure

Upgrade actions will be located under the folder _/lib/upgrades_ (one folder per upgrade step):

For instance, the _/lib/upgrades/v0.2.1_ folder will contain the upgrade actions (schema and/or data) to be executed to upgrade to version 0.2.1.

Every upgrade folder will contain a series of file (one file per action) and an index.ts file that defines the upgrade suite.

For more detailed information, see the Custom Actions page later in this section.

## General idea

-   An **upgrade process** upgrades a package from a given version to another, not necessarily consecutive.
    Upgrade process steps are detailed in the Upgrade process section.

-   An **upgrade suite** upgrades a package from a given version to the next consecutive version.
    The upgrade process calls a succession of upgrade suites.

-   A **schema action** is used to update the database schema. Schema actions are part of an upgrade suite.
    Some schema actions are managed automatically by the framework, others need custom code from applicative code.

-   A **data_action** is used to update the database data. Data actions are part of an upgrade suite.
    Data actions need custom code from applicative code.

## JIRA epic

For more details, you can have a look to the JIRA epic [X3-204992](https://jira.sage.com/browse/X3-204992)
