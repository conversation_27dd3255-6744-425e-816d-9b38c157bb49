PATH: XTREEM/19+Upgrades/8+SQL+files

The upgrade engine is creating/using SQL files.

# Generation of SQL files

The [_xtrem_patch_release_ job](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1286) is run every night: it bumps the patch version of all the packages and upgrades them from x.y.z to x.y.(z+1).
While upgrading, it generates a SQL file that contains all the SQL statements that are needed to upgrade from x.y.z to x.y.(z+1).

All these SQL files are committed to gitHub (in the _sql_ folder of the application).

`Example`

The current version of _xtrem-services-main_ is 7.0.3, the _xtrem_patch_release_ job will bump it to 7.0.4 and will generate <NAME_EMAIL>_ in the _sql_ folder of the _xtrem-services-main_ package (and commit it).

# Daily upgrade: [cluster-CI](http://login.dev-sagextrem.com/) from x.y.z to x.y.(z+)

Every day, the _cluster_CI_ is upgraded from its current version (day -1) to the last "official" version.

This upgrade process only replays the existing SQL files.

`Example`

The current version of the _cluster-CI_ is 7.0.2 (version of the xtrem-services-main package). The _xtrem_patch_release_ job was executed twice yesterday, so the cluster-CI will be upgraded from 7.0.2 to 7.0.4.

The upgrade process will read the SQL statements from these 2 files and execute them (without running any upgrade action):

-   xtrem-services-main/sql/<EMAIL>
-   xtrem-services-main/sql/<EMAIL>

# Monthy upgrade: from x.0.0 to (x+1).0.0

A monthly upgrade consists in upgrading a cluster from its current version to the next major release.

`Example`

The cluster A is in version 7.0.0 and the current "official" version is 8.0.0
The upgrade process will read the SQL statements from all the SQL files that cover the upgrade range and execute them (without running any upgrade action):

-   xtrem-services-main/sql/<EMAIL>
-   xtrem-services-main/sql/<EMAIL>
-   ...
-   xtrem-services-main/sql/<EMAIL>

# PR validation pipeline

Every PR is validated by the [_xtrem-mono-pr_](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1405) pipeline.

This pipeline has a step (Services/Test upgrade) that ensures that the PR only contains modifications that are compliant with the upgrade engine.
This pipeline will restore the database to the last official version (from a db dump stored in S3) and will then execute all the upgrade actions defined by the PR.
If the PR was created a long time ago, it may happen that this step first has to replay some SQL files.

`Example`

The PR was created when the current version was 7.0.3 (xtrem-services-main package) but is now submitted when the current version is 7.0.6.

The _Test upgrade_ step will first replay these files:

-   xtrem-services-main/sql/<EMAIL>
-   xtrem-services-main/sql/<EMAIL>
-   xtrem-services-main/sql/<EMAIL>

and then execute all the upgrade actions (if any) defined by the PR.

# Locally testing an upgrade

Local testing of an upgrade will do pretty much the same as the `PR validation pipeline` (see previous part): restore a db dump from S3, replay (if needed) SQL files and execute the actions.

For more detail, please refer to chapter _6-test-upgrades_.
