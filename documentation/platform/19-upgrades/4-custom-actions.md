PATH: XTREEM/19+Upgrades/4+Custom+actions

# Custom actions

## Generalities

### Terminology reminder

- An **upgrade suite** is the set of actions (schema and/or data actions) that must be run to upgrade an application to a given version.

- A custom **schema action** is an action provided by applicative teams to manage some special cases that can't be handled automatically by the upgrade engine.

- A **data action** is a custom action, provided by applicative teams, to update/create the data stored in the database.

### Need for an upgrade suite

- If there are custom schema or data actions to be performed to upgrade a version to the next consecutive one, an upgrade suite has to be created.

- If the upgrade can be performed without custom actions, no upgrade suite needs to be defined.

### File system structure

By convention, all the upgrade suites must be located in the /lib/upgrades/ folder, one sub-folder (named vxxx where xxx is the 'toVersion' of the suite: v1.2.3, v2.5.1, ...) per upgrade suite.

![file-system-structure](assets/images/file-system-structure.png)

Here, 3 upgrade suites are provided (1 to upgrade to version 0.9.1, 1 to upgrade to version 1.1.2 and the last one to upgrade to version 1.2.0).

For instance, the _v0.9.1_ folder will contain the upgrade actions (schema and/or data) to be executed to upgrade to version 0.9.1.

Every upgrade folder will contain a series of files (one file per action) and an index.ts file that defines the upgrade suite.

> ⚠️ **[IMPORTANT]**
> The name of the folder must be the version, prefixed with 'v' (v0.2.1, v0.7.4, v1.5.8, ....)

## Create the upgrade suite

- First create a folder named after the destination version in /lib/upgrades/.

- The /lib/upgrades/vX.Y.Z folder must contain:
    1. One file per upgrade action to perform. In this example: a schema action and a data action.
    2. An index.ts file defining the custom actions necessary to perform the upgrade suite.

### Create a custom schema action: rename a property

When an applicative team renames a property, the upgrade engine can't handle it automatically as the renaming would be detected as 2 unrelated events:

- the old property was deleted
- the new property has been created

The data present in the old column would then be lost.

To handle this case, a specialized schema action (SchemaRenamePropertyAction) is available to declare the renamings:

```ts
import { SchemaRenamePropertyAction } from '@sage/xtrem-system';

export const renamePropertyAction = new SchemaRenamePropertyAction({
    node: () => NodeWithPropertyToRename, // Replace with the proper Node
    oldPropertyName: 'oldPropertyName',
    newPropertyName: 'newPropertyName',
});
```

| Step           | Action(s)              |
| -------------- | ---------------------- |
| pre-upgrade    | The column is renamed. |
| custom actions | -                      |
| post-upgrade   | -                      |

### Create a custom schema action: rename a node

When an applicative team renames a node (and so, its table), the upgrade engine can't handle it automatically as the renaming would be detected as 2 unrelated events:

- the old node was deleted
- the new node has been created

The data present in the old table would then be lost.

To handle this case, a specialized schema action (SchemaRenameNodeAction) is available to declare the renamings:

```ts
import { SchemaRenameNodeAction } from '@sage/xtrem-system';

export const renameNodeAction = new SchemaRenameNodeAction({
    node: () => NodeWithNewName, // Replace with the proper Node
    oldNodeName: 'oldNodeName',
});
```

| Step           | Action(s)             |
| -------------- | --------------------- |
| pre-upgrade    | The table is renamed. |
| custom actions | -                     |
| post-upgrade   | -                     |

### Create a custom schema action: upgrade enum

When an applicative team changes the enum values, the upgrade engine can't handle it automatically as a replacement value must be provided. This custom action can be used to:

- Rename an enum value, the new value should be specified in the valuesMapping.
- Remove an enum value, with a replacement value for the already existing rows in the database.

The example below implements the custom action to handle the following use case:

- rename value1V1 -> value1V2
- rename value2V1 -> value2V2
- rename value3V1 -> value3V2
- remove value4V1 and replace the existing records with value1V2
- remove value5V1 and replace the existing records with null (very dangerous),

```ts
import { SchemaEnumUpgradeAction } from '@sage/xtrem-system';
import { upgradeEnumDataType } from '../../enums/upgrade-rename-enum';

export const enumUpgrade = new SchemaEnumUpgradeAction({
    description: 'Upgrade enum UpgradeEnum',
    dataType: upgradeEnumDataType,
    valuesMapping: {
        value1V1: 'value1V2',
        value2V1: 'value2V2',
        value3V1: 'value3V2',
    },
    membersToDelete: {
        value4V1: 'value1V2', // existing values in db equals to 'value4V1' will be replaced with 'value1V2'
        value5V1: null, // no replacement for existing rows
    },
});
```

> ⚠️ **[WARNING]** > not providing any replacement value for a deleted member (here value5V1:null) is **HIGHLY RISKY** as the action will fail if the member is referenced by at least one record (which could happen on customers databases). This option should not be encouraged, please try to ALWAYS provide a replacement value

### Create a custom schema action: change property enum datatype

When an applicative team changes the datatype on an enum property, the upgrade engine does not handle it automatically, as replacement values must be provided for the previous enum datatype values. This custom action can be used to:

- Update the enum property datatype on the database.
- Replace the values of the old enum datatype, specified in the valuesMapping. If the property is nullable, the replacement value could be null. All values of the old enum datatype must be included in the valuesMapping

The example below implements the custom action to handle the use case described:

```ts
import { SchemaEnumPropertyDatatypeUpgradeAction } from '@sage/xtrem-system';
import { MyNode } from '../../nodes/my-node';

export const updateMyEnumDatatype = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Upgrade enum property myEnum of node MyNode',
    node: () => MyNode,
    propertyName: 'myEnum',
    valuesMapping: {
        oldEnumValue1: 'newEnumValue1',
        oldEnumValue2: 'newEnumValue2',
        oldEnumValue3: 'newEnumValue1',
    },
});
```

**N.B**: Enum mapping updates are done prior to any defined data actions, therefore the actions (if any) should consider the new mapped enum value instead of the old value.

### Create a custom data action: execute custom SQL statements (classic SQL pool)

> ⚠️ **[DANGER ZONE]** > This action is very powerful and efficient but also extremely dangerous and may lead to data loss. Please, use it with extrem caution and only when no other solution can be used.

```ts
import { CustomSqlAction } from '@sage/xtrem-system';

export const dataCustomSqlAction = new CustomSqlAction({
    description: 'foo',
    body: async helper => {
        await helper.executeSql(`UPDATE ${context.schemaName}.foo_table SET new_value = old_value`, []);
    },
});
```

The helper parameter of the body provides some functions:

- _helper.schemaName_: the schema name for the SQL query
- _helper.executeSql_: execute a SQL statement (with optional args)

| Step           | Action(s)                |
| -------------- | ------------------------ |
| pre-upgrade    | -                        |
| custom actions | the foo_table is updated |
| post-upgrade   | -                        |

### Create a custom schema action: execute custom SQL statements (system pool)

> ⚠️ **[DANGER ZONE]** > This action is very powerful and efficient but also extremely dangerous and may lead to data loss. Please, use it with extrem caution and only when no other solution can be used.
>
> This action uses a `system pool` to run the queries which means that you can have access to the schema (and potentially change and break it).
> If you don't need so much power, please use a `CustomSqlAction` instead.

```ts
import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const unsafeCustomSqlAction = new UnsafeCustomSqlAction({
    description: 'Custom SQL action',
    tableNamesToRenameAndDrop: ['upgrade_custom_sql_v_1'],
    body: async helper => {
        const systemColumns = helper.getSystemColumnNames(UpgradeCustomSqlV2);
        await helper.executeSql(
            `INSERT
                INTO
                ${helper.schemaName}.upgrade_custom_sql_v_2 (
                ${systemColumns},
                string_2,
                int_2,
                bool_2)
            SELECT
                ${systemColumns},
                'V2' || string_1,
                int_1 * 2,
                NOT(bool_1)
            FROM
                ${helper.schemaName}.temp_upgrade_custom_sql_v_1`,
        );
    },
});
```

The helper parameter of the body provides some functions:

- _helper.schemaName_: the schema name for the SQL query
- _helper.executeSql_: execute a SQL statement (with optional args)
- _helper.getSystemColumnNames_: returns the system columns for a given node (_\_id_, _\_update_stamp_, _\_create_user_, ...)

| Step           | Action(s)                                                                                                                                         |
| -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| pre-upgrade    | The table _upgrade_custom_sql_v_1_ is renamed to _temp_upgrade_custom_sql_v_1_ and _upgrade_custom_sql_v_2_ is re-created from its new definition |
| custom actions | The content of the table _temp_upgrade_custom_sql_v_1_ is copied to the new _table upgrade_custom_sql_v_2_                                        |
| post-upgrade   | The table _temp_upgrade_custom_sql_v_1_ is dropped                                                                                                |

### Upgrade encrypted properties

To encrypt a string property that have been already stored in plain-text, EncryptPropertiesAction should be:

```ts
import { EncryptPropertiesAction } from '@sage/xtrem-system';

export const encryptProperty = new EncryptPropertiesAction({
    node: () => UpgradePropertyToEncrypt,
    properties: ['encryptedValue1', 'encryptedValue2'],
});
```

Where 'encryptedValue1' and 'encryptedValue2' are string properties defined in the following node:

```ts
...
export class UpgradePropertyToEncrypt extends Node {
    ...
    @decorators.stringProperty<UpgradePropertyToEncrypt, 'encryptedValue1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isStoredEncrypted: true,
    })
    readonly encryptedValue1: Promise<string>;

    @decorators.stringProperty<UpgradePropertyToEncrypt, 'encryptedValue2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isStoredEncrypted: true,
    })
    readonly encryptedValue2: Promise<string>;
    ...
}
```

The existing value of the properties defined in properties are then encrypted and saved in the database.

### Create a data action

When a new column is added, its creation will be automatically managed by the upgrade engine (see § 2.1.1) but this column may need to be initialized with new values. This is especially true when dealing with non nullable columns. A DataUpdateAction must be used to initialize these new columns:

```ts
import { DataUpdateAction } from '@sage/xtrem-system';

export const addColumnsData = new DataUpdateAction({
    description: 'any description',
    node: () => NodeWithNewColumn, // Replace with the proper Node
    async where() {
        return (await (await this.customer).country) === 'FRA';
    },
    set: {
        location: 'xxx',
        async site() {
            return (await this.otherObject).site;
        },
    },
});
```

This sample will set the values for the 2 properties location/site for all the nodes that match the where() filter.

If more than one filter is needed, the application will have to provide one DataUpdateAction per filter.

> ⚠️ **[IMPORTANT]** > `DataUpdateActions` used to update a non-nullable reference **must not** provide any where() clause.
> The functions in the set/where clauses must be simple enough to be converted into SQL.
> If some more complex functions are needed, then please refer to '7-complex upgrades' chapter.

### Write the index.ts file to create an Upgrade suite

The upgrade suite of a version must be defined in the index.ts file under the /lib/upgrades/vA.B.C folder:

```ts
import { UpgradeSuite } from '@sage/xtrem-system';
import { addColumnsData } from './add-columns-data';
import { renameNodeAction } from './rename-node-action';
import { renamePropertyAction } from './rename-property-action';

export const upgradeSuite = new UpgradeSuite({
    actions: [renameNodeAction, renamePropertyAction, addColumnsData],
});
```

There must be one (and only one) upgrade suite for every upgrade folder (/lib/upgrades/vA.B.C).
