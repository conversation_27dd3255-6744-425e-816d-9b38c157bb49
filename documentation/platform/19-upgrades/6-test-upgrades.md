PATH: XTREEM/19+Upgrades/6+Test-Upgrades

# Test upgrades

## Requirements

The `Postgres Command Line Tools` must be installed on your machine.

### Windows

-   Download the MSI installer from [this page](https://www.enterprisedb.com/downloads/postgres-postgresql-downloads)
-   Run the install and make sure `Command Line Tools` is selected (you may want to install the other components, but it's not needed to test the upgrades).
    ![file-system-structure](assets/images/install-postgres-windows.png)
-   Make sure your Windows PATH refers to the folder where you installed Postgres + \bin.
    For instance, if you installed Postgres version 10 to the default folder (_C:\Program Files\PostgreSQL\10_), you must ensure your Windows PATH includes the folder _C:\Program Files\PostgreSQL\10\bin_.

### Linux

Please refer to [this page](https://www.postgresqltutorial.com/postgresql-getting-started/)

## Test an upgrade

Thanks to the database dumps that are stored in the _s3://xtrem-developers-utility_ S3 bucket, developers can now locally check if their new nodes/properties are compliant with the upgrade engine.

To be able to access the S3 bucket, your `xtrem-config.yml` file must contain your S3 credentials. These credentials are for reading only and available in `AWS Xtrem cache RO access` record from https://keepersecurity.eu/vault/#:

```
s3:
  xtrem-developers-utility:
    accessKey: 'check-your-keeper-vault'
    secret: 'check-your-keeper-vault'
```

⚠️The following commands must be run in the xtrem-services-main folder.

-   restore a backup of cluster-ci to your local database

    `pnpm run xtrem schema --restore-from-s3 --s3ConfigType=clusterCiBackup`

-   test your upgrade locally

    `pnpm run xtrem upgrade --test --install-dependencies --skip-db-restore`

-   check your database to make sure your upgrade was correctly executed

# Azure pipeline

The _xtrem-services-patch-release_ azure pipeline also tests the upgrade to ensure that the new image is valid. The azure pipeline can be launched in a **force** mode (checkbox to enable when running the pipeline).

-   if the **force** flag is not set (default), the pipeline will fail if the upgrade can't be done
-   if the **force** flag is set, the pipeline will not fail if the upgrade can't be done but it will delete all the database dumps from the S3 bucket and then store the schema in the latest version. This flag must thus be used with **extreme caution**. This flag should only be used when the upgrade would need too many Data/Schema actions to be developed to succeed (huge refactoring for instance).
