PATH: XTREEM/19+Upgrades/2+Upgrade+process

# Upgrade process

The upgrade process will upgrade a package from '_fromVersion_' to '_toVersion_':

-   _fromVersion_: extracted from the _pack_version_ table. It matches the latest version of the package that was used against this database.
-   _toVersion_: extracted from the _package.json_ file of the package. It matches the latest version of the package that was delivered and published to npm.

When many upgrades are required, all the upgrades will be applied, sorted by ascending version number.

For instance, if a package must be upgraded from version 1.5.4 (version from the _packVersion_ node) to version 2.4.1 (version from the _package.json_ file) and the _lib/upgrades folder contains the following sub-folders (each sub-folder will contain a series of action AND an upgrade suite defined in the \_index.ts_ file):

-   v1.2.8
-   v1.5.4
-   v1.5.6
-   v1.8.2
-   v2.3.1
-   v2.4.1

The following _upgrade suites_ will be applied:

-   v1.5.6
-   v1.8.2
-   v2.3.1
-   v2.4.1

Note that the upgrade v1.5.4 will not be applied (it was already previously applied when the package got upgraded to version 1.5.4).

## Upgrade sequence

When applying an upgrade, the following steps will be executed:

-   all the changes to the database that can be managed automatically will be applied (new column, new table, ...). No constraint will be created at this step.
-   invoke all the _actions_ of the current _upgrade suite_
-   set all the automatic constraints (indexes/foreign keys, ...) and remove all the deleted columns/tables.

Note: all the columns/tables to be deleted will only be deleted at the very end so that _data actions_ can retrieve and use the 'old values'.

## Upgrade process steps

More specifically, when upgrading an application (from 1.0.0 to 2.0.0 for instance), the following steps will be run.

### Pre-upgrade

-   The pre-upgrade step will prepare the database for the migration.

-   This step will create all the new objects without any constraint and will remove the constraints on the updated objects.

-   This step is automatically managed by the upgrade engine.

### Actions

-   Will run all the custom actions defined by applicative teams.

-   These actions (schema/data) are needed to cover what can't be done automatically by the upgrade engine.

### Post-upgrade

-   The post-upgrade step finalizes the upgrade of the process.

-   This step will create all the constraints on all the new/updated items.

-   This step is automatically managed by the upgrade engine.
