PATH: XTREEM/19+Upgrades/12+custom-sql+scripts

# Purpose

The purpose of _custom SQL scripts_ is to be able to execute a SQL script on a production environment.

We already can execute upgrade actions on a cluster where some data need to be fixed but this implies to create a new hotfix and then deploy it on the environments.

With _custom SQL scripts_, we can simply execute a script that was previously uploaded to S3, without having to create any new image and without any deployment on the environment.

More details can be found here: https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Custom+sql+data+fix+tool

# Using _custom SQL scripts_

To use a _custom SQL script_, the following steps have to happen:

-   Create the SQL script to be applied
-   Locally test the SQL script
-   Create a PR for your script on a release branch
-   Upload the SQL script to S3
-   Ask LSO to execute the script, you will have to provide the list of tenants where the script has to be applied

⚠️**IMPORTANT** A custom SQL script can cause a lot of troubles if it contains any mistake. All these steps must be double-checked to make sure that no data will be broken in a production environment.

## Create the SQL script to be applied

A SQL script is a simple text file (with .sql extension) that contains the SQL script with some markers:

### Example

```sql
update %%SCHEMA_NAME%%.the_table set (the_column=valid_value) where _tenant_id=%%TENANT_ID%% AND the_column=old_invalid_value
```

**NOTE**: a custom SQL script only patches ONE tenant. A SQL script **MUST NOT** patch data across multiple tenants.

If more than one tenant has data issues to fix in the production environment, the script will be executed against all these tenants.

### Markers

A SQL script **MUST NOT** contain any static schema name and/or static tenant id.

Instead, some markers have to be used. They will be resolved with the right values, when the script will be executed:

-   %%SCHEMA_NAME%% will be replaced with the name of the schema
-   %%TENANT_ID%% will be replaced with the tenant id where the script is executed.

## Locally test the SQL script

Once THE script is written, you can test it by using this CLI command:
`pnpm run xtrem tenant --execute-custom-sql --location=xxxx --tenants="aaa","bbb"`

-   xxxx is the (local) path to the SQL script
-   "aaa","bbb" list of the tenants on which you want to apply the script

⚠️**IMPORTANT**:

-   your script **MUST** be applied on a database that contains the same issue as the one your script is addressing (either restore a database or manually create the issue in database before running the script)
-   you **MUST** check that the issue is fixed, after the execution of the script.

## Create a PR

You will then have to create a PR to merge your script to the release branch that matches the version of the environment you want to fix.
Such a PR must be reviewed with extreme caution as it could break data on a production environment.

### Example

An issue has to be fixed in the database of a production environment. The environment is running the latest image for v37 (37.0.48 for instance).

You will have to create a PR to merge your script to the _release/37.0_ branch.

## Upload the SQL script to S3

Use [this pipeline](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4383) to upload your script to S3.

You will have to provide:

-   branch/tag: the branch to use. It must be a release branch (release/37.0 for instance). This branch must contain the script to upload (see previous paragraph)
-   the location of the script within the branch. This location is relative to the root of the monorepo, without any leading '/'. For instance
    `services/main/xtrem-services-main/custom-sql/my-wonderfull-script.sql`

The pipeline will test that your script can be executed and then upload it to S3: its URI will be `s3://xtrem-developers-utility/custom-sql/x.x.x/my-wonderfull-script.sql` where `x.x.x` will be replaced by the current version of release branch.

⚠️**IMPORTANT** the pipeline will only test that the script can be executed. It will not test that the fix is correct or that nothing was corrupted in the database.

### Example

An issue has to be fixed in the database of a production environment. The environment is running the latest image for v37 (37.0.48 for instance).

Once your PR has been merged to release/37.0, you will have to run the pipeline:

-   branch/tag: release/47.0
-   location of the script: path-to-the-script/my-wonderfull-script.sql

If the script does not fail, it will be uploaded to `s3://xtrem-developers-utility/custom-sql/37.0.48/my-wonderfull-script.sql`

## Ask LSO to execute the script

# Example of scenario

A data issue has to be fixed in the database of a production environment. The environment is running the latest image for v37 (37.0.48 for instance).

-   I create a branch from release/37.0 (because the environment is running the latest v37 image)
-   In this branch, I create a SQL script to fix the data issue
-   I test the SQL script locally from a database that contains exactly the same kind of data issue
-   I create a PR to merge my branch into release/37.0
-   Reviewers will have to be extremly rigorous
-   I merge the PR into the release/37.0 branch
-   I run the pipeline to upload my script to S3
-   I ask LSO to run the script on the production environment to fix the data issue on the impacted tenants
-   I check that the data issue has been fixed by the script (I can use Sumologic to get the logs)
