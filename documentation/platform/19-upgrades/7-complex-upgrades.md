PATH: XTREEM/19+Upgrades/7+Complex+upgrades

As seen in chapter '4-custom actions', the set/where clauses of `DataUpdateActions` must be simple enough to be converted to SQL.

Sometimes, the _upgrade actions_ needed to update a property cannot be written with simple functions. If so, you need to use _complex upgrades_.

A _complex upgrade_ is based on an _upgrade bundle_, i.e. a special type of bundle that will be installed on a running server to prepare the data for the future upgrade.

Here is the sequence:

-   the server is running
-   the _upgrade bundle_ is installed
-   the _upgrade bundle_ prepares some data (this step may take a lot of time depending on the size of the database)
    -   new values for properties
    -   new nodes
-   the server is stopped
-   the server is started in _upgrade_ mode (_pnpm run xtrem schema --upgrade_). Please note that the server will be off-line while the upgrade will be running.
-   the upgrade engine updates the schema and copies all the prepared data (nodes/properties) into the upgraded tables/columns

# Structure of an _upgrade bundle_

## package.json

The `package.json` of an _upgrade bundle_ MUST contain the following declaration:

```json
    "xtrem": {
        "isBundle": true,
        "isUpgradeBundle": true,
    },
```

## lib/nodes

The _lib/nodes/_ folder will contain the declaration of all the new nodes, i.e. the nodes that will be added to the next version and will be created by some node extensions.

Every node must contain, in its decorators, a bundleId that points to the package name of the _upgrade bundle_ (the bundleId must match the name of the package - set in `package.json`). For instance, if a new _ResourceCost_ node is needed:

```typescript
@decorators.node<ResourceCost>({
    storage: 'sql',
    isPublished: false,
    bundleId: '@sage/xtrem-upgrade-to-v1.7',
})
export class ResourceCost extends Node {
    @decorators.stringProperty<ResourceCost, 'label'>({
        isStored: true,
        isPublished: false,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly label: Promise<string>;
}
```

## lib/node-extensions

The _lib/nodes-extensions/_ folder will contain the extensions for all the existing nodes that need to be extended with some new properties.

Every node extension must contain, in its decorators, a bundleId that points to the package name of the _upgrade bundle_ (the bundleId must match the name of the package - set in `package.json`). For instance, an extension of a _BaseResource_ that would add a _resourceCost_ non nullable reference may look like:

```typescript
@decorators.nodeExtension<BaseResourceForUpgrade>({
    extends: () => BaseResource,
    bundleId: '@sage/xtrem-upgrade-to-v1.7',
})
export class BaseResourceForUpgrade extends NodeExtension<BaseResource> {
    @decorators.referenceProperty<BaseResourceForUpgrade, 'resourceCost'>({
        isStored: true,
        node: () => ResourceCost,
        upgrade() {
            if (await this.resourceCost != null) return;
            // Create an instance of ResourceCost
            const newNode = await this.$.context.create(ResourceCost, { label: ... });
            await newNode.$.save();
            this.resourceCost = newNode;
        },
    })
    readonly resourceCost: Promise<ResourceCost>;
    ...
}
```

> ⚠️ **[NOTES]**
> The code inside the _upgrade_ function has some limitations:
>
> -   you can only update the properties defined in the bundle. Here, setting _resourceCost_ is allowed because the _resourceCost_ property is defined in the upgrade bundle but setting a property defined in the _BaseResource_ node would raise an error
> -   you can only create instances of a node defined in the _upgrade bundle_. Here, creating a new _ResourceCost_ is allowed as the _ResourceCost_ node is defined in the _upgrade bundle_ but creating an instance of _BaseResource_, for instance, would raise an error.

# Bundle installation

_Upgrade bundles_ can be installed the same way as classic bundles.

## Activation of the bundle

When an _upgrade bundle_ is installed on a cluster, it is automatically activated for all the tenants of the cluster and will start to prepare the data.

## Data preparation

All the existing records in the database for all the defined _node-extensions_ will be read. For every record, a node will be instantiated and the _upgrade_ function of all the properties will be invoked.

The code inside the _upgrade_ function is in charge of setting the value for the property.

These temporary values will be stored in the _\_custom_data_ column of the table (in JSON format).

For instance, installing an _upgrade bundle_ with the previous `BaseResourceForUpgrade` node extension will read (1 by 1) all the records of the `base_resource` table and create an instance for every record. The _instance.resource.upgrade()_ function will then be invoked for every instance.

The _upgrade_ functions can:

-   find an existing instance to reference
-   create a new instance and reference it. The _upgrade_ function will only allow to create instances of _nodes_ declared in the same _upgrade bundle_. For instance, here, the _upgrade_ function can only create instances of `ResourceCost`.

> ⚠️ **[WARNING]**
> It's important that every _upgrade_ function only set the value for the property it is bound to.
> An _upgrade_ function should not delete anything and MUST NOT update any other existing instance.

> ⚠️ **[IMPORTANT]**
> Even when the preparation step is over, you **MUST NOT** unistall the _upgrade bundle_ until the database has been upgraded: the _upgrade_ function will not only prepare the data for all the existing records but also prepare the data for any record which is created or updated while the service is running.

# Upgrade sequence

-   Online
    -   install the _upgrade bundle(s)_
    -   Wait for the _upgrade bundle(s)_ to prepare the data
-   Offline
    -   Deploy a new image with all the new features (new nodes / new properties)
    -   Start the upgrade process (_pnpm run xtrem schema --upgrade_). The upgrade engine will update the schema and copy all the prepared data (nodes/properties) into the tables/columns to upgrade.

<img src="./assets/images/upgrade-sequence.png" alt="Upgrade sequence" width="500"/>

_Summary of the upgrade sequence_

# Develop an _upgrade bundle_

To develop an _upgrade bundle_, please refer to [this page](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/upgrade-bundles/xtrem-upgrade-bundle-template).

# Install an upgrade bundle with command line

Please refer to the xtrem-cli documentation.

# Special cases

## Changing a computed property into a stored property (with a formula that can't be translated into SQL)

Let's say, you have a `computed` property `Customer.age` that you want to convert into a `stored` property and let's imagine that the formula to compute the age is too complex to be translated into SQL.

You have to:

-   create an upgrade bundle with a nodeExtension of `Customer` that defines a new `age2` property (the name of this property is not important but it must not collide with any other existing property of `Customer`).
-   the `age2` property has a `upgradePropertyName` decorator that contains the name of the existing computed property:

```ts
    @decorators.integerProperty<CustomerExtension, 'age2'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['age'],
        upgradePropertyName: 'age',
        async upgrade() {
            await this.$.set({ age2: await this.age });
        },
    })
    readonly age2: Promise<number>;
```

-   the `upgrade` decorator of the `age2` property simply copies the value of the computed property (involving your complex formula) into the stored property.

And that's it :-)

When activated (cluster on-line), your upgrade bundle will prepare the data and store the value for the `age2` property in the `bundle_data` columns of the customer table.

When upgrading (cluster off-line), the upgrade engine will create the `age` column (and not `age2` thanks to the `upgradePropertyName` decorator attribute) and will copy the values prepared by the upgrade bundle into this new column.
