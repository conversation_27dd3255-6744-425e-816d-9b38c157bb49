PATH: XTREEM/19+Upgrades/5+Database+dumps+in+S3+buckets

# S3 buckets

The _s3://xtrem-developers-utility_ S3 bucket stores database dumps that can be used by developpers to reset their local database

# Restore a dump

`pnpm run xtrem schema --restore-from-s3 x.x.x`

will restore the version x.x.x (if available in the S3 bucket) of the current application.

`pnpm run xtrem schema --restore-from-s3`

will restore the last available version

`pnpm run xtrem schema --restore-from-s3 --s3ConfigType=clusterCuBackup`

will restore the last available continuous upgrade backup

`pnpm run xtrem schema --restore-from-s3 --s3ConfigType=upgradeBundles`

will restore the last available upgrade bundles

# List available versions

`pnpm run xtrem schema --list-s3-versions`
will list the available dump.

# Configuration

To be able to restore a database dump, you need to setup your `xtrem-config.yml` with S3 credentials. These credentials are for reading only and available in `AWS Xtrem cache RO access` record from https://keepersecurity.eu/vault/#:

```
s3:
  xtrem-developers-utility:
    accessKey: 'check-your-keeper-vault'
    secret: 'check-your-keeper-vault'
```

# When are these dumps created ?

Database dumps are created and stored to S3 by the _xtrem-services-patch-release_ azure pipeline (the one that is used to publish _xtrem-services_ packages to _npm_)
