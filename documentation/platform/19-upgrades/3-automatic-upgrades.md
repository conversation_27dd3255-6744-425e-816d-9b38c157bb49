PATH: XTREEM/19+Upgrades/3+Automatic+upgrades

# Automatic upgrades

This section lists the schema actions automatically performed by the framework.

## Columns

### New column

| Step           | Action(s)                                                                                                                                    |
| -------------- | -------------------------------------------------------------------------------------------------------------------------------------------- |
| pre-upgrade    | The new column is created with no constraints (index, foreignKey, isNullable, ...).                                                          |
| custom actions | User actions are in charge of providing data for the new column - especially for mandatory columns with no default value (references, ....). |
| post-upgrade   | All the constraints for the new column are set.                                                                                              |

### Deleted column

| Step           | Action(s)                                             |
| -------------- | ----------------------------------------------------- |
| pre-upgrade    | Remove all the constraints on the column to delete.   |
| custom actions | Actions can use the data from the column.             |
| post-upgrade   | Delete the column (and its values) from the database. |

### Updated column

**isNullable: for now, only the 'isNullable' attribute of a property can be automatically upgraded.**

| Step           | Action(s)                                             |
| -------------- | ----------------------------------------------------- |
| pre-upgrade    | Remove all the constraints on the out-of-date column. |
| custom actions | Actions can use the data from these columns.          |
| post-upgrade   | The column is upgraded.                               |

### Change the type of a column

The upgrade engine supports the following type changes:

-   string to reference
-   string to integer
-   string to textStream
-   string to string: this kind of change is limited to the extension of a column
-   string to localized string: to a json { "en": "current string value" }
-   integer to decimal

| Step           | Action(s)                                                                |
| -------------- | ------------------------------------------------------------------------ |
| pre-upgrade    | Remove all the constraints on the out-of-date column.                    |
|                | Alter the column type and use the defaultValue decorator to migrate data |
| custom actions | -                                                                        |
| post-upgrade   | Constraints are restored                                                 |

## Indexes

### New index

| Step           | Action(s)                                 |
| -------------- | ----------------------------------------- |
| pre-upgrade    | -                                         |
| custom actions | -                                         |
| post-upgrade   | The new index is created in the database. |

### Deleted index

| Step           | Action(s)                                        |
| -------------- | ------------------------------------------------ |
| pre-upgrade    | The obsolete index is deleted from the database. |
| custom actions | -                                                |
| post-upgrade   | -                                                |

### Updated index

| Step           | Action(s)                                                     |
| -------------- | ------------------------------------------------------------- |
| pre-upgrade    | The obsolete index is deleted from the database.              |
| custom actions | -                                                             |
| post-upgrade   | The index is created in the database with its new definition. |
