PATH: XTREEM/19+Upgrades/9+Type+changing

The following type changes on a column are supported by the upgrade engine:

| Initial type     | New type                    |
| ---------------- | --------------------------- |
| string           | reference                   |
| string           | string (encrypted)          |
| string           | string (with larger length) |
| string           | localized string            |
| localized string | string                      |
| integer          | decimal                     |
| date             | datetime                    |
| datetime         | date                        |

Please create enhancement requests for additional types to be supported.

A `defaultValue` or a `data action` should be provided to manage the conversion. The `defaultValue` of the property will be used to compute the new value for the column if it was provided, if not, the `data action` will be used.

For now, only very basic `defaultValue` are supported.
