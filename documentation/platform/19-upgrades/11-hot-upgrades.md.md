PATH: XTREEM/19+Upgrades/11+Hot+upgrades

# Hot upgrades

When an application is released, its _package.json_ file is updated with the `isReleased` flag:

```
    "xtrem": {
        "packageName": "foo",
        "isReleased": true
    },
```

From then, no schema changes are allowed but dataActions are still allowed to fix some data-related issues.

Applicative teams can still register upgrades with ONLY data actions (for instance no `SchemaCustomSqlAction` will be allowed as they could be used to change the schema, instead `DataCustomSqlAction` should be used if TypeScript is required for the action)

The _patch-release_ pipeline will still generate new SQL files for released applications.

When a new image is generated for this release, it will be deployed as usual but when starting, the container will detect that a hot upgrade is required and will run it.
The hot upgrade will contain:

-   all the new dataActions registered by applicative teams,
-   the reloading of all the updated CSV files

Notes:

-   no CLI command will be executed to trigger the hot upgrade (simply `pnpm run start` as usual)
-   if more than one container is using the same database, only one of them will execute the hot upgrade.
