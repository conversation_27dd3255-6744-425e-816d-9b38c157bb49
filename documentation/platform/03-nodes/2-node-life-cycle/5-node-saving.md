PATH: XTREEM/2+Node+life+cycle/4+Node+saving

# Node saving

Any modification made to a node is not persisted to the database until you explicitly save the node.

## How to save a node

To save a node, just run:

```ts
node.$.save();
```

This will throw an error if the saving fails for any reason (error diagnoses added by _saveBegin_ / _saveEnd_ / ... events)

## Deferred save

It's also possible to defer the saving of a node. The node will then only be saved at the end of the transaction.

```ts
node.$.save({ deferred: true });
```

This option is important for performance. It must be set when your logic may save a given node several times in the same transaction. Deferring ensures that the node will be saved only once.
