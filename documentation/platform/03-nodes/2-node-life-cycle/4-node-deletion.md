PATH: XTREEM/2+Node+life+cycle/4+Node+deletion

# Node deletion

## How to delete a node

To delete a node, just run:

```ts
node.$.delete();
```

This will throw an error if the delete fails for any reason (error diagnoses added by _controlDelete_ events - see below)

You can also use:

```ts
const result = node.$.tryDelete();
```

_tryDelete_ returns a boolean indicating whether the delete was successfull.
If _result_ is false, you can get the reason in the context diagnoses:

```ts
const diagnoses = node.$.context.diagnoses;
```

## The deletion process in details

When a node is deleted, the following steps are run:

### Invoke the controlDelete() event

The delete algorithm executes the controlDelete rules on all the nodes of its vital subgraph (traversing vital collections and references, recursively): these nodes are fetched from the database only if the nodes of the vital subgraph carry deleteControl rules

```ts
@decorators.node<InvoiceLine>({
    ...
    controlDelete(cx) {
        ...
    },
    ...
})
```

It's the right place for applicative code to decide whether a node can be deleted. If for any reason, the node can't be deleted, a diagnose must be added to the context (_cx_) provided as a parameter to prevent the deletion of the node.

```ts
cx.addDiagnoseAtPath(ValidationSeverity.error, `This node can't be deleted because ...`);
```

**Note** When the `controlDelete` event is invoked, the node status (_node.\$.internalStatus_) is not updated yet.

### Invoke the deleteBegin() event

The `deleteBegin` event, if defined, is invoked on the node to be deleted, before its actual deletion.

**Note**: the `internalStatus` of the node is now set to _deleted_.

> ⚠️ **[IMPORTANT]**
> The applicative code **must** be protected against errors. The `controlDelete` event has already stated that the node could be deleted. The `deleteBegin` event must only be used to update some other nodes, statistics and **must not** be used to prevent the node from being deleted.

### Delete from database

All the nodes are effectively deleted from the database.

### Invoke the deleteEnd() event

The `deleteEnd` event, if defined, is invoked on the node to be deleted, after its actual deletion.

### Update the internalStatus

Once deleted, the `internalStatus` of all the nodes is set to `stale`. That means that any applicative code that would hold a reference to a `stale` node must no longer use it.
