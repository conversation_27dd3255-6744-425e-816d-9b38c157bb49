PATH: XTREEM/2+Node+life+cycle/1+Applicative+events

# Applicative events

## What are applicative events

Applicative events are entry points provided by the framework. They allow applicative developers to short circuit a save/delete/read operation on a node and inject their own logic to perform the operation. These events are defined on the decorator of the node.

```ts
applicativeEvents: {
        /**
         * @param cx validation context
         **/
        save(cx) {
            //save logic
        },

        /**
         * @param cx validation context
         **/
        delete(cx){
            //delete logic
        },

        /**
         * @param context context
         * @param key
         **/
        read(context,key){
            //read logic
        },
    },
```

## Available applicative events

### save

This event short-circuits all the SQL save logic (saveXxx events, insert, update events). It can be used to serialize the node and send it to another service which will persist it. The overridden logic will have to save the node and all its children (collections), where required.

The `save` event gets past 2 parameters

-   the current node (`this`)
-   the validation context (`ValidationContext`)

```ts
/**
 * @param cx validation context
 **/
save(cx) {
            //save logic
    },
```

### delete

This event short-circuits all the SQL delete logic. It can be used to send the node key to another service which will delete it.

The `delete` event gets past 2 parameters

-   the current node (`this`)
-   the validation context (`ValidationContext`)

```ts
/**
 * @param cx validation context
 **/
delete(cx) {
            //delete logic
    },
```

### read

This event short-circuits all the SQL read logic. It can be used to read the node from another service which persists it.

The `read` event gets past 2 parameters

-   current context (`context`)
-   the key passed into the node read (`key`)

```ts
/**
 * @param context context
 * @param key
 **/
read(context,key){
    //read logic
},
```

## Note

There is no `query` event as yet, only `read`.
