PATH: XTREEM/18+Managers/7+Readonly-database

# Using Read-Only Endpoints with Amazon Aurora Read Replicas

A read-only endpoint in Amazon Aurora is used to distribute read traffic across read replicas, enhancing the performance and scalability of the database.

## How It Works

-   **Primary Instance**: Handles all write operations and critical read operations where the latest data is required.
-   **Read Replicas**: Handle read-only operations. They receive updates from the primary instance asynchronously, which may result in a slight delay (replica lag) in reflecting the most recent data.

## Configuring the Read-Only Endpoint

In your SQL configuration, the read-only endpoint is typically specified under `readonlyHostname` and `readonlyPort`. This endpoint directs read queries to the read replicas, distributing the load and enhancing performance.

```yaml
storage:
    sql:
        readonlyHostname: 'readonly-hostname.tld'
```
