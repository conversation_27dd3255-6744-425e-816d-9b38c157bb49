PATH: XTREEM/18+Managers/2+Access+rights+manager

## Access Rights Manager

## What is the Access Rights Manager

The `access rights manager` is a class that implements the `AccessRightsManager` interface from the `@sage/xtrem-core` package. The `access rights manager` exposes functions that can be used to retrieve access specific data, as well to test access specific rules based on the application requirements.

```ts
export class myAppAccessRightsManager implements AccessRightsManager
```

This class can then be instantiated and assigned to the `accessRightsManager` attribute in the `Context` class from the `@sage/xtrem-core` package, thereby exposing it throughout the application.

```ts
const accessRightsManager = new myAppAccessRightsManager();
```

```ts
Context.accessRightsManager = accessRightsManager;
```

## Functions and attributes that need to be implemented from AccessRightsManager interface

### getUserAccessFor

Taking in the `context`, `nodeName`, `propertyOrOperation` and `options`. Based on these inputs the application developer must determine the access of the user to the provided `propertyOrOperation`. The function must return an object of type 'UserAccess'. This object will comprise of the of the following attributes;

-   `status` - the status is the user's access status to the operation. It can have the value, `authorized`, `unauthorized` or `unavailable`. `authorized` is used if the user has access to the operation. `unauthorized` is used if the user does not have access to operation. `unavailable` is used if the operation is not deployed or enabled.
-   `sites` - is a list of sites that the user has access to for the related operation. This list will be used for the site property filter by the framework.
-   `accessCodes` - is a list of accessCodes that the user has access to for the related operation. This list will be used for the accessCodes property filter by the framework.

```ts
getUserAccessFor(
        context: Context,
        nodeName: string,
        propertyOrOperation: string,
        options?: { authorizationCode?: string },
    ): UserAccess
```

export type UserAccess = {
status: AccessStatus;
sites: string[] | null;
accessCodes: string[] | null;
};

### isAccessCodeAvailable

Taking in the `context`, `userCode` and `accessCode` (from property decorator). Based on these inputs the application developer must determine if the user has the required access to the provided `accessCode`. If function returns true then properties that have been allocated the passed in `accessCode` will be restricted.

```ts
isAccessCodeAvailable(context: Context, userCode: string, accessCode: string): boolean{
    return (userCode==='<EMAIL>'||accessCode==='ALL');
}
```

### getUser

Taking in the `context` and `userCode`. Based on these inputs the application developer must return the relevant `UserInfo`.

```ts
getUser(context: Context, userCode: string): UserInfo {
        const user = context.query(User, { filter: { user: userCode.toLowerCase() }, first:1 });
        return user;
    }
```

### getPermissions

Taking in the `context` and `activity`. Based on these inputs the application developer must return an array of the access permissions for the supplied activity (function code for ADC). The user can be accessed from the context passed.

```ts
getPermissions(context: Context, activity: string): string[] {
        if (!context.user) return []

        if(activity === 'someActivity'){
            return ['read','create','update'];
        } else {
            return ['read'];
        }
    }
```

## Types

### UserInfo

-   `code` - user code.

-   `login` - user login code.

## Note

It is critical for that the access rights manager is implemented for the platform to work correctly.
