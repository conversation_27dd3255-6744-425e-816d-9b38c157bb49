PATH: XTREEM/18+Managers/4+External+storage+manager

# External storage manager

In order to fully utilise the external storage manager and not rely on a database connection, the `xtrem-config.yml` should be configured as follows:

```yaml
storage:
    managedExternal: true
```

For externally managed applications, it is important to implement an `ExternalStorageManager` to handle the crud operations on the nodes. Please refer to the [node storage documentation](https://confluence.sage.com/display/XTREM/4+Thorough+catalogue+on+node+decorated+class/1+Attributes/1+Storage) on how to use the external storage nodes.
