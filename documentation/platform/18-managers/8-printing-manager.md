PATH: XTREEM/18+Managers/8+printing+manager

# Printing Manager

## What is the Printing Manager?

The Printing Manager is a class that implements the `PrintingManager` interface from the `@sage/xtrem-core` package. Currently it provides URLs for various printing wizards used in the application.

## Example Implementation

Here is an example of an implementation in the `@sage/xtrem-reporting` package:

```ts
import { PrintingManager as IPrintingManager } from '@sage/xtrem-core';

export class PrintingManager implements IPrintingManager {
    getPrintingAssignmentDialogUrl(): Promise<string> {
        return Promise.resolve('@sage/xtrem-reporting/PrintingAssignmentDialog');
    }

    getListPrintingWizardUrl(): Promise<string> {
        return Promise.resolve('@sage/xtrem-reporting/PrintingDialog');
    }

    getRecordPrintingWizardUrl(): Promise<string> {
        return Promise.resolve('@sage/xtrem-reporting/PrintingDialog');
    }
}
```

This class can be implemented, instantiated and assigned to `CoreHooks.createPrintingManager` in packages where it is needed.

```ts
CoreHooks.createPrintingManager = () => new PrintingManager();
```

The loaded printing manager will then be used when generating the printing settings fragment meta data.
