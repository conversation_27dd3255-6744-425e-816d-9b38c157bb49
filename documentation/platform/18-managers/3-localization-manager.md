PATH: XTREEM/18+Managers/3+Localization+manager

# Localization Manager

## What is the Localization Manager

The `localization manager` is a class that implements the `LocalizationManager` interface from the `@sage/xtrem-core` package. The `localization manager` exposes functions that can be used to retrieve localization specific data such as languages and configuration.

```ts
export class myAppLocalizationManager implements LocalizationManager
```

This class can then be instantiated and assigned to the `localizationManager` attribute in the `Context` class from the `@sage/xtrem-core` package, thereby exposing it throughout the application.

```ts
const localizationManager = new myAppLocalizationManager();
```

```ts
Context.localizationManager = localizationManager;
```

## Functions that need to be implemented from LocalizationManager interface

### getDefaultTenantLocale

Returns the default locale for the current tenant.

```ts
 getDefaultTenantLocale(): string {
    return Context.readonly(
        context =>
            context.query(xtremSystem.nodes.Locale, {
                filter: { isDefaultLocale: true },
            })[0]?.id || 'base',
    );
}
```

### isMasterLocale

Returns true/false based on whether the current locale is set as the master locale for a language.
The value of the locale language entry will only be updated for the master locale.

```ts
isMasterLocale(context: Context): boolean {
    return context.read(Locale, { code: context.currentLocale }).isLanguageMasterLocale;
}
```

### createTenantLocale

Creates the first locale entry for a tenant and set the default locale, based on the create admin user data.

```ts
createTenantLocale(context: Context, locale: string): void {
    const newLocale = context.create(Locale, { code: locale, isDefaultLocale: true, isLanguageMasterLocale: true });
    newLocale.$.save();
}
```

## Note

-   The current context instance is passed in as a parameter.
-   All data are read from the Locale table in `xtrem-services`, and setup for each tenant.
