PATH: XTREEM/18+Managers/9+Data+Validation+Manager

# DataValidationManager

The `DataValidationManager` interface defines a service responsible for managing data validators and generating validation reports within the XTREEM platform. It provides methods to register validators for specific node classes, build validation reports, and retrieve registered validators.

## Interface: `DataValidationManager`

### Methods

#### `registerValidator<T extends Node>(nodeClass: StaticThis<T>, validator?: DataValidator<T>): void`

Registers a data validator for a specific Node class.

- **Parameters:**
    - `nodeClass`: The Node class to register the validator for.
    - `validator` (optional): The data validator to register.

#### `buildReports(application: Application): AsyncResponse<void>`

Builds validation reports for the given application, generating one report per tenant.

- **Parameters:**
    - `application`: The application for which to build validation reports.
- **Returns:** `AsyncResponse<void>`

#### `buildReport(writableContext: Context): AsyncResponse<number>`

Builds a validation report for the given writable context, concerning only the tenant of the context.

- **Parameters:**
    - `writableContext`: The writable context to generate the report for.
- **Returns:** `AsyncResponse<number>` — The `_id` of the generated validation report.

#### `getValidators(): DataValidator[]`

Retrieves all registered data validators.

- **Returns:** An array of registered `DataValidator` instances.

---
