PATH: XTREEM/18+Managers/9+Data+Validation+Manager/2+Data+Validators

# Introduction

A validator is in charge of validating a single instance.

Validators can be registered by using the DataValidatorManager.

# Default validator

A default validator is provided, it will invoke `instance.$.control()` and build the result according to the content of the context's diagnoses.

```ts
CoreHooks.getDataValidationManager().registerValidator(TheNodeToValidate);
```

# Register a custom validator

For more complex validations, a custom validator must be registered.

Such a validator is in charge of returning the result of the validation of ONE instance as a list of logs.

Each log will have the following format:

```ts
export interface InstanceDataValidationResultLine {
    /**
     * The message of the error/warning/info.
     */
    message: string;
    /**
     * The severity of the message (error/warning/info).
     */
    severity: MessageSeverity;
    /**
     * The validation path that raised the error/warning/info.
     */
    path: string;
    /**
     * Extra information about the error/warning/info.
     */
    extraInfo?: any;
}
```

To register a validator, simply use:

```ts
CoreHooks.getDataValidationManager().registerValidator(TheNodeToValidate, async (context, instance) => {
    // ... Return a list of logs (if any error/warning/info)
});
```
