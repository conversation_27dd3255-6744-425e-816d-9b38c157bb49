PATH: XTREEM/18+Managers/9+Data+Validation+Manager/1+Data+Validation

# Introduction

Some external events could break some data. For instance, if a property used in a report is deleted, the report won't work anymore.

This kind of issues can easily happen after an upgrade.

The data validation process will loop over existing data to detect potential errors and build a report of all these errors.

A validator must be registered for all the nodes to check. This validator is in charge of validating an instance of a node and return the list of errors for this instance. (see [manager](./0-introduction.md))
