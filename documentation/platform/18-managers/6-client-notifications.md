PATH: XTREEM/18+Managers/5+Client+Notifications+manager

# NotificationManager Interface

The `NotificationManager` interface defines methods for managing user notifications.

## Methods

### `getUserNotifications(context: Context): Promise<Array<PromisifyProperties<Notification>>>`

Retrieve user notifications.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.

**Returns:**

-   A Promise that resolves to an array of user notifications.

### `markRead(context: Context, _id: string): Promise<boolean>`

Mark a specific notification as read.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.
-   `_id` (type: `string`): The unique identifier of the notification to mark as read.

**Returns:**

-   A Promise that resolves to a boolean indicating the success of the operation.

### `markUnread(context: Context, _id: string): Promise<boolean>`

Mark a specific notification as unread.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.
-   `_id` (type: `string`): The unique identifier of the notification to mark as unread.

**Returns:**

-   A Promise that resolves to a boolean indicating the success of the operation.

### `markAllRead(context: Context): Promise<boolean>`

Mark all notifications as read for a user.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.

**Returns:**

-   A Promise that resolves to a boolean indicating the success of the operation.

### `delete(context: Context, _id: string): Promise<boolean>`

Delete a specific notification.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.
-   `_id` (type: `string`): The unique identifier of the notification to delete.

**Returns:**

-   A Promise that resolves to a boolean indicating the success of the operation.

### `dispatchUserNotification(context: Context, notification?: InitialNotification): Promise<void>`

Dispatch a notification to the user.

**Parameters:**

-   `context` (type: `Context`): The context object for the request.
-   `notification` (optional, type: `InitialNotification`): The initial notification to dispatch.

**Returns:**

-   A Promise that resolves when the notification has been dispatched.

### `context.notifyUser(notification: InitialNotification): Promise<void>`

Notifies a user with the specified `InitialNotification`.

**Parameters:**

-   `notification` (type: `InitialNotification`): The notification payload to be sent to the user. This payload contains the details of the notification, including its content and actions.

**Returns:**

-   A Promise that resolves when the notification has been successfully dispatched to the user. It does not return any specific value, as the notification dispatch operation is asynchronous.

This method is used to send notifications to users, utilizing the `InitialNotification` payload that contains the necessary information to create and display the notification.

## Notification Payload

The `Notification` payload is an object that represents a user notification. It contains the following properties:

-   `level` (type: `_NotificationLevel`): The notification level, which determines its importance or severity. `'error' | 'warning' | 'info' | 'success' | 'approval'`
-   `shouldDisplayToast` (type: `Boolean`): A boolean flag indicating whether a toast notification should be displayed for this notification.
-   `isRead` (type: `Boolean`): A boolean flag indicating whether the notification has been marked as read.
-   `icon` (type: `String`): The icon associated with the notification.
-   `title` (type: `String`): The title of the notification.
-   `description` (type: `String`): A detailed description or message associated with the notification.
-   `actions` (type: `Array<_NotificationAction>`): An array of notification actions that can be performed in response to the notification.

### Notification Action

The `_NotificationAction` represents an action that can be taken in response to a notification. It contains the following properties:

-   `title` (type: `String`): The title or label for the action.
-   `link` (type: `String`): A URL or link associated with the action.
-   `style` (type: `_NotificationActionStyle`): The style or appearance of the action. It has values `'primary' | 'secondary' | 'tertiary' | 'link'`

This payload and action structure is used to convey information about user notifications and allow users to interact with them by performing various actions based on the provided actions.

### Metadata Mutation Methods

These metadata mutation methods are used to modify and manage the status of user notifications and metadata associated with them.

#### `markRead(_id: String!): Boolean`

Marks a specific notification as read.

**Parameters:**

-   `_id` (type: `String!`): A required unique identifier for the notification to be marked as read.

**Returns:**

-   A Boolean value indicating the success of the operation. `true` if the notification was successfully marked as read, `false` otherwise.

#### `markUnread(_id: String!): Boolean`

Marks a specific notification as unread.

**Parameters:**

-   `_id` (type: `String!`): A required unique identifier for the notification to be marked as unread.

**Returns:**

-   A Boolean value indicating the success of the operation. `true` if the notification was successfully marked as unread, `false` otherwise.

#### `markAllRead: Boolean`

Marks all notifications as read for a user.

**Returns:**

-   A Boolean value indicating the success of the operation. `true` if all notifications were successfully marked as read, `false` otherwise.

#### `delete(_id: String!): Boolean`

Deletes a specific notification.

**Parameters:**

-   `_id` (type: `String!`): A required unique identifier for the notification to be deleted.

**Returns:**

-   A Boolean value indicating the success of the operation. `true` if the notification was successfully deleted, `false` otherwise.

These mutation methods allow you to modify the read/unread status, mark all notifications as read, and delete individual notifications. They are essential for managing user notifications and their associated metadata.
