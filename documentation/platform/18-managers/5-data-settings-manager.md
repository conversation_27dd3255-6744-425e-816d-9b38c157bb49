PATH: XTREEM/18+Managers/5+Data+Settings+manager

## Data Settings Manager

## What is the Data Settings Manager

The data settings manager is a class that implements the `DataSettingsManager` interface from the `@sage/xtrem-core` package. This class manages the following system properties:

-   `_vendor` indicates to which software vendor from `SysVendor` the data belongs to.

```ts
export class myDataSettingsManager implements DataSettingsManager;
```

This class can then be instantiated and assigned to the `dataSettingsManager` property in the `Context` class from the `@sage/xtrem-core` package, thereby exposing it throughout the application.

```ts
const dataSettingsManager = new myDataSettingsManager();
```

```ts
Context.dataSettingsManager = dataSettingsManager;
```

## Functions that need to be implemented from DataSettingsManager interface

The manage must implement the DataSettingsManager interface:

```ts
export interface DataSettingsManager extends BaseManager {
    getSysVendorNode(): StaticThis<Node>;
}
```

### getSysVendorNode

This function returns the `SysVendor` node. This node contains information about the vendor: name and description.

```ts
 getSysVendorNode(): StaticThis<Node> {
        return TestSysVendor;
}
```

## SysVendor Node

`SysVendor` node contains information about the vendor. It contains two properties: `name` and `description`.

By default all the setup nodes that have a natural key index add the `_vendor` system reference property, except for nodes that:

-   have `isPlatformNode` attribute to `true`
-   have `isSharedByAllTenants` attribute to `true`
-   have `hasVendorProperty` attribute to `false` (ex: `User` node)

N.B: `_vendor` column is nullable. Its default value is null.
