PATH: XTREEM/16+Service+model/2+Package+Activation

# Package activation

## Activate technical packages

Technical packages are packages that are required by all the services, which are:

-   xtrem-system,
-   xtrem-technical-data,
-   xtrem-master-data,
-   xtrem-structure.

Technical packages are activated by default and cannot be deactivated. They are not displayed in the admin page.

To set a package as technical, add an `isHidden` property to the `xtrem` key in its package.json file, as follows:

```json
{
    ...,
    "xtrem": {
        "isHidden": true
    },
}
```

## Automatic activation of new packages

New packages may be added to the application by an upgrade. By default these packages will be inactive in all tenants, unless they are dependencies of active packages.

This behavior can be overridden with a configuration parameter:

```yaml
upgrade:
    activateNewPackages: true,
```

If this `activateNewPackages` configuration flag is true, new packages will be automatically activated in all tenants.
