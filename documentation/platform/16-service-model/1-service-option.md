PATH: XTREEM/16+Service+model/1+Service+Option

# Service Options

## What is a Service Option?

A service option is a specific feature of a service that can be activated or deactivated by the administrator.

A service option may be chargeable through subscription, or free.

A service option can be used for:

-   disabling a node's property:
    -   if a property is inactive its rules (defaultValue, prepare, control, updatedValue) are inhibited. The default value will be the default for its type (null if nullable, '' for non-nullable string, ...).
    -   if a property is inactive and applicative code tries to read/write it will raise an error.
-   disabling a node:
    -   if a node is inactive all queries and mutations will be forbidden (this includes the custom queries and mutations of the node)

## ServiceOption class

Service options are hard-coded as singletons. Packages have a lib/service-options directory with all their service options (one file per service option)
Each file in this directory exports a const object typed to the ServiceOption interface.

The ServiceOption instance carries the metadata which is used by all tenants:

-   \_\_filename: the path of the current file from which the package and the name of the option are deduced (example: @sage/xtrem-show-case and showCaseDiscountOption)
-   status: the status of the option
    -   workInProgress: feature is incomplete. CI can activate it, but the customer cannot.
    -   experimental: feature is complete but new. The customer can activate it and a special image is displayed next to the option on the page where the tenant admin manages the options.
    -   released: feature is a normal, released, feature.
-   description: the description of the option.
-   code: is an optional attribute that external applications can use to store an additional value, that can be used in resolving if the service option is active or not.
-   isActiveByDefault: when the service option state is stored, is set to active by default.
-   onEnabled: A callback function, with a `Context` as parameter, that allows validation to determine whether a specific service option can be activated. If the validation fails, a `BusinessRuleError` should be raised within the callback function, which will prevent the service option from being enabled.
-   onDisabled: The same callback functionality as for onEnabled, but for disabling the service option.

Service options can be used to create temporary options which will be removed once the feature is fully released. These options will go through the workInProgress and experimental statuses but will be removed instead of getting the released status.

```ts
import { ServiceOption } from '@sage/xtrem-core';

export const showCaseDiscountOption = new ServiceOption({
    __filename,
    status: 'experimental',
    description: 'showCase discount option',
    isSubscribable: false,
    isHidden: false,
});
```

Experimental and workInProgress service options are hidden by default in production mode and displayed in development mode. This behavior can be overridden in the `xtrem-config.yml` file, by setting the `serviceOptions/level` key to one of the following values: `released`, `experimental`, `workInProgress`.

-   released: only released service options will be displayed and available
-   experimental: only released and experimental service options will be displayed and available
-   workInProgress: all service options will be displayed and available

In xtrem-config.yml:

```yml
serviceOptions:
    level: released
```

## Organization of service options

Some service options will be combinations of other service options:

-   High-level service options may activate more than one low-level option,
-   Low-level, technical, service options are used to tag API elements (nodes, properties),
-   Low-level service options aren't exposed directly on the page where the tenant administrator manages options,
-   The tenant administrator only manages high-level options,
-   Low-level options may be activated by more than one high-level option,
-   Low-level options are flagged with _isHidden: true_.
-   The dependencies between service options are expressed by an _activates_ attribute on the service option. This attribute is set on the high-level options and gives the list of low-level options that this option activates.

## Service option usage

Service options can be allocated to a node or property on their decorators, by providing a value to the `serviceOptions` attribute which is a callback that returns an array of `ServiceOption`.

In the following example, the property 'discount' will be present in the payload if and only if the 'discountOption' option is active or not.

```ts
    @decorators.integerProperty<TestServiceOptionInPropDecorator, 'discount'>({
        serviceOptions: () => [discountOption],
        isPublished: true,
        isStored: true,
        defaultValue() {
            return 20;
        },
    })
    readonly discount: Promise<integer>;

```

If the serviceOptions attribute is not provided on the node/property decorator, the node/property will be active by default.

## context.isServiceOptionEnabled(option)

The context.isServiceOptionEnabled(option) is intended to be used by business rules in order to check if an option is activated or not for the current tenant.
To be active an option has to be activated and to belong to an active package. The management of service options is delivered with the @sage/xtrem-system package.

In the following example, the computed property 'discount' will have a different value depending on whether the 'discountOption' option is active or not.

```ts
    @decorators.integerProperty<ShowCaseProduct, 'discount'>({
        isPublished: true,
        getValue() {
            return this.$.context.isServiceOptionEnabled(discountOption) ? 20 : 0;
        },
    })
    readonly discount: Promise<integer>;
```

## context.serviceOptionManager.setServiceOption(context, serviceOption, isActivable, isActive)

When activating/deactivating a service option, it will consider its new value and activate its children OR deactivate its parents based on the action.

If the service option is activated then all the children will be activated. This process is recursive and propagates to the grand-children and so on.
Note: The parents are not affected by this change.

If the service option is deactivated then all the parents will be deactivated. This process is recursive and propagates to the grandparents and so on.
Note: The children are not affected by this change.
