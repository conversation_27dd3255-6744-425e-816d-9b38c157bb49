PATH: XTREEM/2+Node+events/3+control+begin+and+end

# controlBegin / controlEnd

The _control_ rules are executed during the save operation, after the _prepare_ rules and before the _save_ rules.

The `controlBegin` rule is executed on the node before the control rules of the node's properties.
The `controlEnd` rule is executed on the node after the control rules of the node's properties.

The control rules of mutable reference and collection properties recurse on the child nodes. So the `controlBegin` / `controlEnd` calls are naturally nested. The `controlBegin` / `controlEnd` rules of a document header bracket the list of `controlBegin` / `controlEnd` calls on the document lines.
