PATH: XTREEM/2+Node+events/1+prepare+begin+and+end

# prepareBegin / prepareEnd

The _prepare_ rules are executed at the beginning of the save operation, before the _control_ rules.

The `prepareBegin` rule is executed on the node before the prepare rules of the node's properties.
The `prepareEnd` rule is executed on the node after the prepare rules of the node's properties.

The prepare rules of mutable reference and collection properties recurse on the child nodes. So the `prepareBegin` / `prepareEnd` calls are naturally nested. The `prepareBegin` / `prepareEnd` rules of a document header bracket the list of `prepareBegin` / `prepareEnd` calls on the document lines.
