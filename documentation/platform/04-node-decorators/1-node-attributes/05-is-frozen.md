PATH: XTREEM/1+Node+attributes/05+Node+attribute+isFrozen

# isFrozen

## What is a frozen node

A `frozen` node is a node that cannot be modified anymore.

A node may be partially frozen, in which case only some of its properties cannot be modified, or completely frozen, in which case the entire node cannot be modified.
The `isFrozen` attribute may be placed either in a node decorator or in a property decorator, to handle these two cases.

There is usually a business rule that defines whether a node is frozen or not.
For example, when a document becomes frozen when archived.

If a node is frozen, any attempt to assign a new value to any of its properties will trigger an exception.

The UI reflects the frozen state of a node by disabling all the UI controls that modify it.

## How to declare the rule for the frozen state of a node

Simply set the `isFrozen` attribute on the node decorator to a function returning a boolean.

```ts
@decorators.node<MyDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    async isFrozen() {
        return (await this.status) === StatusEnum.archived;
    },
})
export class MyDocument extends Node {
    @decorators.enumProperty<MyNode, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => statusDataType,
    })
    readonly status: Promise<StatusEnum>;
}
```

In this example, the `isFrozen` rule will return true if the status of the document is `archived`.

Note: you can also set `isFrozen` to a constant (`isFrozen: true`), for example on a truly immutable object that cannot be modifed after being constructed. This is a shortcut for `isFrozen() { return true; }`.
