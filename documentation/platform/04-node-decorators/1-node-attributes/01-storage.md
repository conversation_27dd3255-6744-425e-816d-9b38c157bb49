PATH: XTREEM/1+Node+attributes/01+Storage

# Storage

Node storage is the type of storage method the node will use to manage its data.

The `storage` attribute on the node decorator where the storage method is specified.

```ts
@decorators.node<MyNode>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
})
export class MyNode extends Node {
    @decorators.stringProperty<MyNode, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<MyNode, 'description'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly endTime: Promise<string | null>;
}
```

# Storage Methods

There are 3 storage methods:

| **Storage** |
| ----------- |
| sql         |
| external    |
| json        |

## SQL Storage

The SQL storage method indicates that the node is stored and managed on the Postgres database, and CRUD operations will be managed by the framework.

```ts
@decorators.node<MySqlNode>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
})
```

## External Storage

The external storage method indicates that the node is stored and managed externally, and CRUD operations will be managed by the logic provided by the `externalStorageManager`. The `externalStorageManager` is an attribute on the node decorator that provides an instance of a class that implements the `ExternalStorageManager` interface.

External storage nodes can be used for nodes that represent data retrieved from other applications (via interfaces like web services, etc.), nodes that represent data read from the file system, data from other databases directly, etc.

```ts
@decorators.node<MyExternalNode>({
    storage: 'external',
    externalStorageManager: new MyExternalStorageManager(args),
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
})

```

When doing a node extension for an external storage node an `externalStorageManagerExtension` can be provided. The `externalStorageManagerExtension` is an attribute on the node extension decorator that provides and instance of a class that implements the `ExternalStorageManagerExtension` interface. This class will be merged with the `ExternalStorageManager` of the base class.

```ts
@decorators.nodeExtension<TestNodeExtension>({
    extends: () => TestNodeExtension,
    indexes: [],
    externalStorageManagerExtension: new MyExternalStorageManagerExtension(joins),
})
```

### ExternalStorageManager Interface

The class that implements the `ExternalStorageManager` interface controls the CRUD actions of the node it is assigned to. To manage these actions the class has to implement all the methods of the interface

| Method/Attribute     | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| factory              | `factory` is the instance of the NodeFactory class that the external storage manager is allocated to.                                                                                                                                                                                                                                                                                                                                                                           |
| insert               | `insert` is the function that manages the creation action, i.e. creating a new record of a node for the provided node instance                                                                                                                                                                                                                                                                                                                                                  |
| update               | `update` is the function that manages the update action, i.e. updating a record of a node for the provided node instance                                                                                                                                                                                                                                                                                                                                                        |
| delete               | `delete` is the function that manages the delete action, i.e. deleting a record of a node for the provided node instance.                                                                                                                                                                                                                                                                                                                                                       |
| delete               | `delete` is the function that manages the delete action, i.e. deleting a record of a node for the provided node instance.                                                                                                                                                                                                                                                                                                                                                       |
| getCollectionJoin    | `getCollectionJoin` is a function that returns the join value for the supplied collection property name. This function returns a dictionary of string or function, where the dictionary key is the property name on the target node and the value is either a function or string (current node property name).                                                                                                                                                                  |
| getReferenceJoin     | `getCollectionJoin` is a function that returns the join value for the supplied reference property name. This function returns a dictionary of string or function, where the dictionary key is the property name on the target node and the value is either a function or string (current node property name).                                                                                                                                                                   |
| mapRecordIn          | `mapRecordIn` is a function that takes in a record and maps it to an object where the key is the property name and the value is the property value.                                                                                                                                                                                                                                                                                                                             |
| mapAggregateRecordIn | `mapAggregateRecordIn` is a function that takes in a record and maps it to an object where the key is the request aggregate name and the value is the aggregated value.                                                                                                                                                                                                                                                                                                         |
| parseOrderBy         | `parseOrderBy` is a function that takes in an `OrderBy` and parses it to an `OrderByClause` array.                                                                                                                                                                                                                                                                                                                                                                              |
| parseCursor          | `parseCursor` is a function that takes in a cursor value and `OrderByClause` array and parses the value to an `PropertyAndValue` array.                                                                                                                                                                                                                                                                                                                                         |
| parseId              | `parseId` is a function that takes in a node's data and resolves a unique id for the record.                                                                                                                                                                                                                                                                                                                                                                                    | =   |
| getKeyValues         | `getKeyValues` is a function that takes in a node's data and returns a dictionary of the key values of the record.                                                                                                                                                                                                                                                                                                                                                              |
| getJoinValues        | `getJoinValues` is a function that based on the instance and data passed, returns the resolved join values for the reference, referenceArray or collection property that is also passed. For the returned dictionary the key is the target node property name in the join, and the value is the actual value using the supplied data and node instance. The functions `getCollectionJoin` and `getReferenceJoin` can be leveraged in this function to get the joins to resolve. |
| defaultOrderBy       | `defaultOrderBy` is an attribute of the class that needs to hold the default `OrderBy` of the node.                                                                                                                                                                                                                                                                                                                                                                             |

```ts
class MyExternalStorageManager<This extends Node> implements ExternalStorageManager<This> {
    factory: NodeFactory;

    insert(node: Extend<This>, cx: ValidationContext): void {
        // insert logic
    }

    update(node: Extend<This>, data: Partial<This>, cx: ValidationContext): number {
        // update logic
    }

    delete(node: Extend<This>, cx: ValidationContext): number {
        // delete logic
    }

    getAllowedAccessCodes(
        context: Context,
        tag: AccessRightsPropertyFilterTag,
        allowedAccessCodes: string[] | null,
    ): string[] | null {
        // optional function  logic to determine if the node this storage manager is allocated to needs different logic to determine allowed codes
    }

    query(context: Context, options: NodeExternalQueryOptions<This>): Reader<any> {
        // query logic
    }

    getCollectionJoin(propertyName: string): InternalPropertyJoin<This> | undefined {
        // getCollectionJoin logic
    }

    getReferenceJoin(propertyName: string) => InternalPropertyJoin<This> | undefined {
        // optional - getReferenceJoin logic
    }

    mapRecordIn(record: any) => any {
        // optional - mapRecordIn logic
    }

    parseOrderBy(context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        // parseOrderBy logic
    }

    parseCursor(orderByClauses: OrderByClause[], value: string): PropertyAndValue[] {
        // parseCursor logic
    }

    parseId(values: Dict<any>): string {
        // parseId logic
    }

    getKeyValues(values: any): Dict<any> {
        // getKeyValues logic
    }

    getJoinValues(node: Extend<This>, data: any, propertyName: string, index?: number): Dict<any> | undefined {
        // getJoinValues logic
    }

    get defaultOrderBy(): OrderBy<Node> {
        // return the default order by
    }
}
```

### ExternalStorageManagerExtension Interface

The ExternalStorageManagerExtension interface is an empty interface that can be implemented as a class with attributes and methods that the `ExternalStorageManager` may use, e.g. resolving reference or collection joins, logic required for queries.

```ts
export interface ExternalStorageManagerExtension<This extends Node = Node> {}
```

## JSON Storage

Node with json storage methods will not persist directly to a table in Postgres or an external party like described in the previous 2 node storage methods. A node with json storage will be used to define nodes that will represent reference or collection properties in other nodes. These properties will persist as the JSON objects to a column in the source node.

Refer to the documentation on vital properties to see how json nodes can be utilized.

```ts
@decorators.node<MyJsonNode>({
    storage: 'json',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
})
```

### Utilities - Get a NodeFactory using a Node constructor

In the application instance, a utility is provided that allows us to retrieve the NodeFactory instance of the passed-in Node constructor.

```ts
const myFactory = node.$.context.application.getFactoryByConstructor(MyNode);
```
