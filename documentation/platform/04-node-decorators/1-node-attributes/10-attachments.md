PATH: XTREEM/1+Node+attributes/10+attachments

## hasAttachments

The `hasAttachments` node decorator attribute is a boolean that, when set to `true`, enables the `\_attachments` system property
for a node. This property is a collection of `AttachmentAssociation` objects, establishing a many-to-many relationship
between the `Source Node` and `Attachment`. The `\_attachments` property is mutable, which means it can be provided as part of the node create and update data, similar to a vital collection.

This attribute can be applied to nodes, abstract nodes, sub nodes, and node extensions.

### Inheritance and Overrides

The `hasAttachments` attribute is inherited by subnodes from their superclass. If not already set (i.e., `false`)
on the parent node, it can be individually set on subnodes.

Node extensions can also specify this attribute. If they do, it will override the `hasAttachments` value of the original node.

### Usage

Here's an example of how to use the `hasAttachments` attribute in a node definition:

```ts
@decorators.node<TestAttachmentDocument>({
    isPublished: true,
    storage: 'sql',
    hasAttachments: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestAttachmentDocument extends Node {
    // ....
}
```

In this example, the `TestAttachmentDocument` node has the `hasAttachments` attribute set to true, enabling the
`\_attachments` system property. It can be used in the same way for abstract node, subnodes and node extensions.

## Uploading Attachments CSV Data

To effectively manage and upload attachment data for a specific layer, follow these detailed steps:

1. **Create a Dedicated Directory:**

    - **Path Setup:** Establish a new directory specifically for your attachments by navigating to `/data/layers/test/attachments` on your system. This is where all attachment files will be stored.

2. **Transfer Files to Directory:**

    - **File Placement:** Ensure all files intended for upload are transferred to the `attachments` directory you just created. Verify that all necessary files are present before proceeding to the next step.

3. **Prepare Metadata in CSV Format:**

    - **CSV File Creation:** Generate a file named `attachment.csv` within the same directory. This CSV file should catalog the metadata associated with each attachment.
    - **Content Requirements:** Each row in the CSV file should represent one attachment, including essential details such as the attachment key, filename, MIME type, and status.

4. **Standardize Attachment Keys:**
    - **Key Formatting:** Prefix each attachment’s filename with "local-" in the CSV file to create the attachment key. For instance, if your file is named `my-attachment.pdf`, the attachment key should be recorded as `local-my-attachment.pdf`.

By carefully following these steps, you will be able to efficiently add and manage attachments within a layer. Make sure each step is completed accurately to ensure smooth operation and data integrity.
