PATH: XTREEM/1+Node+attributes/09+isContentAddressable

# isContentAddressable

The `isContentAddressable` node decorator attribute sets the node in _content-addressable_ mode. For example:

```ts
@decorators.node<Address>({
    storage: 'sql',
    isContentAddressable: true,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class Address extends Node {
    // ....
}
```

A _content-addressable_ node is a node that is stored as a table of _immutable_ records.

When you create a node instance and save it for the first time, the framework looks for a record with identical content
in the table.
If a matching record is found it is used and no new record is created. Otherwise, if not found, a new record is
inserted into the table.

When you read a node instance from the database, modify it and then save it, the framework does the same thing: it
looks for a matching record in the table and uses it if found. Otherwise, it inserts a new record.
It will never _update_ an existing record.

Note that the _records_ are immutable but the _node instances_ are still mutable. The `isContentAddressable` node attribute
changes the storage strategy; it does not _freeze_ the node instances.

Under the hood, the framework adds a `_valuesHash` system property to the node, and thus a `_values_hash` column to its table,
with a unique index on it.
When saving a node (for create or update), the framework computes a hash on the stored properties of the node and it uses this
hash to find a matching record in the table.

Note that records may get orphaned. The table may contain records that are not referenced from anywhere.
The framework will garbage-collect these records automatically.

## Use cases

Content-addressable nodes should be used for simple records when the same data may be referenced from many places and
when that data does not change often.

Addresses and contacts are an ideal use case. With traditional approaches, we have to copy the address and contact of
a customer or supplier when we create documents because this data must not change in existing documents if we modify
the address or contact of the customer or supplier later.
This leads to a large amount of duplicates.
Instead, with this approach, the documents will share immutable addresses and contacts; new addresses and contacts will only
be created (inserted into the table) when someone modifies an address or contact in a customer or supplier.

Moreover, as the records are immutable they can be cached very efficiently.

## Referencing content-addressable nodes

It is often desirable to treat content-addressable nodes as _vital_ children of another node.

For example, the address and contact of a document are _vital_ children of a document:

-   They are part of the document.
-   They may be modified together with the document, often under certain conditions, like modifiable if the document is a draft but not if it is archived.
-   They are deleted if the document is deleted.

The framework supports this in full but references to content-addressable nodes must be configured differently than references to _normal_ vital children.
They must be tagged with `isMutable: true` and `isStored: true` rather than `isVital: true`.

For example:

```ts
@decorators.node<Document>({
    storage: 'sql',
    isPublished: true,
    // ...
})
export class Document extends Node {
    // ....

    @decorators.referenceProperty<Document, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        node: () => Address,
        // ...
    })
    readonly billToAddress: Reference<Address>;
}
```
