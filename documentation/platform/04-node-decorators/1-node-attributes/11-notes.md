PATH: XTREEM/1+Node+attributes/10+attachments

## hasNotes

The `hasNotes` node decorator attribute is a boolean that, when set to `true`, enables the `\_notes` system property
for a node. This property is a collection of `SysNoteAssociation` objects, establishing a many-to-many relationship
between the `Source Node` and `SysNote`. The `\_notes` property is mutable, which means it can be provided as part of the node create and update data, similar to a vital collection.

This attribute can be applied to nodes, abstract nodes, sub nodes, and node extensions.

### Inheritance and Overrides

The `hasNotes` attribute is inherited by subnodes from their superclass. If not already set (i.e., `false`)
on the parent node, it can be individually set on subnodes.

Node extensions can also specify this attribute. If they do, it will override the `hasNotes` value of the original node.

### Usage

Here's an example of how to use the `hasNotes` attribute in a node definition:

Steps to create a note in a Node:

1 - set `hasNotes` decorator to `true`

```ts
@decorators.node<TestNotesDocument>({
    isPublished: true,
    storage: 'sql',
    hasNotes: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestNotesDocument extends Node {
    // ....
}
```

In this example, the `TestNotesDocument` node has the `hasNotes` attribute set to true, enabling the
`\_notes` system property. It can be used in the same way for abstract node, subnodes and node extensions.

2 - create `SysNote` Record and used the \_id from the reponse to update the `TestNotesDocument`

```groovy
        mutation {
            TestPackage {
                TestNotesDocument {
                    create (data: {title: "My Note 1", content: {value: "<html><body>My Note 1</body></html>"},_createUser:7}) {
                        _id
                        title
                            content {
                        value
                        }
                        _createStamp
                            _updateStamp
                        _createUser {
                            displayName
                        }
                    }
                }
            }
        }
```

3 - use create action to add `SysNoteAssociation` record for `TestNotesDocument`

```groovy
mutation {
    xtremStock {
        stockReceipt {
            update (data: {_id: "30",_notes: [{ _action:"create", _id:"-1" ,note:"_id:1" }]}) {
                _id
                __notesList: _notes {
                    query (first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                note {
                                    title
                                    content {
                                  		value
                                		}
                                    _tags {
                                        _id
                                        name
                                        description
                                    }
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                    _id
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
```
