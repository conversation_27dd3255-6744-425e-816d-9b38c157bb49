PATH: XTREEM/1+Node+attributes/02+Cache

# Cache

Queries can be cached on some node factories. This increases performance
on small tables that are read very often (settings, for instance).

## How to enable the cache on a factory

The node decorator must contain the following _isCached_ attribute:

```ts
@decorators.node<...>({
    ...
    isCached: true,
    ...
})
```

This enables the caching of queries made to the factory.

## Configuration

2 settings can be configured in the configuration file (_settings_ section) :

```yml
settings:
    globalCache:
        maxCost: 100000
    tableCache:
        ttl: 10
```

-globalCache/maxCost (default = 500 000): controls the max cost of the global cache. Every time a query is cached, a cost will be computed for the result (no specific unit but the cost will take into account: the number of columns, the size of the values, ...). Once the total cost of the cache exceeds globalCache/maxCost, some instances will be released to free memory.
-tableCache/ttl (default = 30): the Time To Live (in seconds) of every cached query result. Once the TTL has expired, the data
will be removed from the cache
