PATH: XTREEM/1+Node+attributes/08+synchronization

## isSynchronizable

The `isSynchronizable` boolean attribute flags a node as being a source of synchronization.

This attribute may only be set on nodes that have `'sql'` storage and that have a natural key.

This attribute is inherited by subnodes. It may also be set individually on subnodes but in this case,
it should not be set on an ancestor node.

```ts
@decorators.node<Item>({
    storage: 'sql',
    isPublished: true,
    isSynchronizable: true,
    indexes: [{ orderBy: 'id', isUnique: true, isNaturalKey: true }],
})
export class Item extends Node {
    // ....
}
```

## isSynchronizedand synchronizationTransforms

The `isSynchronized` attribute flags a node as being a target of synchronization.
Nodes that carry this attribute must have a `synchronizationTransforms` attribute which provides the
transformations that synchronization will use.

The `isSynchronized` attribute may only be set on nodes that have `'sql'` storage and that have a natural key.
It cannot be set on abstract nodes.

The value of the `synchronizationTransforms` attribute is an array of transform configurations.
The transforms are placed in a separate synchronization-transforms subdirectory, to keep the node definition concise.

```ts
import { itemTransforms } from '../synchronization-transforms/item-transforms';

@decorators.node<Item>({
    storage: 'sql',
    isPublished: true,
    isSynchronized: true,
    synchronizationTransforms: itemTransforms,
    indexes: [{ orderBy: 'id', isUnique: true, isNaturalKey: true }],
})
export class Item extends Node {
    // ....
}
```

## Synchronization transforms

A synchronization transform provides the rules to transform a synchronization payload retrieved from the source node
into a payload for the target node.

Assuming that our target node will be an `Item` with properties like `id`, `name`, `unit` (`id`, `name`) and `density`
and that we want to be able to synchronize it from SDMO version 38, we will use the following transform:

```ts
// Interface for the SDMO Item in version 38
export interface SdmoItemV38 {
    id: string;
    name: string;
    stockUnit: { id: string, name: string };
    weight: decimal;
    volume: decimal;
    category: { name: string };
}

// Transformation to synchronize from an SDMO version 38 Item source
const sdmoTransformV38: SynchronizationTransformDecorator<Item, SdmoItemV38> = {
    from: {
        app: 'sdmo',
        node: 'Item',
        version: '^38.0.0',
    },
    mappings: {
        id: { from: 'id' },
        name: { from: 'name' },
        unit: {
            from: 'stockUnit',
            mappings: {
                id: { from: 'id' },
                name: { from: 'name' },
            },
        },
        density: {
            from: ['weight', 'volume'],
            computedBy: from => from.volume ? from.weight / from.volume : 0,
        },
        categoryName: { from: 'category.name' },
        ...
    }
}
```

The `from` property identifies the source: the app, the node and the version.
The `version` uses _semver_ syntax so we can specify ranges. For example: '>= 38.0.7 < 40.0.0'.
You don't have to define a separate transform for each version; you can use the same transform for a range of versions.

The `mappings` section defines how properties _from_ the source node are transformed into properties of the target node.
The source and target properties may be nested.
The source properties may be identified by paths in _dot_ syntax, like `categoryName` in the example above.
The `mappings` can include references and collections on both sides, but only vital ones on the target side.

A property mapping may involve a computation. In this case, the `from` value is an array of the paths of the source
properties used by the computation and `computedBy` is a function that performs the computation.

The `synchronizationTransforms` decorator attribute references an array of `SynchronizationTransformDecorator`
configurations.
This array may contain transforms for several versions and several source apps.
For example:

```ts
export const itemTransforms = [sdmoTransformV37, sdmoTransformV38, x3TransformV35];
```

The transformation engine will select the appropriate configuration based on the source app that the tenant has configured
and the current version of this source app.

Note that a target node may only be synchronized with _one_ source node from _one_ source app.
OTOH, a given source may be synchronized to several targets, with different property mappings in each one.
