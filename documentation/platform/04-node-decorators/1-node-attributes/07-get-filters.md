PATH: XTREEM/1+Node+attributes/07+getFilters

# getFilters

The `getFilters` attribute allows you to define a list of filters that will be applied to all queries on this node.

For example, you could use this to implement a `PersonalNote` node that lets each user see only the notes that he has created.

```ts
@decorators.node<PersonalNote>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    async getFilters(context) {
        return [{ belongsTo: await context.user }];
    },
})
export class PersonalNote extends Node {
    @decorators.referenceProperty<PersonalNote, 'belongsTo'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAuthorization.nodes.User,
        async defaultValue() {
            return await this.$.context.user;
        },
    })
    readonly belongsTo: Reference<xtremAuthorization.nodes.User>;

    @decorators.stringProperty<PersonalNote, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.descriptionDataType,
    })
    readonly text: Promise<string>;
}
```

With this filter, queries on `PersonalNote` will only return notes for which `belongsTo` to match the current user.
