PATH: XTREEM/1+Node+attributes/04+Indexes

# Indexes

## Overview

The `indexes` attribute defines the list of indexes that are relevant for a node. Every index is defined with the following parameters:

| Parameter | Type    | Description                                                                                         |
| --------- | ------- | --------------------------------------------------------------------------------------------------- |
| name      | string  | name of the index. If omited, a default name is generated based on the node name and index position |
| orderBy   | object  | an object with the property names as key and `1` or `-1` as value for specifying ASC or DESC order  |
| isUnique  | boolean | should we add a unique constraint on the index                                                      |

Indexes have 2 main goals:

1. Adding a unique constraint on a set of properties
2. Improving the performance of queries

The recommendation is to add only indexes for a unique constraint reason because they can also have a bad performance impact on create and update operations, so it is usually better to have few indexes and add only those that are necessary after a deep performance analysis on a large set of data when the system is stressed.

## Nullable properties in indexes

You need to pay special attention to nullable properties when creating a unique index.
Nullable properties in an index can introduce duplicates because `null` is always different from `null` from a SQL perspective.
Verification is done by the framework and an error will be raised except if the following conditions are met:

1. The property type is `reference`, `enum`, `integer` or `date`
2. The property has the `allowedInUniqueIndex` decorator attribute set to `true`

If these conditions are met, a replacement value for `null` is set in the index using a `COALESCE` expression but the column value remains unchanged, meaning that a query will still return `null` and then does not impact the business logic.

## Sample without nullable properties

```ts
@decorators.node<StockJournal>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    indexes: [
        {
            orderBy: { site: 1, isUpdate: 1, item: 1, effectiveDate: -1, sequence: 1 },
            isUnique: true,
        },
    ],
})
```

## Sample with nullable properties

```ts
@decorators.node<UnitConversionFactor>({
    package: 'xtrem-master-data',
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isPublished: true,
    isVitalReferenceChild: true,
    indexes: [
        {
            orderBy: {
                fromUnit: 1,
                toUnit: 1,
                type: 1,
                item: 1,
                customer: 1,
                supplier: 1,
            },
            isUnique: true,
        },
    ],
})

...

    @decorators.referenceProperty<UnitConversionFactor, 'fromUnit'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['toUnit'],
    })
    readonly fromUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<UnitConversionFactor, 'toUnit'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly toUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.enumProperty<UnitConversionFactor, 'type'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        dataType: () => xtremMasterData.enums.unitConversionTypeDataType,
    })
    readonly type: Reference<xtremMasterData.enums.UnitConversionType | null>;

    @decorators.referenceProperty<UnitConversionFactor, 'item'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<UnitConversionFactor, 'customer'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<UnitConversionFactor, 'supplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;
```
