PATH: XTREEM/04+Node+decorators/3+Error+management

# Throw errors in node events

Node events can be used to check some conditions on the node properties, and throw errors if they aren't met.
For instance, the developer could want to throw an error if the property `price` of the node `Article` is negative or empty. These specific checks belong in the `control` method of the `price` property decorator.

## Custom conditions

It could be done this way, with custom `if` conditions, and localized custom messages:

```ts
    @decorators.decimalProperty<Article, 'price'>({
        isStored: true,
        isPublished: true,
        control(cx, val) {
            if (!val) {
                cx.error.addLocalized(
                    '@sage/xtrem-my-package/nodes__article_price_mandatory',
                    "Articles can't be created without a price."
                );
            }
            if (val < 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-my-package/nodes__article_price_positive',
                    "Articles can't be created with a negative price."
                );
            }
        },
    })
    readonly price: Promise<decimal>;
```

## Generic conditions: chai-like syntax

Another way to do this is to use the server-provided chai-like syntax.

### The error and expect keywords

This can be done using either the `error` or the `expect` keywords.
These errors will be automatically generated by the server with generic localized error messages, here 'value (0) must not be zero' if the price is null and 'value (-1) must not be negative' if the price is -1.

With the `error` keyword:

```ts
    @decorators.decimalProperty<Article, 'price'>({
        isStored: true,
        isPublished: true,
        control(cx, val) {
            cx.error.if(val).is.zero();
            cx.error.if(val).is.negative();
        },
    })
    readonly price: Promise<decimal>;
```

With the `expect` keyword:

```ts
    @decorators.decimalProperty<Article, 'price'>({
        isStored: true,
        isPublished: true,
        control(cx, val) {
            cx.expect(val).to.be.not.zero();
            cx.expect(val).to.be.positive();
        },
    })
    readonly price: Promise<decimal>;
```

### Custom messages

If the developer wants custom error messages, they can use the withMessage method, with `expect` as well as with `error`.

With `error`:

```ts
    @decorators.decimalProperty<Article, 'price'>({
        isStored: true,
        isPublished: true,
        control(cx, val) {
            cx.error.withMessage(
                        '@sage/xtrem-my-package/nodes__article_price_mandatory',
                        "Articles can't be created without a price."
                ).if(val).is.zero();
            cx.error.withMessage(
                        '@sage/xtrem-my-package/nodes__article_price_positive',
                        "Wrong price: {{ price }} Articles can't be created with a negative price.",
                        () => { price: val }
                ).if(val).is.negative();
        },
    })
    readonly price: Promise<decimal>;
```

With `expect`:

```ts
    @decorators.decimalProperty<Article, 'price'>({
        isStored: true,
        isPublished: true,
        control(cx, val) {
            cx.expect(val).withMessage(
                        '@sage/xtrem-my-package/nodes__article_price_mandatory',
                        "Articles can't be created without a price."
                ).to.be.not.zero();
            cx.expect(val).withMessage(
                        '@sage/xtrem-my-package/nodes__article_price_positive',
                        "Wrong price: {{ price }} Articles can't be created with a negative price.",
                        () => { price: val }
                ).to.be.positive();
        },
    })
    readonly price: Promise<decimal>;
```

### All the generic conditions provided by the chai-like syntax

```ts
context.expect(property).to.be.[not.]empty();
context.expect(property).to.be.[not.]equal.to(value);
context.expect(property).to.be.[not.]greater.than(value);
context.expect(property).to.be.[not.]less.than(value);
context.expect(property).to.be.[not.]at.least(value);
context.expect(property).to.be.[not.]at.most(value);
context.expect(property).to.be.[not.]in(set);
context.expect(property).to.be.[not.]true();
context.expect(property).to.be.[not.]false();
context.expect(property).to.be.[not.]matching(regex);
context.expect(property).to.be.[not.]zero();
context.expect(property).to.be.[not.]positive();
context.expect(property).to.be.[not.]negative();
context.expect(date1).to.be.[not.]before(date2);
context.expect(date1).to.be.[not.]after(date2);

context.error.if(property).is.[not.]empty();
context.error.if(property).is.[not.]equal.to(value);
context.error.if(property).is.[not.]greater.than(value);
context.error.if(property).is.[not.]less.than(value);
context.error.if(property).is.[not.]at.least(value);
context.error.if(property).is.[not.]at.most(value);
context.error.if(property).is.[not.]in(set);
context.error.if(property).is.[not.]true();
context.error.if(property).is.[not.]false();
context.error.if(property).is.[not.]matching(regex);
context.error.if(property).is.[not.]zero();
context.error.if(property).is.[not.]positive();
context.error.if(property).is.[not.]negative();
context.error.if(date1).is.[not.]before(date2);
context.error.if(date1).is.[not.]after(date2);
```

### Change the error path

The chai-like syntax allows the developer to set the desired graphql path of the thrown error.
When the error is thrown from inside a property decorator, the path will default to this property.
If the error is thrown from a node-level method, for instance `controlEnd`, the path needs to be explicitly set by the developer with the `at` keyword.

For instance, here the paths are both set to 'property1':

```ts
@decorators.node<MyNode>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isPublished: true,
    // Class level controls
    // This is the right place for controls involving several properties when there is no clear hierarchy between the properties.
    async controlEnd(cx): void {
        cx.at('property1').error.if(await this.property1).is.not.equal.to(await this.property2);
        cx.at('property1').error.if(await this.property1).is.not.greater.than(await this.property3);
    },
})
```
