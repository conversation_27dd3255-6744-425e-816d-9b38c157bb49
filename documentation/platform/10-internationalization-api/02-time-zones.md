PATH: XTREEM/10+Internationalization+API/02+Time+zones

# Time zones

## Introduction

The xtrem-date-time library allows you to work with dates and times in multiple time zones

## Getting the list of time zones

The list of available time zones is obtained with TimeZone.getAvailableTimeZones():

```ts
import { TimeZone } from '@sage/xtrem-date-time';

const timeZones = TimeZone.getAvailableTimeZones();
```

This returns the [IANA names](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) for all the time zones available in the JavaScript runtime. Examples: `America/New_York`, `Europe/Paris`, `Asia/Tokyo`.

## Datetime

A `Datetime` instance represents a _point in time_ (a timestamp) and lets you manipulate it in two time zones: UTC and _TZ_, a time zone of your choice.

You specify _TZ_ in the static methods that create `Datetime` instances, via an optional `timeZone` parameter at the end of the parameter list:

-   `Datetime.make(year, month, day: number, hour, minute, second, millis, timeZone)`
-   `Datetime.fromJsDate(jsDate, timeZone)`
-   `Datetime.fromValue(value, timeZone)`
-   `Datetime.now(withMillis, timeZone)`
-   `Datetime.parse(str, locale, format, timeZone)`

If you omit the `timeZone` parameter, _TZ_ is the time zone your JavaScript engine.
In the browser, this is the time zone of your browser.
On the server side, it is the time zone of the server by default but it may be overridden with the `TZ` environment variable.

For example:

```ts
const tokyoDt1 = Datetime.make(2024, 2, 8, 6, 0, 0, 0, 'Asia/Tokyo');
const localDt1 = Datetime.make(2024, 2, 8, 6, 0, 0, 0);

const tokyoDt2 = Datetime.fromJsDate(new Date(), 'Asia/Tokyo');
const localDt2 = Datetime.fromJsDate(new Date());

const tokyoDt3 = Datetime.now('Asia/Tokyo');
const localDt3 = Datetime.now();
```

If we run this code on a server located in Paris configured for the Paris time zone, on Feb 8 2024 at 18:10:23 local time, we obtain:

| instance | day | hour | format('base', 'YYYY-MM-DD HH:mm:ss z') | utcDay | utcHour | toString()           |
| -------- | --- | ---- | --------------------------------------- | ------ | ------- | -------------------- |
| tokyoDt1 | 8   | 6    | 2024-02-08 06:00:00 JST                 | 7      | 21      | 2024-02-07T21:00:00Z |
| localDt1 | 8   | 6    | 2024-02-08 06:00:00 CET                 | 8      | 5       | 2024-02-08T05:00:00Z |
|          |     |      |                                         |        |         |                      |
| tokyoDt2 | 9   | 2    | 2024-02-09 02:10:23 JST                 | 8      | 17      | 2024-02-08T17:10:23Z |
| localDt2 | 8   | 18   | 2024-02-08 18:10:23 CET                 | 8      | 17      | 2024-02-08T17:10:23Z |
|          |     |      |                                         |        |         |                      |
| tokyoDt3 | 9   | 2    | 2024-02-09 02:10:23 JST                 | 8      | 17      | 2024-02-08T17:10:23Z |
| localDt3 | 8   | 18   | 2024-02-08 18:10:23 CET                 | 8      | 17      | 2024-02-08T17:10:23Z |

Note: Paris is 1 hour ahead of UTC in winter.

-   The default components (`day`, `hour`) and the `format(...)` value are the values of the instance in its _TZ_ time zone.
    They are identical for `tokyoDt1` and `localDt1` because we constructed these instances with identical component values (`year`, `month`, `day`, `hour`, `minute`, `second`).
-   The _utc_ components (`utcDay`, `utcHour`) and the `toString()` value are the values of the instance in the
    UTC time zone.
    They are identical for `tokyoDt2`, `tokyoDt3`, `localDt2` and `localDt3` because all these instances were constructed at the same time with the current date time. They represent the same _point in time_.
    On the the other hand, `tokyoDt1` and `localDt1` are different _points in time_. They differ by 8 hours.

The time zone is preserved when we apply methods to generate new instances. For example:

| expression                                                              | value                   |
| ----------------------------------------------------------------------- | ----------------------- |
| tokyoDt1.addDays(2).addHours(5).format('base', 'YYYY-MM-DD HH:mm:ss z') | 2024-02-10 11:00:00 JST |
| tokyoDt1.addDays(2).addHours(5).toString()                              | 2024-02-10T02:00:00Z    |

A `Datetime` instance can be converted to a different time zone with the `inTimeZone(timeZone)` method.
The new instance represents the same _point in time_ but its components and its formatting reflect the values in the new time zone.

For example:

| expression                          | format('base', 'YYYY-MM-DD HH:mm:ss z') | toString()           |
| ----------------------------------- | --------------------------------------- | -------------------- |
| tokyoDt1                            | 2024-02-08 06:00:00 JST                 | 2024-02-07T21:00:00Z |
| tokyoDt1.inTimeZone('Europe/Paris') | 2024-02-07 22:00:00 CET                 | 2024-02-07T21:00:00Z |
| tokyoDt1.inTimeZone('UTC')          | 2024-02-07 21:00:00 UTC                 | 2024-02-07T21:00:00Z |

## DateValue

A `DateValue` instance has `year`, `month` and `day` components. It does not carry any time zone information.

But still, `DateValue` has several methods that take an optional `timeZone` parameter:

-   `date.at(time, timeZone)` (instance method)
-   `date.toJsDate(timeZone)` (instance method)
-   `DateValue.fromJsDate(jsDate, timeZone)` (static method)
-   `DateValue.today(timeZone)` (static method)

The `timeZone` parameter is necessary for these methods because the returned values depend on the time zone.

Like previously, in `DateTime`, the time zone of your JavaScript engine will be used if you omit the `timeZone` parameter.

The following table demonstrates how the `timeZone` parameter influences the result of the `today` method.

| UTC                  | today('UTC') | today('Asia/Tokyo') | today('Europe/Paris') | today('America/Phoenix') |
| -------------------- | ------------ | ------------------- | --------------------- | ------------------------ |
| 2024-02-07 21:00:00Z | 2024-02-07   | 2024-02-08          | 2024-02-07            | 2024-02-07               |

At 21:00 UTC it is 14:00 in Phoenix and 22:00 in Paris the same day, but 06:00 in Tokyo the next day.

## Time

A `Time` instance has `hour`, `minute`, and `second` components. It does not carry any time zone information.

But still, `Time` has two static methods that take an optional `timeZone` parameter:

-   `Time.fromJsDate(js, timeZone)`
-   `Time.now(timeZone)`

Like previously, in `DateTime`, the time zone your JavaScript engine will be used if you omit the `timeZone` parameter.

The following table demonstrates how the `timeZone` parameter influences the result of the `now` method.

| UTC                  | now('UTC') | now('Asia/Tokyo') | now('Europe/Paris') | now('America/Phoenix') |
| -------------------- | ---------- | ----------------- | ------------------- | ---------------------- |
| 2024-02-07 21:00:00Z | 21:00:00   | 06:00:00          | 22:00:00            | 14:00:00               |
