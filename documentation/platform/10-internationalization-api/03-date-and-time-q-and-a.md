PATH: XTREEM/10+Internationalization+API/03+Data+and+Time+Q+and+A

# Date and time Q & A

## Q: Why aren't the documents ALWAYS created simply using UTC and then the user views in the UI the time in their local timezone?

Before answering this, it is important to understand that we have 3 distinct data types:

-   Datetime: This is a "point in time", a date with a time. It is always stored and transferred as a UTC value.
-   Date (DateValue): This is a _pure_ date, without time. It is stored and transferred as year + month + day only (no time, no time zone).
-   Time: this is a pure time, without a date. It is stored and transferred as hour + minute + second only (no date, no time zone)

A document date is a DateValue, not a Datetime.

It is exchanged in YYYY-MM-DD format and the database stores it in a compact format which only holds the year, month and day.
It requires fewer storage bytes than a full Datetime and operations are faster because this is a smaller, simpler object.

A DateValue is never re-interpreted as a different date because of a time zone difference.
If you create a sales invoice in Paris at 20:00 on 2024-01-31, its date will be 2024-01-31 regardless of where you are.
Although Tokyo is 8 hours ahead of Paris and is already on 2024-02-01, a user in Tokyo must see 2024-01-31
because the document will be accounted for January, not February.

This is very important and pervasive in business applications.
For example, the sales aggregate for January should include this invoice, even if you request it from Tokyo.

To get back to the question: a document date is managed as a DateValue that only holds year, month and day.
It is not intended to represent a specific point in time.
Instead, it will be used to aggregate values on periods (month, quarter, year).
It should be unambiguous and as efficient as possible.

## Q: But do we need a time zone to set the document's date? If so, which one?

Yes, we usually want to use the current date, but this date depends on the choice of a time zone.

If two users, one in Paris and one in Tokyo create a document at the same time, around Jan 31 18:00 Paris time,
they will be on different dates. The Tokyo user will be one day ahead of the Paris user.

Should we use the user's dates?

This could create problems. If the sequence number includes the month and a counter value that runs over the entire year, and if the Tokyo user creates his document first, his document will get a high month but a lower counter value.

Should we use the server's dates or the UTC dates? This would not work well because our servers will handle tenants located in different time zones. A tenant who has its main office in Phoenix wants its documents to get the Phoenix date by default, even if the server is in California. For this tenant, UTC would be a terrible choice, as his time zone is 7 hours behind UTC.

And what about a tenant who has one site in Paris and another one in Tokyo?
If the tenant has configured different sequence numbers for his two sites he would likely want the documents to get their dates from their respective sites.

This can be implemented as follows:

-   We add a timeZone property on the Site node.
-   We set the defaultValue rule of the document date property to
    `defaultValue() { return DateValue.today(await (await this.site).timeZone; }`

## Q: You said that a date doesn't have a time zone and you passed a time zone to DateValue.today. Contradiction?

No, there is no real contradiction.
The time zone is used by the `today` method to compute the year/month/day values at the time the method was called.
The method uses these values to build a DateValue instance but the time zone is not recorded in the DateValue instance.

## What if I don't pass any time zone?

If you don't pass a time zone, the `today` method will use the browser's time zone in the browser and
the server's time zone on the server.
The server's time zone is the operating system's time zone which may be overridden with the TZ environment variable.

## What about Datetime? When should I use it? Does it have a time zone?

You should use Datetime when you need to represent a specific _point in time_, for example, the time at which a mail was sent, the start of a meeting, or a manufacturing operation, etc.

The `Datetime` instance tracks the time in its `value` property, as the number of milliseconds since Jan 1st, 1970 UTC.
It also has an optional `timeZone` property.

The `value` is stored in the database but the `timeZone` isn't.

The timeZone is set when you parse a time and used when you extract the components (year, month, day, hour, minute) of the `Datetime` instance. For example:

```ts
const dt1 = Datetime.parse('2024-01-31T20:00:00Z', undefined, undefined, 'Europe/Paris');
console.log(dt1.timeZone, dt1.month, dt1.day, dt1.hour);
// -> Europe/Paris 1 31 21 (Paris is 1 hour ahead of UTC)
console.log(dt1.value);
// -> 1706731200000
const dt2 = Datetime.parse('2024-01-31T20:00:00Z', undefined, undefined, 'Asia/Tokyo');
console.log(dt2.timeZone, dt2.month, dt2.day, dt2.hour);
// -> Asia/Tokyo 2 1 5 (Tokyo is 9 hours ahead of UTC)
console.log(dt2.value);
// -> 1706731200000
```

The components (month, day, hour) are different because they are taken in different time zones.
The value is the same because the instances represent the same _point in time_, the same UTC value.

The `timeZone` is also used when you format a `Datetime` instance:

```ts
dt1.format('en-US', 'DD/MM/YYYY HH:mm');
// -> '31/01/2024 21:00'
dt2.format('en-US', 'DD/MM/YYYY HH:mm');
// -> '01/02/2024 05:00'
```

You can also format it with the time zone, using the 'z' or 'zzz' directives:

```ts
dt2.format('en-US', 'DD/MM/YYYY HH:mm z');
// -> '01/02/2024 05:00 GMT+9'
dt2.format('en-US', 'DD/MM/YYYY HH:mm zzz');
// -> '01/02/2024 05:00 Asia/Tokyo'
```

The `Datetime` instances that you obtain from a node property do not have any `timeZone` but you can set it
with the `inTimeZone` method.
You can also apply this method to the `dt1` or `dt2` instances above, to change their timeZone:

```ts
dt1.inTimeZone('Asia/Tokyo').format('en-US', 'DD/MM/YYYY HH:mm');
// -> '01/02/2024 05:00 Asia/Tokyo'
dt1.inTimeZone('America/Phoenix').format('en-US', 'DD/MM/YYYY HH:mm');
// -> '31/01/2024 13:00'
```

This method does not modify the value so the instance is still at the same _point in time_:

```ts
console.log(dt1.inTimeZone('America/Phoenix').value);
// -> 1706731200000
```

## What about Time? It's a time so it should have a time zone, no?

`Time` is a bit like `DateValue`, it is a _pure_ time. It only has hour, minute, second, and optional millisecond components. It does not have a time zone.

The motivation is mostly a practical one.
Let us take an example with work shifts:
if you create a work shift like 8:00 to 17:00, you want to be able to use it on all your sites, even if they are in different time zones.
So the times that you have configured should be just pure times, they should not be tied to a time zone.

Once you have a date (a `DateValue` instance) and a time (a `Time` instance) you can build a datetime with them, and this is when you specify the time zone.
You can build the datetime with the `at` method.

```ts
const chosenDate = DateValue.parse('2024-02-15');
const chosenTime = Time.parse('06:00:00');
const dt1 = chosenDate.at(chosenTime, 'Europe/Paris');
console.log(dt1.toString());
// -> '2024-02-15T05:00:00.000Z' -- Paris is 1 hour ahead of UTC
const dt2 = chosenDate.at(chosenTime, 'Asia/Tokyo');
console.log(dt2.toString());
// -> '2024-02-14T21:00:00.000Z' -- falls on previous day in UTC because Tokyo is ahead of UTC
console.log(dt2.format('en-GB', 'YYYY-MM-DD HH:mm:ss zzz'));
// -> '2024-02-15 06:00:00 Asia/Tokyo' -- this is what we asked for
```

Things get subtle here. This is different from:

```ts
const dt3 = chosenDate.at(chosenTime).inTimeZone('Asia/Tokyo');
console.log(dt3.toString());
// -> '2024-02-15T05:00:00.000Z' -- assuming this was run on a server in the Paris time zone.
// The Datetime was built by `chosenDate.at(chosenTime)` so it took the Paris time zone.
// Its UTC value is 1 hour behind Paris, so 05:00
console.log(dt3.format('en-GB', 'YYYY-MM-DD HH:mm:ss zzz'));
// -> 2024-02-15 14:00:00 Asia/Tokyo
// dt3 is the same _point in time_, but in Tokyo time zone.
// When it is 06:00 in Paris, it is 14:00 in Tokyo.
```

In summary:

-   `chosenDate.at(chosenTime, 'Asia/Tokyo')` is 2024-02-15 06:00:00 occurring in Tokyo
-   `chosenDate.at(chosenTime).inTimeZone('Asia/Tokyo')` is 2024-02-15 06:00:00 occurring in Paris but viewed in Tokyo
