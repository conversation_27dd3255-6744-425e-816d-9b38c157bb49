PATH: XTREEM/10+Internationalization+API/01+User+messages+Localization

# Internationalization Framework for User Messages.

## Introduction

This framework use the same API as the client i18n API except that it supports only the `localize` function and the enums localization.

The general idea is to use a key for the message, a default string used when no other translation is available for the current locale and an optional object containing variable values.

## Common usages of localize method

The signature of the method is:

```
    cx.localize(key: string, value: string, parameters?: any)
```

where `cx` is an instance of `Context` or `ValidationContext`.

`value` can be a templated string using the handlebars syntax where each variable must have a corresponding key in the `parameters` object.

### Sample with ValidationContext

```ts
@decorators.node<ShowCaseProduct>({
    kind: 'persistent',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    controlEnd(cx: ValidationContext) {
        cx.localize('@sage/xtrem-show-case/productKey', 'product value');
        cx.localize('@sage/xtrem-show-case/parameterizedKey', 'parameterized value {{num}}', { num: 2 });
        cx.info.add('product validation successful');
    },
})
export class ShowCaseProduct extends Node {
    ...
}
```

### Sample with Context

```ts
@decorators.node<WorkOrder>({
    package: 'xtrem-manufacturing',
    kind: 'persistent',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    async createEnd() {
        if (!!(await this._sequenceNumberCode) && !(await this.number)) {
            const sequenceNumber = await this.$.context.tryRead(xtremMasterData.nodes.SequenceNumber, {
                code: this._sequenceNumberCode,
            });
            if (sequenceNumber) {
                ...
            } else {
                this.$.context.diagnoses.push({
                    message: this.$.context.localize(
                        '@sage/xtrem-manufacturing/work-order/noSequenceCounter',
                        'No sequence counter entry with the code {{code}}',
                        { code: this._sequenceNumberCode },
                    ),
                    path: [],
                    severity: ValidationSeverity.warn,
                });
            }
        } else {
            this.$.context.diagnoses.push({
                message: this.$.context.localize(
                    '@sage/xtrem-manufacturing/work-order/noSequenceAuto',
                    'No default sequence number code. Unable to automatically generate a work order number',
                ),
                path: [],
                severity: ValidationSeverity.warn,
            });
        }
    },
})
export class WorkOrder extends Node {
    ...
}
```

## Common usages of enums localization

Given a enums declared as follows:

```ts
import { EnumDataType } from '@sage/xtrem-core';

export enum ShowCaseProductCategoryEnum {
    great,
    good,
    ok,
    notBad,
    awful,
}

export type ShowCaseProductCategory = keyof typeof ShowCaseProductCategoryEnum;

export const showCaseProductCategoryDataType = new EnumDataType<ShowCaseProductCategory>(
    ShowCaseProductCategoryEnum,
    __filename,
);
```

You can obtain the localized value of a member with:

```ts
showCaseProductCategoryDataType.getLocalizedValue(context, 'notBad');
```

Or all member values with:

```ts
const localizedEnum: Enums = showCaseProductCategoryDataType.getLocalizedValues(context);
```
