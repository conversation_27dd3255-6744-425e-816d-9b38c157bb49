PATH: XTREEM/06+Node+inheritance/2+A+thorough+example

# A thorough example

We'll define the following hierarchy:

```
             Animal
               |
    +----->  Mammal <-----+
    |                     |
   Dog                   Cat
```

## How to defined a node hierarchy

You need to use the `@decorators.subNode` decorator:

```ts
@decorators.subNode<Dog>({
    extends: () => Mammal,
})
export class Dog extends Mammal {
    ...
}
```

You have to define the `extends` at both _TypeScript_ and _decorator_ levels.

The base node (_Mammal_) must be declared as `abstract` :

```ts
@decorators.node<Mammal>({
    storage: 'sql',
    isAbstract: true,
})
export class Mammal extends Node {
    ...
}
```

## Multi-levels hierarchy

A base node can also have a base node.
For instance, to define a multi-level hierarchy where :

-   Mammal extends Animal
-   Dog and Cat both extend Mammal

```ts
@decorators.node<Animal>({
    storage: 'sql',
    isAbstract: true,
})
export class Animal extends Node {
    ...
}

@decorators.subNode<Mammal>({
    isAbstract: true,
    extends: () => Animal,
})
export class Mammal extends Animal {
    ...
}

@decorators.subNode<Dog>({
    extends: () => Mammal,
})
export class Dog extends Mammal {
    ...
}

@decorators.subNode<Cat>({
    extends: () => Mammal,
})
export class Cat extends Mammal {
    ...
}

```

Notes:

-   the intermediates nodes (here _Mammal_) must still be declared as `abstract`.

## Creating nodes

Only the leaf nodes (_Cat_, _Dog_) can be used to create nodes, i.e. an abstract node can't be used to create a node (_Mammal_, _Animal_).

```ts
context.create(Cat, {....}) // will create a Cat

context.create(Mammal, {....}) // will throw an exception
```

## Reading nodes

You can read an existing node from any of its classes:

```ts
const node1 = context.read(Dog, { _id: 2 });
const node2 = context.read(Mammal, { _id: 2 });
const node3 = context.read(Animal, { _id: 2 });
```

Here, _node1, node2, node3_ are equal and all typed as Dog.

## Not yet implemented

The following features are Not Yet implemented but should be released very soon :

-   Filters on inherited properties (for context.query(...) and/or context.deleteMany(...))
-   OrderBys on inherited properties
