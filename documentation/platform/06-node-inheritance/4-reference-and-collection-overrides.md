PATH: XTREEM/06+Node+inheritance/4+Reference-and-collection-overrides

Polymorphic reference and collection properties can be overridden to improve the TypeScript and GraphQL APIs

# Example

The typical scenario is a document hierarchy with base classes like:

```ts
@decorators.node<BaseDocument>({
    isAbstract: true,
    // more ...
})
export class BaseDocument extends Node {
    @decorators.collectionProperty<BaseDocument, 'lines'>({
        isVital: true,
        node: () => BaseDocumentLine,
        // more ...
    })
    readonly lines: Collection<BaseDocumentLine>;

    // more ...
}

@decorators.node<BaseDocumentLine>({
    isAbstract: true,
    // more ...
})
export class BaseDocumentLine extends Node {
    @decorators.referenceProperty<BaseDocumentLine, 'document'>({
        node: () => BaseDocument,
        // more ...
    })
    readonly document: Reference<BaseDocument>;

    // more ...
}
```

and a subclass like:

```ts
@decorators.subNode<Invoice>({
    extends: () => BaseDocument,
    // more ...
})
export class Invoice extends BaseDocument {
    // invoice properties ...
}

@decorators.subNode<InvoiceLine>({
    extends: () => BaseDocumentLine,
    // more ...
})
export class InvoiceLine extends BaseDocumentLine {
    // invoice line properties ...
}
```

With these definitions, the typing is not ideal:

-   `Invoice` inherits its `lines` collection property from `BaseDocument` but it is a collection of `BaseDocumentLine`. It should be a collection of `InvoiceLine`.
-   `InvoiceLine` inherits its `document` reference property from `BaseDocument` but it is a reference to a `BaseDocument`. It should be a reference to an `Invoice`.

# Fixing the types with property overrides

This can be fixed with property overrides:

```ts

@decorators.subNode<Invoice>({
    extends: () => BaseDocument,
    // more ...
})
export class Invoice extends BaseDocument {
    decorators.collectionPropertyOverride<Invoice, 'lines'>({
        node: () => InvoiceLine,
    })
    readonly lines: Collection<InvoiceLine>;

    // more invoice properties ...
}

@decorators.subNode<InvoiceLine>({
    extends: () => BaseDocumentLine,
    // more ...
})
export class InvoiceLine extends BaseDocumentLine {
    decorators.referencePropertyOverride<InvoiceLine, 'document'>({
        node: () => Invoice,
    })
    readonly document: Reference<Invoice>;

    // more invoice line properties ...
}
```

With these overrides, the TypeScript API is improved:

-   `invoice.lines` is a collection of `InvoiceLine`.
-   `invoiceLine.document` is an `Invoice`.

The GraphQL API is also improved:

-   The `lines` property of an invoice can be directly read and mutated as a collection of invoice lines.
-   The `document` property of an invoice line is directly read as an invoice document.

# Gotchas

The GraphQL API has a special tweak: Polymorphic collections which are overridden in subclasses are renamed in the base classes. In our example, the `lines` collection is exposed as `anyLines` rather than `lines` in `BaseDocument`. The framework automatically adds an `any` prefix to the collection name.

This gotcha only applies to **overridden collections** in the **GraphQL API**. The TypeScript API is not impacted.
