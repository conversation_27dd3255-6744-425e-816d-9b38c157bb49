PATH: XTREEM/01+Global+architecture+of+the+XtreeM+project/1+Sage+repositories

# Sage mono repository (xtrem)

There is one repository for the project and it can be found [here](https://github.com/Sage-ERP-X3/xtrem).

The repository contains several top-level folders:

-   `documentation`: Contains the documentation of the project.
-   `performance-test`: Contains performance tests created within the project.
-   `pipeline`: Contains [Azure Pipelines](https://sage-liveservices.visualstudio.com/X3%20XTREM) scripts
-   **`platform`**: Contains framework and development tools. For more information, click [here](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/README.md).
-   `scripts`: Contains the automation of internal processes of the project's life cycle (releases, documentation publishing scripts, etc)
-   **`services`**: Contains the [business logic](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/README.md) of the project.
-   `tools`: Contains the project files for the [Glossary project](https://github.com/Sage-ERP-X3/xtrem/blob/master/tools/README.md).

-   `wms`: Contain the project files fo the [Warehouse project](https://github.com/Sage-ERP-X3/xtrem/blob/master/wms/README.md).
-   **`x3-services`**: Contains the integration between this project and Sage X3, for more information click [here](https://github.com/Sage-ERP-X3/xtrem/blob/master/x3-services/README.md).
