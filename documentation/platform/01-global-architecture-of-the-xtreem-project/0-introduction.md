PATH: XTREEM/Server+Framework+Documentation/01+Global+architecture+of+the+XtreeM+project

# Global architecture of the XTreeM project

This section is an introduction to the architecture of the XTreeM project.

We will have a quick word about all the major technical choices that were made for this project:

-   The choice of having several Sage repositories to distinguish platform logic from applicative logic
-   The distribution of applicative logic among packages and possibly bundles
-   The choice of using Nodes and decorators
-   The use of a GraphQL API
-   The choice of a multi-tenant cluster architecture
