PATH: XTREEM/Server+Framework+Documentation/17+Communication+framework

# Communication framework

This section presents the communication framework.

This framework includes 2 systems to address 2 different use-cases:

-   A notification system
-   A messaging system

## Notification system

The purpose of this system is to provide an internal communication channel between XTreeM Services.
It is based on the pub-sub (publish – subscribe) model to allow services to communicate with each other via notifications.

A notification carries a topic and services will only receive notification from the topics they listen to.

## Messaging system

Messaging provides a way to communicate with external services. To utilize messaging, it is required to create an XTreeM Adapter Service.
This is a point-to-point model, with uni-directional channels. You need 2 channels to communicate in both directions.
The Adapter is responsible for implementing the logic behind this communication, the framework only provides the technical stack to do it.

## Details

Details on APIs are provided in the following sections:

-   Enabling the communication framework
-   Configuring communication queues
-   Notify and send API
