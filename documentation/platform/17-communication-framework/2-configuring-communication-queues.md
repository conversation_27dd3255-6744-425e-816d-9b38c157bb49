PATH: XTREEM/17+Communication+framework/2+Configuring+communication+queues

# Declaring and configuring communication queues

XTreeM uses SQS queues to exchange messages with external services and to notify other XTreeM services.
You must declare and configure these SQS queues.

Note: in development mode you can use ElasticMQ which is compatible with SQS.

## Message queues

If your package communicates with an external service you must declare:

-   a _send_ queue if you send messages to the external service.
-   a _receive_ queue if you receive messages from the external service.

You cannot use the same queue to send and receive, except for a loopback test; you need a pair of queues
if you exchange messages in both directions.
But you won't always need two queues. For example, you may use direct API calls (SOAP, REST, GraphQL) to call the other service,
and the other service may notify you by posting to an SQS queue. In this case you only need a receive queue.

## Notification queue

Communication between XTreeM services is based on a _PubSub_ (Publish and Subscribe) middleware:
you _publish_ notifications which carry a topic and all the services which have _subscribed_ to this topic will receive them.

This PubSub middleware uses a single queue. So your package only needs one notification queue.

## Declaring the queues

The queues are declared in a `lib/queues` directory in your package.
You must create this directory if it does not already exist.

Inside this directory you should have one source file per queue.

### Send queue

If you send messages to an external service, declare a send queue as follows:

```typescript
import { SqsSendQueue } from '@sage/xtrem-core';

export const toIntacct = new SqsSendQueue({ name: 'toIntacct', description: 'SQS queue to send messages to Intacct' });
```

The file name should correspond to the queue name. In this case: `lib/queues/to-intacct.ts`

### Receive queue

Receive queues follow a similar pattern:

```typescript
import { SqsReceiveQueue } from '@sage/xtrem-core';

export const intacctReceive = new SqsReceiveQueue({
    name: 'intacctReceive',
    description: 'SQS queue to receive messages from Intacct',
});
```

The file name should correspond to the queue name. In this case: `lib/queues/from-intacct.ts`

### Notification queue

The notification queue is also declared with the same pattern:

```typescript
import { NotificationQueue } from '@sage/xtrem-core';

export const manufacturing = new NotificationQueue({
    name: 'manufacturing',
    description: 'Notification queue for manufacturing service',
});
```

The file name should correspond to the queue name. In this case: `lib/queues/manufacturing.ts`.

### Exporting all the queues

Once you have defined your queues, you add an \_index.ts file where you export all your queues:

```typescript
export * from './from-intacct';
export * from './to-intacct';
export * from './manufacturing';
```

Then you export all the queues by adding the following code to your `lib/index.ts` file:

```typescript
import * as queues from './queues/_index`;
export { queues };
```

## Configuring the queues

The TypeScript source file only contains a declaration for the queue; it does not contain its full configuration.

The queues are configured in the xtrem-config.yml file:

```yml
interop:
    queues:
        intacctReceive:
            url: 'http://127.0.0.1:9324/queue/unit-test-queue.fifo'
            concurrentLimit: 20
        toIntacct:
            url: 'http://127.0.0.1:9324/queue/unit-test-queue.fifo'
    routingPollingSeconds: 5
    routingReadCount: 3
    sendRetryCount: 3
    sendRetrySeconds: 90
    receiveRetryCount: 3
    receiveRetrySeconds: 1
    concurrentNotificationsLimit: 10
    concurrentMessagesLimit: 15
    messageVisibilitySeconds: 30
    sqsGroupsPerTenantUser: 10
```

-   `routingPollingSecond` (The polling interval parameter) is optional. The default value of routingPollingSecond is 1 second.
-   `routingReadCount` (The number of repeats) is optional. The default value of routingReadCount is 3.
-   `sendRetryCount` (The number of retries) is optional. The default value of sendRetryCount is 3.
-   `sendRetrySeconds` (The number of seconds between retries) is optional. The default value of sendRetrySeconds is 90.
-   `receiveRetryCount` (The number of retries) is optional. The default value of receiveRetryCount is 3.
-   `receiveRetrySeconds` (The number of seconds between retries) is optional. The default value of receiveRetrySeconds is 1.
-   `concurrentNotificationsLimit` (The notification receiving queue funnel size) is optional. The default value of 10.
-   `concurrentMessagesLimit` (The message receiving queue funnel size) is optional. The default value of 10.
-   `concurrentLimit` (The funnel size of the queue ). If this is set, then it will be used instead of `concurrentNotificationsLimit`/`concurrentMessagesLimit`
-   `messageVisibilitySeconds` (The number of seconds a message is invisible when processing is attempted) is optional. The default value of messageVisibilitySeconds is 30 seconds.
-   `sqsGroupsPerTenantUser` (The maximum number of SQS message groups per tenant user) is optional. The default value of sqsGroupsPerTenantUser is 5 message groups.

This configuration is a development config for ElasticMQ. For SQS you also need to provide your AWS credentials:

TODO: example with SQS url and AWS credentials
