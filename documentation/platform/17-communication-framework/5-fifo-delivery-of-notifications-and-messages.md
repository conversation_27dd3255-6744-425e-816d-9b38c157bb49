PATH: XTREEM/17+Communication+framework/5+FIFO+delivery+of+notifications+and+messages

# FIFO delivery of notifications and messages

Notifications and messages which are sent during the same transaction will always be delivered in the order in which they have been sent.

To ensure that the routing service dequeues and requeues notifications and messages in the correct order, an advisory lock is created when dequeue notifications and messages from the _onNotificationQueued_ / _onMessageQueued_ event handlers.

Dequeueing events that are triggered after the current event in progress, will wait to acquire an advisory lock, before processing the notifications or messages.

To ensure SQS fair use, the MessageGroupId is set to the `${context.tenantId}-${context.userId}-{random integer from 0 to sqsGroupsPerTenantUser in InteropConfig}`. At any time, there is no more than `sqsGroupsPerTenantUser` messages being processed for a given tenant user. More info on the MessageGroupId property can be found [here](https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/using-messagegroupid-property.html).
