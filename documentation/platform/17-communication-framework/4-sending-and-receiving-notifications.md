PATH: XTREEM/17+Communication+framework/4+Sending+and+receiving+notifications

# Sending and receiving notifications

## CRUD notification

### Defining the operation events for auto-notifications

A node can automatically send notifications when node instances are created, updated or deleted. This is done by defining the list of event names in the `notifies` attribute of the `node` decorator.

The possible values for events names are:

-   `'created'`
-   `'updated'`
-   `'deleted'`

The notification payload will contain two properties: `_id` and `_updateTick`

For example:

```ts
@decorators.node<Customer>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
    notifies: ['created', 'updated', 'deleted'],
})
export class Customer extends Node {
    @decorators.stringProperty<Customer, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Customer, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly isActive: Promise<boolean>;
}
```

### Sending a notification payload

A notification can be sent with its node payload with:

```ts
const notificationId = context.notify('Customer/activated', customer.$.notificationPayload, {
    replyTopic: 'myOptionalTopic',
});
```

There is an option to explicitly pass `replyTopic` which can be used in the listener.

The notificationId (a nanoId that identifies a notification) is returned from `context.notify`.

### Replying to a notification

A reply to a specific notification can be sent with:

```ts
context.reply('Customer/activated', customer.$.notificationPayload, { replyId: myOldNotfication });
```

There is an option to explicitly pass `replyId`, which is the notificationId of the notification we are replying to.

A reply can be also initiated in the static listener method of a notification. In this case, the replyId does not need to be supplied,
as it will be derived from the `notificationId` of the original message.

```ts
@decorators.notificationListener<typeof Customer>({
    queue: () => Customer,
    topic: 'onHold',
})
static async customerOnhold(context: Context, payload: <CustomerOnHoldPayload>): Promise<void> {
    // some other logic maybe
        const notificationId = await context.reply<CustomerOnHoldPayload>('SalesOrder/onHoldCustomer', payload);
}
```

In the example above, we have a listener that is fired when a customer is placed on hold. This listener then calls `context.reply` which will trigger a notification for the topic `onHoldCustomer`. The reply notification will have a `replyId` of the current notification.

The notificationId and replyId, will be used in the tracking of notications in logs.

### Notification context values

In the notification static listener method the following values can be retrieved using the `context.getContextValue('valueKey')` method.

| Key            | Description                                                              |
| -------------- | ------------------------------------------------------------------------ |
| tenantId       | tenantId that the notification relates to                                |
| topic          | topic that the notification relates to                                   |
| userEmail      | email of the user that initiated the notification                        |
| locale         | locale of the notification                                               |
| notificationId | notificationId of the notification that this notification is replying to |

### Sending a notification extended payload

The notification payload can be sent with the same syntax without any changes:

```ts
context.notify('Customer/activated', customer.$.notificationPayload);
```

## Defining a notification listener

A node can listen to a topic of a queue by adding a `notificationListener` decorator on a static method as follows:

```ts
@decorators.notificationListener<MyNode, CustomerNotificationPayload>({
    queue: () => xtremManufacturing.queues.manufacturing,
    topic: ‘customer/created’,
    onError: (context, envelope, error) => {
        SysNotificationHistory.createNotificationHistory(context, envelope, 'notSent', error);
    },
    startsReadOnly(context, envelope) {
            return !!envelope.payload.startsReadOnly;
    },
})
static async onCustomerCreate(context: Context, payload: CustomerNotificationPayload): Promise<void> {
    // process the payload
}
```

Where the decorator attributes are:

-   queue: An arrow function without arguments that returns the package's notification queue. Please, be careful to return a const and not instantiate a new queue in that function.
-   topic: The topic to listen to.
-   onError: _(optional)_ A function that manages an error thrown when a listener processes a message. If the onError function is not provided then the default action is to insert a record into the sys_notfication_history table (SysNotificationHistory node). Developers can use the static method `createNotificationHistory` in the `SysNotificationHistory` class to log notification history.
-   startsReadOnly: _(optional)_ A function or boolean value that indicates whether the context passed to the listener static method is read-only.

The tenant id, user and locale are automatically propagated from the source context (the `context.notify` call) to
the destination context (the `onCustomerCreate` listener above)
