PATH: XTREEM/17+Communication+framework/1+Enabling+the+communication+framework

# Enabling the communication framework

The communication framework is an extension of the `@sage/xtrem-core` API which is provided by the `@sage/xtrem-communication` package. To use it in a package you must add the `@sage/xtrem-communication` package as a dependency and import it, as follows:

-   add `@sage/xtrem-communication` to the `dependencies` section of your `package.json` file.
-   add an `import '@sage/xtrem-communication';` directive in your `lib/index.ts` file.

If one of the dependencies of your package already imports `@sage/xtrem-communication` you do not need to import it in your own package so you can skip this step.

On the main packages of your application, you will also need to add `@sage/xtrem-routing` to the `dependencies` as well. This is so that the routing service is started when the application is started.

For non-main application packages where you wish to perform unit tests, you will need to add `@sage/xtrem-routing` to the `devDependencies` of the package.

Remember to run `pnpm run clean:install` after you modified your package.json.
