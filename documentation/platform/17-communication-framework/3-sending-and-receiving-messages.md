PATH: XTREEM/17+Communication+framework/3+Sending+and+receiving+messages

# Sending and receiving messages

## Sending a message

To send a message to an external queue, you use the `context.send` API:

```ts
context.send(xtremIntacctAdapter.queues.toIntacct, { toIntacctData1: 'data1', toIntacctData2: 'data2' });
```

The `toIntacct` queue was declared in the `lib/queues/to-intacct.ts` source file.

## Defining a message listener

A node can listen to messages from a queue by adding a `messageListener` decorator on a static method as follows:

```ts
@decorators.messageListener<typeof MyNode, string>({
    queue: () => xtremIntacctAdapter.queues.intacctReceive,
    format: 'text',
    integrationSolution: 'foo',
    getTenantId(envelope) {
            return envelope.attributes.tenantId || envelope.attributes.ClaimedTenantId || '0'.repeat(21);
    },
    getContextAttributes(context, envelope) {
        const { user, locale } = extractAttributes(envelope.payload);
        return { user, locale };
    },
    onError(context, envelope, error) {
        // Manage gracefully or rethrow
    },
    getId(context, envelope) {
        return extractHistoryId(envelope.payload);
    },
    startsReadOnly(context, envelope) {
            return !!envelope.payload.startsReadOnly;
    },
})
static async onIntacctMessage(context: Context, envelope: MessageEnvelope<string>): Promise<void> {
    // process the envelope
}
```

The decorator has the following attributes:

-   `queue`: an arrow function that returns the queue.
    Please, always return a queue exported by `lib/queues/_index`. Do not instantiate a new queue in the arrow function.
-   `format`: the format of the message: `json` or `text`.
-   `integrationSolution`: _(optional)_ the name of the integration solution, this will be one of the key values stored in SysMessageHistory. If this attribute is not supplied, the listener's full name will be used.
-   `getTenantId`: a mandatory function which extracts the tenant id.
-   `getContextAttributes`: a mandatory function which extracts the user and the locale from the envelope.
-   `onError`: _(optional)_ A function that manages an error thrown when processing a message.
-   `getId`: _(optional)_ A function that gets the `id` stored in SysMessageHistory. If not supplied then the `id` will be a generated uuid.
-   `startsReadOnly`: _(optional)_ A function or boolean value that indicates whether the context passed to the listener static method is read-only.

### MessageEnvelope

The `envelope` parameter will be typed as a `MessageEnvelope`. This type will provide 2 properties

-   `payload`: If the format is `json`, the payload will be parsed as an object, otherwise it will a string.
-   `attributes`: this will contain an object with string properties, representing the `MessageAttribute` sent via the SQS queue.

### getTenantId

The `getTenantId` function returns the tenant id which will be used to initialize the context of the static method. This function will typically use mapping tables to lookup the tenant id.

### getContextAttributes

The `getContextAttributes` function returns the user and locale which will be used to initialize the context of the static method. The user can be queried from the `xtremSystem.nodes.User` node. The locale can be set to a default like `'en-US'` if it cannot be derived from the payload and attributes in the `MessageEnvelope`.

### onError

The `onError` function, if provided on the listener decorator, will be used if the processing of a message throws and error. If the onError function is not provided then the default action is to insert a record into the sys_message_history table (SysMessageHistory node).

## messageListener static method

The `messageListener` static method has two parameters:

-   the first one (`typeof MyNode` in the example) is the type of the node constructor.
-   the second (`MessageEnvelope`) is the envelope of the message (see definition of `MessageEnvelope` above).
