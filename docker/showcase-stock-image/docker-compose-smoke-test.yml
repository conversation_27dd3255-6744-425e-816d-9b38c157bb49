version: '3'
services:
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    restart: always
    command: >
      -c max_locks_per_transaction=256
    environment:
      - POSTGRES_USER=${PG_SYSUSER}
      - POSTGRES_PASSWORD=${PG_SYSPWD}
  xtrem:
    image: xtrem-showcase-stock:smoke-test
    command: >
      /xtrem/scripts/smoke-test.sh
    volumes:
      - ./:/xtrem/config
      - ./scripts:/xtrem/scripts
    depends_on:
      - pgdb
