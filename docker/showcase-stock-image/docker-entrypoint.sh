#!/bin/sh

displayAsciiArt() {
  # Font: graffiti
  cat << "EOF"

       .__                                                                  __                 __
  _____|  |__   ______  _  __ ____ _____    ______ ____             _______/  |_  ____   ____ |  | __
 /  ___/  |  \ /  _ \ \/ \/ // ___\\__  \  /  ___// __ \   ______  /  ___/\   __\/  _ \_/ ___\|  |/ /
 \___ \|   Y  (  <_> )     /\  \___ / __ \_\___ \\  ___/  /_____/  \___ \  |  | (  <_> )  \___|    <
/____  >___|  /\____/ \/\_/  \___  >____  /____  >\___  >         /____  > |__|  \____/ \___  >__|_ \
     \/     \/                   \/     \/     \/     \/               \/                   \/     \/

EOF
}

versionInfo() {
    # display CLI version
    grep '"version":' /xtrem/app/package.json | sed 's/[",:]//g' | awk '{ print $1" "$2 }'
    echo ""
}

initEnv() {
    # Default env value is local if not set before
    ${ENV:="local"}
    echo "Env is set to $ENV"

    # Create symlinks on config files (xtrem-config.yml, xtrem-security.yml) to allow hot reload
    ln -s /xtrem/config/xtrem-config.yml /xtrem/app/xtrem-config.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-security.yml /xtrem/app/xtrem-security.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-ui.yml /xtrem/app/xtrem-ui.yml 2> /dev/null
}

displayAsciiArt
versionInfo
initEnv

# Now exec the npm script or command system command
set -e

if [ "${1#-}" != "${1}" ] || [ -z "$(command -v "${1}")" ]; then
  set -- xtrem "$@"
fi

exec "$@"
