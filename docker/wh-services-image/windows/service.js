const { Service } = require('node-windows');
const path = require('path');
const os = require('os');

// Basic argument parsing, as requiring yargs breaks node-windows
function mapArgs(args) {
    let currentArgName = '';
    const argsCopy = [...args];
    const result = {};
    while (argsCopy.length > 0) {
        const arg = argsCopy.shift();
        if (currentArgName === '') {
            if (arg.startsWith('--')) {
                currentArgName = arg.slice(2);
            }
        } else {
            result[currentArgName] = arg;
            currentArgName = '';
        }
    }
    return result;
}

const argv = mapArgs(process.argv);

const platform = `${os.platform()}-${os.arch()}`;

const errorCodes = {
    badOperation: 1,
    windowsOnly: 2,
    cannotSetServiceFailure: 3,
};
const serviceConfig = {
    name: argv.name ?? 'Sage Warehouse Services',
    description: argv.description ?? 'Sage Warehouse Services',
    script: path.join(__dirname, 'node_modules/@sage/xtrem-cli/bin/xtrem'),
    nodeOptions: ['--max_old_space_size=4096'],
    workingDirectory: __dirname,
    execPath: path.join(__dirname, 'nodejs', `${platform}`, 'node.exe'),
};

const operations = {
    install: args => {
        const svc = new Service({
            ...serviceConfig,
            scriptOptions: 'start',
            allowServiceLogon: !!args.user,
        });

        if (args.domain) svc.logOnAs.domain = args.domain;
        if (args.user) {
            svc.logOnAs.account = args.user;
            if (!args.password && !args.base64passwd) throw new Error('password required when user provided');
        } else if (args.password | args.base64passwd) {
            throw new Error('user required when password provided');
        }

        if (args.password) {
            svc.logOnAs.password = args.password;
        }

        if (args.base64passwd) {
            if (args.password) throw new Error('cannot supply both password and base64passwd arguments');
            svc.logOnAs.password = Buffer.from(args.base64passwd, 'base64').toString();
        }

        // Listen for the "install" event, which indicates the
        // process is available as a service.
        svc.on('install', () => {
            console.log('Install complete.');
            // node-windows use its own wrapper to monitor the script, even if the xtreemx3 process is killed the
            // script will continue to run, this may also have some drawbacks but for now it works as is.
            svc.start();
        });

        svc.on('start', () => {
            console.log('Start complete.');
        });

        svc.on('error', err => {
            console.log(`An error occured: ${err}`);
        });

        svc.on('alreadyinstalled ', () => console.error(`Service ${serviceConfig.name} is already installed`));

        svc.install();
    },

    uninstall: () => {
        const svc = new Service({
            name: serviceConfig.name,
            script: serviceConfig.script,
        });

        // Listen for the "uninstall" event so we know when it's done.
        svc.on('uninstall', function () {
            console.log('Uninstall complete.');
            console.log('The service exists: ', svc.exists);
        });

        // Uninstall the service.
        svc.uninstall();
    },
};

if (platform !== 'win32-x64') {
    console.error('This service management is available only on Windows x64 platforms');
    process.exit(errorCodes.windowsOnly);
}
const op = argv.operation;

if (operations[op]) {
    operations[op](argv);
} else {
    console.error(`Operation ${op} not supported.`);
    console.error(`usage: service {install | uninstall}`);
    process.exit(errorCodes.badOperation);
}
