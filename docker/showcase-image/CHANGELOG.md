# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [24.0.8](https://github.com/compare/@sage/show-case@24.0.7...@sage/show-case@24.0.8) (2022-10-18)

## [24.0.7](https://github.com/compare/@sage/show-case@24.0.6...@sage/show-case@24.0.7) (2022-10-16)

# [24.0.6](https://github.com/compare/@sage/show-case@24.0.5...@sage/show-case@24.0.6) (2022-10-16)

## [24.0.6](https://github.com/compare/@sage/show-case@24.0.5...@sage/show-case@24.0.6) (2022-10-15)

# [24.0.5](https://github.com/compare/@sage/show-case@24.0.4...@sage/show-case@24.0.5) (2022-10-15)

## [24.0.5](https://github.com/compare/@sage/show-case@24.0.4...@sage/show-case@24.0.5) (2022-10-15)

### Bug Fixes


# [24.0.4](https://github.com/compare/@sage/show-case@24.0.3...@sage/show-case@24.0.4) (2022-10-15)

### Bug Fixes

* use multi-stage build to improve security and size (XT-34151) ([#9343](https://github.com/issues/9343))  ([4d90a41](https://github.com/commit/4d90a419b65ba7d083d025b10c0e4381d6dfb493))

## [24.0.4](https://github.com/compare/@sage/show-case@24.0.3...@sage/show-case@24.0.4) (2022-10-11)

# [24.0.3](https://github.com/compare/@sage/show-case@24.0.2...@sage/show-case@24.0.3) (2022-10-11)

## [24.0.3](https://github.com/compare/@sage/show-case@24.0.2...@sage/show-case@24.0.3) (2022-10-11)

# [24.0.2](https://github.com/compare/@sage/show-case@24.0.1...@sage/show-case@24.0.2) (2022-10-11)

## [24.0.2](https://github.com/compare/@sage/show-case@24.0.1...@sage/show-case@24.0.2) (2022-10-11)

### Features


# [24.0.1](https://github.com/compare/@sage/show-case@24.0.0...@sage/show-case@24.0.1) (2022-10-11)

### Features

* replace sqreen by tCell (XT-24441) ([#9174](https://github.com/issues/9174))  ([c5ed339](https://github.com/commit/c5ed3393e1ccd54f415b72d4132a79b5eaf786ad))

## [24.0.1](https://github.com/compare/@sage/show-case@24.0.0...@sage/show-case@24.0.1) (2022-09-29)

# [24.0.0](https://github.com/compare/@sage/show-case@23.0.48...@sage/show-case@24.0.0) (2022-09-29)

## [24.0.0](https://github.com/compare/@sage/show-case@23.0.48...@sage/show-case@24.0.0) (2022-09-29)

# [23.0.48](https://github.com/compare/@sage/show-case@23.0.47...@sage/show-case@23.0.48) (2022-09-29)

## [23.0.48](https://github.com/compare/@sage/show-case@23.0.47...@sage/show-case@23.0.48) (2022-09-29)

### Bug Fixes


# [23.0.47](https://github.com/compare/@sage/show-case@23.0.45...@sage/show-case@23.0.47) (2022-09-29)

### Bug Fixes

* failed to start show case image (XT-33833) ([#9121](https://github.com/issues/9121))  ([cecd2ff](https://github.com/commit/cecd2ff7a1aa1a0d88fa04480ffad68757fee5a6))

## [23.0.47](https://github.com/compare/@sage/show-case@23.0.45...@sage/show-case@23.0.47) (2022-09-28)

# [23.0.46](https://github.com/compare/@sage/show-case@23.0.45...@sage/show-case@23.0.46) (2022-09-28)

### Features


# [23.0.45](https://github.com/compare/@sage/show-case@23.0.44...@sage/show-case@23.0.45) (2022-09-28)

### Features

* **cli:** move plugins to devDependencies (X3-283449) ([#9001](https://github.com/issues/9001))  ([61f93dc](https://github.com/commit/61f93dcbd42d74c8e13a098aae55e7a43de39e5e))

## [23.0.45](https://github.com/compare/@sage/show-case@23.0.44...@sage/show-case@23.0.45) (2022-09-26)

# [23.0.44](https://github.com/compare/@sage/show-case@23.0.43...@sage/show-case@23.0.44) (2022-09-26)

## [23.0.44](https://github.com/compare/@sage/show-case@23.0.43...@sage/show-case@23.0.44) (2022-09-25)

# [23.0.43](https://github.com/compare/@sage/show-case@23.0.42...@sage/show-case@23.0.43) (2022-09-25)

## [23.0.43](https://github.com/compare/@sage/show-case@23.0.42...@sage/show-case@23.0.43) (2022-09-24)

# [23.0.42](https://github.com/compare/@sage/show-case@23.0.41...@sage/show-case@23.0.42) (2022-09-24)

## [23.0.42](https://github.com/compare/@sage/show-case@23.0.41...@sage/show-case@23.0.42) (2022-09-23)

# [23.0.41](https://github.com/compare/@sage/show-case@23.0.40...@sage/show-case@23.0.41) (2022-09-23)

## [23.0.41](https://github.com/compare/@sage/show-case@23.0.40...@sage/show-case@23.0.41) (2022-09-22)

# [23.0.40](https://github.com/compare/@sage/show-case@23.0.39...@sage/show-case@23.0.40) (2022-09-22)

## [23.0.40](https://github.com/compare/@sage/show-case@23.0.39...@sage/show-case@23.0.40) (2022-09-21)

# [23.0.39](https://github.com/compare/@sage/show-case@23.0.38...@sage/show-case@23.0.39) (2022-09-21)

## [23.0.39](https://github.com/compare/@sage/show-case@23.0.38...@sage/show-case@23.0.39) (2022-09-21)

# [23.0.38](https://github.com/compare/@sage/show-case@23.0.37...@sage/show-case@23.0.38) (2022-09-21)

## [23.0.38](https://github.com/compare/@sage/show-case@23.0.37...@sage/show-case@23.0.38) (2022-09-21)

# [23.0.37](https://github.com/compare/@sage/show-case@23.0.36...@sage/show-case@23.0.37) (2022-09-21)

## [23.0.37](https://github.com/compare/@sage/show-case@23.0.36...@sage/show-case@23.0.37) (2022-09-19)

# [23.0.36](https://github.com/compare/@sage/show-case@23.0.35...@sage/show-case@23.0.36) (2022-09-19)

## [23.0.36](https://github.com/compare/@sage/show-case@23.0.35...@sage/show-case@23.0.36) (2022-09-19)

# [23.0.35](https://github.com/compare/@sage/show-case@23.0.34...@sage/show-case@23.0.35) (2022-09-19)

## [23.0.35](https://github.com/compare/@sage/show-case@23.0.34...@sage/show-case@23.0.35) (2022-09-19)

# [23.0.34](https://github.com/compare/@sage/show-case@23.0.33...@sage/show-case@23.0.34) (2022-09-19)

## [23.0.34](https://github.com/compare/@sage/show-case@23.0.33...@sage/show-case@23.0.34) (2022-09-19)

# [23.0.33](https://github.com/compare/@sage/show-case@23.0.32...@sage/show-case@23.0.33) (2022-09-19)

## [23.0.33](https://github.com/compare/@sage/show-case@23.0.32...@sage/show-case@23.0.33) (2022-09-18)

# [23.0.32](https://github.com/compare/@sage/show-case@23.0.31...@sage/show-case@23.0.32) (2022-09-18)

## [23.0.32](https://github.com/compare/@sage/show-case@23.0.31...@sage/show-case@23.0.32) (2022-09-18)

# [23.0.31](https://github.com/compare/@sage/show-case@23.0.30...@sage/show-case@23.0.31) (2022-09-18)

## [23.0.31](https://github.com/compare/@sage/show-case@23.0.30...@sage/show-case@23.0.31) (2022-09-18)

# [23.0.30](https://github.com/compare/@sage/show-case@23.0.29...@sage/show-case@23.0.30) (2022-09-18)

## [23.0.30](https://github.com/compare/@sage/show-case@23.0.29...@sage/show-case@23.0.30) (2022-09-18)

# [23.0.29](https://github.com/compare/@sage/show-case@23.0.28...@sage/show-case@23.0.29) (2022-09-18)

## [23.0.29](https://github.com/compare/@sage/show-case@23.0.28...@sage/show-case@23.0.29) (2022-09-17)

# [23.0.28](https://github.com/compare/@sage/show-case@23.0.27...@sage/show-case@23.0.28) (2022-09-17)

## [23.0.28](https://github.com/compare/@sage/show-case@23.0.27...@sage/show-case@23.0.28) (2022-09-17)

# [23.0.27](https://github.com/compare/@sage/show-case@23.0.26...@sage/show-case@23.0.27) (2022-09-17)

## [23.0.27](https://github.com/compare/@sage/show-case@23.0.26...@sage/show-case@23.0.27) (2022-09-16)

# [23.0.26](https://github.com/compare/@sage/show-case@23.0.25...@sage/show-case@23.0.26) (2022-09-16)

## [23.0.26](https://github.com/compare/@sage/show-case@23.0.25...@sage/show-case@23.0.26) (2022-09-16)

# [23.0.25](https://github.com/compare/@sage/show-case@23.0.24...@sage/show-case@23.0.25) (2022-09-16)

## [23.0.25](https://github.com/compare/@sage/show-case@23.0.24...@sage/show-case@23.0.25) (2022-09-16)

# [23.0.24](https://github.com/compare/@sage/show-case@23.0.23...@sage/show-case@23.0.24) (2022-09-16)

## [23.0.24](https://github.com/compare/@sage/show-case@23.0.23...@sage/show-case@23.0.24) (2022-09-16)

# [23.0.23](https://github.com/compare/@sage/show-case@23.0.22...@sage/show-case@23.0.23) (2022-09-16)

## [23.0.23](https://github.com/compare/@sage/show-case@23.0.22...@sage/show-case@23.0.23) (2022-09-14)

# [23.0.22](https://github.com/compare/@sage/show-case@23.0.21...@sage/show-case@23.0.22) (2022-09-14)

## [23.0.22](https://github.com/compare/@sage/show-case@23.0.21...@sage/show-case@23.0.22) (2022-09-14)

# [23.0.21](https://github.com/compare/@sage/show-case@23.0.20...@sage/show-case@23.0.21) (2022-09-14)

## [23.0.21](https://github.com/compare/@sage/show-case@23.0.20...@sage/show-case@23.0.21) (2022-09-12)

# [23.0.20](https://github.com/compare/@sage/show-case@23.0.19...@sage/show-case@23.0.20) (2022-09-12)

## [23.0.20](https://github.com/compare/@sage/show-case@23.0.19...@sage/show-case@23.0.20) (2022-09-12)

# [23.0.19](https://github.com/compare/@sage/show-case@23.0.18...@sage/show-case@23.0.19) (2022-09-12)

## [23.0.19](https://github.com/compare/@sage/show-case@23.0.18...@sage/show-case@23.0.19) (2022-09-11)

# [23.0.18](https://github.com/compare/@sage/show-case@23.0.17...@sage/show-case@23.0.18) (2022-09-11)

## [23.0.18](https://github.com/compare/@sage/show-case@23.0.17...@sage/show-case@23.0.18) (2022-09-10)

# [23.0.17](https://github.com/compare/@sage/show-case@23.0.16...@sage/show-case@23.0.17) (2022-09-10)

## [23.0.17](https://github.com/compare/@sage/show-case@23.0.16...@sage/show-case@23.0.17) (2022-09-09)

# [23.0.16](https://github.com/compare/@sage/show-case@23.0.15...@sage/show-case@23.0.16) (2022-09-09)

## [23.0.16](https://github.com/compare/@sage/show-case@23.0.15...@sage/show-case@23.0.16) (2022-09-09)

# [23.0.15](https://github.com/compare/@sage/show-case@23.0.14...@sage/show-case@23.0.15) (2022-09-09)

## [23.0.15](https://github.com/compare/@sage/show-case@23.0.14...@sage/show-case@23.0.15) (2022-09-07)

# [23.0.14](https://github.com/compare/@sage/show-case@23.0.13...@sage/show-case@23.0.14) (2022-09-07)

## [23.0.14](https://github.com/compare/@sage/show-case@23.0.13...@sage/show-case@23.0.14) (2022-09-07)

# [23.0.13](https://github.com/compare/@sage/show-case@23.0.12...@sage/show-case@23.0.13) (2022-09-07)

## [23.0.13](https://github.com/compare/@sage/show-case@23.0.12...@sage/show-case@23.0.13) (2022-09-06)

# [23.0.12](https://github.com/compare/@sage/show-case@23.0.11...@sage/show-case@23.0.12) (2022-09-06)

## [23.0.12](https://github.com/compare/@sage/show-case@23.0.11...@sage/show-case@23.0.12) (2022-09-05)

# [23.0.11](https://github.com/compare/@sage/show-case@23.0.10...@sage/show-case@23.0.11) (2022-09-05)

## [23.0.11](https://github.com/compare/@sage/show-case@23.0.10...@sage/show-case@23.0.11) (2022-09-05)

# [23.0.10](https://github.com/compare/@sage/show-case@23.0.9...@sage/show-case@23.0.10) (2022-09-05)

## [23.0.10](https://github.com/compare/@sage/show-case@23.0.9...@sage/show-case@23.0.10) (2022-09-04)

# [23.0.9](https://github.com/compare/@sage/show-case@23.0.8...@sage/show-case@23.0.9) (2022-09-04)

## [23.0.9](https://github.com/compare/@sage/show-case@23.0.8...@sage/show-case@23.0.9) (2022-09-03)

# [23.0.8](https://github.com/compare/@sage/show-case@23.0.7...@sage/show-case@23.0.8) (2022-09-03)

## [23.0.8](https://github.com/compare/@sage/show-case@23.0.7...@sage/show-case@23.0.8) (2022-09-02)

# [23.0.7](https://github.com/compare/@sage/show-case@23.0.6...@sage/show-case@23.0.7) (2022-09-02)

## [23.0.7](https://github.com/compare/@sage/show-case@23.0.6...@sage/show-case@23.0.7) (2022-09-01)

# [23.0.6](https://github.com/compare/@sage/show-case@23.0.5...@sage/show-case@23.0.6) (2022-09-01)

## [23.0.6](https://github.com/compare/@sage/show-case@23.0.5...@sage/show-case@23.0.6) (2022-08-31)

# [23.0.5](https://github.com/compare/@sage/show-case@23.0.4...@sage/show-case@23.0.5) (2022-08-31)

## [23.0.5](https://github.com/compare/@sage/show-case@23.0.4...@sage/show-case@23.0.5) (2022-08-30)

# [23.0.4](https://github.com/compare/@sage/show-case@23.0.3...@sage/show-case@23.0.4) (2022-08-30)

## [23.0.4](https://github.com/compare/@sage/show-case@23.0.3...@sage/show-case@23.0.4) (2022-08-30)

# [23.0.3](https://github.com/compare/@sage/show-case@23.0.2...@sage/show-case@23.0.3) (2022-08-30)

## [23.0.3](https://github.com/compare/@sage/show-case@23.0.2...@sage/show-case@23.0.3) (2022-08-29)

# [23.0.2](https://github.com/compare/@sage/show-case@23.0.1...@sage/show-case@23.0.2) (2022-08-29)

## [23.0.2](https://github.com/compare/@sage/show-case@23.0.1...@sage/show-case@23.0.2) (2022-08-29)

# [23.0.1](https://github.com/compare/@sage/show-case@23.0.0...@sage/show-case@23.0.1) (2022-08-29)

## [23.0.1](https://github.com/compare/@sage/show-case@23.0.0...@sage/show-case@23.0.1) (2022-08-26)

# [23.0.0](https://github.com/compare/@sage/show-case@22.0.25...@sage/show-case@23.0.0) (2022-08-26)

## [23.0.0](https://github.com/compare/@sage/show-case@22.0.25...@sage/show-case@23.0.0) (2022-08-25)

# [22.0.25](https://github.com/compare/@sage/show-case@22.0.24...@sage/show-case@22.0.25) (2022-08-25)

## [22.0.25](https://github.com/compare/@sage/show-case@22.0.24...@sage/show-case@22.0.25) (2022-08-24)

# [22.0.24](https://github.com/compare/@sage/show-case@22.0.23...@sage/show-case@22.0.24) (2022-08-24)

## [22.0.24](https://github.com/compare/@sage/show-case@22.0.23...@sage/show-case@22.0.24) (2022-08-23)

# [22.0.23](https://github.com/compare/@sage/show-case@22.0.22...@sage/show-case@22.0.23) (2022-08-23)

## [22.0.23](https://github.com/compare/@sage/show-case@22.0.22...@sage/show-case@22.0.23) (2022-08-22)

# [22.0.22](https://github.com/compare/@sage/show-case@22.0.21...@sage/show-case@22.0.22) (2022-08-22)

## [22.0.22](https://github.com/compare/@sage/show-case@22.0.21...@sage/show-case@22.0.22) (2022-08-21)

# [22.0.21](https://github.com/compare/@sage/show-case@22.0.20...@sage/show-case@22.0.21) (2022-08-21)

## [22.0.21](https://github.com/compare/@sage/show-case@22.0.20...@sage/show-case@22.0.21) (2022-08-19)

# [22.0.20](https://github.com/compare/@sage/show-case@22.0.19...@sage/show-case@22.0.20) (2022-08-19)

## [22.0.20](https://github.com/compare/@sage/show-case@22.0.19...@sage/show-case@22.0.20) (2022-08-18)

# [22.0.19](https://github.com/compare/@sage/show-case@22.0.18...@sage/show-case@22.0.19) (2022-08-18)

## [22.0.19](https://github.com/compare/@sage/show-case@22.0.18...@sage/show-case@22.0.19) (2022-08-17)

# [22.0.18](https://github.com/compare/@sage/show-case@22.0.17...@sage/show-case@22.0.18) (2022-08-17)

## [22.0.18](https://github.com/compare/@sage/show-case@22.0.17...@sage/show-case@22.0.18) (2022-08-16)

# [22.0.17](https://github.com/compare/@sage/show-case@22.0.16...@sage/show-case@22.0.17) (2022-08-16)

## [22.0.17](https://github.com/compare/@sage/show-case@22.0.16...@sage/show-case@22.0.17) (2022-08-15)

# [22.0.16](https://github.com/compare/@sage/show-case@22.0.14...@sage/show-case@22.0.16) (2022-08-15)

## [22.0.16](https://github.com/compare/@sage/show-case@22.0.14...@sage/show-case@22.0.16) (2022-08-14)

# [22.0.15](https://github.com/compare/@sage/show-case@22.0.14...@sage/show-case@22.0.15) (2022-08-14)

# [22.0.14](https://github.com/compare/@sage/show-case@22.0.13...@sage/show-case@22.0.14) (2022-08-13)

## [22.0.14](https://github.com/compare/@sage/show-case@22.0.13...@sage/show-case@22.0.14) (2022-08-11)

# [22.0.13](https://github.com/compare/@sage/show-case@22.0.12...@sage/show-case@22.0.13) (2022-08-11)

## [22.0.13](https://github.com/compare/@sage/show-case@22.0.12...@sage/show-case@22.0.13) (2022-08-11)

# [22.0.12](https://github.com/compare/@sage/show-case@22.0.11...@sage/show-case@22.0.12) (2022-08-11)

## [22.0.12](https://github.com/compare/@sage/show-case@22.0.11...@sage/show-case@22.0.12) (2022-08-09)

# [22.0.11](https://github.com/compare/@sage/show-case@22.0.10...@sage/show-case@22.0.11) (2022-08-09)

## [22.0.11](https://github.com/compare/@sage/show-case@22.0.10...@sage/show-case@22.0.11) (2022-08-08)

# [22.0.10](https://github.com/compare/@sage/show-case@22.0.9...@sage/show-case@22.0.10) (2022-08-08)

## [22.0.10](https://github.com/compare/@sage/show-case@22.0.9...@sage/show-case@22.0.10) (2022-08-07)

# [22.0.9](https://github.com/compare/@sage/show-case@22.0.8...@sage/show-case@22.0.9) (2022-08-07)

## [22.0.9](https://github.com/compare/@sage/show-case@22.0.8...@sage/show-case@22.0.9) (2022-08-06)

# [22.0.8](https://github.com/compare/@sage/show-case@22.0.7...@sage/show-case@22.0.8) (2022-08-06)

## [22.0.8](https://github.com/compare/@sage/show-case@22.0.7...@sage/show-case@22.0.8) (2022-08-05)

# [22.0.7](https://github.com/compare/@sage/show-case@22.0.6...@sage/show-case@22.0.7) (2022-08-05)

## [22.0.7](https://github.com/compare/@sage/show-case@22.0.6...@sage/show-case@22.0.7) (2022-08-04)

# [22.0.6](https://github.com/compare/@sage/show-case@22.0.5...@sage/show-case@22.0.6) (2022-08-04)

## [22.0.6](https://github.com/compare/@sage/show-case@22.0.5...@sage/show-case@22.0.6) (2022-08-01)

### Bug Fixes


# [22.0.5](https://github.com/compare/@sage/show-case@22.0.4...@sage/show-case@22.0.5) (2022-08-01)

### Bug Fixes

* XT-30471 fix layers in glossary and show case layers on image pipeline smoke tests ([#8216](https://github.com/issues/8216))  ([3bb8396](https://github.com/commit/3bb8396b0d5e9407afb8fd5cc946894c8b3db2ac))

## [22.0.5](https://github.com/compare/@sage/show-case@22.0.4...@sage/show-case@22.0.5) (2022-07-31)

# [22.0.4](https://github.com/compare/@sage/show-case@22.0.3...@sage/show-case@22.0.4) (2022-07-31)

## [22.0.4](https://github.com/compare/@sage/show-case@22.0.3...@sage/show-case@22.0.4) (2022-07-30)

# [22.0.3](https://github.com/compare/@sage/show-case@22.0.2...@sage/show-case@22.0.3) (2022-07-30)

## [22.0.3](https://github.com/compare/@sage/show-case@22.0.2...@sage/show-case@22.0.3) (2022-07-29)

# [22.0.2](https://github.com/compare/@sage/show-case@22.0.1...@sage/show-case@22.0.2) (2022-07-29)

## [22.0.2](https://github.com/compare/@sage/show-case@22.0.1...@sage/show-case@22.0.2) (2022-07-28)

# [22.0.1](https://github.com/compare/@sage/show-case@22.0.0...@sage/show-case@22.0.1) (2022-07-28)

## [22.0.1](https://github.com/compare/@sage/show-case@22.0.0...@sage/show-case@22.0.1) (2022-07-28)

# [22.0.0](https://github.com/compare/@sage/show-case@21.0.36...@sage/show-case@22.0.0) (2022-07-28)

## [22.0.0](https://github.com/compare/@sage/show-case@21.0.36...@sage/show-case@22.0.0) (2022-07-28)

# [21.0.36](https://github.com/compare/@sage/show-case@21.0.35...@sage/show-case@21.0.36) (2022-07-28)

## [21.0.36](https://github.com/compare/@sage/show-case@21.0.35...@sage/show-case@21.0.36) (2022-07-27)

# [21.0.35](https://github.com/compare/@sage/show-case@21.0.34...@sage/show-case@21.0.35) (2022-07-27)

## [21.0.35](https://github.com/compare/@sage/show-case@21.0.34...@sage/show-case@21.0.35) (2022-07-26)

# [21.0.34](https://github.com/compare/@sage/show-case@21.0.33...@sage/show-case@21.0.34) (2022-07-26)

## [21.0.34](https://github.com/compare/@sage/show-case@21.0.33...@sage/show-case@21.0.34) (2022-07-25)

### Features


# [21.0.33](https://github.com/compare/@sage/show-case@21.0.32...@sage/show-case@21.0.33) (2022-07-25)

### Features

* aurora v2 - PostgreSQL 13.6 (XT-28146) ([#8094](https://github.com/issues/8094))  ([fdb9c00](https://github.com/commit/fdb9c00c493f1ab9b399436e877a9704ac36e0b5))

## [21.0.33](https://github.com/compare/@sage/show-case@21.0.32...@sage/show-case@21.0.33) (2022-07-24)

# [21.0.32](https://github.com/compare/@sage/show-case@21.0.31...@sage/show-case@21.0.32) (2022-07-24)

## [21.0.32](https://github.com/compare/@sage/show-case@21.0.31...@sage/show-case@21.0.32) (2022-07-23)

# [21.0.31](https://github.com/compare/@sage/show-case@21.0.30...@sage/show-case@21.0.31) (2022-07-23)

## [21.0.31](https://github.com/compare/@sage/show-case@21.0.30...@sage/show-case@21.0.31) (2022-07-22)

# [21.0.30](https://github.com/compare/@sage/show-case@21.0.29...@sage/show-case@21.0.30) (2022-07-22)

## [21.0.30](https://github.com/compare/@sage/show-case@21.0.29...@sage/show-case@21.0.30) (2022-07-21)

# [21.0.29](https://github.com/compare/@sage/show-case@21.0.28...@sage/show-case@21.0.29) (2022-07-21)

## [21.0.29](https://github.com/compare/@sage/show-case@21.0.28...@sage/show-case@21.0.29) (2022-07-20)

# [21.0.28](https://github.com/compare/@sage/show-case@21.0.27...@sage/show-case@21.0.28) (2022-07-20)

## [21.0.28](https://github.com/compare/@sage/show-case@21.0.27...@sage/show-case@21.0.28) (2022-07-19)

# [21.0.27](https://github.com/compare/@sage/show-case@21.0.26...@sage/show-case@21.0.27) (2022-07-19)

## [21.0.27](https://github.com/compare/@sage/show-case@21.0.26...@sage/show-case@21.0.27) (2022-07-19)

# [21.0.26](https://github.com/compare/@sage/show-case@21.0.25...@sage/show-case@21.0.26) (2022-07-19)

## [21.0.26](https://github.com/compare/@sage/show-case@21.0.25...@sage/show-case@21.0.26) (2022-07-18)

# [21.0.25](https://github.com/compare/@sage/show-case@21.0.24...@sage/show-case@21.0.25) (2022-07-18)

## [21.0.25](https://github.com/compare/@sage/show-case@21.0.24...@sage/show-case@21.0.25) (2022-07-17)

# [21.0.24](https://github.com/compare/@sage/show-case@21.0.23...@sage/show-case@21.0.24) (2022-07-17)

## [21.0.24](https://github.com/compare/@sage/show-case@21.0.23...@sage/show-case@21.0.24) (2022-07-16)

# [21.0.23](https://github.com/compare/@sage/show-case@21.0.22...@sage/show-case@21.0.23) (2022-07-16)

## [21.0.23](https://github.com/compare/@sage/show-case@21.0.22...@sage/show-case@21.0.23) (2022-07-15)

# [21.0.22](https://github.com/compare/@sage/show-case@21.0.21...@sage/show-case@21.0.22) (2022-07-15)

## [21.0.22](https://github.com/compare/@sage/show-case@21.0.21...@sage/show-case@21.0.22) (2022-07-14)

# [21.0.21](https://github.com/compare/@sage/show-case@21.0.20...@sage/show-case@21.0.21) (2022-07-14)

## [21.0.21](https://github.com/compare/@sage/show-case@21.0.20...@sage/show-case@21.0.21) (2022-07-14)

# [21.0.20](https://github.com/compare/@sage/show-case@21.0.19...@sage/show-case@21.0.20) (2022-07-14)

## [21.0.20](https://github.com/compare/@sage/show-case@21.0.19...@sage/show-case@21.0.20) (2022-07-13)

# [21.0.19](https://github.com/compare/@sage/show-case@21.0.18...@sage/show-case@21.0.19) (2022-07-13)

## [21.0.19](https://github.com/compare/@sage/show-case@21.0.18...@sage/show-case@21.0.19) (2022-07-13)

# [21.0.18](https://github.com/compare/@sage/show-case@21.0.17...@sage/show-case@21.0.18) (2022-07-13)

## [21.0.18](https://github.com/compare/@sage/show-case@21.0.17...@sage/show-case@21.0.18) (2022-07-10)

# [21.0.17](https://github.com/compare/@sage/show-case@21.0.16...@sage/show-case@21.0.17) (2022-07-10)

## [21.0.17](https://github.com/compare/@sage/show-case@21.0.16...@sage/show-case@21.0.17) (2022-07-09)

# [21.0.16](https://github.com/compare/@sage/show-case@21.0.15...@sage/show-case@21.0.16) (2022-07-09)

## [21.0.16](https://github.com/compare/@sage/show-case@21.0.15...@sage/show-case@21.0.16) (2022-07-08)

# [21.0.15](https://github.com/compare/@sage/show-case@21.0.14...@sage/show-case@21.0.15) (2022-07-08)

## [21.0.15](https://github.com/compare/@sage/show-case@21.0.14...@sage/show-case@21.0.15) (2022-07-07)

# [21.0.14](https://github.com/compare/@sage/show-case@21.0.13...@sage/show-case@21.0.14) (2022-07-07)

## [21.0.14](https://github.com/compare/@sage/show-case@21.0.13...@sage/show-case@21.0.14) (2022-07-06)

# [21.0.13](https://github.com/compare/@sage/show-case@21.0.12...@sage/show-case@21.0.13) (2022-07-06)

## [21.0.13](https://github.com/compare/@sage/show-case@21.0.12...@sage/show-case@21.0.13) (2022-07-05)

# [21.0.12](https://github.com/compare/@sage/show-case@21.0.11...@sage/show-case@21.0.12) (2022-07-05)

## [21.0.12](https://github.com/compare/@sage/show-case@21.0.11...@sage/show-case@21.0.12) (2022-07-04)

# [21.0.11](https://github.com/compare/@sage/show-case@21.0.10...@sage/show-case@21.0.11) (2022-07-04)

## [21.0.11](https://github.com/compare/@sage/show-case@21.0.10...@sage/show-case@21.0.11) (2022-07-04)

# [21.0.10](https://github.com/compare/@sage/show-case@21.0.9...@sage/show-case@21.0.10) (2022-07-04)

## [21.0.10](https://github.com/compare/@sage/show-case@21.0.9...@sage/show-case@21.0.10) (2022-07-02)

# [21.0.9](https://github.com/compare/@sage/show-case@21.0.8...@sage/show-case@21.0.9) (2022-07-02)

## [21.0.9](https://github.com/compare/@sage/show-case@21.0.8...@sage/show-case@21.0.9) (2022-07-01)

# [21.0.8](https://github.com/compare/@sage/show-case@21.0.7...@sage/show-case@21.0.8) (2022-07-01)

## [21.0.8](https://github.com/compare/@sage/show-case@21.0.7...@sage/show-case@21.0.8) (2022-06-30)

# [21.0.7](https://github.com/compare/@sage/show-case@21.0.6...@sage/show-case@21.0.7) (2022-06-30)

## [21.0.7](https://github.com/compare/@sage/show-case@21.0.6...@sage/show-case@21.0.7) (2022-06-29)

# [21.0.6](https://github.com/compare/@sage/show-case@21.0.5...@sage/show-case@21.0.6) (2022-06-29)

## [21.0.6](https://github.com/compare/@sage/show-case@21.0.5...@sage/show-case@21.0.6) (2022-06-28)

### Bug Fixes


# [21.0.5](https://github.com/compare/@sage/show-case@21.0.4...@sage/show-case@21.0.5) (2022-06-28)

### Bug Fixes

* ansi-regex and ssri CVE (XT-28081) ([#7626](https://github.com/issues/7626))  ([208775c](https://github.com/commit/208775c1bb3bb5811afbeae0cd143189af4fe633))

## [21.0.5](https://github.com/compare/@sage/show-case@21.0.4...@sage/show-case@21.0.5) (2022-06-27)

# [21.0.4](https://github.com/compare/@sage/show-case@21.0.3...@sage/show-case@21.0.4) (2022-06-27)

## [21.0.4](https://github.com/compare/@sage/show-case@21.0.3...@sage/show-case@21.0.4) (2022-06-26)

# [21.0.3](https://github.com/compare/@sage/show-case@21.0.2...@sage/show-case@21.0.3) (2022-06-26)

## [21.0.3](https://github.com/compare/@sage/show-case@21.0.2...@sage/show-case@21.0.3) (2022-06-25)

# [21.0.2](https://github.com/compare/@sage/show-case@21.0.1...@sage/show-case@21.0.2) (2022-06-25)

## [21.0.2](https://github.com/compare/@sage/show-case@21.0.1...@sage/show-case@21.0.2) (2022-06-24)

# [21.0.1](https://github.com/compare/@sage/show-case@21.0.0...@sage/show-case@21.0.1) (2022-06-24)

## [21.0.1](https://github.com/compare/@sage/show-case@21.0.0...@sage/show-case@21.0.1) (2022-06-23)

# [21.0.0](https://github.com/compare/@sage/show-case@20.0.32...@sage/show-case@21.0.0) (2022-06-23)

## [21.0.0](https://github.com/compare/@sage/show-case@20.0.32...@sage/show-case@21.0.0) (2022-06-23)

# [20.0.32](https://github.com/compare/@sage/show-case@20.0.31...@sage/show-case@20.0.32) (2022-06-23)

## [20.0.32](https://github.com/compare/@sage/show-case@20.0.31...@sage/show-case@20.0.32) (2022-06-22)

# [20.0.31](https://github.com/compare/@sage/show-case@20.0.30...@sage/show-case@20.0.31) (2022-06-22)

## [20.0.31](https://github.com/compare/@sage/show-case@20.0.30...@sage/show-case@20.0.31) (2022-06-21)

# [20.0.30](https://github.com/compare/@sage/show-case@20.0.29...@sage/show-case@20.0.30) (2022-06-21)

## [20.0.30](https://github.com/compare/@sage/show-case@20.0.29...@sage/show-case@20.0.30) (2022-06-21)

# [20.0.29](https://github.com/compare/@sage/show-case@20.0.28...@sage/show-case@20.0.29) (2022-06-21)

## [20.0.29](https://github.com/compare/@sage/show-case@20.0.28...@sage/show-case@20.0.29) (2022-06-20)

# [20.0.28](https://github.com/compare/@sage/show-case@20.0.27...@sage/show-case@20.0.28) (2022-06-20)

## [20.0.28](https://github.com/compare/@sage/show-case@20.0.27...@sage/show-case@20.0.28) (2022-06-19)

# [20.0.27](https://github.com/compare/@sage/show-case@20.0.26...@sage/show-case@20.0.27) (2022-06-19)

## [20.0.27](https://github.com/compare/@sage/show-case@20.0.26...@sage/show-case@20.0.27) (2022-06-18)

# [20.0.26](https://github.com/compare/@sage/show-case@20.0.25...@sage/show-case@20.0.26) (2022-06-18)

## [20.0.26](https://github.com/compare/@sage/show-case@20.0.25...@sage/show-case@20.0.26) (2022-06-17)

# [20.0.25](https://github.com/compare/@sage/show-case@20.0.24...@sage/show-case@20.0.25) (2022-06-17)

## [20.0.25](https://github.com/compare/@sage/show-case@20.0.24...@sage/show-case@20.0.25) (2022-06-16)

# [20.0.24](https://github.com/compare/@sage/show-case@20.0.23...@sage/show-case@20.0.24) (2022-06-16)

## [20.0.24](https://github.com/compare/@sage/show-case@20.0.23...@sage/show-case@20.0.24) (2022-06-16)

# [20.0.23](https://github.com/compare/@sage/show-case@20.0.22...@sage/show-case@20.0.23) (2022-06-16)

## [20.0.23](https://github.com/compare/@sage/show-case@20.0.22...@sage/show-case@20.0.23) (2022-06-14)

# [20.0.22](https://github.com/compare/@sage/show-case@20.0.21...@sage/show-case@20.0.22) (2022-06-14)

## [20.0.22](https://github.com/compare/@sage/show-case@20.0.21...@sage/show-case@20.0.22) (2022-06-14)

# [20.0.21](https://github.com/compare/@sage/show-case@20.0.20...@sage/show-case@20.0.21) (2022-06-14)

## [20.0.21](https://github.com/compare/@sage/show-case@20.0.20...@sage/show-case@20.0.21) (2022-06-13)

# [20.0.20](https://github.com/compare/@sage/show-case@20.0.19...@sage/show-case@20.0.20) (2022-06-13)

## [20.0.20](https://github.com/compare/@sage/show-case@20.0.19...@sage/show-case@20.0.20) (2022-06-12)

# [20.0.19](https://github.com/compare/@sage/show-case@20.0.18...@sage/show-case@20.0.19) (2022-06-12)

## [20.0.19](https://github.com/compare/@sage/show-case@20.0.18...@sage/show-case@20.0.19) (2022-06-11)

# [20.0.18](https://github.com/compare/@sage/show-case@20.0.17...@sage/show-case@20.0.18) (2022-06-11)

## [20.0.18](https://github.com/compare/@sage/show-case@20.0.17...@sage/show-case@20.0.18) (2022-06-10)

# [20.0.17](https://github.com/compare/@sage/show-case@20.0.16...@sage/show-case@20.0.17) (2022-06-10)

## [20.0.17](https://github.com/compare/@sage/show-case@20.0.16...@sage/show-case@20.0.17) (2022-06-10)

# [20.0.16](https://github.com/compare/@sage/show-case@20.0.15...@sage/show-case@20.0.16) (2022-06-10)

## [20.0.16](https://github.com/compare/@sage/show-case@20.0.15...@sage/show-case@20.0.16) (2022-06-08)

# [20.0.15](https://github.com/compare/@sage/show-case@20.0.14...@sage/show-case@20.0.15) (2022-06-08)

## [20.0.15](https://github.com/compare/@sage/show-case@20.0.14...@sage/show-case@20.0.15) (2022-06-07)

# [20.0.14](https://github.com/compare/@sage/show-case@20.0.13...@sage/show-case@20.0.14) (2022-06-07)

## [20.0.14](https://github.com/compare/@sage/show-case@20.0.13...@sage/show-case@20.0.14) (2022-06-06)

# [20.0.13](https://github.com/compare/@sage/show-case@20.0.12...@sage/show-case@20.0.13) (2022-06-06)

## [20.0.13](https://github.com/compare/@sage/show-case@20.0.12...@sage/show-case@20.0.13) (2022-06-05)

# [20.0.12](https://github.com/compare/@sage/show-case@20.0.11...@sage/show-case@20.0.12) (2022-06-05)

## [20.0.12](https://github.com/compare/@sage/show-case@20.0.11...@sage/show-case@20.0.12) (2022-06-04)

# [20.0.11](https://github.com/compare/@sage/show-case@20.0.10...@sage/show-case@20.0.11) (2022-06-04)

## [20.0.11](https://github.com/compare/@sage/show-case@20.0.10...@sage/show-case@20.0.11) (2022-06-03)

# [20.0.10](https://github.com/compare/@sage/show-case@20.0.9...@sage/show-case@20.0.10) (2022-06-03)

## [20.0.10](https://github.com/compare/@sage/show-case@20.0.9...@sage/show-case@20.0.10) (2022-06-03)

# [20.0.9](https://github.com/compare/@sage/show-case@20.0.6...@sage/show-case@20.0.9) (2022-06-03)

## [20.0.9](https://github.com/compare/@sage/show-case@20.0.6...@sage/show-case@20.0.9) (2022-06-02)

### Bug Fixes


## [20.0.6](https://github.com/compare/@sage/show-case@20.0.5...@sage/show-case@20.0.6) (2022-06-02)

## [20.0.5](https://github.com/compare/@sage/show-case@20.0.4...@sage/show-case@20.0.5) (2022-06-01)

## [20.0.4](https://github.com/compare/@sage/show-case@20.0.3...@sage/show-case@20.0.4) (2022-05-29)

## [20.0.3](https://github.com/compare/@sage/show-case@20.0.2...@sage/show-case@20.0.3) (2022-05-28)

## [20.0.2](https://github.com/compare/@sage/show-case@20.0.1...@sage/show-case@20.0.2) (2022-05-27)

## [20.0.1](https://github.com/compare/@sage/show-case@20.0.0...@sage/show-case@20.0.1) (2022-05-26)

## [20.0.0](https://github.com/compare/@sage/show-case@19.0.33...@sage/show-case@20.0.0) (2022-05-26)

## [19.0.33](https://github.com/compare/@sage/show-case@19.0.32...@sage/show-case@19.0.33) (2022-05-26)

## [19.0.32](https://github.com/compare/@sage/show-case@19.0.31...@sage/show-case@19.0.32) (2022-05-24)

## [19.0.31](https://github.com/compare/@sage/show-case@19.0.30...@sage/show-case@19.0.31) (2022-05-24)

## [19.0.30](https://github.com/compare/@sage/show-case@19.0.29...@sage/show-case@19.0.30) (2022-05-23)

## [19.0.29](https://github.com/compare/@sage/show-case@19.0.28...@sage/show-case@19.0.29) (2022-05-23)

## [19.0.28](https://github.com/compare/@sage/show-case@19.0.27...@sage/show-case@19.0.28) (2022-05-22)

## [19.0.27](https://github.com/compare/@sage/show-case@19.0.26...@sage/show-case@19.0.27) (2022-05-21)

## [19.0.26](https://github.com/compare/@sage/show-case@19.0.25...@sage/show-case@19.0.26) (2022-05-20)

## [19.0.25](https://github.com/compare/@sage/show-case@19.0.24...@sage/show-case@19.0.25) (2022-05-20)

### Bug Fixes


## [19.0.24](https://github.com/compare/@sage/show-case@19.0.23...@sage/show-case@19.0.24) (2022-05-18)

## [19.0.23](https://github.com/compare/@sage/show-case@19.0.22...@sage/show-case@19.0.23) (2022-05-17)

## [19.0.22](https://github.com/compare/@sage/show-case@19.0.21...@sage/show-case@19.0.22) (2022-05-16)

### Bug Fixes


## [19.0.21](https://github.com/compare/@sage/show-case@19.0.20...@sage/show-case@19.0.21) (2022-05-15)

## [19.0.20](https://github.com/compare/@sage/show-case@19.0.19...@sage/show-case@19.0.20) (2022-05-14)

## [19.0.19](https://github.com/compare/@sage/show-case@19.0.18...@sage/show-case@19.0.19) (2022-05-13)

## [19.0.18](https://github.com/compare/@sage/show-case@19.0.17...@sage/show-case@19.0.18) (2022-05-13)

## [19.0.17](https://github.com/compare/@sage/show-case@19.0.16...@sage/show-case@19.0.17) (2022-05-12)

## [19.0.16](https://github.com/compare/@sage/show-case@19.0.15...@sage/show-case@19.0.16) (2022-05-11)

## [19.0.15](https://github.com/compare/@sage/show-case@19.0.14...@sage/show-case@19.0.15) (2022-05-10)

## [19.0.14](https://github.com/compare/@sage/show-case@19.0.13...@sage/show-case@19.0.14) (2022-05-10)

## [19.0.13](https://github.com/compare/@sage/show-case@19.0.12...@sage/show-case@19.0.13) (2022-05-09)

## [19.0.12](https://github.com/compare/@sage/show-case@19.0.11...@sage/show-case@19.0.12) (2022-05-08)

## [19.0.11](https://github.com/compare/@sage/show-case@19.0.10...@sage/show-case@19.0.11) (2022-05-07)

## [19.0.10](https://github.com/compare/@sage/show-case@19.0.9...@sage/show-case@19.0.10) (2022-05-06)

## [19.0.9](https://github.com/compare/@sage/show-case@19.0.8...@sage/show-case@19.0.9) (2022-05-06)

## [19.0.8](https://github.com/compare/@sage/show-case@19.0.7...@sage/show-case@19.0.8) (2022-05-04)

## [19.0.7](https://github.com/compare/@sage/show-case@19.0.6...@sage/show-case@19.0.7) (2022-05-03)

## [19.0.6](https://github.com/compare/@sage/show-case@19.0.5...@sage/show-case@19.0.6) (2022-05-02)

## [19.0.5](https://github.com/compare/@sage/show-case@19.0.4...@sage/show-case@19.0.5) (2022-05-01)

## [19.0.4](https://github.com/compare/@sage/show-case@19.0.3...@sage/show-case@19.0.4) (2022-04-30)

## [19.0.3](https://github.com/compare/@sage/show-case@19.0.2...@sage/show-case@19.0.3) (2022-04-29)

## [19.0.2](https://github.com/compare/@sage/show-case@19.0.1...@sage/show-case@19.0.2) (2022-04-28)

## [19.0.1](https://github.com/compare/@sage/show-case@19.0.0...@sage/show-case@19.0.1) (2022-04-28)

## [19.0.0](https://github.com/compare/@sage/show-case@18.0.37...@sage/show-case@19.0.0) (2022-04-28)

## [18.0.37](https://github.com/compare/@sage/show-case@18.0.36...@sage/show-case@18.0.37) (2022-04-27)

## [18.0.36](https://github.com/compare/@sage/show-case@18.0.35...@sage/show-case@18.0.36) (2022-04-27)

## [18.0.35](https://github.com/compare/@sage/show-case@18.0.34...@sage/show-case@18.0.35) (2022-04-26)

## [18.0.34](https://github.com/compare/@sage/show-case@18.0.33...@sage/show-case@18.0.34) (2022-04-26)

## [18.0.33](https://github.com/compare/@sage/show-case@18.0.32...@sage/show-case@18.0.33) (2022-04-25)

## [18.0.32](https://github.com/compare/@sage/show-case@18.0.31...@sage/show-case@18.0.32) (2022-04-25)

## [18.0.31](https://github.com/compare/@sage/show-case@18.0.30...@sage/show-case@18.0.31) (2022-04-21)

## [18.0.30](https://github.com/compare/@sage/show-case@18.0.29...@sage/show-case@18.0.30) (2022-04-21)

## [18.0.29](https://github.com/compare/@sage/show-case@18.0.28...@sage/show-case@18.0.29) (2022-04-20)

## [18.0.28](https://github.com/compare/@sage/show-case@18.0.27...@sage/show-case@18.0.28) (2022-04-20)

## [18.0.27](https://github.com/compare/@sage/show-case@18.0.26...@sage/show-case@18.0.27) (2022-04-18)

## [18.0.26](https://github.com/compare/@sage/show-case@18.0.25...@sage/show-case@18.0.26) (2022-04-18)

## [18.0.25](https://github.com/compare/@sage/show-case@18.0.24...@sage/show-case@18.0.25) (2022-04-16)

## [18.0.24](https://github.com/compare/@sage/show-case@18.0.23...@sage/show-case@18.0.24) (2022-04-15)

## [18.0.23](https://github.com/compare/@sage/show-case@18.0.22...@sage/show-case@18.0.23) (2022-04-14)

## [18.0.22](https://github.com/compare/@sage/show-case@18.0.21...@sage/show-case@18.0.22) (2022-04-13)

## [18.0.21](https://github.com/compare/@sage/show-case@18.0.20...@sage/show-case@18.0.21) (2022-04-12)

## [18.0.20](https://github.com/compare/@sage/show-case@18.0.19...@sage/show-case@18.0.20) (2022-04-11)

## [18.0.19](https://github.com/compare/@sage/show-case@18.0.18...@sage/show-case@18.0.19) (2022-04-10)

## [18.0.18](https://github.com/compare/@sage/show-case@18.0.17...@sage/show-case@18.0.18) (2022-04-09)

## [18.0.17](https://github.com/compare/@sage/show-case@18.0.16...@sage/show-case@18.0.17) (2022-04-08)

## [18.0.16](https://github.com/compare/@sage/show-case@18.0.15...@sage/show-case@18.0.16) (2022-04-07)

## [18.0.15](https://github.com/compare/@sage/show-case@18.0.14...@sage/show-case@18.0.15) (2022-04-06)

## [18.0.14](https://github.com/compare/@sage/show-case@18.0.13...@sage/show-case@18.0.14) (2022-04-05)

## [18.0.13](https://github.com/compare/@sage/show-case@18.0.12...@sage/show-case@18.0.13) (2022-04-04)

## [18.0.12](https://github.com/compare/@sage/show-case@18.0.11...@sage/show-case@18.0.12) (2022-04-03)

## [18.0.11](https://github.com/compare/@sage/show-case@18.0.10...@sage/show-case@18.0.11) (2022-04-02)

## [18.0.10](https://github.com/compare/@sage/show-case@18.0.9...@sage/show-case@18.0.10) (2022-04-01)

## [18.0.9](https://github.com/compare/@sage/show-case@18.0.8...@sage/show-case@18.0.9) (2022-03-31)

## [18.0.8](https://github.com/compare/@sage/show-case@18.0.7...@sage/show-case@18.0.8) (2022-03-31)

## [18.0.7](https://github.com/compare/@sage/show-case@18.0.6...@sage/show-case@18.0.7) (2022-03-30)

## [18.0.6](https://github.com/compare/@sage/show-case@18.0.5...@sage/show-case@18.0.6) (2022-03-29)

## [18.0.5](https://github.com/compare/@sage/show-case@18.0.4...@sage/show-case@18.0.5) (2022-03-28)

## [18.0.4](https://github.com/compare/@sage/show-case@18.0.3...@sage/show-case@18.0.4) (2022-03-27)

## [18.0.3](https://github.com/compare/@sage/show-case@18.0.2...@sage/show-case@18.0.3) (2022-03-26)

## [18.0.2](https://github.com/compare/@sage/show-case@18.0.1...@sage/show-case@18.0.2) (2022-03-25)

## [18.0.1](https://github.com/compare/@sage/show-case@18.0.0...@sage/show-case@18.0.1) (2022-03-24)

## [18.0.0](https://github.com/compare/@sage/show-case@17.0.29...@sage/show-case@18.0.0) (2022-03-24)

## [17.0.29](https://github.com/compare/@sage/show-case@17.0.28...@sage/show-case@17.0.29) (2022-03-24)

## [17.0.28](https://github.com/compare/@sage/show-case@17.0.27...@sage/show-case@17.0.28) (2022-03-23)

## [17.0.27](https://github.com/compare/@sage/show-case@17.0.26...@sage/show-case@17.0.27) (2022-03-22)

### Bug Fixes


## [17.0.26](https://github.com/compare/@sage/show-case@17.0.25...@sage/show-case@17.0.26) (2022-03-21)

## [17.0.25](https://github.com/compare/@sage/show-case@17.0.24...@sage/show-case@17.0.25) (2022-03-20)

## [17.0.24](https://github.com/compare/@sage/show-case@17.0.23...@sage/show-case@17.0.24) (2022-03-20)

## [17.0.23](https://github.com/compare/@sage/show-case@17.0.22...@sage/show-case@17.0.23) (2022-03-19)

## [17.0.22](https://github.com/compare/@sage/show-case@17.0.21...@sage/show-case@17.0.22) (2022-03-19)

## [17.0.21](https://github.com/compare/@sage/show-case@17.0.20...@sage/show-case@17.0.21) (2022-03-18)

## [17.0.20](https://github.com/compare/@sage/show-case@17.0.19...@sage/show-case@17.0.20) (2022-03-18)

## [17.0.19](https://github.com/compare/@sage/show-case@17.0.18...@sage/show-case@17.0.19) (2022-03-18)

## [17.0.18](https://github.com/compare/@sage/show-case@17.0.17...@sage/show-case@17.0.18) (2022-03-17)

## [17.0.17](https://github.com/compare/@sage/show-case@17.0.16...@sage/show-case@17.0.17) (2022-03-17)

## [17.0.16](https://github.com/compare/@sage/show-case@17.0.15...@sage/show-case@17.0.16) (2022-03-13)

## [17.0.15](https://github.com/compare/@sage/show-case@17.0.14...@sage/show-case@17.0.15) (2022-03-10)

## [17.0.14](https://github.com/compare/@sage/show-case@17.0.13...@sage/show-case@17.0.14) (2022-03-09)

## [17.0.13](https://github.com/compare/@sage/show-case@17.0.12...@sage/show-case@17.0.13) (2022-03-09)

## [17.0.12](https://github.com/compare/@sage/show-case@17.0.11...@sage/show-case@17.0.12) (2022-03-08)

## [17.0.11](https://github.com/compare/@sage/show-case@17.0.10...@sage/show-case@17.0.11) (2022-03-07)

## [17.0.10](https://github.com/compare/@sage/show-case@17.0.9...@sage/show-case@17.0.10) (2022-03-06)

## [17.0.9](https://github.com/compare/@sage/show-case@17.0.8...@sage/show-case@17.0.9) (2022-03-05)

## [17.0.8](https://github.com/compare/@sage/show-case@17.0.7...@sage/show-case@17.0.8) (2022-03-04)

## [17.0.7](https://github.com/compare/@sage/show-case@17.0.6...@sage/show-case@17.0.7) (2022-03-03)

## [17.0.6](https://github.com/compare/@sage/show-case@17.0.5...@sage/show-case@17.0.6) (2022-03-03)

## [17.0.5](https://github.com/compare/@sage/show-case@17.0.4...@sage/show-case@17.0.5) (2022-03-01)

## [17.0.4](https://github.com/compare/@sage/show-case@17.0.3...@sage/show-case@17.0.4) (2022-02-28)

## [17.0.3](https://github.com/compare/@sage/show-case@17.0.2...@sage/show-case@17.0.3) (2022-02-27)

## [17.0.2](https://github.com/compare/@sage/show-case@17.0.1...@sage/show-case@17.0.2) (2022-02-26)

## [17.0.1](https://github.com/compare/@sage/show-case@17.0.0...@sage/show-case@17.0.1) (2022-02-25)

## [17.0.0](https://github.com/compare/@sage/show-case@16.0.29...@sage/show-case@17.0.0) (2022-02-24)

## [16.0.29](https://github.com/compare/@sage/show-case@16.0.28...@sage/show-case@16.0.29) (2022-02-24)

## [16.0.28](https://github.com/compare/@sage/show-case@16.0.27...@sage/show-case@16.0.28) (2022-02-24)

### Bug Fixes


## [16.0.27](https://github.com/compare/@sage/show-case@16.0.26...@sage/show-case@16.0.27) (2022-02-23)

## [16.0.26](https://github.com/compare/@sage/show-case@16.0.25...@sage/show-case@16.0.26) (2022-02-22)

### Bug Fixes


## [16.0.25](https://github.com/compare/@sage/show-case@16.0.24...@sage/show-case@16.0.25) (2022-02-21)

## [16.0.24](https://github.com/compare/@sage/show-case@16.0.23...@sage/show-case@16.0.24) (2022-02-20)

## [16.0.23](https://github.com/compare/@sage/show-case@16.0.22...@sage/show-case@16.0.23) (2022-02-19)

## [16.0.22](https://github.com/compare/@sage/show-case@16.0.21...@sage/show-case@16.0.22) (2022-02-18)

## [16.0.21](https://github.com/compare/@sage/show-case@16.0.20...@sage/show-case@16.0.21) (2022-02-17)

## [16.0.20](https://github.com/compare/@sage/show-case@16.0.19...@sage/show-case@16.0.20) (2022-02-16)

## [16.0.19](https://github.com/compare/@sage/show-case@16.0.18...@sage/show-case@16.0.19) (2022-02-15)

## [16.0.18](https://github.com/compare/@sage/show-case@16.0.17...@sage/show-case@16.0.18) (2022-02-13)

## [16.0.17](https://github.com/compare/@sage/show-case@16.0.16...@sage/show-case@16.0.17) (2022-02-12)

## [16.0.16](https://github.com/compare/@sage/show-case@16.0.15...@sage/show-case@16.0.16) (2022-02-11)

## [16.0.15](https://github.com/compare/@sage/show-case@16.0.14...@sage/show-case@16.0.15) (2022-02-10)

## [16.0.14](https://github.com/compare/@sage/show-case@16.0.13...@sage/show-case@16.0.14) (2022-02-10)

## [16.0.13](https://github.com/compare/@sage/show-case@16.0.12...@sage/show-case@16.0.13) (2022-02-08)

## [16.0.12](https://github.com/compare/@sage/show-case@16.0.11...@sage/show-case@16.0.12) (2022-02-07)

## [16.0.11](https://github.com/compare/@sage/show-case@16.0.10...@sage/show-case@16.0.11) (2022-02-07)

## [16.0.10](https://github.com/compare/@sage/show-case@16.0.9...@sage/show-case@16.0.10) (2022-02-06)

## [16.0.9](https://github.com/compare/@sage/show-case@16.0.8...@sage/show-case@16.0.9) (2022-02-05)

## [16.0.8](https://github.com/compare/@sage/show-case@16.0.7...@sage/show-case@16.0.8) (2022-02-04)

## [16.0.7](https://github.com/compare/@sage/show-case@16.0.6...@sage/show-case@16.0.7) (2022-02-04)

## [16.0.6](https://github.com/compare/@sage/show-case@16.0.5...@sage/show-case@16.0.6) (2022-02-02)

## [16.0.5](https://github.com/compare/@sage/show-case@16.0.4...@sage/show-case@16.0.5) (2022-02-01)

## [16.0.4](https://github.com/compare/@sage/show-case@16.0.3...@sage/show-case@16.0.4) (2022-01-31)

## [16.0.3](https://github.com/compare/@sage/show-case@16.0.2...@sage/show-case@16.0.3) (2022-01-30)

## [16.0.2](https://github.com/compare/@sage/show-case@16.0.1...@sage/show-case@16.0.2) (2022-01-29)

## [16.0.1](https://github.com/compare/@sage/show-case@16.0.0...@sage/show-case@16.0.1) (2022-01-29)

## [16.0.0](https://github.com/compare/@sage/show-case@15.0.36...@sage/show-case@16.0.0) (2022-01-28)

## [15.0.36](https://github.com/compare/@sage/show-case@15.0.35...@sage/show-case@15.0.36) (2022-01-28)

## [15.0.35](https://github.com/compare/@sage/show-case@15.0.34...@sage/show-case@15.0.35) (2022-01-28)

## [15.0.34](https://github.com/compare/@sage/show-case@15.0.33...@sage/show-case@15.0.34) (2022-01-26)

## [15.0.33](https://github.com/compare/@sage/show-case@15.0.32...@sage/show-case@15.0.33) (2022-01-26)

## [15.0.32](https://github.com/compare/@sage/show-case@15.0.31...@sage/show-case@15.0.32) (2022-01-25)

## [15.0.31](https://github.com/compare/@sage/show-case@15.0.30...@sage/show-case@15.0.31) (2022-01-25)

## [15.0.30](https://github.com/compare/@sage/show-case@15.0.29...@sage/show-case@15.0.30) (2022-01-24)

## [15.0.29](https://github.com/compare/@sage/show-case@15.0.28...@sage/show-case@15.0.29) (2022-01-24)

## [15.0.28](https://github.com/compare/@sage/show-case@15.0.27...@sage/show-case@15.0.28) (2022-01-23)

## [15.0.27](https://github.com/compare/@sage/show-case@15.0.26...@sage/show-case@15.0.27) (2022-01-23)

### Bug Fixes


## [15.0.26](https://github.com/compare/@sage/show-case@15.0.25...@sage/show-case@15.0.26) (2022-01-21)

### Bug Fixes


## [15.0.25](https://github.com/compare/@sage/show-case@15.0.24...@sage/show-case@15.0.25) (2022-01-18)

## [15.0.24](https://github.com/compare/@sage/show-case@15.0.23...@sage/show-case@15.0.24) (2022-01-18)

## [15.0.23](https://github.com/compare/@sage/show-case@15.0.22...@sage/show-case@15.0.23) (2022-01-17)

## [15.0.22](https://github.com/compare/@sage/show-case@15.0.21...@sage/show-case@15.0.22) (2022-01-16)

## [15.0.21](https://github.com/compare/@sage/show-case@15.0.20...@sage/show-case@15.0.21) (2022-01-15)

## [15.0.20](https://github.com/compare/@sage/show-case@15.0.19...@sage/show-case@15.0.20) (2022-01-14)

## [15.0.19](https://github.com/compare/@sage/show-case@15.0.18...@sage/show-case@15.0.19) (2022-01-14)

## [15.0.18](https://github.com/compare/@sage/show-case@15.0.17...@sage/show-case@15.0.18) (2022-01-13)

## [15.0.17](https://github.com/compare/@sage/show-case@15.0.16...@sage/show-case@15.0.17) (2022-01-12)

## [15.0.16](https://github.com/compare/@sage/show-case@15.0.15...@sage/show-case@15.0.16) (2022-01-11)

## [15.0.15](https://github.com/compare/@sage/show-case@15.0.14...@sage/show-case@15.0.15) (2022-01-11)

## [15.0.14](https://github.com/compare/@sage/show-case@15.0.13...@sage/show-case@15.0.14) (2022-01-11)

## [15.0.13](https://github.com/compare/@sage/show-case@15.0.12...@sage/show-case@15.0.13) (2022-01-10)

## [15.0.12](https://github.com/compare/@sage/show-case@15.0.11...@sage/show-case@15.0.12) (2022-01-09)

## [15.0.11](https://github.com/compare/@sage/show-case@15.0.10...@sage/show-case@15.0.11) (2022-01-08)

## [15.0.10](https://github.com/compare/@sage/show-case@15.0.8...@sage/show-case@15.0.10) (2022-01-07)

## [15.0.8](https://github.com/compare/@sage/show-case@15.0.7...@sage/show-case@15.0.8) (2022-01-06)

## [15.0.7](https://github.com/compare/@sage/show-case@15.0.6...@sage/show-case@15.0.7) (2022-01-05)

## [15.0.6](https://github.com/compare/@sage/show-case@15.0.5...@sage/show-case@15.0.6) (2022-01-04)

## [15.0.5](https://github.com/compare/@sage/show-case@15.0.4...@sage/show-case@15.0.5) (2022-01-03)

## [15.0.4](https://github.com/compare/@sage/show-case@15.0.3...@sage/show-case@15.0.4) (2022-01-02)

## [15.0.3](https://github.com/compare/@sage/show-case@15.0.2...@sage/show-case@15.0.3) (2022-01-01)

## [15.0.2](https://github.com/compare/@sage/show-case@15.0.1...@sage/show-case@15.0.2) (2021-12-31)

## [15.0.1](https://github.com/compare/@sage/show-case@15.0.0...@sage/show-case@15.0.1) (2021-12-30)

## [15.0.0](https://github.com/compare/@sage/show-case@14.0.29...@sage/show-case@15.0.0) (2021-12-30)

## [14.0.29](https://github.com/compare/@sage/show-case@14.0.28...@sage/show-case@14.0.29) (2021-12-29)

## [14.0.28](https://github.com/compare/@sage/show-case@14.0.27...@sage/show-case@14.0.28) (2021-12-28)

## [14.0.27](https://github.com/compare/@sage/show-case@14.0.26...@sage/show-case@14.0.27) (2021-12-27)

## [14.0.26](https://github.com/compare/@sage/show-case@14.0.25...@sage/show-case@14.0.26) (2021-12-27)

## [14.0.25](https://github.com/compare/@sage/show-case@14.0.24...@sage/show-case@14.0.25) (2021-12-22)

## [14.0.24](https://github.com/compare/@sage/show-case@14.0.23...@sage/show-case@14.0.24) (2021-12-21)

## [14.0.23](https://github.com/compare/@sage/show-case@14.0.22...@sage/show-case@14.0.23) (2021-12-15)

## [14.0.22](https://github.com/compare/@sage/show-case@14.0.21...@sage/show-case@14.0.22) (2021-12-14)

## [14.0.21](https://github.com/compare/@sage/show-case@14.0.20...@sage/show-case@14.0.21) (2021-12-13)

## [14.0.20](https://github.com/compare/@sage/show-case@14.0.19...@sage/show-case@14.0.20) (2021-12-12)

## [14.0.19](https://github.com/compare/@sage/show-case@14.0.18...@sage/show-case@14.0.19) (2021-12-11)

## [14.0.18](https://github.com/compare/@sage/show-case@14.0.17...@sage/show-case@14.0.18) (2021-12-10)

## [14.0.17](https://github.com/compare/@sage/show-case@14.0.16...@sage/show-case@14.0.17) (2021-12-09)

## [14.0.16](https://github.com/compare/@sage/show-case@14.0.15...@sage/show-case@14.0.16) (2021-12-09)

## [14.0.15](https://github.com/compare/@sage/show-case@14.0.14...@sage/show-case@14.0.15) (2021-12-08)

## [14.0.14](https://github.com/compare/@sage/show-case@14.0.13...@sage/show-case@14.0.14) (2021-12-07)

## [14.0.13](https://github.com/compare/@sage/show-case@14.0.12...@sage/show-case@14.0.13) (2021-12-07)

## [14.0.12](https://github.com/compare/@sage/show-case@14.0.11...@sage/show-case@14.0.12) (2021-12-06)

## [14.0.11](https://github.com/compare/@sage/show-case@14.0.10...@sage/show-case@14.0.11) (2021-12-05)

## [14.0.10](https://github.com/compare/@sage/show-case@14.0.9...@sage/show-case@14.0.10) (2021-12-04)

## [14.0.9](https://github.com/compare/@sage/show-case@14.0.8...@sage/show-case@14.0.9) (2021-12-03)

## [14.0.8](https://github.com/compare/@sage/show-case@14.0.7...@sage/show-case@14.0.8) (2021-12-03)

## [14.0.7](https://github.com/compare/@sage/show-case@14.0.6...@sage/show-case@14.0.7) (2021-12-02)

## [14.0.6](https://github.com/compare/@sage/show-case@14.0.5...@sage/show-case@14.0.6) (2021-12-02)

## [14.0.5](https://github.com/compare/@sage/show-case@14.0.3...@sage/show-case@14.0.5) (2021-12-02)

## [14.0.3](https://github.com/compare/@sage/show-case@14.0.2...@sage/show-case@14.0.3) (2021-12-01)

## [14.0.2](https://github.com/compare/@sage/show-case@14.0.0...@sage/show-case@14.0.2) (2021-11-29)

## [14.0.0](https://github.com/compare/@sage/show-case@13.0.28...@sage/show-case@14.0.0) (2021-11-29)

## [13.0.28](https://github.com/compare/@sage/show-case@13.0.27...@sage/show-case@13.0.28) (2021-11-28)

## [13.0.27](https://github.com/compare/@sage/show-case@13.0.26...@sage/show-case@13.0.27) (2021-11-27)

## [13.0.26](https://github.com/compare/@sage/show-case@13.0.25...@sage/show-case@13.0.26) (2021-11-26)

## [13.0.25](https://github.com/compare/@sage/show-case@13.0.24...@sage/show-case@13.0.25) (2021-11-25)

## [13.0.24](https://github.com/compare/@sage/show-case@13.0.23...@sage/show-case@13.0.24) (2021-11-24)

## [13.0.23](https://github.com/compare/@sage/show-case@13.0.22...@sage/show-case@13.0.23) (2021-11-23)

## [13.0.22](https://github.com/compare/@sage/show-case@13.0.21...@sage/show-case@13.0.22) (2021-11-22)

## [13.0.21](https://github.com/compare/@sage/show-case@13.0.20...@sage/show-case@13.0.21) (2021-11-22)

## [13.0.20](https://github.com/compare/@sage/show-case@13.0.19...@sage/show-case@13.0.20) (2021-11-19)

## [13.0.19](https://github.com/compare/@sage/show-case@13.0.18...@sage/show-case@13.0.19) (2021-11-18)

## [13.0.18](https://github.com/compare/@sage/show-case@13.0.17...@sage/show-case@13.0.18) (2021-11-18)

## [13.0.17](https://github.com/compare/@sage/show-case@13.0.16...@sage/show-case@13.0.17) (2021-11-17)

## [13.0.16](https://github.com/compare/@sage/show-case@13.0.15...@sage/show-case@13.0.16) (2021-11-17)

### Features


## [13.0.15](https://github.com/compare/@sage/show-case@13.0.14...@sage/show-case@13.0.15) (2021-11-14)

## [13.0.14](https://github.com/compare/@sage/show-case@13.0.13...@sage/show-case@13.0.14) (2021-11-13)

## [13.0.13](https://github.com/compare/@sage/show-case@13.0.12...@sage/show-case@13.0.13) (2021-11-12)

## [13.0.12](https://github.com/compare/@sage/show-case@13.0.11...@sage/show-case@13.0.12) (2021-11-11)

## [13.0.11](https://github.com/compare/@sage/show-case@13.0.10...@sage/show-case@13.0.11) (2021-11-10)

## [13.0.10](https://github.com/compare/@sage/show-case@13.0.9...@sage/show-case@13.0.10) (2021-11-09)

## [13.0.9](https://github.com/compare/@sage/show-case@13.0.8...@sage/show-case@13.0.9) (2021-11-09)

## [13.0.8](https://github.com/compare/@sage/show-case@13.0.7...@sage/show-case@13.0.8) (2021-11-09)

### Features


## [13.0.7](https://github.com/compare/@sage/show-case@13.0.6...@sage/show-case@13.0.7) (2021-11-08)

## [13.0.6](https://github.com/compare/@sage/show-case@13.0.5...@sage/show-case@13.0.6) (2021-11-07)

## [13.0.5](https://github.com/compare/@sage/show-case@13.0.4...@sage/show-case@13.0.5) (2021-11-06)

## [13.0.4](https://github.com/compare/@sage/show-case@13.0.3...@sage/show-case@13.0.4) (2021-11-05)

## [13.0.3](https://github.com/compare/@sage/show-case@13.0.2...@sage/show-case@13.0.3) (2021-11-04)

## [13.0.2](https://github.com/compare/@sage/show-case@13.0.1...@sage/show-case@13.0.2) (2021-11-03)

## [13.0.1](https://github.com/compare/@sage/show-case@13.0.0...@sage/show-case@13.0.1) (2021-11-03)

## [13.0.0](https://github.com/compare/@sage/show-case@12.0.35...@sage/show-case@13.0.0) (2021-11-02)

## [12.0.35](https://github.com/compare/@sage/show-case@12.0.34...@sage/show-case@12.0.35) (2021-10-31)

## [12.0.34](https://github.com/compare/@sage/show-case@12.0.33...@sage/show-case@12.0.34) (2021-10-30)

## [12.0.33](https://github.com/compare/@sage/show-case@12.0.32...@sage/show-case@12.0.33) (2021-10-29)

## [12.0.32](https://github.com/compare/@sage/show-case@12.0.30...@sage/show-case@12.0.32) (2021-10-28)

### Features


## [12.0.30](https://github.com/compare/@sage/show-case@12.0.29...@sage/show-case@12.0.30) (2021-10-26)

## [12.0.29](https://github.com/compare/@sage/show-case@12.0.28...@sage/show-case@12.0.29) (2021-10-26)

## [12.0.28](https://github.com/compare/@sage/show-case@12.0.27...@sage/show-case@12.0.28) (2021-10-25)

## [12.0.27](https://github.com/compare/@sage/show-case@12.0.26...@sage/show-case@12.0.27) (2021-10-25)

## [12.0.26](https://github.com/compare/@sage/show-case@12.0.25...@sage/show-case@12.0.26) (2021-10-24)

## [12.0.25](https://github.com/compare/@sage/show-case@12.0.24...@sage/show-case@12.0.25) (2021-10-24)

## [12.0.24](https://github.com/compare/@sage/show-case@12.0.23...@sage/show-case@12.0.24) (2021-10-23)

## [12.0.23](https://github.com/compare/@sage/show-case@12.0.22...@sage/show-case@12.0.23) (2021-10-22)

## [12.0.22](https://github.com/compare/@sage/show-case@12.0.21...@sage/show-case@12.0.22) (2021-10-22)

## [12.0.21](https://github.com/compare/@sage/show-case@12.0.20...@sage/show-case@12.0.21) (2021-10-20)

## [12.0.20](https://github.com/compare/@sage/show-case@12.0.19...@sage/show-case@12.0.20) (2021-10-19)

## [12.0.19](https://github.com/compare/@sage/show-case@12.0.18...@sage/show-case@12.0.19) (2021-10-18)

## [12.0.18](https://github.com/compare/@sage/show-case@12.0.17...@sage/show-case@12.0.18) (2021-10-17)

## [12.0.17](https://github.com/compare/@sage/show-case@12.0.16...@sage/show-case@12.0.17) (2021-10-17)

## [12.0.16](https://github.com/compare/@sage/show-case@12.0.15...@sage/show-case@12.0.16) (2021-10-16)

## [12.0.15](https://github.com/compare/@sage/show-case@12.0.14...@sage/show-case@12.0.15) (2021-10-15)

## [12.0.14](https://github.com/compare/@sage/show-case@12.0.13...@sage/show-case@12.0.14) (2021-10-14)

## [12.0.13](https://github.com/compare/@sage/show-case@12.0.12...@sage/show-case@12.0.13) (2021-10-13)

## [12.0.12](https://github.com/compare/@sage/show-case@12.0.11...@sage/show-case@12.0.12) (2021-10-12)

## [12.0.11](https://github.com/compare/@sage/show-case@12.0.10...@sage/show-case@12.0.11) (2021-10-12)

## [12.0.10](https://github.com/compare/@sage/show-case@12.0.9...@sage/show-case@12.0.10) (2021-10-10)

## [12.0.9](https://github.com/compare/@sage/show-case@12.0.8...@sage/show-case@12.0.9) (2021-10-09)

## [12.0.8](https://github.com/compare/@sage/show-case@12.0.7...@sage/show-case@12.0.8) (2021-10-08)

## [12.0.7](https://github.com/compare/@sage/show-case@12.0.6...@sage/show-case@12.0.7) (2021-10-08)

## [12.0.6](https://github.com/compare/@sage/show-case@12.0.5...@sage/show-case@12.0.6) (2021-10-07)

## [12.0.5](https://github.com/compare/@sage/show-case@12.0.4...@sage/show-case@12.0.5) (2021-10-06)

## [12.0.4](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.4) (2021-10-05)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [20.0.8](https://github.com/compare/@sage/show-case@20.0.6...@sage/show-case@20.0.8) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

## [20.0.6](https://github.com/compare/@sage/show-case@20.0.5...@sage/show-case@20.0.6) (2022-06-02)

# [20.0.5](https://github.com/compare/@sage/show-case@20.0.4...@sage/show-case@20.0.5) (2022-06-02)

## [20.0.5](https://github.com/compare/@sage/show-case@20.0.4...@sage/show-case@20.0.5) (2022-06-01)

# [20.0.4](https://github.com/compare/@sage/show-case@20.0.3...@sage/show-case@20.0.4) (2022-06-01)

## [20.0.4](https://github.com/compare/@sage/show-case@20.0.3...@sage/show-case@20.0.4) (2022-05-29)

# [20.0.3](https://github.com/compare/@sage/show-case@20.0.2...@sage/show-case@20.0.3) (2022-05-29)

## [20.0.3](https://github.com/compare/@sage/show-case@20.0.2...@sage/show-case@20.0.3) (2022-05-28)

# [20.0.2](https://github.com/compare/@sage/show-case@20.0.1...@sage/show-case@20.0.2) (2022-05-28)

## [20.0.2](https://github.com/compare/@sage/show-case@20.0.1...@sage/show-case@20.0.2) (2022-05-27)

# [20.0.1](https://github.com/compare/@sage/show-case@20.0.0...@sage/show-case@20.0.1) (2022-05-27)

## [20.0.1](https://github.com/compare/@sage/show-case@20.0.0...@sage/show-case@20.0.1) (2022-05-26)

# [20.0.0](https://github.com/compare/@sage/show-case@19.0.33...@sage/show-case@20.0.0) (2022-05-26)

## [20.0.0](https://github.com/compare/@sage/show-case@19.0.33...@sage/show-case@20.0.0) (2022-05-26)

# [19.0.33](https://github.com/compare/@sage/show-case@19.0.32...@sage/show-case@19.0.33) (2022-05-26)

## [19.0.33](https://github.com/compare/@sage/show-case@19.0.32...@sage/show-case@19.0.33) (2022-05-26)

# [19.0.32](https://github.com/compare/@sage/show-case@19.0.31...@sage/show-case@19.0.32) (2022-05-26)

## [19.0.32](https://github.com/compare/@sage/show-case@19.0.31...@sage/show-case@19.0.32) (2022-05-24)

# [19.0.31](https://github.com/compare/@sage/show-case@19.0.30...@sage/show-case@19.0.31) (2022-05-24)

## [19.0.31](https://github.com/compare/@sage/show-case@19.0.30...@sage/show-case@19.0.31) (2022-05-24)

# [19.0.30](https://github.com/compare/@sage/show-case@19.0.29...@sage/show-case@19.0.30) (2022-05-24)

## [19.0.30](https://github.com/compare/@sage/show-case@19.0.29...@sage/show-case@19.0.30) (2022-05-23)

# [19.0.29](https://github.com/compare/@sage/show-case@19.0.28...@sage/show-case@19.0.29) (2022-05-23)

## [19.0.29](https://github.com/compare/@sage/show-case@19.0.28...@sage/show-case@19.0.29) (2022-05-23)

# [19.0.28](https://github.com/compare/@sage/show-case@19.0.27...@sage/show-case@19.0.28) (2022-05-23)

## [19.0.28](https://github.com/compare/@sage/show-case@19.0.27...@sage/show-case@19.0.28) (2022-05-22)

# [19.0.27](https://github.com/compare/@sage/show-case@19.0.26...@sage/show-case@19.0.27) (2022-05-22)

## [19.0.27](https://github.com/compare/@sage/show-case@19.0.26...@sage/show-case@19.0.27) (2022-05-21)

# [19.0.26](https://github.com/compare/@sage/show-case@19.0.25...@sage/show-case@19.0.26) (2022-05-21)

## [19.0.26](https://github.com/compare/@sage/show-case@19.0.25...@sage/show-case@19.0.26) (2022-05-20)

# [19.0.25](https://github.com/compare/@sage/show-case@19.0.24...@sage/show-case@19.0.25) (2022-05-20)

## [19.0.25](https://github.com/compare/@sage/show-case@19.0.24...@sage/show-case@19.0.25) (2022-05-20)

### Bug Fixes


# [19.0.24](https://github.com/compare/@sage/show-case@19.0.23...@sage/show-case@19.0.24) (2022-05-20)

### Bug Fixes

* docker image minimist vulnerability (XT-26106) ([#7114](https://github.com/issues/7114))  ([d0d1ce7](https://github.com/commit/d0d1ce7d97a1923a028888ffe9e8f1f526cdb403))

## [19.0.24](https://github.com/compare/@sage/show-case@19.0.23...@sage/show-case@19.0.24) (2022-05-18)

# [19.0.23](https://github.com/compare/@sage/show-case@19.0.22...@sage/show-case@19.0.23) (2022-05-18)

## [19.0.23](https://github.com/compare/@sage/show-case@19.0.22...@sage/show-case@19.0.23) (2022-05-17)

# [19.0.22](https://github.com/compare/@sage/show-case@19.0.21...@sage/show-case@19.0.22) (2022-05-17)

## [19.0.22](https://github.com/compare/@sage/show-case@19.0.21...@sage/show-case@19.0.22) (2022-05-16)

### Bug Fixes


# [19.0.21](https://github.com/compare/@sage/show-case@19.0.20...@sage/show-case@19.0.21) (2022-05-16)

### Bug Fixes

* XT-24320 install postgresql on showcase image ([#7017](https://github.com/issues/7017))  ([f0dae7f](https://github.com/commit/f0dae7f9b8b0afea2801f8edf05a42c0e70c8cd0))

## [19.0.21](https://github.com/compare/@sage/show-case@19.0.20...@sage/show-case@19.0.21) (2022-05-15)

# [19.0.20](https://github.com/compare/@sage/show-case@19.0.19...@sage/show-case@19.0.20) (2022-05-15)

## [19.0.20](https://github.com/compare/@sage/show-case@19.0.19...@sage/show-case@19.0.20) (2022-05-14)

# [19.0.19](https://github.com/compare/@sage/show-case@19.0.18...@sage/show-case@19.0.19) (2022-05-14)

## [19.0.19](https://github.com/compare/@sage/show-case@19.0.18...@sage/show-case@19.0.19) (2022-05-13)

# [19.0.18](https://github.com/compare/@sage/show-case@19.0.17...@sage/show-case@19.0.18) (2022-05-13)

## [19.0.18](https://github.com/compare/@sage/show-case@19.0.17...@sage/show-case@19.0.18) (2022-05-13)

# [19.0.17](https://github.com/compare/@sage/show-case@19.0.16...@sage/show-case@19.0.17) (2022-05-13)

## [19.0.17](https://github.com/compare/@sage/show-case@19.0.16...@sage/show-case@19.0.17) (2022-05-12)

# [19.0.16](https://github.com/compare/@sage/show-case@19.0.15...@sage/show-case@19.0.16) (2022-05-12)

## [19.0.16](https://github.com/compare/@sage/show-case@19.0.15...@sage/show-case@19.0.16) (2022-05-11)

# [19.0.15](https://github.com/compare/@sage/show-case@19.0.14...@sage/show-case@19.0.15) (2022-05-11)

## [19.0.15](https://github.com/compare/@sage/show-case@19.0.14...@sage/show-case@19.0.15) (2022-05-10)

# [19.0.14](https://github.com/compare/@sage/show-case@19.0.13...@sage/show-case@19.0.14) (2022-05-10)

## [19.0.14](https://github.com/compare/@sage/show-case@19.0.13...@sage/show-case@19.0.14) (2022-05-10)

# [19.0.13](https://github.com/compare/@sage/show-case@19.0.12...@sage/show-case@19.0.13) (2022-05-10)

## [19.0.13](https://github.com/compare/@sage/show-case@19.0.12...@sage/show-case@19.0.13) (2022-05-09)

# [19.0.12](https://github.com/compare/@sage/show-case@19.0.11...@sage/show-case@19.0.12) (2022-05-09)

## [19.0.12](https://github.com/compare/@sage/show-case@19.0.11...@sage/show-case@19.0.12) (2022-05-08)

# [19.0.11](https://github.com/compare/@sage/show-case@19.0.10...@sage/show-case@19.0.11) (2022-05-08)

## [19.0.11](https://github.com/compare/@sage/show-case@19.0.10...@sage/show-case@19.0.11) (2022-05-07)

# [19.0.10](https://github.com/compare/@sage/show-case@19.0.9...@sage/show-case@19.0.10) (2022-05-07)

## [19.0.10](https://github.com/compare/@sage/show-case@19.0.9...@sage/show-case@19.0.10) (2022-05-06)

# [19.0.9](https://github.com/compare/@sage/show-case@19.0.8...@sage/show-case@19.0.9) (2022-05-06)

## [19.0.9](https://github.com/compare/@sage/show-case@19.0.8...@sage/show-case@19.0.9) (2022-05-06)

# [19.0.8](https://github.com/compare/@sage/show-case@19.0.7...@sage/show-case@19.0.8) (2022-05-06)

## [19.0.8](https://github.com/compare/@sage/show-case@19.0.7...@sage/show-case@19.0.8) (2022-05-04)

# [19.0.7](https://github.com/compare/@sage/show-case@19.0.6...@sage/show-case@19.0.7) (2022-05-04)

## [19.0.7](https://github.com/compare/@sage/show-case@19.0.6...@sage/show-case@19.0.7) (2022-05-03)

# [19.0.6](https://github.com/compare/@sage/show-case@19.0.5...@sage/show-case@19.0.6) (2022-05-03)

## [19.0.6](https://github.com/compare/@sage/show-case@19.0.5...@sage/show-case@19.0.6) (2022-05-02)

# [19.0.5](https://github.com/compare/@sage/show-case@19.0.4...@sage/show-case@19.0.5) (2022-05-02)

## [19.0.5](https://github.com/compare/@sage/show-case@19.0.4...@sage/show-case@19.0.5) (2022-05-01)

# [19.0.4](https://github.com/compare/@sage/show-case@19.0.3...@sage/show-case@19.0.4) (2022-05-01)

## [19.0.4](https://github.com/compare/@sage/show-case@19.0.3...@sage/show-case@19.0.4) (2022-04-30)

# [19.0.3](https://github.com/compare/@sage/show-case@19.0.2...@sage/show-case@19.0.3) (2022-04-30)

## [19.0.3](https://github.com/compare/@sage/show-case@19.0.2...@sage/show-case@19.0.3) (2022-04-29)

# [19.0.2](https://github.com/compare/@sage/show-case@19.0.1...@sage/show-case@19.0.2) (2022-04-29)

## [19.0.2](https://github.com/compare/@sage/show-case@19.0.1...@sage/show-case@19.0.2) (2022-04-28)

# [19.0.1](https://github.com/compare/@sage/show-case@19.0.0...@sage/show-case@19.0.1) (2022-04-28)

## [19.0.1](https://github.com/compare/@sage/show-case@19.0.0...@sage/show-case@19.0.1) (2022-04-28)

# [19.0.0](https://github.com/compare/@sage/show-case@18.0.37...@sage/show-case@19.0.0) (2022-04-28)

## [19.0.0](https://github.com/compare/@sage/show-case@18.0.37...@sage/show-case@19.0.0) (2022-04-28)

# [18.0.37](https://github.com/compare/@sage/show-case@18.0.36...@sage/show-case@18.0.37) (2022-04-28)

## [18.0.37](https://github.com/compare/@sage/show-case@18.0.36...@sage/show-case@18.0.37) (2022-04-27)

# [18.0.36](https://github.com/compare/@sage/show-case@18.0.35...@sage/show-case@18.0.36) (2022-04-27)

## [18.0.36](https://github.com/compare/@sage/show-case@18.0.35...@sage/show-case@18.0.36) (2022-04-27)

# [18.0.35](https://github.com/compare/@sage/show-case@18.0.34...@sage/show-case@18.0.35) (2022-04-27)

## [18.0.35](https://github.com/compare/@sage/show-case@18.0.34...@sage/show-case@18.0.35) (2022-04-26)

# [18.0.34](https://github.com/compare/@sage/show-case@18.0.33...@sage/show-case@18.0.34) (2022-04-26)

## [18.0.34](https://github.com/compare/@sage/show-case@18.0.33...@sage/show-case@18.0.34) (2022-04-26)

# [18.0.33](https://github.com/compare/@sage/show-case@18.0.32...@sage/show-case@18.0.33) (2022-04-26)

## [18.0.33](https://github.com/compare/@sage/show-case@18.0.32...@sage/show-case@18.0.33) (2022-04-25)

# [18.0.32](https://github.com/compare/@sage/show-case@18.0.31...@sage/show-case@18.0.32) (2022-04-25)

## [18.0.32](https://github.com/compare/@sage/show-case@18.0.31...@sage/show-case@18.0.32) (2022-04-25)

# [18.0.31](https://github.com/compare/@sage/show-case@18.0.30...@sage/show-case@18.0.31) (2022-04-25)

## [18.0.31](https://github.com/compare/@sage/show-case@18.0.30...@sage/show-case@18.0.31) (2022-04-21)

# [18.0.30](https://github.com/compare/@sage/show-case@18.0.29...@sage/show-case@18.0.30) (2022-04-21)

## [18.0.30](https://github.com/compare/@sage/show-case@18.0.29...@sage/show-case@18.0.30) (2022-04-21)

# [18.0.29](https://github.com/compare/@sage/show-case@18.0.28...@sage/show-case@18.0.29) (2022-04-21)

## [18.0.29](https://github.com/compare/@sage/show-case@18.0.28...@sage/show-case@18.0.29) (2022-04-20)

# [18.0.28](https://github.com/compare/@sage/show-case@18.0.27...@sage/show-case@18.0.28) (2022-04-20)

## [18.0.28](https://github.com/compare/@sage/show-case@18.0.27...@sage/show-case@18.0.28) (2022-04-20)

# [18.0.27](https://github.com/compare/@sage/show-case@18.0.26...@sage/show-case@18.0.27) (2022-04-20)

## [18.0.27](https://github.com/compare/@sage/show-case@18.0.26...@sage/show-case@18.0.27) (2022-04-18)

# [18.0.26](https://github.com/compare/@sage/show-case@18.0.25...@sage/show-case@18.0.26) (2022-04-18)

## [18.0.26](https://github.com/compare/@sage/show-case@18.0.25...@sage/show-case@18.0.26) (2022-04-18)

# [18.0.25](https://github.com/compare/@sage/show-case@18.0.24...@sage/show-case@18.0.25) (2022-04-18)

## [18.0.25](https://github.com/compare/@sage/show-case@18.0.24...@sage/show-case@18.0.25) (2022-04-16)

# [18.0.24](https://github.com/compare/@sage/show-case@18.0.23...@sage/show-case@18.0.24) (2022-04-16)

## [18.0.24](https://github.com/compare/@sage/show-case@18.0.23...@sage/show-case@18.0.24) (2022-04-15)

# [18.0.23](https://github.com/compare/@sage/show-case@18.0.22...@sage/show-case@18.0.23) (2022-04-15)

## [18.0.23](https://github.com/compare/@sage/show-case@18.0.22...@sage/show-case@18.0.23) (2022-04-14)

# [18.0.22](https://github.com/compare/@sage/show-case@18.0.21...@sage/show-case@18.0.22) (2022-04-14)

## [18.0.22](https://github.com/compare/@sage/show-case@18.0.21...@sage/show-case@18.0.22) (2022-04-13)

# [18.0.21](https://github.com/compare/@sage/show-case@18.0.20...@sage/show-case@18.0.21) (2022-04-13)

## [18.0.21](https://github.com/compare/@sage/show-case@18.0.20...@sage/show-case@18.0.21) (2022-04-12)

# [18.0.20](https://github.com/compare/@sage/show-case@18.0.19...@sage/show-case@18.0.20) (2022-04-12)

## [18.0.20](https://github.com/compare/@sage/show-case@18.0.19...@sage/show-case@18.0.20) (2022-04-11)

# [18.0.19](https://github.com/compare/@sage/show-case@18.0.18...@sage/show-case@18.0.19) (2022-04-11)

## [18.0.19](https://github.com/compare/@sage/show-case@18.0.18...@sage/show-case@18.0.19) (2022-04-10)

# [18.0.18](https://github.com/compare/@sage/show-case@18.0.17...@sage/show-case@18.0.18) (2022-04-10)

## [18.0.18](https://github.com/compare/@sage/show-case@18.0.17...@sage/show-case@18.0.18) (2022-04-09)

# [18.0.17](https://github.com/compare/@sage/show-case@18.0.16...@sage/show-case@18.0.17) (2022-04-09)

## [18.0.17](https://github.com/compare/@sage/show-case@18.0.16...@sage/show-case@18.0.17) (2022-04-08)

# [18.0.16](https://github.com/compare/@sage/show-case@18.0.15...@sage/show-case@18.0.16) (2022-04-08)

## [18.0.16](https://github.com/compare/@sage/show-case@18.0.15...@sage/show-case@18.0.16) (2022-04-07)

# [18.0.15](https://github.com/compare/@sage/show-case@18.0.14...@sage/show-case@18.0.15) (2022-04-07)

## [18.0.15](https://github.com/compare/@sage/show-case@18.0.14...@sage/show-case@18.0.15) (2022-04-06)

# [18.0.14](https://github.com/compare/@sage/show-case@18.0.13...@sage/show-case@18.0.14) (2022-04-06)

## [18.0.14](https://github.com/compare/@sage/show-case@18.0.13...@sage/show-case@18.0.14) (2022-04-05)

# [18.0.13](https://github.com/compare/@sage/show-case@18.0.12...@sage/show-case@18.0.13) (2022-04-05)

## [18.0.13](https://github.com/compare/@sage/show-case@18.0.12...@sage/show-case@18.0.13) (2022-04-04)

# [18.0.12](https://github.com/compare/@sage/show-case@18.0.11...@sage/show-case@18.0.12) (2022-04-04)

## [18.0.12](https://github.com/compare/@sage/show-case@18.0.11...@sage/show-case@18.0.12) (2022-04-03)

# [18.0.11](https://github.com/compare/@sage/show-case@18.0.10...@sage/show-case@18.0.11) (2022-04-03)

## [18.0.11](https://github.com/compare/@sage/show-case@18.0.10...@sage/show-case@18.0.11) (2022-04-02)

# [18.0.10](https://github.com/compare/@sage/show-case@18.0.9...@sage/show-case@18.0.10) (2022-04-02)

## [18.0.10](https://github.com/compare/@sage/show-case@18.0.9...@sage/show-case@18.0.10) (2022-04-01)

# [18.0.9](https://github.com/compare/@sage/show-case@18.0.8...@sage/show-case@18.0.9) (2022-04-01)

## [18.0.9](https://github.com/compare/@sage/show-case@18.0.8...@sage/show-case@18.0.9) (2022-03-31)

# [18.0.8](https://github.com/compare/@sage/show-case@18.0.7...@sage/show-case@18.0.8) (2022-03-31)

## [18.0.8](https://github.com/compare/@sage/show-case@18.0.7...@sage/show-case@18.0.8) (2022-03-31)

# [18.0.7](https://github.com/compare/@sage/show-case@18.0.6...@sage/show-case@18.0.7) (2022-03-31)

## [18.0.7](https://github.com/compare/@sage/show-case@18.0.6...@sage/show-case@18.0.7) (2022-03-30)

# [18.0.6](https://github.com/compare/@sage/show-case@18.0.5...@sage/show-case@18.0.6) (2022-03-30)

## [18.0.6](https://github.com/compare/@sage/show-case@18.0.5...@sage/show-case@18.0.6) (2022-03-29)

# [18.0.5](https://github.com/compare/@sage/show-case@18.0.4...@sage/show-case@18.0.5) (2022-03-29)

## [18.0.5](https://github.com/compare/@sage/show-case@18.0.4...@sage/show-case@18.0.5) (2022-03-28)

# [18.0.4](https://github.com/compare/@sage/show-case@18.0.3...@sage/show-case@18.0.4) (2022-03-28)

## [18.0.4](https://github.com/compare/@sage/show-case@18.0.3...@sage/show-case@18.0.4) (2022-03-27)

# [18.0.3](https://github.com/compare/@sage/show-case@18.0.2...@sage/show-case@18.0.3) (2022-03-27)

## [18.0.3](https://github.com/compare/@sage/show-case@18.0.2...@sage/show-case@18.0.3) (2022-03-26)

# [18.0.2](https://github.com/compare/@sage/show-case@18.0.1...@sage/show-case@18.0.2) (2022-03-26)

## [18.0.2](https://github.com/compare/@sage/show-case@18.0.1...@sage/show-case@18.0.2) (2022-03-25)

# [18.0.1](https://github.com/compare/@sage/show-case@18.0.0...@sage/show-case@18.0.1) (2022-03-25)

## [18.0.1](https://github.com/compare/@sage/show-case@18.0.0...@sage/show-case@18.0.1) (2022-03-24)

# [18.0.0](https://github.com/compare/@sage/show-case@17.0.29...@sage/show-case@18.0.0) (2022-03-24)

## [18.0.0](https://github.com/compare/@sage/show-case@17.0.29...@sage/show-case@18.0.0) (2022-03-24)

# [17.0.29](https://github.com/compare/@sage/show-case@17.0.28...@sage/show-case@17.0.29) (2022-03-24)

## [17.0.29](https://github.com/compare/@sage/show-case@17.0.28...@sage/show-case@17.0.29) (2022-03-24)

# [17.0.28](https://github.com/compare/@sage/show-case@17.0.27...@sage/show-case@17.0.28) (2022-03-24)

## [17.0.28](https://github.com/compare/@sage/show-case@17.0.27...@sage/show-case@17.0.28) (2022-03-23)

# [17.0.27](https://github.com/compare/@sage/show-case@17.0.26...@sage/show-case@17.0.27) (2022-03-23)

## [17.0.27](https://github.com/compare/@sage/show-case@17.0.26...@sage/show-case@17.0.27) (2022-03-22)

### Bug Fixes


# [17.0.26](https://github.com/compare/@sage/show-case@17.0.25...@sage/show-case@17.0.26) (2022-03-22)

### Bug Fixes

* **sec:** node 14-alpine to fix CVE-2022-0778 (XT-22023) ([#5991](https://github.com/issues/5991))  ([4ea5994](https://github.com/commit/4ea5994b7836baf7f0f2745f9ee3f9c6aa075e7b))

## [17.0.26](https://github.com/compare/@sage/show-case@17.0.25...@sage/show-case@17.0.26) (2022-03-21)

# [17.0.25](https://github.com/compare/@sage/show-case@17.0.24...@sage/show-case@17.0.25) (2022-03-21)

## [17.0.25](https://github.com/compare/@sage/show-case@17.0.24...@sage/show-case@17.0.25) (2022-03-20)

# [17.0.24](https://github.com/compare/@sage/show-case@17.0.23...@sage/show-case@17.0.24) (2022-03-20)

## [17.0.24](https://github.com/compare/@sage/show-case@17.0.23...@sage/show-case@17.0.24) (2022-03-20)

# [17.0.23](https://github.com/compare/@sage/show-case@17.0.22...@sage/show-case@17.0.23) (2022-03-20)

## [17.0.23](https://github.com/compare/@sage/show-case@17.0.22...@sage/show-case@17.0.23) (2022-03-19)

# [17.0.22](https://github.com/compare/@sage/show-case@17.0.21...@sage/show-case@17.0.22) (2022-03-19)

## [17.0.22](https://github.com/compare/@sage/show-case@17.0.21...@sage/show-case@17.0.22) (2022-03-19)

# [17.0.21](https://github.com/compare/@sage/show-case@17.0.20...@sage/show-case@17.0.21) (2022-03-19)

## [17.0.21](https://github.com/compare/@sage/show-case@17.0.20...@sage/show-case@17.0.21) (2022-03-18)

# [17.0.20](https://github.com/compare/@sage/show-case@17.0.19...@sage/show-case@17.0.20) (2022-03-18)

## [17.0.20](https://github.com/compare/@sage/show-case@17.0.19...@sage/show-case@17.0.20) (2022-03-18)

# [17.0.19](https://github.com/compare/@sage/show-case@17.0.18...@sage/show-case@17.0.19) (2022-03-18)

## [17.0.19](https://github.com/compare/@sage/show-case@17.0.18...@sage/show-case@17.0.19) (2022-03-18)

# [17.0.18](https://github.com/compare/@sage/show-case@17.0.17...@sage/show-case@17.0.18) (2022-03-18)

## [17.0.18](https://github.com/compare/@sage/show-case@17.0.17...@sage/show-case@17.0.18) (2022-03-17)

# [17.0.17](https://github.com/compare/@sage/show-case@17.0.16...@sage/show-case@17.0.17) (2022-03-17)

## [17.0.17](https://github.com/compare/@sage/show-case@17.0.16...@sage/show-case@17.0.17) (2022-03-17)

# [17.0.16](https://github.com/compare/@sage/show-case@17.0.15...@sage/show-case@17.0.16) (2022-03-17)

## [17.0.16](https://github.com/compare/@sage/show-case@17.0.15...@sage/show-case@17.0.16) (2022-03-13)

# [17.0.15](https://github.com/compare/@sage/show-case@17.0.14...@sage/show-case@17.0.15) (2022-03-13)

## [17.0.15](https://github.com/compare/@sage/show-case@17.0.14...@sage/show-case@17.0.15) (2022-03-10)

# [17.0.14](https://github.com/compare/@sage/show-case@17.0.13...@sage/show-case@17.0.14) (2022-03-10)

## [17.0.14](https://github.com/compare/@sage/show-case@17.0.13...@sage/show-case@17.0.14) (2022-03-09)

# [17.0.13](https://github.com/compare/@sage/show-case@17.0.12...@sage/show-case@17.0.13) (2022-03-09)

## [17.0.13](https://github.com/compare/@sage/show-case@17.0.12...@sage/show-case@17.0.13) (2022-03-09)

# [17.0.12](https://github.com/compare/@sage/show-case@17.0.11...@sage/show-case@17.0.12) (2022-03-09)

## [17.0.12](https://github.com/compare/@sage/show-case@17.0.11...@sage/show-case@17.0.12) (2022-03-08)

# [17.0.11](https://github.com/compare/@sage/show-case@17.0.10...@sage/show-case@17.0.11) (2022-03-08)

## [17.0.11](https://github.com/compare/@sage/show-case@17.0.10...@sage/show-case@17.0.11) (2022-03-07)

# [17.0.10](https://github.com/compare/@sage/show-case@17.0.9...@sage/show-case@17.0.10) (2022-03-07)

## [17.0.10](https://github.com/compare/@sage/show-case@17.0.9...@sage/show-case@17.0.10) (2022-03-06)

# [17.0.9](https://github.com/compare/@sage/show-case@17.0.8...@sage/show-case@17.0.9) (2022-03-06)

## [17.0.9](https://github.com/compare/@sage/show-case@17.0.8...@sage/show-case@17.0.9) (2022-03-05)

# [17.0.8](https://github.com/compare/@sage/show-case@17.0.7...@sage/show-case@17.0.8) (2022-03-05)

## [17.0.8](https://github.com/compare/@sage/show-case@17.0.7...@sage/show-case@17.0.8) (2022-03-04)

# [17.0.7](https://github.com/compare/@sage/show-case@17.0.6...@sage/show-case@17.0.7) (2022-03-04)

## [17.0.7](https://github.com/compare/@sage/show-case@17.0.6...@sage/show-case@17.0.7) (2022-03-03)

# [17.0.6](https://github.com/compare/@sage/show-case@17.0.5...@sage/show-case@17.0.6) (2022-03-03)

## [17.0.6](https://github.com/compare/@sage/show-case@17.0.5...@sage/show-case@17.0.6) (2022-03-03)

# [17.0.5](https://github.com/compare/@sage/show-case@17.0.4...@sage/show-case@17.0.5) (2022-03-03)

## [17.0.5](https://github.com/compare/@sage/show-case@17.0.4...@sage/show-case@17.0.5) (2022-03-01)

# [17.0.4](https://github.com/compare/@sage/show-case@17.0.3...@sage/show-case@17.0.4) (2022-03-01)

## [17.0.4](https://github.com/compare/@sage/show-case@17.0.3...@sage/show-case@17.0.4) (2022-02-28)

# [17.0.3](https://github.com/compare/@sage/show-case@17.0.2...@sage/show-case@17.0.3) (2022-02-28)

## [17.0.3](https://github.com/compare/@sage/show-case@17.0.2...@sage/show-case@17.0.3) (2022-02-27)

# [17.0.2](https://github.com/compare/@sage/show-case@17.0.1...@sage/show-case@17.0.2) (2022-02-27)

## [17.0.2](https://github.com/compare/@sage/show-case@17.0.1...@sage/show-case@17.0.2) (2022-02-26)

# [17.0.1](https://github.com/compare/@sage/show-case@17.0.0...@sage/show-case@17.0.1) (2022-02-26)

## [17.0.1](https://github.com/compare/@sage/show-case@17.0.0...@sage/show-case@17.0.1) (2022-02-25)

# [17.0.0](https://github.com/compare/@sage/show-case@16.0.29...@sage/show-case@17.0.0) (2022-02-25)

## [17.0.0](https://github.com/compare/@sage/show-case@16.0.29...@sage/show-case@17.0.0) (2022-02-24)

# [16.0.29](https://github.com/compare/@sage/show-case@16.0.28...@sage/show-case@16.0.29) (2022-02-24)

## [16.0.29](https://github.com/compare/@sage/show-case@16.0.28...@sage/show-case@16.0.29) (2022-02-24)

# [16.0.28](https://github.com/compare/@sage/show-case@16.0.27...@sage/show-case@16.0.28) (2022-02-24)

## [16.0.28](https://github.com/compare/@sage/show-case@16.0.27...@sage/show-case@16.0.28) (2022-02-24)

### Bug Fixes


# [16.0.27](https://github.com/compare/@sage/show-case@16.0.26...@sage/show-case@16.0.27) (2022-02-24)

### Bug Fixes

* glossary and showcase image smoke test (XT-20115) ([#5539](https://github.com/issues/5539))  ([2ddfa11](https://github.com/commit/2ddfa116e585b6f6247cf36db4d448febf04e3d9))

## [16.0.27](https://github.com/compare/@sage/show-case@16.0.26...@sage/show-case@16.0.27) (2022-02-23)

# [16.0.26](https://github.com/compare/@sage/show-case@16.0.25...@sage/show-case@16.0.26) (2022-02-23)

## [16.0.26](https://github.com/compare/@sage/show-case@16.0.25...@sage/show-case@16.0.26) (2022-02-22)

### Bug Fixes


# [16.0.25](https://github.com/compare/@sage/show-case@16.0.24...@sage/show-case@16.0.25) (2022-02-22)

### Bug Fixes

* image failed on reset test XT-19796 ([#5427](https://github.com/issues/5427))  ([7995d5d](https://github.com/commit/7995d5d144e0a11c27ae5bb3f9033f6ea64e3bc4))

## [16.0.25](https://github.com/compare/@sage/show-case@16.0.24...@sage/show-case@16.0.25) (2022-02-21)

# [16.0.24](https://github.com/compare/@sage/show-case@16.0.23...@sage/show-case@16.0.24) (2022-02-21)

## [16.0.24](https://github.com/compare/@sage/show-case@16.0.23...@sage/show-case@16.0.24) (2022-02-20)

# [16.0.23](https://github.com/compare/@sage/show-case@16.0.22...@sage/show-case@16.0.23) (2022-02-20)

## [16.0.23](https://github.com/compare/@sage/show-case@16.0.22...@sage/show-case@16.0.23) (2022-02-19)

# [16.0.22](https://github.com/compare/@sage/show-case@16.0.21...@sage/show-case@16.0.22) (2022-02-19)

## [16.0.22](https://github.com/compare/@sage/show-case@16.0.21...@sage/show-case@16.0.22) (2022-02-18)

# [16.0.21](https://github.com/compare/@sage/show-case@16.0.20...@sage/show-case@16.0.21) (2022-02-18)

## [16.0.21](https://github.com/compare/@sage/show-case@16.0.20...@sage/show-case@16.0.21) (2022-02-17)

# [16.0.20](https://github.com/compare/@sage/show-case@16.0.19...@sage/show-case@16.0.20) (2022-02-17)

## [16.0.20](https://github.com/compare/@sage/show-case@16.0.19...@sage/show-case@16.0.20) (2022-02-16)

# [16.0.19](https://github.com/compare/@sage/show-case@16.0.18...@sage/show-case@16.0.19) (2022-02-16)

## [16.0.19](https://github.com/compare/@sage/show-case@16.0.18...@sage/show-case@16.0.19) (2022-02-15)

# [16.0.18](https://github.com/compare/@sage/show-case@16.0.17...@sage/show-case@16.0.18) (2022-02-15)

## [16.0.18](https://github.com/compare/@sage/show-case@16.0.17...@sage/show-case@16.0.18) (2022-02-13)

# [16.0.17](https://github.com/compare/@sage/show-case@16.0.16...@sage/show-case@16.0.17) (2022-02-13)

## [16.0.17](https://github.com/compare/@sage/show-case@16.0.16...@sage/show-case@16.0.17) (2022-02-12)

# [16.0.16](https://github.com/compare/@sage/show-case@16.0.15...@sage/show-case@16.0.16) (2022-02-12)

## [16.0.16](https://github.com/compare/@sage/show-case@16.0.15...@sage/show-case@16.0.16) (2022-02-11)

# [16.0.15](https://github.com/compare/@sage/show-case@16.0.14...@sage/show-case@16.0.15) (2022-02-11)

## [16.0.15](https://github.com/compare/@sage/show-case@16.0.14...@sage/show-case@16.0.15) (2022-02-10)

# [16.0.14](https://github.com/compare/@sage/show-case@16.0.13...@sage/show-case@16.0.14) (2022-02-10)

## [16.0.14](https://github.com/compare/@sage/show-case@16.0.13...@sage/show-case@16.0.14) (2022-02-10)

# [16.0.13](https://github.com/compare/@sage/show-case@16.0.12...@sage/show-case@16.0.13) (2022-02-10)

## [16.0.13](https://github.com/compare/@sage/show-case@16.0.12...@sage/show-case@16.0.13) (2022-02-08)

# [16.0.12](https://github.com/compare/@sage/show-case@16.0.11...@sage/show-case@16.0.12) (2022-02-08)

## [16.0.12](https://github.com/compare/@sage/show-case@16.0.11...@sage/show-case@16.0.12) (2022-02-07)

# [16.0.11](https://github.com/compare/@sage/show-case@16.0.10...@sage/show-case@16.0.11) (2022-02-07)

## [16.0.11](https://github.com/compare/@sage/show-case@16.0.10...@sage/show-case@16.0.11) (2022-02-07)

# [16.0.10](https://github.com/compare/@sage/show-case@16.0.9...@sage/show-case@16.0.10) (2022-02-07)

## [16.0.10](https://github.com/compare/@sage/show-case@16.0.9...@sage/show-case@16.0.10) (2022-02-06)

# [16.0.9](https://github.com/compare/@sage/show-case@16.0.8...@sage/show-case@16.0.9) (2022-02-06)

## [16.0.9](https://github.com/compare/@sage/show-case@16.0.8...@sage/show-case@16.0.9) (2022-02-05)

# [16.0.8](https://github.com/compare/@sage/show-case@16.0.7...@sage/show-case@16.0.8) (2022-02-05)

## [16.0.8](https://github.com/compare/@sage/show-case@16.0.7...@sage/show-case@16.0.8) (2022-02-04)

# [16.0.7](https://github.com/compare/@sage/show-case@16.0.6...@sage/show-case@16.0.7) (2022-02-04)

## [16.0.7](https://github.com/compare/@sage/show-case@16.0.6...@sage/show-case@16.0.7) (2022-02-04)

# [16.0.6](https://github.com/compare/@sage/show-case@16.0.5...@sage/show-case@16.0.6) (2022-02-04)

## [16.0.6](https://github.com/compare/@sage/show-case@16.0.5...@sage/show-case@16.0.6) (2022-02-02)

# [16.0.5](https://github.com/compare/@sage/show-case@16.0.4...@sage/show-case@16.0.5) (2022-02-02)

## [16.0.5](https://github.com/compare/@sage/show-case@16.0.4...@sage/show-case@16.0.5) (2022-02-01)

# [16.0.4](https://github.com/compare/@sage/show-case@16.0.3...@sage/show-case@16.0.4) (2022-02-01)

## [16.0.4](https://github.com/compare/@sage/show-case@16.0.3...@sage/show-case@16.0.4) (2022-01-31)

# [16.0.3](https://github.com/compare/@sage/show-case@16.0.2...@sage/show-case@16.0.3) (2022-01-31)

## [16.0.3](https://github.com/compare/@sage/show-case@16.0.2...@sage/show-case@16.0.3) (2022-01-30)

# [16.0.2](https://github.com/compare/@sage/show-case@16.0.1...@sage/show-case@16.0.2) (2022-01-30)

## [16.0.2](https://github.com/compare/@sage/show-case@16.0.1...@sage/show-case@16.0.2) (2022-01-29)

# [16.0.1](https://github.com/compare/@sage/show-case@16.0.0...@sage/show-case@16.0.1) (2022-01-29)

## [16.0.1](https://github.com/compare/@sage/show-case@16.0.0...@sage/show-case@16.0.1) (2022-01-29)

# [16.0.0](https://github.com/compare/@sage/show-case@15.0.36...@sage/show-case@16.0.0) (2022-01-29)

## [16.0.0](https://github.com/compare/@sage/show-case@15.0.36...@sage/show-case@16.0.0) (2022-01-28)

# [15.0.36](https://github.com/compare/@sage/show-case@15.0.35...@sage/show-case@15.0.36) (2022-01-28)

## [15.0.36](https://github.com/compare/@sage/show-case@15.0.35...@sage/show-case@15.0.36) (2022-01-28)

# [15.0.35](https://github.com/compare/@sage/show-case@15.0.34...@sage/show-case@15.0.35) (2022-01-28)

## [15.0.35](https://github.com/compare/@sage/show-case@15.0.34...@sage/show-case@15.0.35) (2022-01-28)

# [15.0.34](https://github.com/compare/@sage/show-case@15.0.33...@sage/show-case@15.0.34) (2022-01-28)

## [15.0.34](https://github.com/compare/@sage/show-case@15.0.33...@sage/show-case@15.0.34) (2022-01-26)

# [15.0.33](https://github.com/compare/@sage/show-case@15.0.32...@sage/show-case@15.0.33) (2022-01-26)

## [15.0.33](https://github.com/compare/@sage/show-case@15.0.32...@sage/show-case@15.0.33) (2022-01-26)

# [15.0.32](https://github.com/compare/@sage/show-case@15.0.31...@sage/show-case@15.0.32) (2022-01-26)

## [15.0.32](https://github.com/compare/@sage/show-case@15.0.31...@sage/show-case@15.0.32) (2022-01-25)

# [15.0.31](https://github.com/compare/@sage/show-case@15.0.30...@sage/show-case@15.0.31) (2022-01-25)

## [15.0.31](https://github.com/compare/@sage/show-case@15.0.30...@sage/show-case@15.0.31) (2022-01-25)

# [15.0.30](https://github.com/compare/@sage/show-case@15.0.29...@sage/show-case@15.0.30) (2022-01-25)

## [15.0.30](https://github.com/compare/@sage/show-case@15.0.29...@sage/show-case@15.0.30) (2022-01-24)

# [15.0.29](https://github.com/compare/@sage/show-case@15.0.28...@sage/show-case@15.0.29) (2022-01-24)

## [15.0.29](https://github.com/compare/@sage/show-case@15.0.28...@sage/show-case@15.0.29) (2022-01-24)

# [15.0.28](https://github.com/compare/@sage/show-case@15.0.27...@sage/show-case@15.0.28) (2022-01-24)

## [15.0.28](https://github.com/compare/@sage/show-case@15.0.27...@sage/show-case@15.0.28) (2022-01-23)

# [15.0.27](https://github.com/compare/@sage/show-case@15.0.26...@sage/show-case@15.0.27) (2022-01-23)

## [15.0.27](https://github.com/compare/@sage/show-case@15.0.26...@sage/show-case@15.0.27) (2022-01-23)

### Bug Fixes


# [15.0.26](https://github.com/compare/@sage/show-case@15.0.25...@sage/show-case@15.0.26) (2022-01-23)

### Bug Fixes

* ST-999999 fix references to old docker-compose.yml ([#4821](https://github.com/issues/4821))  ([e5a3864](https://github.com/commit/e5a3864ca055b860640d47b6b2cd2b41d17f656d))

## [15.0.26](https://github.com/compare/@sage/show-case@15.0.25...@sage/show-case@15.0.26) (2022-01-21)

### Bug Fixes


# [15.0.25](https://github.com/compare/@sage/show-case@15.0.24...@sage/show-case@15.0.25) (2022-01-21)

### Bug Fixes

* **basalt:** XT-16849 Tag images and rename to smoke test ([#4751](https://github.com/issues/4751))  ([49f30d9](https://github.com/commit/49f30d951a80c12d39f658f30fb8779dec480772))

## [15.0.25](https://github.com/compare/@sage/show-case@15.0.24...@sage/show-case@15.0.25) (2022-01-18)

# [15.0.24](https://github.com/compare/@sage/show-case@15.0.23...@sage/show-case@15.0.24) (2022-01-18)

## [15.0.24](https://github.com/compare/@sage/show-case@15.0.23...@sage/show-case@15.0.24) (2022-01-18)

# [15.0.23](https://github.com/compare/@sage/show-case@15.0.22...@sage/show-case@15.0.23) (2022-01-18)

## [15.0.23](https://github.com/compare/@sage/show-case@15.0.22...@sage/show-case@15.0.23) (2022-01-17)

# [15.0.22](https://github.com/compare/@sage/show-case@15.0.21...@sage/show-case@15.0.22) (2022-01-17)

## [15.0.22](https://github.com/compare/@sage/show-case@15.0.21...@sage/show-case@15.0.22) (2022-01-16)

# [15.0.21](https://github.com/compare/@sage/show-case@15.0.20...@sage/show-case@15.0.21) (2022-01-16)

## [15.0.21](https://github.com/compare/@sage/show-case@15.0.20...@sage/show-case@15.0.21) (2022-01-15)

# [15.0.20](https://github.com/compare/@sage/show-case@15.0.19...@sage/show-case@15.0.20) (2022-01-15)

## [15.0.20](https://github.com/compare/@sage/show-case@15.0.19...@sage/show-case@15.0.20) (2022-01-14)

# [15.0.19](https://github.com/compare/@sage/show-case@15.0.18...@sage/show-case@15.0.19) (2022-01-14)

## [15.0.19](https://github.com/compare/@sage/show-case@15.0.18...@sage/show-case@15.0.19) (2022-01-14)

# [15.0.18](https://github.com/compare/@sage/show-case@15.0.17...@sage/show-case@15.0.18) (2022-01-14)

## [15.0.18](https://github.com/compare/@sage/show-case@15.0.17...@sage/show-case@15.0.18) (2022-01-13)

# [15.0.17](https://github.com/compare/@sage/show-case@15.0.16...@sage/show-case@15.0.17) (2022-01-13)

## [15.0.17](https://github.com/compare/@sage/show-case@15.0.16...@sage/show-case@15.0.17) (2022-01-12)

# [15.0.16](https://github.com/compare/@sage/show-case@15.0.15...@sage/show-case@15.0.16) (2022-01-12)

## [15.0.16](https://github.com/compare/@sage/show-case@15.0.15...@sage/show-case@15.0.16) (2022-01-11)

# [15.0.15](https://github.com/compare/@sage/show-case@15.0.14...@sage/show-case@15.0.15) (2022-01-11)

## [15.0.15](https://github.com/compare/@sage/show-case@15.0.14...@sage/show-case@15.0.15) (2022-01-11)

# [15.0.14](https://github.com/compare/@sage/show-case@15.0.13...@sage/show-case@15.0.14) (2022-01-11)

## [15.0.14](https://github.com/compare/@sage/show-case@15.0.13...@sage/show-case@15.0.14) (2022-01-11)

# [15.0.13](https://github.com/compare/@sage/show-case@15.0.12...@sage/show-case@15.0.13) (2022-01-11)

## [15.0.13](https://github.com/compare/@sage/show-case@15.0.12...@sage/show-case@15.0.13) (2022-01-10)

# [15.0.12](https://github.com/compare/@sage/show-case@15.0.11...@sage/show-case@15.0.12) (2022-01-10)

## [15.0.12](https://github.com/compare/@sage/show-case@15.0.11...@sage/show-case@15.0.12) (2022-01-09)

# [15.0.11](https://github.com/compare/@sage/show-case@15.0.10...@sage/show-case@15.0.11) (2022-01-09)

## [15.0.11](https://github.com/compare/@sage/show-case@15.0.10...@sage/show-case@15.0.11) (2022-01-08)

# [15.0.10](https://github.com/compare/@sage/show-case@15.0.8...@sage/show-case@15.0.10) (2022-01-08)

## [15.0.10](https://github.com/compare/@sage/show-case@15.0.8...@sage/show-case@15.0.10) (2022-01-07)

# [15.0.9](https://github.com/compare/@sage/show-case@15.0.8...@sage/show-case@15.0.9) (2022-01-07)

# [15.0.8](https://github.com/compare/@sage/show-case@15.0.7...@sage/show-case@15.0.8) (2022-01-07)

## [15.0.8](https://github.com/compare/@sage/show-case@15.0.7...@sage/show-case@15.0.8) (2022-01-06)

# [15.0.7](https://github.com/compare/@sage/show-case@15.0.6...@sage/show-case@15.0.7) (2022-01-06)

## [15.0.7](https://github.com/compare/@sage/show-case@15.0.6...@sage/show-case@15.0.7) (2022-01-05)

# [15.0.6](https://github.com/compare/@sage/show-case@15.0.5...@sage/show-case@15.0.6) (2022-01-05)

## [15.0.6](https://github.com/compare/@sage/show-case@15.0.5...@sage/show-case@15.0.6) (2022-01-04)

# [15.0.5](https://github.com/compare/@sage/show-case@15.0.4...@sage/show-case@15.0.5) (2022-01-04)

## [15.0.5](https://github.com/compare/@sage/show-case@15.0.4...@sage/show-case@15.0.5) (2022-01-03)

# [15.0.4](https://github.com/compare/@sage/show-case@15.0.3...@sage/show-case@15.0.4) (2022-01-03)

## [15.0.4](https://github.com/compare/@sage/show-case@15.0.3...@sage/show-case@15.0.4) (2022-01-02)

# [15.0.3](https://github.com/compare/@sage/show-case@15.0.2...@sage/show-case@15.0.3) (2022-01-02)

## [15.0.3](https://github.com/compare/@sage/show-case@15.0.2...@sage/show-case@15.0.3) (2022-01-01)

# [15.0.2](https://github.com/compare/@sage/show-case@15.0.1...@sage/show-case@15.0.2) (2022-01-01)

## [15.0.2](https://github.com/compare/@sage/show-case@15.0.1...@sage/show-case@15.0.2) (2021-12-31)

# [15.0.1](https://github.com/compare/@sage/show-case@15.0.0...@sage/show-case@15.0.1) (2021-12-31)

## [15.0.1](https://github.com/compare/@sage/show-case@15.0.0...@sage/show-case@15.0.1) (2021-12-30)

# [15.0.0](https://github.com/compare/@sage/show-case@14.0.29...@sage/show-case@15.0.0) (2021-12-30)

## [15.0.0](https://github.com/compare/@sage/show-case@14.0.29...@sage/show-case@15.0.0) (2021-12-30)

# [14.0.29](https://github.com/compare/@sage/show-case@14.0.28...@sage/show-case@14.0.29) (2021-12-30)

## [14.0.29](https://github.com/compare/@sage/show-case@14.0.28...@sage/show-case@14.0.29) (2021-12-29)

# [14.0.28](https://github.com/compare/@sage/show-case@14.0.27...@sage/show-case@14.0.28) (2021-12-29)

## [14.0.28](https://github.com/compare/@sage/show-case@14.0.27...@sage/show-case@14.0.28) (2021-12-28)

# [14.0.27](https://github.com/compare/@sage/show-case@14.0.26...@sage/show-case@14.0.27) (2021-12-28)

## [14.0.27](https://github.com/compare/@sage/show-case@14.0.26...@sage/show-case@14.0.27) (2021-12-27)

# [14.0.26](https://github.com/compare/@sage/show-case@14.0.25...@sage/show-case@14.0.26) (2021-12-27)

## [14.0.26](https://github.com/compare/@sage/show-case@14.0.25...@sage/show-case@14.0.26) (2021-12-27)

# [14.0.25](https://github.com/compare/@sage/show-case@14.0.24...@sage/show-case@14.0.25) (2021-12-27)

## [14.0.25](https://github.com/compare/@sage/show-case@14.0.24...@sage/show-case@14.0.25) (2021-12-22)

# [14.0.24](https://github.com/compare/@sage/show-case@14.0.23...@sage/show-case@14.0.24) (2021-12-22)

## [14.0.24](https://github.com/compare/@sage/show-case@14.0.23...@sage/show-case@14.0.24) (2021-12-21)

# [14.0.23](https://github.com/compare/@sage/show-case@14.0.22...@sage/show-case@14.0.23) (2021-12-21)

## [14.0.23](https://github.com/compare/@sage/show-case@14.0.22...@sage/show-case@14.0.23) (2021-12-15)

# [14.0.22](https://github.com/compare/@sage/show-case@14.0.21...@sage/show-case@14.0.22) (2021-12-15)

## [14.0.22](https://github.com/compare/@sage/show-case@14.0.21...@sage/show-case@14.0.22) (2021-12-14)

# [14.0.21](https://github.com/compare/@sage/show-case@14.0.20...@sage/show-case@14.0.21) (2021-12-14)

## [14.0.21](https://github.com/compare/@sage/show-case@14.0.20...@sage/show-case@14.0.21) (2021-12-13)

# [14.0.20](https://github.com/compare/@sage/show-case@14.0.19...@sage/show-case@14.0.20) (2021-12-13)

## [14.0.20](https://github.com/compare/@sage/show-case@14.0.19...@sage/show-case@14.0.20) (2021-12-12)

# [14.0.19](https://github.com/compare/@sage/show-case@14.0.18...@sage/show-case@14.0.19) (2021-12-12)

## [14.0.19](https://github.com/compare/@sage/show-case@14.0.18...@sage/show-case@14.0.19) (2021-12-11)

# [14.0.18](https://github.com/compare/@sage/show-case@14.0.17...@sage/show-case@14.0.18) (2021-12-11)

## [14.0.18](https://github.com/compare/@sage/show-case@14.0.17...@sage/show-case@14.0.18) (2021-12-10)

# [14.0.17](https://github.com/compare/@sage/show-case@14.0.16...@sage/show-case@14.0.17) (2021-12-10)

## [14.0.17](https://github.com/compare/@sage/show-case@14.0.16...@sage/show-case@14.0.17) (2021-12-09)

# [14.0.16](https://github.com/compare/@sage/show-case@14.0.15...@sage/show-case@14.0.16) (2021-12-09)

## [14.0.16](https://github.com/compare/@sage/show-case@14.0.15...@sage/show-case@14.0.16) (2021-12-09)

# [14.0.15](https://github.com/compare/@sage/show-case@14.0.14...@sage/show-case@14.0.15) (2021-12-09)

## [14.0.15](https://github.com/compare/@sage/show-case@14.0.14...@sage/show-case@14.0.15) (2021-12-08)

# [14.0.14](https://github.com/compare/@sage/show-case@14.0.13...@sage/show-case@14.0.14) (2021-12-08)

## [14.0.14](https://github.com/compare/@sage/show-case@14.0.13...@sage/show-case@14.0.14) (2021-12-07)

# [14.0.13](https://github.com/compare/@sage/show-case@14.0.12...@sage/show-case@14.0.13) (2021-12-07)

## [14.0.13](https://github.com/compare/@sage/show-case@14.0.12...@sage/show-case@14.0.13) (2021-12-07)

# [14.0.12](https://github.com/compare/@sage/show-case@14.0.11...@sage/show-case@14.0.12) (2021-12-07)

## [14.0.12](https://github.com/compare/@sage/show-case@14.0.11...@sage/show-case@14.0.12) (2021-12-06)

# [14.0.11](https://github.com/compare/@sage/show-case@14.0.10...@sage/show-case@14.0.11) (2021-12-06)

## [14.0.11](https://github.com/compare/@sage/show-case@14.0.10...@sage/show-case@14.0.11) (2021-12-05)

# [14.0.10](https://github.com/compare/@sage/show-case@14.0.9...@sage/show-case@14.0.10) (2021-12-05)

## [14.0.10](https://github.com/compare/@sage/show-case@14.0.9...@sage/show-case@14.0.10) (2021-12-04)

# [14.0.9](https://github.com/compare/@sage/show-case@14.0.8...@sage/show-case@14.0.9) (2021-12-04)

## [14.0.9](https://github.com/compare/@sage/show-case@14.0.8...@sage/show-case@14.0.9) (2021-12-03)

# [14.0.8](https://github.com/compare/@sage/show-case@14.0.7...@sage/show-case@14.0.8) (2021-12-03)

## [14.0.8](https://github.com/compare/@sage/show-case@14.0.7...@sage/show-case@14.0.8) (2021-12-03)

# [14.0.7](https://github.com/compare/@sage/show-case@14.0.6...@sage/show-case@14.0.7) (2021-12-03)

## [14.0.7](https://github.com/compare/@sage/show-case@14.0.6...@sage/show-case@14.0.7) (2021-12-02)

# [14.0.6](https://github.com/compare/@sage/show-case@14.0.5...@sage/show-case@14.0.6) (2021-12-02)

## [14.0.6](https://github.com/compare/@sage/show-case@14.0.5...@sage/show-case@14.0.6) (2021-12-02)

# [14.0.5](https://github.com/compare/@sage/show-case@14.0.3...@sage/show-case@14.0.5) (2021-12-02)

## [14.0.5](https://github.com/compare/@sage/show-case@14.0.3...@sage/show-case@14.0.5) (2021-12-02)

# [14.0.4](https://github.com/compare/@sage/show-case@14.0.3...@sage/show-case@14.0.4) (2021-12-02)

# [14.0.3](https://github.com/compare/@sage/show-case@14.0.2...@sage/show-case@14.0.3) (2021-12-02)

## [14.0.3](https://github.com/compare/@sage/show-case@14.0.2...@sage/show-case@14.0.3) (2021-12-01)

# [14.0.2](https://github.com/compare/@sage/show-case@14.0.0...@sage/show-case@14.0.2) (2021-12-01)

## [14.0.2](https://github.com/compare/@sage/show-case@14.0.0...@sage/show-case@14.0.2) (2021-11-29)

# [14.0.1](https://github.com/compare/@sage/show-case@14.0.0...@sage/show-case@14.0.1) (2021-11-29)

# [14.0.0](https://github.com/compare/@sage/show-case@13.0.28...@sage/show-case@14.0.0) (2021-11-29)

## [14.0.0](https://github.com/compare/@sage/show-case@13.0.28...@sage/show-case@14.0.0) (2021-11-29)

# [13.0.28](https://github.com/compare/@sage/show-case@13.0.27...@sage/show-case@13.0.28) (2021-11-29)

## [13.0.28](https://github.com/compare/@sage/show-case@13.0.27...@sage/show-case@13.0.28) (2021-11-28)

# [13.0.27](https://github.com/compare/@sage/show-case@13.0.26...@sage/show-case@13.0.27) (2021-11-28)

## [13.0.27](https://github.com/compare/@sage/show-case@13.0.26...@sage/show-case@13.0.27) (2021-11-27)

# [13.0.26](https://github.com/compare/@sage/show-case@13.0.25...@sage/show-case@13.0.26) (2021-11-27)

## [13.0.26](https://github.com/compare/@sage/show-case@13.0.25...@sage/show-case@13.0.26) (2021-11-26)

# [13.0.25](https://github.com/compare/@sage/show-case@13.0.24...@sage/show-case@13.0.25) (2021-11-26)

## [13.0.25](https://github.com/compare/@sage/show-case@13.0.24...@sage/show-case@13.0.25) (2021-11-25)

# [13.0.24](https://github.com/compare/@sage/show-case@13.0.23...@sage/show-case@13.0.24) (2021-11-25)

## [13.0.24](https://github.com/compare/@sage/show-case@13.0.23...@sage/show-case@13.0.24) (2021-11-24)

# [13.0.23](https://github.com/compare/@sage/show-case@13.0.22...@sage/show-case@13.0.23) (2021-11-24)

## [13.0.23](https://github.com/compare/@sage/show-case@13.0.22...@sage/show-case@13.0.23) (2021-11-23)

# [13.0.22](https://github.com/compare/@sage/show-case@13.0.21...@sage/show-case@13.0.22) (2021-11-23)

## [13.0.22](https://github.com/compare/@sage/show-case@13.0.21...@sage/show-case@13.0.22) (2021-11-22)

# [13.0.21](https://github.com/compare/@sage/show-case@13.0.20...@sage/show-case@13.0.21) (2021-11-22)

## [13.0.21](https://github.com/compare/@sage/show-case@13.0.20...@sage/show-case@13.0.21) (2021-11-22)

# [13.0.20](https://github.com/compare/@sage/show-case@13.0.19...@sage/show-case@13.0.20) (2021-11-22)

## [13.0.20](https://github.com/compare/@sage/show-case@13.0.19...@sage/show-case@13.0.20) (2021-11-19)

# [13.0.19](https://github.com/compare/@sage/show-case@13.0.18...@sage/show-case@13.0.19) (2021-11-19)

## [13.0.19](https://github.com/compare/@sage/show-case@13.0.18...@sage/show-case@13.0.19) (2021-11-18)

# [13.0.18](https://github.com/compare/@sage/show-case@13.0.17...@sage/show-case@13.0.18) (2021-11-18)

## [13.0.18](https://github.com/compare/@sage/show-case@13.0.17...@sage/show-case@13.0.18) (2021-11-18)

# [13.0.17](https://github.com/compare/@sage/show-case@13.0.16...@sage/show-case@13.0.17) (2021-11-18)

## [13.0.17](https://github.com/compare/@sage/show-case@13.0.16...@sage/show-case@13.0.17) (2021-11-17)

# [13.0.16](https://github.com/compare/@sage/show-case@13.0.15...@sage/show-case@13.0.16) (2021-11-17)

## [13.0.16](https://github.com/compare/@sage/show-case@13.0.15...@sage/show-case@13.0.16) (2021-11-17)

### Features


# [13.0.15](https://github.com/compare/@sage/show-case@13.0.14...@sage/show-case@13.0.15) (2021-11-17)

### Features

* **upgrade:** XT-13097 Reset the schema rather than drop the database ([#3646](https://github.com/issues/3646))  ([b38591f](https://github.com/commit/b38591f75095cf90e8edf5f122319c3468c98489))

## [13.0.15](https://github.com/compare/@sage/show-case@13.0.14...@sage/show-case@13.0.15) (2021-11-14)

# [13.0.14](https://github.com/compare/@sage/show-case@13.0.13...@sage/show-case@13.0.14) (2021-11-14)

## [13.0.14](https://github.com/compare/@sage/show-case@13.0.13...@sage/show-case@13.0.14) (2021-11-13)

# [13.0.13](https://github.com/compare/@sage/show-case@13.0.12...@sage/show-case@13.0.13) (2021-11-13)

## [13.0.13](https://github.com/compare/@sage/show-case@13.0.12...@sage/show-case@13.0.13) (2021-11-12)

# [13.0.12](https://github.com/compare/@sage/show-case@13.0.11...@sage/show-case@13.0.12) (2021-11-12)

## [13.0.12](https://github.com/compare/@sage/show-case@13.0.11...@sage/show-case@13.0.12) (2021-11-11)

# [13.0.11](https://github.com/compare/@sage/show-case@13.0.10...@sage/show-case@13.0.11) (2021-11-11)

## [13.0.11](https://github.com/compare/@sage/show-case@13.0.10...@sage/show-case@13.0.11) (2021-11-10)

# [13.0.10](https://github.com/compare/@sage/show-case@13.0.9...@sage/show-case@13.0.10) (2021-11-10)

## [13.0.10](https://github.com/compare/@sage/show-case@13.0.9...@sage/show-case@13.0.10) (2021-11-09)

# [13.0.9](https://github.com/compare/@sage/show-case@13.0.8...@sage/show-case@13.0.9) (2021-11-09)

## [13.0.9](https://github.com/compare/@sage/show-case@13.0.8...@sage/show-case@13.0.9) (2021-11-09)

# [13.0.8](https://github.com/compare/@sage/show-case@13.0.7...@sage/show-case@13.0.8) (2021-11-09)

## [13.0.8](https://github.com/compare/@sage/show-case@13.0.7...@sage/show-case@13.0.8) (2021-11-09)

### Features


# [13.0.7](https://github.com/compare/@sage/show-case@13.0.6...@sage/show-case@13.0.7) (2021-11-09)

### Features

* **xtrem:** add package-lock to repo XT-4131 ([#3499](https://github.com/issues/3499))  ([b938168](https://github.com/commit/b9381689290ea49d5fb4994cdf9ab5e73fe0b4e3))

## [13.0.7](https://github.com/compare/@sage/show-case@13.0.6...@sage/show-case@13.0.7) (2021-11-08)

# [13.0.6](https://github.com/compare/@sage/show-case@13.0.5...@sage/show-case@13.0.6) (2021-11-08)

## [13.0.6](https://github.com/compare/@sage/show-case@13.0.5...@sage/show-case@13.0.6) (2021-11-07)

# [13.0.5](https://github.com/compare/@sage/show-case@13.0.4...@sage/show-case@13.0.5) (2021-11-07)

## [13.0.5](https://github.com/compare/@sage/show-case@13.0.4...@sage/show-case@13.0.5) (2021-11-06)

# [13.0.4](https://github.com/compare/@sage/show-case@13.0.3...@sage/show-case@13.0.4) (2021-11-06)

## [13.0.4](https://github.com/compare/@sage/show-case@13.0.3...@sage/show-case@13.0.4) (2021-11-05)

# [13.0.3](https://github.com/compare/@sage/show-case@13.0.2...@sage/show-case@13.0.3) (2021-11-05)

## [13.0.3](https://github.com/compare/@sage/show-case@13.0.2...@sage/show-case@13.0.3) (2021-11-04)

# [13.0.2](https://github.com/compare/@sage/show-case@13.0.1...@sage/show-case@13.0.2) (2021-11-04)

## [13.0.2](https://github.com/compare/@sage/show-case@13.0.1...@sage/show-case@13.0.2) (2021-11-03)

# [13.0.1](https://github.com/compare/@sage/show-case@13.0.0...@sage/show-case@13.0.1) (2021-11-03)

## [13.0.1](https://github.com/compare/@sage/show-case@13.0.0...@sage/show-case@13.0.1) (2021-11-03)

# [13.0.0](https://github.com/compare/@sage/show-case@12.0.35...@sage/show-case@13.0.0) (2021-11-03)

## [13.0.0](https://github.com/compare/@sage/show-case@12.0.35...@sage/show-case@13.0.0) (2021-11-02)

# [12.0.35](https://github.com/compare/@sage/show-case@12.0.34...@sage/show-case@12.0.35) (2021-11-02)

## [12.0.35](https://github.com/compare/@sage/show-case@12.0.34...@sage/show-case@12.0.35) (2021-10-31)

# [12.0.34](https://github.com/compare/@sage/show-case@12.0.33...@sage/show-case@12.0.34) (2021-10-31)

## [12.0.34](https://github.com/compare/@sage/show-case@12.0.33...@sage/show-case@12.0.34) (2021-10-30)

# [12.0.33](https://github.com/compare/@sage/show-case@12.0.32...@sage/show-case@12.0.33) (2021-10-30)

## [12.0.33](https://github.com/compare/@sage/show-case@12.0.32...@sage/show-case@12.0.33) (2021-10-29)

# [12.0.32](https://github.com/compare/@sage/show-case@12.0.30...@sage/show-case@12.0.32) (2021-10-29)

## [12.0.32](https://github.com/compare/@sage/show-case@12.0.30...@sage/show-case@12.0.32) (2021-10-28)

### Features


# [12.0.31](https://github.com/compare/@sage/show-case@12.0.30...@sage/show-case@12.0.31) (2021-10-28)

### Features


# [12.0.30](https://github.com/compare/@sage/show-case@12.0.29...@sage/show-case@12.0.30) (2021-10-28)

### Features

* **xtrem:** publish package-lock.json to GitHub XT-4131  ([#3305](https://github.com/issues/3305))  ([56ea800](https://github.com/commit/56ea800fbb72bc327fac3aeef2e9899e1be91f27))

## [12.0.30](https://github.com/compare/@sage/show-case@12.0.29...@sage/show-case@12.0.30) (2021-10-26)

# [12.0.29](https://github.com/compare/@sage/show-case@12.0.28...@sage/show-case@12.0.29) (2021-10-26)

## [12.0.29](https://github.com/compare/@sage/show-case@12.0.28...@sage/show-case@12.0.29) (2021-10-26)

# [12.0.28](https://github.com/compare/@sage/show-case@12.0.27...@sage/show-case@12.0.28) (2021-10-26)

## [12.0.28](https://github.com/compare/@sage/show-case@12.0.27...@sage/show-case@12.0.28) (2021-10-25)

# [12.0.27](https://github.com/compare/@sage/show-case@12.0.26...@sage/show-case@12.0.27) (2021-10-25)

## [12.0.27](https://github.com/compare/@sage/show-case@12.0.26...@sage/show-case@12.0.27) (2021-10-25)

# [12.0.26](https://github.com/compare/@sage/show-case@12.0.25...@sage/show-case@12.0.26) (2021-10-25)

## [12.0.26](https://github.com/compare/@sage/show-case@12.0.25...@sage/show-case@12.0.26) (2021-10-24)

# [12.0.25](https://github.com/compare/@sage/show-case@12.0.24...@sage/show-case@12.0.25) (2021-10-24)

## [12.0.25](https://github.com/compare/@sage/show-case@12.0.24...@sage/show-case@12.0.25) (2021-10-24)

# [12.0.24](https://github.com/compare/@sage/show-case@12.0.23...@sage/show-case@12.0.24) (2021-10-24)

## [12.0.24](https://github.com/compare/@sage/show-case@12.0.23...@sage/show-case@12.0.24) (2021-10-23)

# [12.0.23](https://github.com/compare/@sage/show-case@12.0.22...@sage/show-case@12.0.23) (2021-10-23)

## [12.0.23](https://github.com/compare/@sage/show-case@12.0.22...@sage/show-case@12.0.23) (2021-10-22)

# [12.0.22](https://github.com/compare/@sage/show-case@12.0.21...@sage/show-case@12.0.22) (2021-10-22)

## [12.0.22](https://github.com/compare/@sage/show-case@12.0.21...@sage/show-case@12.0.22) (2021-10-22)

# [12.0.21](https://github.com/compare/@sage/show-case@12.0.20...@sage/show-case@12.0.21) (2021-10-22)

## [12.0.21](https://github.com/compare/@sage/show-case@12.0.20...@sage/show-case@12.0.21) (2021-10-20)

# [12.0.20](https://github.com/compare/@sage/show-case@12.0.19...@sage/show-case@12.0.20) (2021-10-20)

## [12.0.20](https://github.com/compare/@sage/show-case@12.0.19...@sage/show-case@12.0.20) (2021-10-19)

# [12.0.19](https://github.com/compare/@sage/show-case@12.0.18...@sage/show-case@12.0.19) (2021-10-19)

## [12.0.19](https://github.com/compare/@sage/show-case@12.0.18...@sage/show-case@12.0.19) (2021-10-18)

# [12.0.18](https://github.com/compare/@sage/show-case@12.0.17...@sage/show-case@12.0.18) (2021-10-18)

## [12.0.18](https://github.com/compare/@sage/show-case@12.0.17...@sage/show-case@12.0.18) (2021-10-17)

# [12.0.17](https://github.com/compare/@sage/show-case@12.0.16...@sage/show-case@12.0.17) (2021-10-17)

## [12.0.17](https://github.com/compare/@sage/show-case@12.0.16...@sage/show-case@12.0.17) (2021-10-17)

# [12.0.16](https://github.com/compare/@sage/show-case@12.0.15...@sage/show-case@12.0.16) (2021-10-17)

## [12.0.16](https://github.com/compare/@sage/show-case@12.0.15...@sage/show-case@12.0.16) (2021-10-16)

# [12.0.15](https://github.com/compare/@sage/show-case@12.0.14...@sage/show-case@12.0.15) (2021-10-16)

## [12.0.15](https://github.com/compare/@sage/show-case@12.0.14...@sage/show-case@12.0.15) (2021-10-15)

# [12.0.14](https://github.com/compare/@sage/show-case@12.0.13...@sage/show-case@12.0.14) (2021-10-15)

## [12.0.14](https://github.com/compare/@sage/show-case@12.0.13...@sage/show-case@12.0.14) (2021-10-14)

# [12.0.13](https://github.com/compare/@sage/show-case@12.0.12...@sage/show-case@12.0.13) (2021-10-14)

## [12.0.13](https://github.com/compare/@sage/show-case@12.0.12...@sage/show-case@12.0.13) (2021-10-13)

# [12.0.12](https://github.com/compare/@sage/show-case@12.0.11...@sage/show-case@12.0.12) (2021-10-13)

## [12.0.12](https://github.com/compare/@sage/show-case@12.0.11...@sage/show-case@12.0.12) (2021-10-12)

# [12.0.11](https://github.com/compare/@sage/show-case@12.0.10...@sage/show-case@12.0.11) (2021-10-12)

## [12.0.11](https://github.com/compare/@sage/show-case@12.0.10...@sage/show-case@12.0.11) (2021-10-12)

# [12.0.10](https://github.com/compare/@sage/show-case@12.0.9...@sage/show-case@12.0.10) (2021-10-12)

## [12.0.10](https://github.com/compare/@sage/show-case@12.0.9...@sage/show-case@12.0.10) (2021-10-10)

# [12.0.9](https://github.com/compare/@sage/show-case@12.0.8...@sage/show-case@12.0.9) (2021-10-10)

## [12.0.9](https://github.com/compare/@sage/show-case@12.0.8...@sage/show-case@12.0.9) (2021-10-09)

# [12.0.8](https://github.com/compare/@sage/show-case@12.0.7...@sage/show-case@12.0.8) (2021-10-09)

## [12.0.8](https://github.com/compare/@sage/show-case@12.0.7...@sage/show-case@12.0.8) (2021-10-08)

# [12.0.7](https://github.com/compare/@sage/show-case@12.0.6...@sage/show-case@12.0.7) (2021-10-08)

## [12.0.7](https://github.com/compare/@sage/show-case@12.0.6...@sage/show-case@12.0.7) (2021-10-08)

# [12.0.6](https://github.com/compare/@sage/show-case@12.0.5...@sage/show-case@12.0.6) (2021-10-08)

## [12.0.6](https://github.com/compare/@sage/show-case@12.0.5...@sage/show-case@12.0.6) (2021-10-07)

# [12.0.5](https://github.com/compare/@sage/show-case@12.0.4...@sage/show-case@12.0.5) (2021-10-07)

## [12.0.5](https://github.com/compare/@sage/show-case@12.0.4...@sage/show-case@12.0.5) (2021-10-06)

# [12.0.4](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.4) (2021-10-06)

## [12.0.4](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.4) (2021-10-05)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [12.0.3](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.3) (2021-10-05)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [12.0.2](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.2) (2021-10-04)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [12.0.1](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.1) (2021-10-04)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@12.0.0) (2021-10-04)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.40](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.40) (2021-10-04)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.39](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.39) (2021-10-03)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.38](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.38) (2021-10-02)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.37](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.37) (2021-10-01)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.36](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.36) (2021-10-01)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.35](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.35) (2021-09-30)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.34](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.34) (2021-09-29)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.33](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.33) (2021-09-29)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.32](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.32) (2021-09-28)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes


### Features


# [11.0.31](https://github.com/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.31) (2021-09-27)

## [11.0.26](https://github.com/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-21)

## [11.0.25](https://github.com/compare/@sage/show-case@11.0.24...@sage/show-case@11.0.25) (2021-09-20)

## [11.0.24](https://github.com/compare/@sage/show-case@11.0.23...@sage/show-case@11.0.24) (2021-09-19)

## [11.0.23](https://github.com/compare/@sage/show-case@11.0.22...@sage/show-case@11.0.23) (2021-09-18)

## [11.0.22](https://github.com/compare/@sage/show-case@11.0.21...@sage/show-case@11.0.22) (2021-09-17)

## [11.0.21](https://github.com/compare/@sage/show-case@11.0.20...@sage/show-case@11.0.21) (2021-09-16)

## [11.0.20](https://github.com/compare/@sage/show-case@11.0.19...@sage/show-case@11.0.20) (2021-09-15)

## [11.0.19](https://github.com/compare/@sage/show-case@11.0.18...@sage/show-case@11.0.19) (2021-09-14)

## [11.0.18](https://github.com/compare/@sage/show-case@11.0.17...@sage/show-case@11.0.18) (2021-09-13)

## [11.0.17](https://github.com/compare/@sage/show-case@11.0.16...@sage/show-case@11.0.17) (2021-09-12)

## [11.0.16](https://github.com/compare/@sage/show-case@11.0.15...@sage/show-case@11.0.16) (2021-09-11)

## [11.0.15](https://github.com/compare/@sage/show-case@11.0.14...@sage/show-case@11.0.15) (2021-09-10)

## [11.0.14](https://github.com/compare/@sage/show-case@11.0.13...@sage/show-case@11.0.14) (2021-09-10)

## [11.0.13](https://github.com/compare/@sage/show-case@11.0.12...@sage/show-case@11.0.13) (2021-09-08)

## [11.0.12](https://github.com/compare/@sage/show-case@11.0.11...@sage/show-case@11.0.12) (2021-09-07)

## [11.0.11](https://github.com/compare/@sage/show-case@11.0.10...@sage/show-case@11.0.11) (2021-09-07)

## [11.0.10](https://github.com/compare/@sage/show-case@11.0.9...@sage/show-case@11.0.10) (2021-09-07)

## [11.0.9](https://github.com/compare/@sage/show-case@11.0.8...@sage/show-case@11.0.9) (2021-09-06)

## [11.0.8](https://github.com/compare/@sage/show-case@11.0.7...@sage/show-case@11.0.8) (2021-09-05)

## [11.0.7](https://github.com/compare/@sage/show-case@11.0.6...@sage/show-case@11.0.7) (2021-09-04)

## [11.0.6](https://github.com/compare/@sage/show-case@11.0.5...@sage/show-case@11.0.6) (2021-09-03)

## [11.0.5](https://github.com/compare/@sage/show-case@11.0.4...@sage/show-case@11.0.5) (2021-09-02)

## [11.0.4](https://github.com/compare/@sage/show-case@11.0.3...@sage/show-case@11.0.4) (2021-09-02)

## [11.0.3](https://github.com/compare/@sage/show-case@11.0.2...@sage/show-case@11.0.3) (2021-08-31)

## [11.0.2](https://github.com/compare/@sage/show-case@11.0.1...@sage/show-case@11.0.2) (2021-08-30)

## [11.0.1](https://github.com/compare/...@sage/show-case@11.0.1) (2021-08-30)

### Bug Fixes

* gnutls cve (XT-4049)  ([#471](https://github.com/issues/471))  ([a943273](https://github.com/commit/a9432735c6c873d635380db0ab494d344ee53be0))
* revert allow namespace cloning in docker images XT-4366 ([#858](https://github.com/issues/858))  ([df079da](https://github.com/commit/df079daa12b02b92ea8bdbbf26fccadaf2cc0784))
* version bump  ([be66252](https://github.com/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-999999 fix package versions ([#1780](https://github.com/issues/1780))  ([4bd5d4a](https://github.com/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-999999 bump minor versions for images (->7.1.x) ([#1088](https://github.com/issues/1088))  ([9089abc](https://github.com/commit/9089abc05ce9e9630213f655367ffe9089d60827))
* XT-999999 fixed version of packages ([#1089](https://github.com/issues/1089))  ([4030a1f](https://github.com/commit/4030a1f9edbf466ab080ebb6a8547643713cf439))
* **core:** XT-7069 metadata about ([#2165](https://github.com/issues/2165))  ([80ddd48](https://github.com/commit/80ddd4892e36db02486ed3c780605a44d60e848a))

# [11.0.30](https://github.com/Sage-ERP-X3/xtrem/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.30) (2021-09-27)

### Bug Fixes


### Features


# [11.0.29](https://github.com/Sage-ERP-X3/xtrem/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.29) (2021-09-26)

### Bug Fixes


### Features


# [11.0.28](https://github.com/Sage-ERP-X3/xtrem/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.28) (2021-09-25)

### Bug Fixes


### Features


# [11.0.27](https://github.com/Sage-ERP-X3/xtrem/compare/@sage/show-case@11.0.26...@sage/show-case@11.0.27) (2021-09-24)

# [11.0.26](https://github.com/Sage-ERP-X3/xtrem/compare/@sage/show-case@11.0.25...@sage/show-case@11.0.26) (2021-09-22)

### Bug Fixes

* gnutls cve (XT-4049)  ([#471](https://github.com/Sage-ERP-X3/xtrem/issues/471))  ([a943273](https://github.com/Sage-ERP-X3/xtrem/commit/a9432735c6c873d635380db0ab494d344ee53be0))
* revert allow namespace cloning in docker images XT-4366 ([#858](https://github.com/Sage-ERP-X3/xtrem/issues/858))  ([df079da](https://github.com/Sage-ERP-X3/xtrem/commit/df079daa12b02b92ea8bdbbf26fccadaf2cc0784))
* version bump  ([be66252](https://github.com/Sage-ERP-X3/xtrem/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-999999 fix package versions ([#1780](https://github.com/Sage-ERP-X3/xtrem/issues/1780))  ([4bd5d4a](https://github.com/Sage-ERP-X3/xtrem/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-999999 bump minor versions for images (->7.1.x) ([#1088](https://github.com/Sage-ERP-X3/xtrem/issues/1088))  ([9089abc](https://github.com/Sage-ERP-X3/xtrem/commit/9089abc05ce9e9630213f655367ffe9089d60827))
* XT-999999 fixed version of packages ([#1089](https://github.com/Sage-ERP-X3/xtrem/issues/1089))  ([4030a1f](https://github.com/Sage-ERP-X3/xtrem/commit/4030a1f9edbf466ab080ebb6a8547643713cf439))
* **core:** XT-7069 metadata about ([#2165](https://github.com/Sage-ERP-X3/xtrem/issues/2165))  ([80ddd48](https://github.com/Sage-ERP-X3/xtrem/commit/80ddd4892e36db02486ed3c780605a44d60e848a))

