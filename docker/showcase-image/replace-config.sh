#!/bin/sh

sedi() {
    # if it is macOS and not GNU sed, use the -i '' option
    if [ "$(uname)" = "Darwin" ] && ! sed --version >/dev/null 2>&1; then
      sed -i '' "$@"
    else
        sed -i "$@"
    fi
}

sed s/{DBUSER}/$PG_DBUSER/g xtrem-config-image.yml > xtrem-config.yml
sedi s/{DBPWD}/$PG_DBPWD/g xtrem-config.yml
sedi s/{SYSUSER}/$PG_SYSUSER/g xtrem-config.yml
sedi s/{SYSPWD}/$PG_SYSPWD/g xtrem-config.yml
