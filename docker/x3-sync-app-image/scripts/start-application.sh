#!/bin/sh

#===============================================================================
# WARNING:
#   This script aims to run both locally and in the docker image, so it has
#   to respect the bourne shell (sh) syntax and be able to run without npm
#===============================================================================
X3SYNC=$(which x3sync || echo -n 'pnpm run x3sync')

set -e

echo '===== start x3sync application   ====='
$X3SYNC &
pid=$!
trap 'kill $pid' EXIT

# try to connect with 5 retry
echo ""
for i in $(seq 1 5); do
    [ $i -gt 1 ] && sleep 5
    nc -zvw5 "127.0.0.1" 8240 && exit 0 || exitcode=$?;
    echo "Trying to connect to x3sync application (attempt $i)...$(exitcode)"
done;

exit "${exitcode}"
