#!/bin/sh

displayAsciiArt() {
  if [ -f /xtrem/app/ascii-art.txt ]; then
    # Display the ascii art from the file
    cat /xtrem/app/ascii-art.txt
    echo ""
  else
    # Font: graffiti
    cat << "EOF"
 __  __ _____   ____ __   __ _   _   ____
 \ \/ /|___ /  / ___|\ \ / /| \ | | / ___|
  \  /   |_ \  \___ \ \ V / |  \| || |
  /  \  ___) |  ___) | | |  | |\  || |___
 /_/\_\|____/  |____/  |_|  |_| \_| \____|

EOF
  fi
}

versionInfo() {
    # display CLI version
    appVersion=$(grep '"version":' /x3sync/app/package.json | sed 's/[",:]//g' | awk '{ print $2 }')
    appName=$( (grep 'app:' /x3sync/config/xtrem-config.yml 2>/dev/null) | cut -d: -f2 | tr -d ' ')
    if [ -n "$appName" ]; then
        appVersion="$appName $appVersion"
    fi

    echo "app:        $appVersion"
    echo "os:         $(grep PRETTY_NAME /etc/os-release | cut -d= -f 2 | tr -d \")"
    echo "node:       $(node -v | sed 's/v//')"
    echo "PostgreSQL: $( (psql --version | sed 's/psql (PostgreSQL) //' ) || echo "No PostgreSQL client available")"
    echo ""
}

initEnv() {
    # Default env value is local if not set before
    ${ENV:="local"}
    if [ "$NO_BANNER" != "true" ]; then
      echo "Env is set to $ENV"
    fi
    # Create symlinks on config files (xtrem-config.yml, xtrem-security.yml) to allow hot reload
    ln -s /x3sync/config/xtrem-config.yml /x3sync/app/xtrem-config.yml 2> /dev/null
    ln -s /x3sync/config/xtrem-security.yml /x3sync/app/xtrem-security.yml 2> /dev/null
    ln -s /x3sync/config/xtrem-ui.yml /x3sync/app/xtrem-ui.yml 2> /dev/null
}

if [ "$NO_BANNER" != "true" ]; then
  # Display the ascii art
  displayAsciiArt
  versionInfo
fi
initEnv

# Now exec the npm script or command system command
set -e

# if the command passed is not "xtrem args..." and it is not a known system command
# then we prepend "xtrem" to the command line.
# For example:
#   - "xtrem schema --create" will be executed as is
#   - "schema --create" will be replaced by "xtrem schema --create"
if [ "${1}" != "x3sync" ] && { [ "${1#-}" != "${1}" ] || [ -z "$(command -v "${1}")" ]; }; then
  set -- x3sync "$@"
fi
exec "$@"
