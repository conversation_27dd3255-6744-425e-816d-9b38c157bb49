# This multi-stage image can be build from this folder with:
# 1. create a .env file with:
#   AZURE_DEVOPS_TOKEN="******"
# 2. run:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --secret id=xtrem-env,src=.env -t x3sync .
#   on ci, use the plain progress:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --progress=plain --secret id=xtrem-env,src=.env -t x3sync .

ARG NODE_VERSION=22

# ==========================
# Base stage
# ==========================
FROM node:${NODE_VERSION}-alpine as base

ARG node_env="production"

ENV NODE_ENV=$node_env

# update system to get latest security fixes and install required tools for final image
# see https://github.com/puppeteer/puppeteer/blob/main/docs/troubleshooting.md for the puppeteer required packages on alpine system
RUN apk update && apk upgrade \
    && apk --no-cache --update add dumb-init ca-certificates postgresql-client \
    && rm -rf /var/cache/apk/* /tmp \
    && mkdir -p /tmp \
    && chmod 777 /tmp

# create a x3sync bootstrap script to be able to add V8 options like --max-semi-space-size for tuning GC
RUN echo '#!/bin/sh' > /usr/local/bin/x3sync \
    && echo 'exec node ${X3SYNC_V8_OPTIONS} /x3sync/app/node_modules/.bin/x3sync "$@"' >> /usr/local/bin/x3sync \
    && chmod 755 /usr/local/bin/x3sync

WORKDIR /x3sync/app


COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod 755 /usr/local/bin/docker-entrypoint.sh

RUN mkdir -p /home/<USER>/Downloads /x3sync/config

COPY --chown=node:node package.json .
COPY --chown=node:node pnpm-lock.yaml .

# ==========================
# Build stage
# ==========================
FROM base as appbuild

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN corepack enable \
    && corepack prepare $(node -p 'require("./package.json").packageManager.split("+")[0]') --activate
# configure npm to use the azure devops registry
RUN --mount=type=secret,id=xtrem-env \
    <<EOF
    . /run/secrets/xtrem-env
    npm config set "@sage:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "@sageai:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken" "${AZURE_DEVOPS_TOKEN}"
    npm config set progress false
EOF

RUN apk add --no-cache python3 make

RUN pnpm install --prod --lockfile-only --ignore-workspace && pnpm install --prod --frozen-lockfile --config.node-linker=hoisted \
    # override x3sync script created by pnpm which does not work
    && cp node_modules/@sage/xtrem-x3-sync/bin/x3sync node_modules/.bin/x3sync

# Cleanup to prevent aquasec from reporting false positive vulnerabilities:
# - private keys included in test folders
# - node-gyp test and typescript typing to reduce image
# - vulnerable jquery included in test folder of /@mixmark-io/domino
RUN rm -rf node_modules/public-encrypt/test node_modules/@mixmark-io/domino/test \
    && find node_modules/ -type d -name "node-gyp" -exec rm -rf {}/test \; \
    && find node_modules/ -name "*.d.ts" -exec rm -f {} \;

# ==========================
# Clean stage
# ==========================
FROM base as clean

# might come with vulnerabilities
RUN npm uninstall -g npm && rm -rf $PNPM_HOME /usr/local/lib/node_modules/ /tmp/* $HOME/.npm

# ==========================
# Final stage
# ==========================
FROM clean as final

# make the "en_US.UTF-8" locale so postgres client will be utf-8 enabled by default
# alpine doesn't require explicit locale-file generation
# see https://github.com/docker-library/postgres/blob/cc254e85ed86e1f8c9052f9cbf0e3320324f0421/Dockerfile-alpine.template#L49C1-L52C1
ENV LANG en_US.utf8

RUN chown -R node:node /x3sync/app /x3sync/config /home/<USER>/Downloads

USER node

COPY --chown=node:node --chmod=644 ascii-art.txt /x3sync/app/ascii-art.txt
COPY --from=appbuild --chown=node:node /x3sync/app/node_modules ./node_modules

VOLUME /x3sync/config

ENTRYPOINT ["/usr/bin/dumb-init", "--", "docker-entrypoint.sh"]

EXPOSE 8240

# Can be run for example with:
# $ sudo docker run -it --rm --name x3sync-app x3sync
CMD ["x3sync"]
