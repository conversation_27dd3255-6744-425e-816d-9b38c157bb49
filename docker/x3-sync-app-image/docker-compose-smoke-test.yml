services:
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    restart: always
    command: >
      -c max_locks_per_transaction=256
    environment:
      - POSTGRES_USER=${PG_SYSUSER}
      - POSTGRES_PASSWORD=${PG_SYSPWD}
  xtrem:
    image: x3sync:smoke-test
    command: >
      /x3sync/scripts/start-application.sh
    volumes:
      - ./:/x3sync/config
      - ./scripts:/x3sync/scripts
    depends_on:
      - pgdb
