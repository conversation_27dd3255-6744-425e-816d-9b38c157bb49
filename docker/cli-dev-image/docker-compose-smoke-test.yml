version: '3'
services:
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    restart: always
    command: >
      -c max_locks_per_transaction=256
    environment:
      - POSTGRES_USER=${PG_SYSUSER}
      - POSTGRES_PASSWORD=${PG_SYSPWD}
  xtrem:
    image: xtrem-cli-dev:smoke-test
    command: >
      /xtrem/scripts/smoke-test.sh
    volumes:
      - ./:/xtrem/config
      - ./scripts:/xtrem/scripts
    depends_on:
      - pgdb
