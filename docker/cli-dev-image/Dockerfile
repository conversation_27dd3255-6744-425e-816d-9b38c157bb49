# This multi-stage image can be build from this folder with:
# 1. create a .env file with:
#   AZURE_DEVOPS_TOKEN="******"
# 2. run:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --secret id=xtrem-env,src=.env -t xtrem .
#   on ci, use the plain progress:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --progress=plain --secret id=xtrem-env,src=.env -t xtrem .

ARG NODE_VERSION=22

# ==========================
# Base stage
# ==========================
FROM node:${NODE_VERSION}-alpine as base

ARG node_env="production"

ENV NODE_ENV=$node_env

# update system to get latest security fixes and install required tools for final image
# see https://github.com/puppeteer/puppeteer/blob/main/docs/troubleshooting.md for the puppeteer required packages on alpine system
RUN apk update && apk upgrade \
    && apk --no-cache --update add dumb-init g++ chromium nss freetype harfbuzz ca-certificates ttf-freefont postgresql-client \
    && rm -rf /var/cache/apk/* /tmp \
    && mkdir -p /tmp \
    && chmod 777 /tmp

# create a xtrem bootstrap script to be able to add V8 options like --max-semi-space-size for tuning GC
RUN echo '#!/bin/sh' > /usr/local/bin/xtrem \
    && echo 'node ${XTREM_V8_OPTIONS} /xtrem/app/node_modules/.bin/xtrem "$@"' >> /usr/local/bin/xtrem \
    && chmod 755 /usr/local/bin/xtrem

WORKDIR /xtrem/app

# for xtrem-reporting and chromium headless
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod 755 /usr/local/bin/docker-entrypoint.sh

RUN mkdir -p /xtrem/config /home/<USER>/Downloads

COPY --chown=node:node package.json .
COPY --chown=node:node pnpm-lock.yaml .
COPY --chown=node:node xtrem-config-json-schema.json .

# ==========================
# Build stage
# ==========================
FROM base as appbuild

# copy the patches to the image to have the same behavior as the CI and dev environment
COPY --chown=node:node patches ./patches

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN corepack enable \
    && corepack prepare $(node -p 'require("./package.json").packageManager.split("+")[0]') --activate
RUN --mount=type=secret,id=xtrem-env source /run/secrets/xtrem-env \
    && export NPMRC_LOCATION="$(npm config get userconfig)" \
    && echo "Writing to path: ${NPMRC_LOCATION}" \
    && echo "@sage:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/" >> ${NPMRC_LOCATION} \
    && echo "@sageai:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/" >> ${NPMRC_LOCATION} \
    && echo "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken=${AZURE_DEVOPS_TOKEN}" >> ${NPMRC_LOCATION} \
    && echo "always-auth=true" >> ${NPMRC_LOCATION} \
    && echo "progress=false" >> ${NPMRC_LOCATION}

RUN apk add --no-cache python3 make
RUN pnpm install --prod --frozen-lockfile --config.node-linker=hoisted \
    # override xtrem script created by pnpm which does not work
    && cp node_modules/@sage/xtrem-cli/bin/xtrem node_modules/.bin/xtrem

# Cleanup to prevent aquasec from reporting false positive vulnerabilities:
# - private keys included in test folders
# - node-gyp test and typescript typing to reduce image
# - vulnerable jquery included in test folder of /@mixmark-io/domino
RUN rm -rf node_modules/public-encrypt/test node_modules/@mixmark-io/domino/test \
    && find node_modules/ -type d -name "node-gyp" -exec rm -rf {}/test \; \
    && find node_modules/ -name "*.d.ts" -exec rm -f {} \;

# ==========================
# Clean stage
# ==========================
FROM base as clean

# might come with vulnerabilities
RUN npm uninstall -g npm && rm -rf $PNPM_HOME /usr/local/lib/node_modules/ /tmp/* $HOME/.npm

# ==========================
# Final stage
# ==========================
FROM clean as final

RUN chown -R node:node /xtrem/app /xtrem/config /home/<USER>/Downloads

USER node

COPY --from=appbuild --chown=node:node /xtrem/app/node_modules ./node_modules

VOLUME /xtrem/config

ENTRYPOINT ["/usr/bin/dumb-init", "--", "docker-entrypoint.sh"]

EXPOSE 8240

# Can be run for example with:
# $ sudo docker run -it --rm --name xtrem-app xtrem schema --create
# $ sudo docker run -it --rm --name xtrem-app xtrem tenant --init <base64TenantData>
CMD ["xtrem"]
