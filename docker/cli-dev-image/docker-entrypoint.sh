#!/bin/sh

displayAsciiArt() {
  # Font: graffiti https://patorjk.com/software/taag/#p=display&f=Graffiti&t=Xtrem%20CLI
  cat << "EOF"
____  ___ __                           ________                _________ .____    .___
\   \/  //  |________   ____   _____   \______ \   _______  __ \_   ___ \|    |   |   |
 \     /\   __\_  __ \_/ __ \ /     \   |    |  \_/ __ \  \/ / /    \  \/|    |   |   |
 /     \ |  |  |  | \/\  ___/|  Y Y  \  |    `   \  ___/\   /  \     \___|    |___|   |
/___/\  \|__|  |__|    \___  >__|_|  / /_______  /\___  >\_/    \______  /_______ \___|
      \_/                  \/      \/          \/     \/               \/        \/
EOF
}

versionInfo() {
    # display CLI version
    grep '"version":' /xtrem/app/package.json | sed 's/[",:]//g' | awk '{ print $1" "$2 }'
    echo ""
}

initEnv() {
    # Default env value is local if not set before
    ${ENV:="local"}
    echo "Env is set to $ENV"

    # Create symlinks on config files (xtrem-config.yml, xtrem-security.yml) to allow hot reload
    ln -s /xtrem/config/xtrem-config.yml /xtrem/app/xtrem-config.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-security.yml /xtrem/app/xtrem-security.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-ui.yml /xtrem/app/xtrem-ui.yml 2> /dev/null
}

displayAsciiArt
versionInfo
initEnv

# Now exec the pnpm script or command system command
set -e

# if the command passed is not "xtrem args..." and it is not a known system command
# then we prepend "xtrem" to the command line.
# For example:
#   - "xtrem schema --create" will be executed as is
#   - "schema --create" will be replaced by "xtrem schema --create"
if [ "${1}" != "xtrem" ] && { [ "${1#-}" != "${1}" ] || [ -z "$(command -v "${1}")" ]; }; then
  set -- xtrem "$@"
fi
exec "$@"
