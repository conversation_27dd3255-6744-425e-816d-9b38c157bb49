#!/bin/sh

#==============================================================================
# WARNING:
#   This script aims to run both locally and in the docker image, so it has
#   to respect the bourne shell (sh) syntax and be able to run without npm
#===============================================================================
XTREM=$(which xtrem || echo -n 'pnpm run xtrem')

set -e

tenant1Json='{"tenant":{"id":"Test1layer_9kPIOivyrx","name":"test1_xtrem_latest"},"customer":{"id":"Test1layer_9kPIOcust1","name":"customer1"},"adminUsers":[{"email":"<EMAIL>","firstName":"<PERSON>","lastName":"Doe","locale":"en-US"}]}'
tenant2Json='{"tenant":{"id":"Test2layer_9kPIOivyrx","name":"test2_xtrem_latest"},"customer":{"id":"Test2layer_9kPIOcust2","name":"customer2"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}]}'

# do not use gnu or openssl base64 because they are not available in the alpine image
tenant1=$(node -p "Buffer.from('$tenant1Json').toString('base64')")
tenant2=$(node -p "Buffer.from('$tenant2Json').toString('base64')")

echo '================================='
echo '===== create SQL schema     ====='
echo '================================='
$XTREM schema --create --reset-database
echo '================================='
echo '===== init tenant 1 (test)  ====='
echo '================================='
$XTREM tenant --layers=setup,test --init "$tenant1"
echo '================================='
echo '===== init tenant 2 (test)  ====='
echo '================================='
$XTREM tenant --layers=setup,test --init "$tenant2"

# we cannot reset a cluster having tenant with mixed layers
echo '================================='
echo '===== reset schema (test)   ====='
echo '================================='
$XTREM schema --reset --layers=setup,test
