#!/bin/sh

displayAsciiArt() {
  # Font: graffiti
  cat << "EOF"
        .__                      _____ .__
  ______|  |__    ____  ______ _/ ____\|  |    ____    ____ _______
 /  ___/|  |  \  /  _ \ \____ \\   __\ |  |   /  _ \  /  _ \\_  __ \
 \___ \ |   Y  \(  <_> )|  |_> >|  |   |  |__(  <_> )(  <_> )|  | \/
/____  >|___|  / \____/ |   __/ |__|   |____/ \____/  \____/ |__|
     \/      \/         |__|

EOF
}

versionInfo() {
    # display CLI version
    grep '"version":' /xtrem/app/package.json | sed 's/[",:]//g' | awk '{ print $1" "$2 }'
    echo ""
}

initEnv() {
    # Default env value is local if not set before
    ${ENV:="local"}
    echo "Env is set to $ENV"

    # Create symlinks on config files (xtrem-config.yml, xtrem-security.yml) to allow hot reload
    ln -s /xtrem/config/xtrem-config.yml /xtrem/app/xtrem-config.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-security.yml /xtrem/app/xtrem-security.yml 2> /dev/null
    ln -s /xtrem/config/xtrem-ui.yml /xtrem/app/xtrem-ui.yml 2> /dev/null
}

displayAsciiArt
versionInfo
initEnv

# Now exec the npm script or command system command
set -e

if [ "${1#-}" != "${1}" ] || [ -z "$(command -v "${1}")" ]; then
  set -- xtrem "$@"
fi

exec "$@"
