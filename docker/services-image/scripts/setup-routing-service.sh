#!/bin/sh

#===============================================================================
# WARNING:
#   This script aims to run both locally and in the docker image, so it has
#   to respect the bourne shell (sh) syntax and be able to run without npm
#===============================================================================
XTREM=$(which xtrem || echo -n 'pnpm run xtrem')

set -e

echo '===== create SQL schema ====='
$XTREM schema --create --reset-database
echo '===== init tenant 1     ====='

tenant1Json='{"tenant": {"id": "777777777777777777777", "name": "test_xtrem_latest"}, "customer": {"id": "777777777777777777777", "name": "customer1"}, "adminUsers": [{"email": "<EMAIL>", "firstName": "John", "lastName": "Doe", "locale": "en-US"}]}'

# do not use gnu or openssl base64 because they are not available in the alpine image
tenant1=$(node -p "Buffer.from('$tenant1Json').toString('base64')")

$XTREM manage --layers=setup,demo --init-tenant "$tenant1"
