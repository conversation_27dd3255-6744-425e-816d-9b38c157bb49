#!/bin/sh

#===============================================================================
# WARNING:
#   This script aims to run both locally and in the docker image, so it has
#   to respect the bourne shell (sh) syntax and be able to run without npm
#===============================================================================
XTREM=$(which xtrem || echo -n 'pnpm run xtrem')

set -e

tenant1Json='{"tenant":{"id":"tenant1____9kPIOivyrx","name":"test1_xtrem_latest"},"customer":{"id":"tenant1____9kPIOcust1","name":"customer1"},"adminUsers":[{"email":"<EMAIL>","firstName":"John","lastName":"Doe","locale":"en-US"}]}'
tenant2Json='{"tenant":{"id":"tenant2____9kPIOivyrx","name":"test2_xtrem_latest"},"customer":{"id":"tenant2____9kPIOcust2","name":"customer2"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}]}'

# do not use gnu or openssl base64 because they are not available in the alpine image
tenant1=$(node -p "Buffer.from('$tenant1Json').toString('base64')")
tenant2=$(node -p "Buffer.from('$tenant2Json').toString('base64')")

echo '================================='
echo '===== create SQL schema     ====='
echo '================================='
$XTREM schema --create --reset-database
echo '================================='
echo '===== init tenant 1 (demo)  ====='
echo '================================='
$XTREM tenant --layers=setup,demo --init "$tenant1"
echo '================================='
echo '===== init tenant 2 (qa)    ====='
echo '================================='
$XTREM manage --layers=setup,qa --init-tenant "$tenant2"

# we cannot reset a cluster having tenant with mixed layers, so we delete it here
echo '================================='
echo '===== delete tenant 2 (qa) ====='
echo '================================='
$XTREM tenant --delete 'tenant2____9kPIOivyrx'

echo '================================='
echo '===== init tenant 2 (demo)  ====='
echo '================================='
$XTREM tenant --layers=setup,demo --init "$tenant2"

echo '================================='
echo '===== delete bad tenant     ====='
echo '================================='
$XTREM manage --delete-tenant 'does-not-exist'

echo '================================='
echo '===== reset schema (demo)   ====='
echo '================================='
$XTREM schema --reset --layers=setup,demo
