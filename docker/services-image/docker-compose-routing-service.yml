version: '3'
services:
  xtrem:
    image: ghcr.io/sage-erp-x3/xtrem
    links:
      - auth
      - pgdb
      - sqs
    ports:
      - '8240:8240'
    extra_hosts:
      - 'connect.localhost.dev-sagextrem.com:**********'
    expose:
      - '8240'
    command: >
      /xtrem/scripts/start-application.sh
    volumes:
      - ./:/xtrem/config
      - ./scripts:/xtrem/scripts
    networks:
      dock_net:
        ipv4_address: **********
  auth:
    image: 'ghcr.io/sage-erp-x3/xtrem-deployment-authentication:master'
    environment:
      - CLIENT_ID=FOq1MBQM4VBWutTk2PQES7RoVN6lYMLm
      # This secret below is only usable with the sandbox environment, it isn't credential, no data
      # can be accessed with them.
      - CLIENT_SECRET=0cZboJUzFr_dz72QeRhSikQ2HFFOY9PaRTuue69Y4tBNO2rx8pR14Z_V9Hxad3rB
      - ACCESS_TOKEN_LIFETIME=15
      - LOCAL_DEV=true
      - XTREM_PORT=8240
      - MULTI_LOCAL_DEV=true
      - LOCAL_TENANTS=777777777777777777777
      - BASE_URL=http://connect.localhost.dev-sagextrem.com:8080
      - KNOWN_DOMAINS=*.dev-sagextrem.com
      - DEFAULT_LOCALE=${XTREM_LOCALE}
      - CUSTOMER_CLIENT_ID=xx
      - CUSTOMER_CLIENT_SECRET=xx
    ports:
      - '8080:8080'
    expose:
      - '8080'
    networks:
      dock_net:
        ipv4_address: **********
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    expose:
      - '5432'
    ports:
      - '0.0.0.0.:5432:5432'
    restart: always
    environment:
      - POSTGRES_USER=${PG_SYSUSER}
      - POSTGRES_PASSWORD=${PG_SYSPWD}
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      dock_net:
        ipv4_address: **********
  sqs:
    image: softwaremill/elasticmq
    ports:
      - '9324:9324'
      - '9325:9325'
    expose:
      - '9324'
      - '9325'
    volumes:
      - ../../elasticmq.conf:/opt/elasticmq.conf
    networks:
      dock_net:
        ipv4_address: **********
  notifications:
    image: ghcr.io/sage-erp-x3/xtrem
    links:
      - pgdb
      - sqs
    command: >
      /xtrem/scripts/start-notification-service.sh
    volumes:
      - ./:/xtrem/config
      - ./scripts:/xtrem/scripts
    environment:
      - AWS_ACCESS_KEY_ID=${XTREM_CACHE_READ_ONLY_KEY}
      - AWS_SECRET_ACCESS_KEY=${XTREM_CACHE_READ_ONLY_SECRET}
      - AWS_DEFAULT_REGION=
    networks:
      dock_net:
        ipv4_address: **********
networks:
  dock_net:
    ipam:
      driver: default
      config:
        - subnet: **********/16
volumes:
  pgdata:
