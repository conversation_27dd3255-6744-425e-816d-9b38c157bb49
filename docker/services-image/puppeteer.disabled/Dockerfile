# This multi-stage image can be build from this folder with:
# 1. create a .env file with:
#   AZURE_DEVOPS_TOKEN="******"
# 2. run:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --secret id=xtrem-env,src=.env -t xtrem .
#   on ci, use the plain progress:
#   $ DOCKER_BUILDKIT=1 docker build --pull --no-cache --progress=plain --secret id=xtrem-env,src=.env -t xtrem .

ARG PUPPETEER_VERSION=24.4.0

# ==========================
# Base stage
# ==========================
FROM ghcr.io/puppeteer/puppeteer:${PUPPETEER_VERSION} AS base

ARG node_env="production"

ENV NODE_ENV=$node_env

# switch to root user to install packages
USER root

# update system to get latest security fixes and install required tools for final image
RUN apt update && apt upgrade -y

# from https://github.com/docker-library/postgres/blob/cc254e85ed86e1f8c9052f9cbf0e3320324f0421/16/bookworm/Dockerfile#L52-L64
# make the "en_US.UTF-8" locale so postgres will be utf-8 enabled by default
RUN <<EOF
    set -e
    # if this file exists, we're likely in "debian:xxx-slim", and locales are thus being excluded so we need to remove that exclusion (since we need locales)
    if [ -f /etc/dpkg/dpkg.cfg.d/docker ]; then
        grep -q '/usr/share/locale' /etc/dpkg/dpkg.cfg.d/docker
        sed -ri '/\/usr\/share\/locale/d' /etc/dpkg/dpkg.cfg.d/docker
        ! grep -q '/usr/share/locale' /etc/dpkg/dpkg.cfg.d/docker
    fi
    apt-get update
    apt-get install -y --no-install-recommends locales; rm -rf /var/lib/apt/lists/*
    echo 'en_US.UTF-8 UTF-8' >> /etc/locale.gen
    locale-gen
    locale -a | grep 'en_US.utf8'
EOF

ENV LANG=en_US.utf8

RUN <<EOF
    set -e
    # debian 12 has only postgresql-client 15 available by default, we need postgresql-client 16 at least
    echo "deb http://apt.postgresql.org/pub/repos/apt $(grep VERSION_CODENAME /etc/os-release | awk -F= '{print $2}')-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -
    apt update

    # Install the required packages for canvas and postgresql-client
    apt --no-install-recommends --no-install-suggests -y install \
        libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev \
        postgresql-client-16

    # Install dumb-init to handle zombie processes
    DUMB_INIT_PKG=dumb-init_1.2.5_$(dpkg --print-architecture).deb
    wget https://github.com/Yelp/dumb-init/releases/download/v1.2.5/$DUMB_INIT_PKG
    apt install ./$DUMB_INIT_PKG
    rm ./$DUMB_INIT_PKG

    # create a xtrem bootstrap script to be able to add V8 options like --max-semi-space-size for tuning GC
    echo '#!/bin/sh' > /usr/local/bin/xtrem
    echo 'exec node ${XTREM_V8_OPTIONS} /xtrem/app/node_modules/.bin/xtrem "$@"' >> /usr/local/bin/xtrem
    chmod 755 /usr/local/bin/xtrem
EOF

WORKDIR /xtrem/app

# we do need to install chromium, it is already in the puppeteer image
ENV PUPPETEER_CACHE_DIR=/home/<USER>/.cache/puppeteer

COPY docker-entrypoint.sh /usr/local/bin/

RUN <<EOF
    set -e
    chmod 755 /usr/local/bin/docker-entrypoint.sh
    mkdir -p /home/<USER>/Downloads /xtrem/config
EOF

COPY --chown=pptruser:pptruser package.json .
COPY --chown=pptruser:pptruser pnpm-lock.yaml .
COPY --chown=pptruser:pptruser xtrem-config-json-schema.json .

# ==========================
# Build stage
# ==========================
FROM base AS appbuild

# copy the patches to the image to have the same behavior as the CI and dev environment
COPY --chown=pptruser:pptruser patches ./patches

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN <<EOF
    set -e
    corepack enable
    corepack prepare $(node -p 'require("./package.json").packageManager.split("+")[0]') --activate
EOF

# configure npm to use the azure devops registry
RUN --mount=type=secret,id=xtrem-env \
    <<EOF
    . /run/secrets/xtrem-env
    npm config set "@sage:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "@sageai:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken" "${AZURE_DEVOPS_TOKEN}"
    npm config set progress false
EOF

# for node-gyp to install canvas
RUN apt --no-install-recommends --no-install-suggests -y install python3 make

RUN <<EOF
    set -e
    pnpm install --prod --frozen-lockfile --config.node-linker=hoisted

    # Cleanup to prevent aquasec from reporting false positive vulnerabilities:
    # - private keys included in test folders
    # - node-gyp test and typescript typing to reduce image
    # - vulnerable jquery included in test folder of /@mixmark-io/domino
    rm -rf node_modules/public-encrypt/test node_modules/@mixmark-io/domino/test
    find node_modules/ -type d -name "node-gyp" -exec rm -rf {}/test \;
    find node_modules/ -name "*.d.ts" -exec rm -f {} \;
EOF

# ==========================
# Clean stage
# ==========================
FROM base AS clean

COPY --from=appbuild --chown=pptruser:pptruser /home/<USER>/.cache/puppeteer /home/<USER>/.cache/puppeteer

# might come with vulnerabilities
RUN <<EOF
    set -e
    npm uninstall -g npm
    rm -rf $PNPM_HOME /usr/local/lib/node_modules/ /tmp/* $HOME/.npm
EOF

# ==========================
# Final stage
# ==========================
FROM clean AS final

RUN chown -R pptruser:pptruser /xtrem/app /xtrem/config /home/<USER>/Downloads

USER pptruser

COPY --chown=pptruser:pptruser --chmod=644 puppeteer/ascii-art.txt /xtrem/app/ascii-art.txt
COPY --from=appbuild --chown=pptruser:pptruser /xtrem/app/node_modules ./node_modules

VOLUME /xtrem/config

ENTRYPOINT ["/usr/bin/dumb-init", "--", "docker-entrypoint.sh"]

EXPOSE 8240

# Can be run for example with:
# $ sudo docker run -it --rm --name xtrem-app xtrem schema --create
# $ sudo docker run -it --rm --name xtrem-app xtrem tenant --init <base64TenantData>
CMD ["xtrem"]
