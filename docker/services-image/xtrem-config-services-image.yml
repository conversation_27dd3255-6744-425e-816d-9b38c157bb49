deploymentMode: production

sql:
  user: {DBUSER}
  password: {DBPWD}
  hostname: "pgdb"
  database: xtrem0
  port: 5432
  folderName: xtrem0
  schemaName: xtrem0
  sysUser: {SYSUSER}
  sysPassword: {SYSPWD}
  sysDatabase: postgres
interop:
  queues:
    intacctReceive:
      url: 'http://**********:9324/queue/unit-test-message-queue.fifo'
  routingPollingSeconds: 5
  routingReadCount: 3
  receiveRetryCount: 3
  receiveRetrySeconds: 1
  concurrentNotificationsLimit: 10
  concurrentMessagesLimit: 15
