(async () => {
    try {
        // try to connect on localhost server
        await require('oracledb').getConnection({ connectString: 'localhost/foo', user: 'bar', password: 'foobar' });
        // we should not be able to reach that point
        process.exit(1);
    } catch (e) {
        console.log('error:', e.message);
        if (/(ORA-12541|NJS-503):/.test(e.message)) {
            // ORA-12541: TNS:no listener
            // NJS-503: connection to host 127.0.0.1 port 1521 could not be established.
            // If we have these errors, it means we can at least load the oracle libraries
            console.log('Expected error received');
            process.exit(0);
        }
        // we've got another error like 'DPI-1047: Cannot locate a 64-bit Oracle Client library'
        console.log('Unexpected error received');
        process.exit(1);
    }
})();
