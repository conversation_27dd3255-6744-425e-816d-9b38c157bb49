#!/bin/sh

displayAsciiArt() {
  # Font: graffiti
  cat << "EOF"
 __   ___       _____
\ \ / / |     |____ |
 \ V /| |_ _ __   / /_ __ ___
 /   \| __| '__|  \ \ '_ ` _ \
/ /^\ \ |_| | .___/ / | | | | |
\/   \/\__|_| \____/|_| |_| |_|

EOF
}

versionInfo() {
    # display CLI version
    grep '"version":' /xtrem/${TARGET_APP}/package.json | sed 's/[",:]//g' | awk '{ print $1" "$2 }'
    echo ""
}

initEnv() {
    # Default env value is local if not set before
    ${ENV:="local"}
    echo "Env is set to $ENV"

    # # Create symlinks on config files (xtrem-config.yml, xtrem-security.yml) to allow hot reload
    # ln -s /xtrem/config/xtrem-config.yml /xtrem/xtrem-config.yml 2> /dev/null
    # ln -s /xtrem/config/xtrem-security.yml /xtrem/xtrem-security.yml 2> /dev/null
    # ln -s /xtrem/config/xtrem-ui.yml /xtrem/xtrem-ui.yml 2> /dev/null
    # ln -s /xtrem/add-ons /xtrem/app/add-ons 2> /dev/null

    ln -s /xtrem/config/xtrem-config.yml /xtrem/services/main/xtrem-services-main/xtrem-config.yml
    ln -s /xtrem/config/xtrem-security.yml /xtrem/services/main/xtrem-services-main/xtrem-security.yml
    ln -s /xtrem/config/xtrem-ui.yml /xtrem/services/main/xtrem-services-main/xtrem-ui.yml

    ls /xtrem/services/main/xtrem-main-services/
}

displayAsciiArt
versionInfo
initEnv

# Now exec the npm script or command system command
set -e

# if the command passed is not "xtrem args..." and it is not a known system command
# then we prepend "xtrem" to the command line.
# For example:
#   - "xtrem schema --create" will be executed as is
#   - "schema --create" will be replaced by "xtrem schema --create"
if [ "${1}" != "xtrem" ] && { [ "${1#-}" != "${1}" ] || [ -z "$(command -v "${1}")" ]; }; then
  set -- xtrem "$@"
fi

exec "$@"
