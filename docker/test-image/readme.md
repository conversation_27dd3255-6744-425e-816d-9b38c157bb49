# Test-Image

this test image must be built using [this pipeline](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1284)

you need to create a text file named `api-keys.yml` under `docker/test-image` folder
this file should contain:

```
XTREM_INTACCT_SENDER_PASSWORD=#See Keeper: Intacct / XT_MFG Sender ID
XTREM_SERVICE_FABRIC_SIGNING_KEY=#See Keeper: Sage Service Fabric / Sandbox key for dev & qa
XTREM_MAILER_API_KEY=#See Keeper: Mailer
XTREM_SHOPIFY_PRIVATE_API_KEY=#See Keeper: Shopify / Shopify Keys
```

and run from the root folder using `[XTREM_TESTER_EMAIL="<EMAIL>"] [XTREM_2ND_TESTER_EMAIL="<EMAIL>"] [XTREM_ADMIN_EMAIL="<EMAIL>"] [XTREM_TARGET_APP="platform"] [XTREM_LOCALE="fr-FR"] [XTREM_RESET_DATA="true"] [XTREM_DATA_LAYER="demo"] [XTREM_HAS_AUTHORIZATION="true"] ./run-test-image.sh <IMAGE_TAG>`

where:

-   `<IMAGE_TAG>` must be specified and will probably be a jira ticket number
-   `[XTREM_TESTER_EMAIL]` must (for now) be specified as an email address `<EMAIL>` => defaults to `<EMAIL>`
-   `[XTREM_2ND_TESTER_EMAIL]` can be specified as an email address `<EMAIL>` => no defaults
-   `[XTREM_ADMIN_EMAIL]` can be specified as an email address `<EMAIL>` => defaults to `<EMAIL>`
-   `[XTREM_TARGET_APP]` can be specified as one of `services` | `platform` | `tools` | `x3` => defaults to `services`
-   `[XTREM_LOCALE]` can be specified as a locale string such as `fr-FR` => defaults to `en-US`
-   `[XTREM_DATA_LAYER]` can be specified as a layer name such as `demo` => defaults to `test`
-   `[XTREM_RESET_DATA]` can be specified a boolean (`true` | `false`) => defaults to `false`
-   `[XTREM_HAS_AUTHORIZATION]` can be specified a boolean (`true` | `false`) => defaults to `false`

here are some examples:

`XTREM_TESTER_EMAIL="<EMAIL>" XTREM_2ND_TESTER_EMAIL="<EMAIL>" XTREM_ADMIN_EMAIL="<EMAIL>" XTREM_RESET_DATA="true" XTREM_HAS_AUTHORIZATION="true" ./run-test-image.sh XT-XXXX`
which will start the test environnement using the image XT-XXXX, re-create the DB, having the 3 user created, the authorization will be activated, the application started will be SIM (services) and the locale of the user loaded will be en-US

`XTREM_TESTER_EMAIL="<EMAIL>" ./run-test-image.sh XT-XXXX`
which will start the test environnement using the image XT-XXXX, the application started will be SIM (services) and the locale of the user loaded will be en-US

`XTREM_TESTER_EMAIL="<EMAIL>" XTREM_ADMIN_EMAIL="<EMAIL>" XTREM_RESET_DATA="true" ./run-test-image.sh XT-XXXX`
which will start the test environnement using the image XT-XXXX, re-create the DB, having the 2 user created, the application started will be SIM (services) and the locale of the user loaded will be en-US

`XTREM_TESTER_EMAIL="<EMAIL>" XTREM_TARGET_APP="platform" ./run-test-image.sh XT-XXXX`
which will start the test environnement using the image XT-XXXX, re-create the DB, having 1 user created, the application started will be show-case (platform) and the locale of the user loaded will be en-US
