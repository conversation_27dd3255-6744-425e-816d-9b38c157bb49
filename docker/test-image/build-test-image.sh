#!/usr/bin/env bash

set -e

error() {
    echo ""
    echo "!!! ERROR !!! - $1: $2"
    echo ""
    exit 1
}

info() {
    echo ""
    echo ">>> $1"
    echo ""
}

while (( "$#" )); do
  case "$1" in
    --image-name)
        IMAGE_NAME="$2"
        shift 2
      ;;
    --jira)
        JIRA_TICKET="$2"
        shift 2
      ;;
    --app)
        TARGET_APP="$2"
        shift 2
      ;;
    --env)
        DOCKER_ENV_SOURCE="$2"
        shift 2
      ;;
    *)
      shift
      ;;
  esac
done

REPO_ROOT_PATH=${REPO_ROOT_PATH:-"$(git rev-parse --show-toplevel)"}
export REPO_ROOT_PATH
# default target to "branch"
DOCKER_BUILD_TARGET=${DOCKER_BUILD_TARGET:-"branch"}
GIT_FEATURE_BRANCH=${GIT_FEATURE_BRANCH:=$(git rev-parse --abbrev-ref HEAD)}

if [[ -z "${DOCKER_ENV_SOURCE}" && ! -f .env ]]; then
  error "missing env" "Please create a .env file or set DOCKER_ENV_SOURCE variable to the source path"
fi
if [[ -n "${DOCKER_ENV_SOURCE}" ]]; then
  ln -s "${DOCKER_ENV_SOURCE}" .env
fi

# copy the json schema file to the build context
if [ -f "${REPO_ROOT_PATH}"/xtrem-config-json-schema.json ]; then
  cp "${REPO_ROOT_PATH}"/xtrem-config-json-schema.json .
fi

if [[ "${DOCKER_BUILD_TARGET}" == "branch" ]]; then

  if [[ -z "${IMAGE_NAME}" ]]; then
      error "missing variable" "Please set IMAGE_NAME variable to docker image name"
  fi

  if [[ -z "${TARGET_APP}" ]]; then
      error "missing variable" "Please set TARGET_APP variable to target app (platform, services, tools, x3)"
  fi

  if [[ -z "${JIRA_TICKET}" ]]; then
      error "missing variable" "Please set JIRA_TICKET variable to jira ticket number of this feature"
  fi

  if [[ -z "${GIT_FEATURE_BRANCH}" ]]; then
      error "missing variable" "Please set GIT_FEATURE_BRANCH variable to this feature branch"
  fi
fi

NODE_VERSION=$(awk -F '.' '{ print $1"."$2 }' < "$REPO_ROOT_PATH"/.nvmrc)

# default alpine version to the arg of Dockerfile
ALPINE_VERSION=${ALPINE_VERSION:-$(grep "ARG ALPINE_VERSION=" Dockerfile | head -1 | awk -F "=" '{ print $2 }')}

systemDependenciesImage="ghcr.io/sage-erp-x3/xtrem-test-image-azure:system-dependencies-${ALPINE_VERSION}"

pushImage() {
    set -e
    imageName="$1"
    if [[ -z "${imageName}" ]]; then
        error "missing image name" "Please provide an image name to push"
    fi
    # check if the image name starts with ghcr.io/sage-erp-x3/
    if [[ "${imageName}" != ghcr.io/sage-erp-x3/* ]]; then
        error "invalid image name" "Image name must start with ghcr.io/sage-erp-x3/"
    fi
    if [[ "${DOCKER_SKIP_PUSH}" != "1" ]]; then
        info "pushing image ${imageName}"
        docker push "${imageName}"
    else
        info "skipping push for image ${imageName}"
    fi
}

buildAndPushSystemDependencies() {
    set -e
    info "building system-dependencies image ${systemDependenciesImage}"
    docker build \
        --build-arg="NODE_VERSION=${NODE_VERSION}" \
        --build-arg ALPINE_VERSION="${ALPINE_VERSION}" \
        --target system-dependencies-stage \
        -t "${systemDependenciesImage}" .

    info "pushing system-dependencies image ${systemDependenciesImage}"
    if [[ "${DOCKER_SKIP_PUSH}" != "1" ]]; then
        pushImage "${systemDependenciesImage}"
    fi
}

buildAndPushBranch() {
  set -e
  info "building ${TARGET_APP} image ${IMAGE_NAME}:${JIRA_TICKET} from ${GIT_FEATURE_BRANCH}"
  DOCKER_BUILDKIT=1 docker build --secret id=xtrem-env,src=.env \
      --cache-from="${systemDependenciesImage}" \
      --no-cache \
      --build-arg ALPINE_VERSION="${ALPINE_VERSION}" \
      --build-arg GIT_FEATURE_BRANCH="${GIT_FEATURE_BRANCH}" \
      --build-arg TARGET_APP="${TARGET_APP}" \
      --target branch \
       --progress=plain \
      -t "${IMAGE_NAME}:${JIRA_TICKET}" .

  if [[ "${DOCKER_SKIP_PUSH}" != "1" ]]; then
      pushImage "${IMAGE_NAME}:${JIRA_TICKET}"
  fi

  if [[ -n ${SECONDARY_TAG} ]]; then
    docker tag "${IMAGE_NAME}:${JIRA_TICKET}" "${IMAGE_NAME}:${SECONDARY_TAG}"
    if [[ "${DOCKER_SKIP_PUSH}" != "1" ]]; then
        pushImage "${IMAGE_NAME}:${SECONDARY_TAG}"
    fi
  fi
}

set +e
if [[ "${DOCKER_BUILD_TARGET}" != "branch" ]]; then
  case ${DOCKER_BUILD_TARGET} in
    "system-dependencies")
        buildAndPushSystemDependencies
        ;;
    *)
        error "unknown target" "${DOCKER_BUILD_TARGET}"
  esac
  exit $?
fi

info "checking system-dependencies image ${systemDependenciesImage}"
set +e
if ! docker pull "${systemDependenciesImage}"; then
    buildAndPushSystemDependencies
fi


buildAndPushBranch

info "local images"
docker images
