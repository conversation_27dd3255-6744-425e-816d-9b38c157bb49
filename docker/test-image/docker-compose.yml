version: '3'
services:
  xtrem:
    image: 'ghcr.io/sage-erp-x3/xtrem-test-image-azure:${XTREM_TAG}'
    volumes:
      - ../../xtrem-config-test-image.yml:/opt/xtrem/xtrem-config-template.yml
      - ../../xtrem-security-test-image.yml:/opt/xtrem/xtrem-security.yml
      - ./api-keys.yml:/api-keys.yml
      - ./scripts:/opt/xtrem/.scripts
    links:
      - auth
      - db
      - sqs
    environment:
      - NODE_ENV=production
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=eu-west-1
      - XTREM_BASE64_INIT_TOKEN=${XTREM_BASE64_INIT_TOKEN}
      - XTREM_TESTER_EMAIL=${XTREM_TESTER_EMAIL}
      - XTREM_2ND_TESTER_EMAIL=${XTREM_2ND_TESTER_EMAIL}
      - XTREM_ADMIN_EMAIL=${XTREM_ADMIN_EMAIL}
      - XTREM_CLI_PATH=${XTREM_CLI_PATH}
      - XTREM_RESET_DATA=${XTREM_RESET_DATA}
      - XTREM_DATA_LAYER=${XTREM_DATA_LAYER}
      - XTREM_SERVICES_TO_START=${XTREM_SERVICES_TO_START}
    command: >
      sh /opt/xtrem/.scripts/start-xtrem.sh
    ports:
      - '8241:8241'
    extra_hosts:
      - 'connect.localhost.dev-sagextrem.com:**********'
    networks:
      dock_net:
        ipv4_address: **********
    security_opt:
      - seccomp:chrome.json
  auth:
    image: 'ghcr.io/sage-erp-x3/xtrem-deployment-authentication:master'
    environment:
      - CLIENT_ID=FOq1MBQM4VBWutTk2PQES7RoVN6lYMLm
      # This secret below is only usable with the sandbox environment, it isn't credential, no data
      # can be accessed with them.
      - CLIENT_SECRET=0cZboJUzFr_dz72QeRhSikQ2HFFOY9PaRTuue69Y4tBNO2rx8pR14Z_V9Hxad3rB
      - ACCESS_TOKEN_LIFETIME=15
      - LOCAL_DEV=true
      - XTREM_PORT=8241
      - BASE_URL=http://connect.localhost.dev-sagextrem.com:8080
      - KNOWN_DOMAINS=*.dev-sagextrem.com
      - DEFAULT_LOCALE=${XTREM_LOCALE}
      - CUSTOMER_CLIENT_ID=xx
      - CUSTOMER_CLIENT_SECRET=xx
      - AWS_DEFAULT_REGION=eu-west-1
      - LOCAL_SESSION=true
    ports:
      - '8080:8080'
    expose:
      - '8080'
    networks:
      dock_net:
        ipv4_address: **********
  db:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    environment:
      - POSTGRES_PASSWORD=mypassword
      - PGPORT=5433
    command: -c max_locks_per_transaction=256
    ports:
      - '5433:5433'
    networks:
      dock_net:
        ipv4_address: **********
  sqs:
    image: 'softwaremill/elasticmq-native:1.5.4'
    volumes:
      - ../../elasticmq.conf:/opt/elasticmq.conf
    networks:
      dock_net:
        ipv4_address: **********
  xtrem-async:
    image: 'ghcr.io/sage-erp-x3/xtrem-test-image-azure:${XTREM_TAG}'
    links:
      - db
      - sqs
      - xtrem
    depends_on:
      - xtrem
    volumes:
      - ../../xtrem-config-test-image.yml:/opt/xtrem/xtrem-config-template.yml
      - ../../xtrem-security-test-image.yml:/opt/xtrem/xtrem-security.yml
      - ./api-keys.yml:/api-keys.yml
      - ./scripts:/opt/xtrem/.scripts
    environment:
      - NODE_ENV=production
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=eu-west-1
      - XTREM_ADMIN_EMAIL=${XTREM_ADMIN_EMAIL}
      - XTREM_CLI_PATH=${XTREM_CLI_PATH}
    command: >
      sh /opt/xtrem/.scripts/start-xtrem-async.sh
    ports:
      - '58241:8241'
    networks:
      dock_net:
        ipv4_address: **********
    security_opt:
      - seccomp:chrome.json
networks:
  dock_net:
    ipam:
      driver: default
      config:
        - subnet: **********/16
