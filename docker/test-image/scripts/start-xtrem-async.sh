#!/bin/sh

# new xtrem image (> 20.0.30) no longer includes npm but have an xtrem command in the path.
# The issue is that current images still have npm but not the xtrem command then it that case we still have to use npm.
# This is a temporary state until the images are pushed without npm.
XTREM=$(which xtrem || echo -n 'pnpm run xtrem')

echo '===== init config file ====='
/opt/scripts/manage-config-template.sh
cd ${XTREM_CLI_PATH}

echo '===== waiting for services ====='
/opt/scripts/waitlive.sh "xtrem:8241"

echo '===== starting async services ====='
$XTREM start --channels=routing,listeners --services=communication,scheduler
