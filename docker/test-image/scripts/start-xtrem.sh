#!/bin/sh

# new xtrem image (> 20.0.30) no longer includes npm but have an xtrem command in the path.
# The issue is that current images still have npm but not the xtrem command then in that case we still have to use npm.
# This is a temporary state until the images are pushed without npm.
XTREM=$(which xtrem || echo -n 'pnpm run xtrem')

echo "===== init config file ====="
/opt/scripts/manage-config-template.sh

cd ${XTREM_CLI_PATH}
if [ "${XTREM_RESET_DATA}" = "true" ]; then 
    echo '===== prepare CSV data ====='
    echo '"<EMAIL>";"<EMAIL>";"Application";"tester";"Y";;"application";"N"' \
        | sed "s/<EMAIL>/$XTREM_TESTER_EMAIL/g" \
        >> /opt/xtrem/platform/system/xtrem-system/data/layers/setup/user.csv;
    if [[ "${XTREM_2ND_TESTER_EMAIL}" != "" ]]; then
        echo '"<EMAIL>";"<EMAIL>";"Application";"testerAdmin";"Y";;"application";"Y"' \
            | sed "s/<EMAIL>/$XTREM_2ND_TESTER_EMAIL/g" \
            >> /opt/xtrem/platform/system/xtrem-system/data/layers/setup/user.csv;
    fi
    echo '===== create SQL schema ====='
    $XTREM schema --create --reset-database
    echo "===== init tenant 1 ====="
    $XTREM tenant --layers=setup,${XTREM_DATA_LAYER} --init "${XTREM_BASE64_INIT_TOKEN}"
fi
echo "===== starting services ====="
$XTREM start --channels=graphql,listeners --services=${XTREM_SERVICES_TO_START}
