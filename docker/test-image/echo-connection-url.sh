#!/usr/bin/env bash

docker/test-image/waitlive.sh "localhost:58241"

ACCOUNTS="('$XTREM_TESTER_EMAIL'"

if [[ "${XTREM_2ND_TESTER_EMAIL}" != "" ]]; then
    ACCOUNTS="$ACCOUNTS, '$XTREM_2ND_TESTER_EMAIL'";
fi

if [[ "$XTREM_ADMIN_EMAIL" != "<EMAIL>" ]]; then
    ACCOUNTS="$ACCOUNTS, '$XTREM_ADMIN_EMAIL'";
fi

ACCOUNTS="$ACCOUNTS)"

echo ""
echo ""
echo ""
echo ""
echo ""
echo "-------------------------------| IMPORTANT |--------------------------------------"
echo ""
echo "   THE TEST APP IS AVAILABLE AT: http://xtrem.localhost.dev-sagextrem.com:8241/"
echo "   USE ONE OF THESE TEST USER ACCOUNT: $ACCOUNTS"
echo ""
echo "---------------------------------------------------------------------------------- "
echo ""
echo ""
echo ""
echo ""
echo ""
