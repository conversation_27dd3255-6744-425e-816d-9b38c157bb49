#!/usr/bin/env bash
CONFIG_FILE="/api-keys"
KEY_LIST=("XTREM_INTACCT_SENDER_PASSWORD" "XTREM_SERVICE_FABRIC_SIGNING_KEY" "XTREM_MAILER_API_KEY" "XTREM_SHOPIFY_PRIVATE_API_KEY")
CONFIG_DATA=()
PARSED_KEY=""

parseKey() {
    key=$1
    # loop though the CONFIG_DATA lines to find the correspondig key then extracts it value
    for i in "${CONFIG_DATA[@]}"; do
        GREPED_KEY=`echo $i | grep $key`
        if [[ ! "$GREPED_KEY" = "" ]]; then
            PARSED_KEY=`echo $GREPED_KEY | sed 's/'"$key"'=//'`
        fi
    done
}

replaceKeyInTemplate() {
    key=$1
    # parse the key from the CONFIG_DATA array
    parseKey $key
    # replace the template key by the value from the config
    sed -i 's/{'"$key"'}/'"$PARSED_KEY"'/' xtrem-config.yml
    unset PARSED_KEY
}

# read the the CONFIG_FILE as the CONFIG_DATA array 
mapfile -t CONFIG_DATA < $CONFIG_FILE;

# replace the XTREM_ADMIN_EMAIL template key by the value from the env
sed 's/{XTREM_ADMIN_EMAIL}/'"$XTREM_ADMIN_EMAIL"'/' xtrem-config-template.yml > xtrem-config.yml

# parse all key for the KEY_LIST in the CONFIG_DATA and replace the template key by the PARSED_KEY data
for i in "${KEY_LIST[@]}"; do
    replaceKeyInTemplate $i
done
