{
    "atp-graphql-execution": {
        "scope": "feature",
        "prefix": "atp-graphql-execution",
        "body": [
            "Given the user opens the application on a ${1|HD desktop,desktop,tablet,mobile|} using the following link: \"explorer/\"",
            "And the user writes \"${2}\" GraphQL ${3|request,mutation|}",
            "When the user clicks the \"Run\" button in the GraphQL page header",
            "And the user attaches the actual GraphQL response to allure report",
        ],
        "description": "Redirect to GraphQL interface, write GraphQL query/mutation, execute it, and attach the response",
    },
    "atp-graphql-control-response": {
        "scope": "feature",
        "prefix": "atp-graphql-control-response",
        "body": [
            "And the user selects the \"${1}\" GraphQL property",
            "Then the selected GraphQL property value is \"${2}\"",
            "And the selected GraphQL property value contains \"${3}\"",
            "Then the selected GraphQL property value is not \"${4}\"",
            "And the selected GraphQL property value is ${5|greater than,lower than,equal to|} \"${6}\"",
            "And the selected GraphQL property value ${7|is,is not,contains|}",
            "\t\"\"\"",
            "\t${8}",
            "\t\"\"\"",
        ],
        "description": "Verify GraphQL response, select properties, and control results with exact match, contains, not equals, and comparison operations",
    },
    "atp-graphql-control-response-validity": {
        "scope": "feature",
        "prefix": "atp-graphql-control-response-validity",
        "body": ["Then the \"${1}\" GraphQL response is valid"],
        "description": "Control GraphQL response validity",
    },
}
