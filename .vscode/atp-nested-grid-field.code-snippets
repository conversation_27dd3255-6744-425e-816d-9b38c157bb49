{
    "atp-step-nested-grid-row-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user writes \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested ${9|date,dropdown-list,numeric,reference,select,text|} field of the selected row in the nested grid field",
            "Then the value of the \"${7}\" ${8|bound,labelled|} nested ${9|checkbox,date,dropdown-list,label,numeric,progress,reference,select,text|} field of the selected row in the nested grid field is \"${6}\"",
        ],
        "description": "Select a nested grid, a specific row, set a value in a field and verify the value",
    },
    "atp-step-nested-grid-row-set-a-value-multiple-columns-selection": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-set-a-value-multiple-columns-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects the row with the following content in the nested grid field",
            "\t| columnHeader | cellText |",
            "\t| ${4} | ${5} |",
            "And the user writes \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested ${9|date,dropdown-list,numeric,reference,select,text|} field of the selected row in the nested grid field",
            "Then the value of the \"${7}\" ${8|bound,labelled|} nested ${9|checkbox,date,dropdown-list,label,numeric,progress,reference,select,text|} field of the selected row in the nested grid field is \"${6}\"",
        ],
        "description": "Select a nested grid, a specific row by content, set a value in a field and verify the value",
    },
    "atp-step-nested-grid-row-expand-collapse": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-expand-collapse",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user ${6|expands,collapses|} the selected row of the nested grid field",
            "And the user selects row with text \"${7}\" in column with header \"${8}\" in the nested grid field",
        ],
        "description": "Select a nested grid, a specific row, expand/collapse it, and select another row",
    },
    "atp-step-nested-grid-row-expand-collapse-multiple-columns-selection": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-expand-multiple-columns-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects the row with the following content in the nested grid field",
            "\t| columnHeader | cellText |",
            "\t| ${4} | ${5} |",
            "And the user ${6|expands,collapses|} the selected row of the nested grid field",
            "And the user selects the row with the following content in the nested grid field",
            "\t| columnHeader | cellText |",
            "\t| ${7} | ${8} |",
        ],
        "description": "Select a nested grid, a specific row, expand/collapse it, and select another row",
    },
    "atp-step-nested-grid-row-select-main-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-select-main-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user ${6|ticks,unticks|} the main checkbox of the selected row in the nested grid field",
            "Then the main checkbox of the selected row in the nested grid field is ${7|checked,unchecked|}",
        ],
        "description": "Select a nested grid, a specific row, and tick/untick the main checkbox of that row",
    },
    "atp-step-nested-grid-row-add-floating-row": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-add-floating-row",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user adds a new row to the nested grid field",
            "And the user selects the floating row of the selected nested grid field at level ${4:1} with parent id ${5:1}",
            "And the user writes \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested ${9|date,dropdown-list,label,numeric,reference,select,text|} field of the selected floating row in the nested grid field",
            "Then the value of the \"${7}\" ${8|bound,labelled|} nested ${9|date,dropdown-list,label,numeric,reference,select,text|} field of the selected row in the nested grid field is \"${6}\"",
        ],
        "description": "Select a nested grid, add a new row, select the floating row, write into it and verify the value",
    },
    "atp-step-nested-grid-row-click": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-click",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user clicks the selected row of the nested grid field",
        ],
        "description": "Select a nested grid, a specific row by content, and click on the selected row",
    },
    "atp-step-nested-grid-row-dropdown-action-selection": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-dropdown-action-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user clicks the \"${6}\" action of the selected row in the nested grid field",
        ],
        "description": "Select a nested grid, a specific row, and click an action of the selected row",
    },
    "atp-nested-grid-column-filter": {
        "scope": "feature",
        "prefix": "atp-nested-grid-column-filter",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user filters the \"${4:Column name}\" ${5|bound,labelled|} column in the nested grid field with value \"${6:Value}\"",
            "And the user filters the \"${7:Column name}\" ${8|bound,labelled|} column in the nested grid field with filter \"${9:Filter}\" and value \"${10:Value}\"",
            "And the user filters the \"${11:Column name}\" ${12|bound,labelled|} column in the nested grid field with filter type \"${13}\"",
        ],
        "description": "Select a nested grid and apply various types of column filters",
    },
    "atp-step-nested-grid-column-filter-mulitple-values": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-column-filter-mulitple-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user opens the filter of the \"${4}\" ${5|bound,labelled|} column in the nested grid field",
            "And the user searches \"${6}\" in the filter of the nested grid field",
            "And the user ${7|ticks,unticks|} the item with text \"${8}\" in the filter of the nested grid field",
            "And the user closes the filter of the \"${4}\" ${5|bound,labelled|} column in the nested grid field",
        ],
        "description": "Select a nested grid, open a column filter, search for values, select/unselect items, and close the filter",
    },
    "atp-step-nested-grid-column-click": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-column-click",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} column of the nested grid field",
        ],
        "description": "Select a nested grid and click on a specific column",
    },
    "atp-step-nested-grid-option-menu": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-option-menu",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user clicks the option menu of the nested grid field",
            "And the user clicks the \"${4}\" value in the option menu of the nested grid field",
            "And the option menu of the nested grid field is displayed",
        ],
        "description": "Select a nested grid, click the option menu, select a value, and verify the option menu is displayed",
    },
    "atp-step-nested-grid-header-action": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-header-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} action of the nested grid field",
        ],
        "description": "Select a nested grid and click an action in the header of the nested grid",
    },
    "atp-step-nested-grid-paging": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-paging",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the ${4|First,Next,Previous,Last|} page button of the nested grid field is ${5|enabled,disabled|}",
            "And the page number of the nested grid field is \"${6}\"",
            "And the summary row paging information of the nested grid field is \"${7}\"",
            "And the user clicks the ${8|next,previous|} page button of the selected nested grid field at level ${9:1} with parent id ${10:1}",
            "And the ${11|First,Next,Previous,Last|} page button of the nested grid field is ${12|enabled,disabled|} at level ${13:1} with parent id ${14:1}",
            "And the page number of the nested grid field is \"${15}\" at level ${16:1} with parent id ${17:1}",
            "And the summary row paging information of the nested grid field is \"${18}\" at level \"${19}\" with parent id \"${20}\"",
        ],
        "description": "Select a nested grid and perform pagination actions or verify pagination status",
    },
    "atp-step-nested-grid-row-cell-store-value": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-row-cell-store-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "And the user selects row with text \"${4}\" in column with header \"${5}\" in the nested grid field",
            "And the user stores the value of the \"${6}\" ${7|bound,labelled|} nested ${8|checkbox,date,dropdown-list,numeric,progress,reference,select,text|} field of the selected row in the nested grid field with key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Snippet to store the value of a nested grid cell",
    },
}
