{
    "atp-step-radio-button": {
        "scope": "feature",
        "prefix": "atp-step-radio-button-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} radio field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user selects the value \"${4}\" in the radio field",
            "Then the value \"${5}\" of the radio field is selected",
            "Then the value \"${6}\" of the radio field is not selected",
        ],
        "description": "Select radio button field, set and verify values",
    },
    "atp-step-radio-button-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-radio-button-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} radio field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the radio field is ${4|enabled,disabled|}",
            "Then the value \"${5}\" of the radio field is selected",
        ],
        "description": "Verify radio button field state (enabled/disabled) and selected value",
    },
    "atp-step-radio-button-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-radio-button-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} radio field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the radio field is read-only",
            "Then the value \"${4}\" of the radio field is selected",
        ],
        "description": "Verify radio button field read-only state and selected value",
    },
    "atp-step-radio-button-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-radio-button-state-visibility-verification",
        "body": [
            "Then the \"${1}\" ${2|bound,labelled|} radio field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a radio button field is displayed or hidden",
    },
    "atp-step-radio-button-store-value": {
        "scope": "feature",
        "prefix": "atp-step-radio-button-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} radio field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the radio field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a radio-button field",
    },
}
