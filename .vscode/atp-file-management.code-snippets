{
    "atp-csv-file-set-separator": {
        "scope": "feature",
        "prefix": "atp-csv-file-set-separator",
        "body": ["And the user sets the csv separator to \"${1}\""],
        "description": "Set the CSV separator to common delimiters (comma, semicolon, tab, pipe)",
    },
    "atp-csv-file-read-and-control": {
        "scope": "feature",
        "prefix": "atp-csv-file-read-and-control",
        "body": [
            "And the user renames the csv file \"${1}\" with name containing \"${2}\"",
            "When the user reads the \"${3}\" csv file",
            "Then the user verifies the csv file contains",
            "\t\"\"\"",
            "\t${4}",
            "\t\"\"\"",
            "And the csv file content matches the file \"${5}\"",
        ],
        "description": "Rename CSV file, read CSV file, verify CSV content with multi-line verification, and match content with reference file",
    },
    "atp-txt-file-read-and-control": {
        "scope": "feature",
        "prefix": "atp-txt-file-read-and-control",
        "body": [
            "And the user renames the txt file \"${1}\" with name containing \"${2}\"",
            "When the user reads the \"${3}\" txt file",
            "Then the user verifies the txt file contains",
            "\t\"\"\"",
            "\t${4}",
            "\t\"\"\"",
            "And the txt file content matches the file \"${5}\"",
        ],
        "description": "Rename TXT file, read TXT file, verify TXT content with multi-line verification, and match content with reference file",
    },
}
