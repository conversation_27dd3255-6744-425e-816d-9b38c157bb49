{
    "atp-step-step-sequence-control-status": {
        "scope": "feature",
        "prefix": "atp-step-step-sequence-control-status",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} step-sequence field on ${3|the main page,a modal,a full width modal,the detail panel|}",
            "Then the status of the \"${4}\" item of the step-sequence is ${5|complete,incomplete,current|}",
        ],
        "description": "Verify the status of steps in a step-sequence field",
    },
    "atp-step-step-sequence-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-step-sequence-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} step-sequence field on ${3|the main page,a modal,a full width modal,the detail panel|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a step-sequence field is displayed or hidden",
    },
}
