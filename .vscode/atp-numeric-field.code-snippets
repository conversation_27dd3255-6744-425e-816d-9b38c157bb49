{
    "atp-step-numeric-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-numeric-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} numeric field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"$4\" in the numeric field",
            "And the user presses Tab",
            "Then the value of the numeric field is \"$5\"",
        ],
        "description": "Steps to select a numeric field, write a value, press tab and verify the value",
    },
    "atp-step-numeric-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-numeric-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} numeric field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the numeric field is ${4|enabled,disabled|}",
            "And the value of the numeric field is \"$5\"",
        ],
        "description": "Steps to select a numeric field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-numeric-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-numeric-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} numeric field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the numeric field is read-only",
            "And the value of the numeric field is \"$4\"",
        ],
        "description": "Steps to select a numeric field, verify if it's read-only, and verify its value",
    },
    "atp-step-numeric-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-numeric-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} numeric field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a numeric field is displayed or hidden",
    },
    "atp-step-numeric-store-value": {
        "scope": "feature",
        "prefix": "atp-step-numeric-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} numeric field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the numeric field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a numeric field",
    },
}
