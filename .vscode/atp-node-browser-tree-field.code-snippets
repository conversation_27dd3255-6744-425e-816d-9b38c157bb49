{
    "atp-step-node-browser-tree-search": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-search",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "When the user searches for \"${4}\" in the node-browser-tree field",
            "And the user selects the tree-view element of level \"${5}\" with text \"${6}\" in the node-browser-tree field",
            "And the user clears the search field of the node-browser-tree field",
        ],
        "description": "Search in node browser tree field and select an element",
    },
    "atp-step-node-browser-tree-select-element-and-expand-collapse": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-select-element-and-expand-collapse",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the tree-view element of level \"${4}\" with text \"${5}\" in the node-browser-tree field",
            "And the user expands the tree-view element in the node-browser-tree field",
            "Then the tree-view element of the node-browser-tree field is expanded",
            "And the user collapses the tree-view element in the node-browser-tree field",
            "Then the tree-view element of the node-browser-tree field is collapsed",
        ],
        "description": "Select a tree element and expand/collapse it",
    },
    "atp-step-node-browser-tree-select-element-and-tick-untick": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-select-element-and-tick-untick",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the tree-view element of level \"${4}\" with text \"${5}\" in the node-browser-tree field",
            "And the user ticks the tree-view element in the node-browser-tree field",
            "Then the tree-view element of the node-browser-tree field is ticked",
            "And the user unticks the tree-view element in the node-browser-tree field",
            "Then the tree-view element of the node-browser-tree field is unticked",
        ],
        "description": "Select a tree element and tick/untick it",
    },
    "atp-step-node-browser-tree-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the node-browser-tree field is ${4|enabled,disabled|}",
        ],
        "description": "Verify the state of a node browser tree field",
    },
    "atp-step-node-browser-tree-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the node-browser-tree field is read-only",
        ],
        "description": "Verify the read-only state of a node browser tree field",
    },
    "atp-step-node-browser-tree-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-node-browser-tree-state-visibility-verification",
        "body": [
            "Then the \"${1}\" ${2|bound,labelled|} node-browser-tree field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a node browser tree field is displayed or hidden",
    },
}
