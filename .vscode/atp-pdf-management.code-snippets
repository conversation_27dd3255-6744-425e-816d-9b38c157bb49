{
    "atp-step-pdf-store-download-and-rename": {
        "scope": "feature",
        "prefix": "atp-step-pdf-store-download-and-rename",
        "body": [
            "And the user stores the file name from the preview toolbar with the key \"[ENV_PDF_NAME]\"",
            "When the user clicks the \"${1|Download,Print,Save,Export|}\" action in the preview toolbar",
            "And the user renames the file \"${2}\" with name containing \"${3}\"",
        ],
        "description": "Store file name from preview toolbar, click action in preview toolbar, and rename the file with specific name containing pattern",
    },
    "atp-step-pdf-set-reset-vertical-alignment": {
        "scope": "feature",
        "prefix": "atp-step-pdf-set-reset-vertical-alignment",
        "body": [
            "And the user sets the pdf vertical alignment to ${1|top,center,bottom|}",
            "And the user resets the pdf vertical alignment",
        ],
        "description": "Set PDF vertical alignment to top, center, or bottom, and reset PDF vertical alignment",
    },
    "atp-step-pdf-set-reset-threshold": {
        "scope": "feature",
        "prefix": "atp-step-pdf-set-reset-threshold",
        "body": ["And the user sets the pdf threshold to \"${1}\" pt", "And the user resets the pdf threshold"],
        "description": "Set PDF threshold to specific point value and reset PDF threshold",
    },
    "atp-step-pdf-read-and-control": {
        "scope": "feature",
        "prefix": "atp-step-pdf-read-and-control",
        "body": [
            "And the user reads the \"${1}\" pdf file",
            "Then the user verifies the pdf report contains",
            "\t\"\"\"",
            "\t${2}",
            "\t\"\"\"",
        ],
        "description": "Read PDF file and verify that the PDF report contains specific content using multi-line string verification",
    },
}
