{
    "atp-step-dynamic-select-option-selection-with-action-button": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-option-selection-with-action-button",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks the \"${4}\" action button of the dynamic-select field",
            "Then at least the following list of options is displayed for the dynamic-select field:\"${5:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"${6}\" in the dynamic-select field",
            "Then the value of the dynamic-select field is \"${6}\"",
        ],
        "description": "Select a field, click the action button , select an option, and verify the value",
    },
    "atp-step-dynamic-select-option-selection": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-option-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user opens the list of options of the dynamic-select field",
            "Then at least the following list of options is displayed for the dynamic-select field:\"${4:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"${5}\" in the dynamic-select field",
            "Then the value of the dynamic-select field is \"${5}\"",
        ],
        "description": "Select a field, open the list of option, select an option, and verify the value",
    },
    "atp-step-dynamic-select-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"${4}\" in the dynamic-select field",
            "And the user blurs the dynamic-select field",
            "Then the value of the dynamic-select field is \"${4}\"",
        ],
        "description": "Select a field, write in it, and verify the value",
    },
    "atp-step-dynamic-select-state-verificaton": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-state-verificaton",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the dynamic-select field is ${4|enabled,disabled|}",
            "Then the value of the dynamic-select field is \"${5}\"",
        ],
        "description": "Select a field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-dynamic-select-state-read-only-verificaton": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-state-read-only-verificaton",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the dynamic-select field is read-only",
            "Then the value of the dynamic-select field is \"${4}\"",
        ],
        "description": "Select a field, verify if it's read-only, and check its value",
    },
    "atp-step-dynamic-select-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-select-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} dynamic-select field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a dynamic-select field is displayed or hidden",
    },
}
