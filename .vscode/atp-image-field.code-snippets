{
    "atp-step-image-add-or-remove": {
        "scope": "feature",
        "prefix": "atp-step-image-add-or-remove",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} image field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the image field is ${4|undefined,defined|}",
            "When the user ${5|adds the \"./image/sagelogo.png\" image to,removes the image from|} the image field",
            "Then the image field is ${6|defined,undefined|}",
        ],
        "description": "Steps to select an image field, verify defined/undefined state, add/remove image, and verify state changes",
    },
    "atp-step-image-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-image-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} image field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the image field is ${4|defined,undefined|}",
            "And the image field is ${5|enabled,disabled|}",
        ],
        "description": "Steps to select an image field and verify defined/undefined and enabled/disabled states",
    },
    "atp-step-image-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-image-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} image field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the image field is ${4|defined,undefined|}",
            "And the image field is read-only",
        ],
        "description": "Steps to select an image field, verify defined/undefined state and read-only state",
    },
    "atp-step-image-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-image-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} image field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Steps to verify visibility of an image field on a specific page or component",
    },
}
