{"recommendations": ["ms-azuretools.vscode-docker", "alexkrechik.cucumberautocomplete", "dbaeumer.vscode-eslint", "donjayamanne.githistory", "eamodio.gitlens", "esbenp.prettier-vscode", "janisdd.vscode-edit-csv", "mechatroner.rainbow-csv", "wayou.vscode-todo-highlight", "streetsidesoftware.code-spell-checker", "mtxr.sqltools", "mtxr.sqltools-driver-pg", "ms-vscode.live-server", "SonarSource.sonarlint-vscode", "redhat.vscode-yaml", "bierner.markdown-mermaid"]}