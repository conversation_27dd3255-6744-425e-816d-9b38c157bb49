{
    "atp-step-dropdown-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-dropdown-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} dropdown-list field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks in the dropdown-list field",
            "Then at least the following list of options is displayed for the dropdown-list field:\"${4:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"$5\" in the dropdown-list field",
            "Then the value of the dropdown-list field is \"$5\"",
        ],
        "description": "Steps to select a dropdown-list field, click it, verify options, select option and verify the value",
    },
    "atp-step-dropdown-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-dropdown-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} dropdown-list field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the dropdown-list field is ${4|enabled,disabled|}",
            "And the value of the dropdown-list field is \"$5\"",
        ],
        "description": "Steps to select a dropdown-list field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-dropdown-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-dropdown-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} dropdown-list field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the dropdown-list field is read-only",
            "And the value of the dropdown-list field is \"$4\"",
        ],
        "description": "Steps to select a dropdown-list field, verify if it's read-only, and verify its value",
    },
    "atp-step-dropdown-list-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-dropdown-list-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} dropdown-list field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a dropdown-list field is displayed or hidden",
    },
    "atp-step-dropdown-list-store-value": {
        "scope": "feature",
        "prefix": "atp-step-dropdown-list-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} dropdown-list field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the dropdown-list field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a dropdown-list field",
    },
}
