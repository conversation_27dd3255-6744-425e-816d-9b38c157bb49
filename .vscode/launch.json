{
    // Use IntelliSense to learn about possible Node.js debug attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Cucumber",
            "program": "${workspaceFolder}/platform/cli/xtrem-cli/build/lib/cli.js",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": ["test", "--integration", "/${fileBasename}", "--browser"]
        },
        {
            "type": "node",
            "request": "attach",
            "name": "Attach",
            "port": 9229
        },
        {
            "name": "Client-side Debug glossary", //  Debug View name (hotkey crtl+shift+D) dropdown
            "type": "chrome", // specify to use chrome debugger
            "request": "launch", // launch a new instance of the chrome browser against the url property
            "url": "http://localhost:8240/@sage/xtrem-glossary/AdminDashboard", // url that will be launched in the browser
            "webRoot": "${workspaceFolder}/tools/glossary/xtrem-glossary", // resolve script urls to the actual files on disk (use .scripts commands to find out how the scripts loaded in the web application are mapped to the corresponding files on disk. Must be mapped correctly or else your breakpoints will become 'unverified')
            "sourceMapPathOverrides": {
                "webpack://@sage/xtrem-glossary/./*": "${webRoot}/*" // Needed to correct the local path inferred for the source map
            },
            "sourceMaps": true, // to debug on the original sources (typescript rather than transpiled version in javascript)
            "smartStep": true
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Build show-case",
            "program": "${workspaceFolder}/platform/cli/xtrem-cli/build/lib/cli.js",
            "cwd": "${workspaceFolder}/platform/show-case/xtrem-show-case",
            "args": ["build"]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run show-case",
            "program": "${workspaceFolder}/platform/cli/xtrem-cli/build/lib/cli.js",
            "cwd": "${workspaceFolder}/platform/show-case/xtrem-show-case",
            "args": ["start"]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run serivces",
            "program": "${workspaceFolder}/platform/cli/xtrem-cli/build/lib/cli.js",
            "cwd": "${workspaceFolder}/services/main/xtrem-services-main",
            "args": ["start"]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Jest Debug xtrem-ui",
            "runtimeExecutable": "sh", // <-- The important bit!
            "cwd": "${workspaceFolder}/platform/front-end/xtrem-ui",
            "program": "${workspaceFolder}/platform/front-end/xtrem-ui/node_modules/.bin/jest",
            "args": ["${relativeFile}"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "openOnFirstSessionStart"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run AP Automation unit tests",
            "program": "${workspaceFolder}/platform/cli/xtrem-cli/build/lib/cli.js",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/services/adapters/xtrem-ap-automation",
            "args": ["test", "--unit"]
        }
    ]
}
