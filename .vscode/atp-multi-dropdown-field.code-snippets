{
    "atp-step-multi-dropdown-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-multi-dropdown-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi dropdown field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks in the multi dropdown field",
            "Then at least the following list of options is displayed for the multi dropdown field: \"${4:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"${5:Option 1 | Option 2}\" in the multi dropdown field",
            "Then the value of the multi dropdown field is \"${6:Option 1, Option 2}\"",
        ],
        "description": "Steps to select a multi dropdown field, click it, verify options, select options and verify the value",
    },
    "atp-step-multi-dropdown-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-dropdown-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi dropdown field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the multi dropdown field is ${4|enabled,disabled|}",
            "And the value of the multi dropdown field is \"$5\"",
        ],
        "description": "Steps to select a multi dropdown field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-multi-dropdown-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-dropdown-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi dropdown field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the multi dropdown field is read-only",
            "And the value of the multi dropdown field is \"$4\"",
        ],
        "description": "Steps to select a multi dropdown field, verify if it's read-only, and verify its value",
    },
    "atp-step-multi-dropdown-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-dropdown-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} multi dropdown field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a multi dropdown field is displayed or hidden",
    },
    "atp-step-multi-dropdown-store-value": {
        "scope": "feature",
        "prefix": "atp-step-multi-dropdown-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi dropdown field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the multi dropdown field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a multi-dropdown field",
    },
}
