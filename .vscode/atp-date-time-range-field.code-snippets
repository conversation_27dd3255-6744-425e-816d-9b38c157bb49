{
    "atp-step-date-time-range-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-date-time-range-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date-time-range field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "# Start Date-Time Configuration",
            "And the user selects the \"$4\" month of the start date-time-range field",
            "And the user selects the \"$5\" year of the start date-time-range field",
            "And the user selects the \"$6\" day in the start date-time-range field",
            "And the user writes \"$7\" in time field of the start date-time-range field",
            "And the user clicks the \"${8|AM,PM|}\" toggle button of the start date-time-range field",
            "Then the value of the start date-time-range field is \"$9\"",
            "",
            "# End Date-Time Configuration",
            "And the user selects the \"$10\" month of the end date-time-range field",
            "And the user selects the \"$11\" year of the end date-time-range field",
            "And the user selects the \"$12\" day in the end date-time-range field",
            "And the user writes \"$13\" in time field of the end date-time-range field",
            "And the user clicks the \"${14|AM,PM|}\" toggle button of the end date-time-range field",
            "Then the value of the end date-time-range field is \"$15\"",
        ],
        "description": "Steps to select a date-time-range field, set start and end dates, set times, and verify values",
    },
    "atp-step-date-time-range-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-time-range-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date-time-range field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the date-time-range field is ${4|enabled,disabled|}",
            "And the value of the start date-time-range field is \"$5\"",
            "And the value of the end date-time-range field is \"$6\"",
        ],
        "description": "Steps to select a date-time-range field, verify if it's enabled or disabled, and verify its values",
    },
    "atp-step-date-time-range-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-time-range-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date-time-range field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the date-time-range field is read-only",
            "And the value of the start date-time-range field is \"$4\"",
            "And the value of the end date-time-range field is \"$5\"",
        ],
        "description": "Steps to select a date-time-range field, verify if it's read-only, and verify its values",
    },
    "atp-step-date-time-range-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-time-range-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} date-time-range field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a date-time-range field is displayed or hidden",
    },
}
