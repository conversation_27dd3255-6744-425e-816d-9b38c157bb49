{
    "atp-step-filter-select-option-selection": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-option-selection",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks in the filter select field",
            "Then at least the following list of options is displayed for the filter select field:\"${4:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"$5\" in the filter select field",
            "Then the value of the filter select field is \"$5\"",
        ],
        "description": "Steps to select a filter select field, click it, verify options, select option and verify the value",
    },
    "atp-step-filter-select-option-selection-after-write": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-option-selection-after-write",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"$4\" in the filter select field",
            "Then at least the following list of options is displayed for the filter select field:\"${5:Option 1 | Option 2 | Option 3}\"",
            "And the user selects \"$6\" in the filter select field",
            "Then the value of the filter select field is \"$6\"",
        ],
        "description": "Steps to select a filter select field, write text, select option and verify the value",
    },
    "atp-step-filter-select-option-selection-via-lookup": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-option-selection-via-lookup",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks the lookup button of the filter select field",
            "And the user selects \"$4\" in the filter select field",
            "Then the value of the filter select field is \"$4\"",
        ],
        "description": "Filter select field operations via lookup button: select field, click lookup, select option, and verify value",
    },
    "atp-step-filter-select-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the filter select field is ${4|enabled,disabled|}",
            "And the value of the filter select field is \"$5\"",
        ],
        "description": "Steps to select a filter select field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-filter-select-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the filter select field is read-only",
            "And the value of the filter select field is \"$4\"",
        ],
        "description": "Filter select field read-only verification: select field, verify read-only state, and check value",
    },
    "atp-step-filter-select-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} filter select field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a filter select field is displayed or hidden",
    },
    "atp-step-filter-select-store-value": {
        "scope": "feature",
        "prefix": "atp-step-filter-select-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} filter select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the filter select field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a filter select field",
    },
}
