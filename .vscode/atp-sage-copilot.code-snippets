{
    "atp-step-sage-copilot-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-sage-copilot-state-verification",
        "body": [
            "Given the user clicks the \"Sage Copilot\" icon in the navigation bar",
            "Then the sage copilot panel is displayed",
            "When the user closes the sage copilot panel",
            "Then the sage copilot panel is hidden",
        ],
        "description": "Steps to verify sage copilot state",
    },
    "atp-step-sage-copilot-insights-verification": {
        "scope": "feature",
        "prefix": "atp-step-sage-copilot-insights-verification",
        "body": [
            "Then the number of insights is \"$1\"",
            "When the user clicks the sage copilot insights button",
            "Then the sage copilot panel is ${2|displayed,hidden|}",
            "Then the card \"$3\" in the sage copilot panel is ${4|displayed,hidden|} ",
            "When the user selects the card with title \"$3\" in the sage copilot panel",
            "Then the text in the sage copilot card contains the pattern",
            "\t\"\"\"",
            "\t${5}",
            "\t\"\"\"",
            "And the user selects the table in the sage copilot panel",
            "And the user selects the row that has the text \"$6\" in column with header: \"$7\" in the sage copilot panel",
            "And the user selects the cell with header: \"$8\" of selected row in the sage copilot panel",
            "Then the value of the selected cell is \"$9\" in the sage copilot panel",
            "When the user clicks the selected cell in the sage copilot panel",
        ],
        "description": "Steps to verify sage copilot insights",
    },
}
