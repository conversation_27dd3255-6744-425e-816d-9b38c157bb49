{
    "atp-step-text-area-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-text-area-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text area field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"${4}\" in the text area field",
            "Then the value of the text area field is \"${4}\"",
        ],
        "description": "Select a text area field, write in it, and verify the value",
    },
    "atp-step-text-area-control-a-block-of-text": {
        "scope": "feature",
        "prefix": "atp-step-text-area-control-a-block-of-text",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text area field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the value of the text area field ${4|is,contains|}",
            "\t\"\"\"",
            "\t${5}",
            "\t\"\"\"",
        ],
        "description": "Select a text area field, control a block of text",
    },
    "atp-step-text-area-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-area-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text area field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the text area field is ${4|enabled,disabled|}",
            "Then the value of the text area field is \"${5}\"",
        ],
        "description": "Select a text area field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-text-area-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-area-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text area field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the text area field is read-only",
            "Then the value of the text area field is \"${4}\"",
        ],
        "description": "Select a text area field, verify if it's read-only, and check its value",
    },
    "atp-step-text-area-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-area-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} text area field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a text area field is displayed or hidden",
    },
    "atp-step-text-area-store-value": {
        "scope": "feature",
        "prefix": "atp-step-text-area-store-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} text area field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "And the user stores the value of the text area field with the key \"[ENV_MyStoreValue]\"",
        ],
        "description": "Step to store the value of a text area field",
    },
}
