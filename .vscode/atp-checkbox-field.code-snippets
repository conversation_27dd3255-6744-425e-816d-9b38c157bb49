{
    "atp-step-checkbox-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-checkbox-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} checkbox field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user ${4|ticks,unticks|} the checkbox field",
            "Then the value of the checkbox field is \"${5|true,false|}\"",
        ],
        "description": "Steps to select a checkbox field, tick/untick it, and verify the value",
    },
    "atp-step-checkbox-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-checkbox-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} checkbox field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the checkbox field is ${4|enabled,disabled|}",
            "And the value of the checkbox field is \"${5|true,false|}\"",
        ],
        "description": "Steps to select a checkbox field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-checkbox-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-checkbox-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} checkbox field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the checkbox field is read-only",
            "And the value of the checkbox field is \"${4|true,false|}\"",
        ],
        "description": "Steps to select a checkbox field, verify if it's read-only, and verify its value",
    },
    "atp-step-checkbox-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-checkbox-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} checkbox field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a checkbox field is displayed or hidden",
    },
}
