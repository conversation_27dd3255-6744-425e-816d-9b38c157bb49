{
    "atp-step-open-page-sdmo-main-user": {
        "scope": "feature",
        "prefix": "atp-step-open-page-sdmo-main-user",
        "body": [
            "Given the user opens the application on a ${1|ultrawide desktop,HD desktop,desktop,mobile,tablet|} using the following link: \"${2}\"",
            "Then the \"${3}\" titled page is displayed",
            "Then the titled page containing \"${3}\" is displayed",
        ],
        "description": "Steps to open a page on SDMO with the main user and control the page title",
    },
    "atp-step-open-page-sdmo-other-user": {
        "scope": "feature",
        "prefix": "atp-step-open-page-sdmo-other-user",
        "body": [
            "Scenario: 01 Connect with user ${1}",
            "Given the user is logged into the system in mobile mode using the \"param:loginUserName${2}\" user name and \"param:loginPassword${2}\" password",
            "Given the user opens the application on a ${3|ultrawide desktop,HD desktop,desktop,mobile,tablet|} using the following link: \"${4}\"",
            "Then the \"${5}\" titled page is displayed",
            "Then the titled page containing \"${5}\" is displayed",
        ],
        "description": "Steps to open a page on SDMO with a secondary user and control the page title",
    },
    "atp-step-open-page-mobile-automation-x3-main-user": {
        "scope": "feature",
        "prefix": "atp-step-open-page-mobile-automation-x3-main-user",
        "body": [
            "Scenario: 01 Select the endpoint",
            "Given the user opens the application on a mobile",
            "When the user selects the \"param:endPointName1\" endpoint",
            "Then the \"param:endPointName1\" endpoint is selected",
            "\t",
            "\t",
            "Scenario: 02 Set the site ${1}",
            "Given the user opens the application on a mobile",
            "When the user clicks the \"Site\" sticker in the navigation bar",
            "And the user selects the \"Site\" labelled reference field on a full width modal",
            "And the user clicks the lookup button of the reference field",
            "And searches for \"${1}\" in the lookup dialog",
            "And the user selects the \"selectionSite\" bound reference field on a full width modal",
            "Then the value of the reference field is \"${1}\"",
            "And the user clicks the \"Set site\" labelled business action button on a full width modal",
            "\t",
            "\t",
            "Scenario: 03 Open function",
            "Given the user opens the application on a mobile using the following link: \"${2}\"",
            "When the \"${3}\" titled page is displayed",
            "\t",
            "\t",
            "Scenario: 04 Logout",
            "When the user logs out of the system",
        ],
        "description": "Steps to open a page on x3-services mobile automation with the main user",
    },
    "atp-step-open-page-mobile-automation-x3-other-user": {
        "scope": "feature",
        "prefix": "atp-step-open-page-mobile-automation-x3-other-user",
        "body": [
            "Scenario: 01 Connect with userXXX and select the endpoint",
            "Given the user is logged into the system in mobile mode using the \"param:loginUserName${1}\" user name and \"param:loginPassword${1}\" password",
            "When the user selects the \"param:endPointName1\" endpoint",
            "Then the \"param:endPointName1\" endpoint is selected",
            "\t",
            "\t",
            "Scenario: 02 Set the site ${2}",
            "Given the user opens the application on a mobile",
            "When the user clicks the \"Site\" sticker in the navigation bar",
            "And the user selects the \"Site\" labelled reference field on a full width modal",
            "And the user clicks the lookup button of the reference field",
            "And searches for \"${2}\" in the lookup dialog",
            "And the user selects the \"selectionSite\" bound reference field on a full width modal",
            "Then the value of the reference field is \"${2}\"",
            "And the user clicks the \"Set site\" labelled business action button on a full width modal",
            "\t",
            "\t",
            "Scenario: 03 Open function",
            "Given the user opens the application on a mobile using the following link: \"${3}\"",
            "When the \"${4}\" titled page is displayed",
            "\t",
            "\t",
            "Scenario: 04 Logout",
            "When the user logs out of the system",
        ],
        "description": "Steps to open a page on x3-services mobile automation with a secondary or third user",
    },

    "atp-step-open-page-mobile-automation-wh-main-user": {
        "scope": "feature",
        "prefix": "atp-step-open-page-mobile-automation-wh-main-user",
        "body": [
            "Scenario: 01 Select the endpoint",
            "Given the user opens the application on a mobile",
            "When the user selects the \"param:endPointName1\" endpoint",
            "Then the \"param:endPointName1\" endpoint is selected",
            "\t",
            "\t",
            "Scenario: 02 Set a Site and depositor",
            "Given the user opens the application on a mobile",
            "When the user clicks the \"Site and depositor\" sticker in the navigation bar",
            "Then the \"Site / depositor\" titled page is displayed",
            "And the user selects the \"Site\" labelled reference field on a full width modal",
            "And the user writes \"${1}\" in the reference field",
            "Then the value of the reference field is \"${1}\"",
            "And the user selects the \"Depositor\" labelled reference field on a full width modal",
            "And the user writes \"${2}\" in the reference field",
            "Then the value of the reference field is \"${2}\"",
            "And the user clicks the \"Set site and depositor\" labelled business action button on a full width modal",
            "And the user clicks the close button in the navigation bar",
            "Then the site and depositor in the navigation bar is \"${1} / ${2}\"",
            "\t",
            "\t",
            "Scenario: 03 Open a function",
            "Given the user opens the application on a mobile using the following link: \"${3}\"",
            "Then the \"${4}\" titled page is displayed",
            "\t",
            "\t",
            "Scenario: 04 Logout",
            "When the user logs out of the system",
        ],
        "description": "Steps to open a page on wh-service mobile automation with the main user",
    },

    "atp-step-shopfloor-clock-in-clock-out": {
        "scope": "feature",
        "prefix": "atp-step-shopfloor-clock-in-clock-out",
        "body": [
            "Scenario: Clock-in / clock out",
            "Given the user opens the application on a HD desktop",
            "Then the \"Shopfloor Control main dashboard\" titled dashboard is displayed",

            "#clock in",
            "And the user clicks the \"Clock in\" button of the table widget field",
            "And the user selects the \"Operator\" labelled reference field on a modal",
            "And the user writes \"${1}\" in the reference field",
            "And the user selects \"${1}\" in the reference field",
            "And the user selects the \"Site\" labelled reference field on a modal",
            "Then the value of the reference field is \"${2}\"",
            "And the user clicks the \"OK\" labelled business action button on a modal",

            "Given the \"Shopfloor Control main dashboard\" titled dashboard is displayed",
            "And the user selects the \"${3}\" titled table widget field in the dashboard",

            "#Select required period",
            "And the user selects the \"${4}\" period type toggle button in the table widget",
            "And the user selects the \"${5}\" date period in the table widget",

            "#Select required card",
            "And the user selects the card ${6} of the table widget field",
            "Then the value of the row ${7} on the left of the card of the table widget field is \"${8}\"",
            "And the user stores the value of the row ${7} on the left of the card of the table widget with key \"[ENV_MyStoreValue]\"",

            "#Clock out",
            "Given the \"Shopfloor Control main dashboard\" titled dashboard is displayed",
            "And the user clicks the \"Clock out\" button of the table widget field",
            "Then a toast with text \"Operator clocked out successfully\" is displayed",
        ],
        "description": "Steps to open a page on shopfloor with the main user",
    },
}
