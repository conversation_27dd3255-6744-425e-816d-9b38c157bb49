{
    "atp-step-tab-selection": {
        "scope": "feature",
        "prefix": "atp-step-tab-selection",
        "body": [
            "When the user selects the \"$1\" labelled tab on ${2|the main page,a modal,a full width modal,the sidebar|}",
            "Then the \"$1\" labelled tab is selected"

        ],
        "description": "Select the the tab and control the tab is selected",
    },
    "atp-step-tab-detailed-panel-selection": {
        "scope": "feature",
        "prefix": "atp-step-tab-detailed-panel-selection",
        "body": [
            "Then the detail panel is ${1|displayed,hidden|}",
            "When the user selects the \"$2\" labelled tab in the detail panel",

        ],
        "description": "Control the detail panel is displayed and select the tab",
    },
    "atp-step-tab-detailed-panel-preview-selection": {
        "scope": "feature",
        "prefix": "atp-step-tab-detailed-panel-preview-selection",
        "body": [
            "Then the detail panel preview is ${1|displayed,hidden|}",
            "When the user selects the \"$2\" tab in the detail panel preview",

        ],
        "description": "Control the detail panel preview is displayed and select the tab",
    }
