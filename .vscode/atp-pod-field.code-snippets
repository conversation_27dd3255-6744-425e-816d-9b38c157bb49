{
    "atp-step-pod-control-value": {
        "scope": "feature",
        "prefix": "atp-step-pod-control-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the value of the \"${4}\" ${5|bound,labelled|} nested ${6|button,calendar,checkbox,count,date,filter select,icon,label,link,multi dropdown,multi reference,numeric,progress,radio,reference,rich text,scan,select,table,text,text area|} field in the pod field is \"${7}\"",
            "Then the \"${8}\" ${9|bound,labelled|} nested switch field in the pod field is set to \"${10|ON,OFF|}\"",
        ],
        "description": "Select a pod field and verify the value of nested fields including switch fields",
    },
    "atp-step-pod-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the pod field is ${4|enabled,disabled|}",
            "Then the value of the \"${5}\" ${6|bound,labelled|} nested ${7|button,calendar,checkbox,count,date,filter select,icon,label,link,multi dropdown,multi reference,numeric,progress,radio,reference,rich text,scan,select,table,text,text area|} field in the pod field is \"${8}\"",
        ],
        "description": "Select a pod field, verify its state (enabled/disabled), and verify the value of a nested field",
    },
    "atp-step-pod-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-state-visibility-verification",
        "body": [
            "Then the \"${1}\" ${2|bound,labelled|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Verify if a pod field is displayed or hidden on a specific location",
    },
    "atp-step-pod-add-remove-pod": {
        "scope": "feature",
        "prefix": "atp-step-pod-add-remove-pod",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" button of the pod field",
            "And the user clicks the \"${5}\" icon of the pod field",
        ],
        "description": "Select a pod field and perform button/icon click actions for adding or removing pod items",
    },
    "atp-step-pod-action": {
        "scope": "feature",
        "prefix": "atp-step-pod-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" action of the pod field",
        ],
        "description": "Select a pod field and click an action",
    },
    "atp-step-pod-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the action \"${4}\" of the pod field is ${5|enabled,disabled|}",
        ],
        "description": "Select a pod field and verify if an action is enabled or disabled",
    },
    "atp-step-pod-action-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-action-state-visibility-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the action \"${4}\" of the pod field is ${5|displayed,hidden|}",
        ],
        "description": "Select a pod field and verify if an action is displayed or hidden",
    },
    "atp-step-pod-action-state-empty-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-action-state-empty-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the pod field is ${4|empty,not empty|}",
            "Then the pod field header container value is \"${5}\"",
        ],
        "description": "Select a pod field and verify empty state and header container value",
    },
}
