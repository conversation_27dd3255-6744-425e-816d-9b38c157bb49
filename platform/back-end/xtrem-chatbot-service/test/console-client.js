async function runChatbotTest() {
    const response = await fetch('http://localhost:8240/chat/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: [{ role: 'user', content: process.argv[2] || 'give me 3 sales orders' }] }),
    });

    const eventSource = response.body.getReader();
    const decoder = new TextDecoder();

    let chunk = '';
    while (true) {
        const { done, value } = await eventSource.read();
        // console.log('Received chunk:', done, value);
        if (done) break;

        chunk += decoder.decode(value);
        // console.log('Decoded chunk:', chunk);
        const lines = chunk.split('\n');
        // Deal with large data - if we did not find any newline, we need to wait for more data
        // TODO: I don't know if this is correct - maybe server should send events with a special (partial?) type in this case.
        if (lines.length === 1) continue;
        // We have got all the data, reset chunk for next message
        chunk = '';

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                try {
                    const event = JSON.parse(line.slice(6));

                    switch (event.type) {
                        case 'connected':
                            console.log('🔌', 'Connected to streaming endpoint');
                            break;
                        case 'thinking':
                            console.log('🤔', event.message);
                            break;
                        case 'tool_use':
                            console.log(
                                '🔧',
                                `Using ${event.toolName} with parameters: ${JSON.stringify(event.input)}`,
                            );
                            break;
                        case 'tool_result':
                            console.log('📊', event.result);
                            break;
                        case 'text_chunk':
                            process.stdout.write(event.text);
                            break;
                        case 'completed':
                            console.log('\n\n✅', 'Final:', event.finalText);
                            break;
                        case 'error':
                            console.error('❌', event.error);
                            break;
                        default:
                            console.error('❌', 'Unknown event type:', event);
                    }
                } catch (error) {
                    console.error(`Error processing event: line=${line}\n', ${error.stack}`);
                }
            }
        }
    }
}

runChatbotTest().catch(console.error);
