{"name": "@sage/xtrem-chatbot-service", "version": "59.0.9", "description": "Chatbot server for XTreeM", "license": "UNLICENSED", "author": "Sage", "files": ["build"], "main": "build/index.js", "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.24.0", "@anthropic-ai/sdk": "^0.27.0", "@aws-sdk/client-bedrock-agent-runtime": "^3.844.0", "@aws-sdk/client-bedrock-runtime": "^3.844.0", "@aws-sdk/credential-provider-node": "^3.846.0", "@modelcontextprotocol/sdk": "^1.17.1", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-mcp": "workspace:*", "@sage/xtrem-minify": "workspace:*", "diff": "^5.1.0", "express": "^5.0.0", "glob": "^10.3.10", "graphql": "16.1.0-experimental-stream-defer.6", "limiter": "^3.0.0", "lodash": "^4.17.21", "minimatch": "^10.0.0", "zod": "^3.24.1", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/diff": "^5.0.9", "@types/express": "^5.0.0", "@types/lodash": "^4.17.17", "@types/node": "^22.10.2", "typescript": "^5.3.3"}}