graph TB
    subgraph "Client"
        WC[Chatbot Web Client]
    end

    subgraph "AWS Kubernetes Cluster"
        subgraph "Combined Service Pod"
            CS[Chatbot Service]
            MCP[MCP Server]
            BA[Business Application]
            GQL[GraphQL API]

            CS -.->|In-Memory Transport| MCP
            MCP -.->|In-Memory/Direct| GQL
            GQL --> BA
        end
    end

    subgraph "AWS Bedrock"
        CLAUDE[Claude LLM]
    end

    WC -->|HTTPS| CS
    CS -->|API Calls| CLAUDE

    class WC client
    class CS,MCP service
    class BA,GQL business
    class CLAUDE bedrock
