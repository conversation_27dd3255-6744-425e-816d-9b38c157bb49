graph TB
    subgraph "Anthropic"
        CLAUDE[<PERSON>]
    end

    subgraph "Local Environment"
        CD[Claude Desktop]
        MCP[MCP Server]

        subgraph "Business Application Process"
            BA[Business Application]
            GQL[GraphQL API]

            GQL --> BA
        end
    end

    CD -->|API Calls| CLAUDE
    CD -.->|stdio Transport| MCP
    MCP -->|HTTP/GraphQL| GQL
