graph TB
    subgraph "Client"
        WC[Chatbot Web Client]
    end

    subgraph "AWS Kubernetes Cluster"
        subgraph "Chatbot Service Pod"
            CS[Chatbot Service]
            MCP[MCP Server]
            CS -.->|In-Memory Transport| MCP
        end

        subgraph "Business Application Pod"
            BA[Business Application]
            GQL[GraphQL API]
            GQL --> BA
        end
    end

    subgraph "AWS Bedrock"
        CLAUDE[Claude LLM]
    end

    WC -->|HTTPS| CS
    CS -->|API Calls| CLAUDE
    MCP -->|GraphQL Queries| GQL

    class WC client
    class CS,MCP service
    class BA,GQL business
    class CLAUDE bedrock
