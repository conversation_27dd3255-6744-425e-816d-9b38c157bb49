graph TB
    subgraph "GitHub"
        COPILOT[GitHub Copilot LLM]
    end

    subgraph "Local Environment"
        subgraph "VSCode"
            VSC[Copilot Chat]
        end
    MCP[MCP Server]

        subgraph "Business Application Process"
            BA[Business Application]
            GQL[GraphQL API]

            GQL --> BA
        end
    end

    VSC -->|API Calls| COPILOT
    VSC -.->|stdio Transport| MCP
    MCP -->|HTTP/GraphQL| GQL
