# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Development
- `pnpm build` - Compile TypeScript to JavaScript in build/ directory
- `pnpm build:binary` - Clean, build, and minify for production deployment
- `pnpm build:cache` - Build using Turbo with concurrency control
- `pnpm clean` - Remove build artifacts

### Project Structure
This is the xtrem-chatbot service, a TypeScript-based Express router that provides Claude AI chatbot functionality through AWS Bedrock.

**Key Architecture:**
- `index.ts` - Entry point that exports from lib/
- `lib/chat-route.ts` - Express router handling chat requests via POST endpoint
- `lib/claude-client.ts` - AWS Bedrock integration for Claude Sonnet 4 model
- `lib/config.ts` - AWS configuration (region: eu-west-3, Claude Sonnet 4)
- `lib/utils.ts` - Utility functions for message handling

**Dependencies:**
- Uses workspace packages: @sage/xtrem-core, @sage/xtrem-config, @sage/xtrem-log, @sage/xtrem-minify
- AWS SDK for Bedrock runtime
- Express 5.0 for HTTP routing
- TypeScript compilation with project references

**Integration Points:**
- Integrates with XTreeM application context and tenant management
- Uses readonly database context for chat operations
- Handles authentication through XTreeM core Application class
- AWS Bedrock inference profile: anthropic.claude-sonnet-4-********-v1:0

The service processes chat messages through a POST endpoint, maintains conversation context, and returns Claude AI responses via AWS Bedrock.