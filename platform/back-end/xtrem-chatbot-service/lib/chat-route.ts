import { MessageParam } from '@anthropic-ai/sdk/resources';
import { Application, Context, CoreHooks, getTenantId, Logger } from '@sage/xtrem-core';
import { Config } from '@sage/xtrem-shared';
import * as express from 'express';
import { ClientFactory } from './client-factory';
import { StreamEvent } from './client-interface';
import { PageContext } from './utils';

const logger = Logger.getLogger(__filename, 'chatbot-router');

const processChatRequestStreaming = async (
    context: Context,
    messages: MessageParam[],
    pageContext: PageContext,
    onEvent: (event: StreamEvent) => void,
    conversationId?: string,
    abortSignal?: AbortSignal,
): Promise<string> => {
    try {
        logger.debug(
            () =>
                `Processing streaming chat request with abort signal: ${!!abortSignal}, aborted: ${abortSignal?.aborted}`,
        );
        const client = await ClientFactory.createClient(context);

        return client.callStreaming(messages, pageContext, onEvent, conversationId, abortSignal);
    } catch (error) {
        if (error instanceof Error && error.message === 'Operation was cancelled') {
            logger.debug(() => 'Streaming chat request was cancelled');
            onEvent({ type: 'error', error: 'Operation was cancelled' });
            throw error;
        }
        logger.error(() => `Could not process streaming chat request: ${error.stack || error.message}`);
        const errorMessage = `Could not process chat request: ${error.message}`;
        onEvent({ type: 'error', error: errorMessage });
        return errorMessage;
    }
};

/** Chat router
 *
 * See test/console-client.js for an example of how to consume the streaming endpoint.
 */
export const getChatRouter = (application: Application): express.Router => {
    const router = express.Router();

    router.get('/conversation-starter', async (request, response) => {
        const config = response.locals.config as Config;
        await application.withReadonlyContext(
            getTenantId(application, config),
            async context => {
                const isAllowed = await CoreHooks?.createChatbotManager(application).canUseChatbot(context);
                if (!isAllowed) {
                    response.status(403).json({ error: 'Copilot usage is not allowed for this user' });
                    return;
                }

                const client = await ClientFactory.createClient(context);
                const questions = await client.generateStarterQuestions();
                response.json(questions);
            },
            {
                ...config,
                ...response.locals?.context,
                request,
                response,
            },
        );
    });

    const abortHandler = (request: express.Request, response: express.Response): AbortController => {
        // Create abort signal from request
        const abortController = new AbortController();

        // Handle client disconnection/cancellation
        const handleClientDisconnect = (event: string) => {
            logger.debug(() => `Client disconnected from streaming chat: ${event}`);
            if (!abortController.signal.aborted) {
                logger.debug(() => 'Aborting streaming chat request due to client disconnect');
                abortController.abort();
            }
        };

        // More reliable abort detection - listen to multiple events
        // request.on('close', () => handleClientDisconnect('request close'));
        request.on('aborted', () => handleClientDisconnect('request aborted'));
        request.on('error', () => handleClientDisconnect('request error'));

        // Periodic check for client disconnection during long-running requests
        const abortCheckInterval = setInterval(() => {
            if (
                response.socket?.destroyed ||
                response.writableEnded ||
                response.writableAborted ||
                response.writableFinished
            ) {
                // Only abort if response is not finished
                logger.debug(() => 'Streaming request cancelled, aborting');
                handleClientDisconnect('client cancel');
                logger.debug(() => 'clearing abortCheckInterval');

                clearInterval(abortCheckInterval);
            }
        }, 1000);

        // Clean up interval when request completes
        const cleanup = () => {
            clearInterval(abortCheckInterval);
        };
        request.on('close', cleanup);
        response.on('finish', cleanup);

        return abortController;
    };

    // Streaming chat endpoint with Server-Sent Events
    router.post('/stream', async (request, response) => {
        const config = response.locals.config as Config;

        const abortController = abortHandler(request, response);

        // Set up Server-Sent Events with anti-buffering headers
        response.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
            Connection: 'keep-alive',
            'X-Accel-Buffering': 'no', // Disable nginx buffering
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control',
        });

        // Send initial connection event to establish stream
        response.write('data: {"type":"connected"}\n\n');
        const flush = (response as any).flush?.bind(response) ?? (() => {});
        flush();

        const sendEvent = (event: StreamEvent) => {
            response.write(`data: ${JSON.stringify(event)}\n\n`);
            // Force immediate flush to prevent buffering
            flush();
        };

        const sendError = (error: string) => {
            sendEvent({ type: 'error', error });
            response.end();
        };

        try {
            await application.withCommittedContext(
                getTenantId(application, config),
                async context => {
                    const isAllowed = await CoreHooks?.createChatbotManager(application).canUseChatbot(context);
                    if (!isAllowed) {
                        response.status(403).json({ error: 'Copilot usage is not allowed for this user' });
                        return;
                    }

                    logger.debug(() => `Streaming chat request context: ${context.tenantId}, ${context.userId}`);

                    const messages = request.body.messages as MessageParam[];
                    const pageContext: PageContext = request.body.context || {};
                    const conversationId = request.body.conversationId;
                    await processChatRequestStreaming(
                        context,
                        messages,
                        pageContext,
                        sendEvent,
                        conversationId,
                        abortController.signal,
                    );
                    response.end();
                },
                {
                    ...response.locals?.config,
                    ...response.locals?.context,
                    request,
                    response,
                },
            );
        } catch (err) {
            logger.warn(() => `Streaming chat handler error: ${err.stack || err.message}`);
            sendError('Internal server error');
        }
    });

    return router;
};
