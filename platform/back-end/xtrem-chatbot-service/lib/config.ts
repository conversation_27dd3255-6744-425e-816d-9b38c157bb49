import { ConfigManager } from '@sage/xtrem-config';
import * as _ from 'lodash';
export interface ChatbotConfig {
    infer: {
        model: string;
        max_tokens: number;
        temperature: number;
        top_p: number;
    };
    summarize: {
        model: string;
        max_tokens: number;
        temperature: number;
        top_p: number;
    };
    maxWindowTokens: number;
    rateLimit: {
        maxTokensPerMinute: number;
    };
    bedrock: {
        region: string;
        inferenceProfileId: string;
    };
}

export interface ChatbotConfigExtension {
    chatbotService?: ChatbotConfig;
}

declare module '@sage/xtrem-shared/lib/config' {
    interface Config extends ChatbotConfigExtension {}
}

export const defaultChatbotConfig = {
    infer: {
        // Be cost-conscious - default to haiku rather than sonnet for now
        model: 'claude-3-5-haiku-latest',
        // model: 'claude-3-5-sonnet-20241022',
        max_tokens: 1024,
        temperature: 0.1,
        top_p: 0.9,
    },
    summarize: {
        model: 'claude-3-5-haiku-latest',
        max_tokens: 200,
        temperature: 0.1,
        top_p: 0.9,
    },
    maxWindowTokens: 1_000_000,
    rateLimit: {
        maxTokensPerMinute: 150_000,
    },
    bedrock: {
        region: 'eu-west-3',
        // inferenceProfileId: 'eu.anthropic.claude-3-haiku-20240307-v1:0',
        inferenceProfileId: 'eu.anthropic.claude-sonnet-4-20250514-v1:0',
    },
};

export function getConfig(): ChatbotConfig {
    return _.merge({}, defaultChatbotConfig, ConfigManager.current.chatbotService ?? {});
}
