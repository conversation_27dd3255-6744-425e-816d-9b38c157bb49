
export interface PageContext {
    screenTitle?: string;
    nodeName?: string;
    recordId?: string;
}

export function ellipsesText(value: any, maxLength: number = 80): string {
    if (typeof value !== 'string') return value;
    if (value.length <= maxLength) return value;
    return value.slice(0, maxLength - 3) + '...';
}

export function extractGraphQLQuery(response: string): string {
    const matches = response.match(/```graphql([\s\S]*)```/);
    return matches?.[1]?.trim() || '';
}
