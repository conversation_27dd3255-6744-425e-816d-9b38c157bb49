export type StreamEvent =
    | { type: 'connected' }
    | { type: 'thinking'; message: string }
    | { type: 'tool_use'; toolName: string; input: any }
    | { type: 'tool_result_raw'; toolName: string; result: any } // Raw tool result, immediately available
    | { type: 'tool_result'; toolName: string; result: any } // Final processed result
    | { type: 'text_chunk'; text: string }
    | { type: 'completed'; finalText: string; conversationId: string }
    | { type: 'error'; error: string };

export type ClientType = 'bedrock' | 'anthropic';
