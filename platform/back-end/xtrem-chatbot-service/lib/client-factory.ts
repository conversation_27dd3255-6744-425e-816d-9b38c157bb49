import { ConfigManager, Context } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-log';
import { AnthropicClient } from './anthropic-client';
import { BaseClient } from './base-client';
import { BedrockClient } from './bedrock-client';

const logger = Logger.getLogger(__filename, 'client-factory');

export class ClientFactory {
    static createClient(context: Context): BaseClient {
        const clientType = ConfigManager.current?.mcpServer?.claudeClientType ?? 'bedrock';

        logger.debug(() => `Creating ${clientType} client`);

        switch (clientType) {
            case 'anthropic':
                return new AnthropicClient(context);

            case 'bedrock':
            default:
                return new BedrockClient(context);
        }
    }
}
