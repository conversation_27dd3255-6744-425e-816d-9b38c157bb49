import { AnthropicBedrock } from '@anthropic-ai/bedrock-sdk';
import { Context } from '@sage/xtrem-core';
import { BaseClient } from './base-client';
import { getConfig } from './config';

export class BedrockClient extends BaseClient {
    constructor(context: Context) {
        const bedrock = getConfig().bedrock;
        super(
            context,
            new AnthropicBedrock({ awsRegion: process.env.AWS_REGION || bedrock.region }),
            bedrock.inferenceProfileId,
        );
    }
}
