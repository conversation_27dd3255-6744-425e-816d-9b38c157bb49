import { readFileSync } from 'node:fs';
import { join } from 'node:path';
import { z } from 'zod';

export const packageDir = join(__dirname, '../../');
export const tmpDir = join(packageDir, '../../../tmp');

console.error('TMPDIR', tmpDir);

// Current package version so I only need to update it in one place
export const { version } = JSON.parse(readFileSync(join(packageDir, 'package.json'), 'utf-8'));

const EnvSchema = z.object({
    NAME: z.string().default('xtremChatbot'),
    ENDPOINT: z.string().url().default('http://localhost:8240/api'),
    ALLOW_MUTATIONS: z
        .enum(['true', 'false'])
        .transform(value => value === 'true')
        .default('false'),
    HEADERS: z
        .string()
        .default('{}')
        .transform(val => {
            try {
                return JSON.parse(val);
            } catch (e) {
                throw new Error('HEADERS must be a valid JSON string');
            }
        }),
    SCHEMA: z.string().optional(),
});

export const env = EnvSchema.parse(process.env);
