import AnthropicBedrock from '@anthropic-ai/bedrock-sdk';
import Anthropic from '@anthropic-ai/sdk';
import { MessageStream } from '@anthropic-ai/sdk/lib/MessageStream';
import type { MessageParam, ToolResultBlockParam } from '@anthropic-ai/sdk/resources';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { InMemoryTransport } from '@modelcontextprotocol/sdk/inMemory.js';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp';
import { ConfigManager, Context, CoreHooks, InteropGraphqlClient } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-log';
import { ellipsesReplacer, McpServerHelper } from '@sage/xtrem-mcp';
import { ConfigurationError, type Dict } from '@sage/xtrem-shared';
import { RateLimiter } from 'limiter';
import * as _ from 'lodash';
import { StreamEvent } from './client-interface';
import { ChatbotConfig, getConfig } from './config';
import { PageContext } from './utils';

export const logger = Logger.getLogger(__filename, 'chatbot-client');

type ContentItem = MessageParam['content']['0'];

type BlockParam = Exclude<ContentItem, string>;

type Turn = { role: 'user' | 'assistant'; content: BlockParam[] | string };

export interface Ctx {
    system: string;
    tools: Anthropic.Tool[];
    window: Turn[]; // sliding window of recent turns
    summary?: string; // rolling summary of truncated history
    maxWindowTokens: number; // e.g. 6_000
    pageContext?: PageContext; // additional context for the page
}

export abstract class BaseClient {
    // TODO: improve this static
    private mcpClient?: Client;
    private mcpServer?: McpServer;
    private config: ChatbotConfig;

    private static inputLimiter = new RateLimiter({
        tokensPerInterval: getConfig().rateLimit.maxTokensPerMinute,
        interval: 'minute',
    });

    constructor(
        private context: Context,
        private client: Anthropic | AnthropicBedrock,
        model: string,
    ) {
        this.config = _.merge(getConfig(), { infer: { model } });
    }

    private createPageContextMessage(pageContext?: PageContext | null): string | null {
        if (pageContext?.screenTitle && pageContext?.nodeName && pageContext?.recordId) {
            return `The user is on page "${pageContext.screenTitle}", which is bound to the node "${pageContext.nodeName}" with the following \`_id\`: ${JSON.stringify(pageContext?.recordId)}.`;
        }
        if (pageContext?.nodeName) {
            return `The user is on to page "${pageContext?.screenTitle}", which is bound to the node "${pageContext?.nodeName}".`;
        }

        if (pageContext?.screenTitle) {
            return `The user is on to page "${pageContext?.screenTitle}".`;
        }

        return null;
    }

    private async withRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await fn();
            } catch (error: any) {
                const isRetryableError =
                    error?.error?.type === 'overloaded_error' ||
                    error?.status === 529 ||
                    error?.message?.includes('overloaded');

                if (isRetryableError && attempt < maxRetries) {
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Exponential backoff, max 10s
                    logger.warn(`Claude overloaded (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    continue;
                }
                throw error;
            }
        }
        throw new Error('Max retries exceeded');
    }

    private async init(): Promise<this> {
        if (!this.mcpClient) {
            logger.info(`Initializing MCP server`);

            const appName = ConfigManager.current.app;
            if (!appName)
                throw new ConfigurationError(
                    'No app in config. You did not activate multi-app. Copy xtrem-config-template.yml to xtrem-config.yml at the root of your app',
                );
            const tenantId = this.context.tenantId;
            if (!tenantId) throw new ConfigurationError('No tenant ID in context');
            const userEmail = this.context.userEmail;
            if (!userEmail) throw new ConfigurationError('No user email in context');

            const bearerToken = await InteropGraphqlClient.getBearerToken({
                tenantId,
                sourceUserEmail: userEmail,
                appName,
                scope: 'sourceUser',
                expiresIn: '10m',
            });

            const apiUrl = InteropGraphqlClient.getInteropUrl(appName);

            // Create the MCP server
            this.mcpServer = await McpServerHelper.create({
                applicationName: appName,
                endpoint: apiUrl,
                tenantId,
                userEmail,
                bearerToken,
            });

            // Create memory transport and connect client to server
            const [clientTransport, serverTransport] = InMemoryTransport.createLinkedPair();

            // Connect server to its transport
            await this.mcpServer.server.connect(serverTransport);

            logger.info(`Initializing MCP client`);
            // Create and connect client
            this.mcpClient = new Client(
                {
                    name: 'xtrem-chatbot-client',
                    version: '1.0.0',
                },
                { capabilities: {} },
            );

            await this.mcpClient.connect(clientTransport);
            logger.info('MCP client with memory transport enabled for chatbot client');
        }
        return this;
    }

    private async getAvailableTools() {
        if (!this.mcpClient) return undefined;

        try {
            const toolsResponse = await this.mcpClient.listTools();
            return toolsResponse.tools.map(tool => ({
                name: tool.name,
                description: tool.description,
                input_schema: tool.inputSchema,
            }));
        } catch (error) {
            logger.error(() => `Failed to get MCP tools: ${error.message}`);
            return undefined;
        }
    }

    private getBlockLength(block: ContentItem): number {
        if (typeof block === 'string') {
            return block.length;
        }
        switch (block.type) {
            case 'text':
                return block.text.length;
            case 'image':
                // Claude does not tokenize base64 image data
                // It processes it with a vision encoder.
                // But this may need review/tuning
                return 1;
            case 'tool_use':
                return typeof block.input === 'string' ? block.input.length : JSON.stringify(block.input).length;
            case 'tool_result':
                return this.getMessageLength(block.content ?? '');
            default:
                return 0;
        }
    }

    private getMessageLength(content: Anthropic.Messages.MessageParam['content']): number {
        if (typeof content === 'string') {
            return content.length;
        } else {
            return content.reduce((acc, part) => acc + this.getBlockLength(part), 0);
        }
    }

    private getTokenCount(length: number): number {
        return Math.floor(length / 4);
    }

    private estimateInputTokens(messages: MessageParam[]): number {
        const length = messages.reduce((acc, msg) => acc + this.getMessageLength(msg.content), 0);
        return this.getTokenCount(length);
    }

    async spy<T>(name: string, fn: () => Promise<T>) {
        const t0 = Date.now();
        try {
            return await fn();
        } finally {
            const duration = Date.now() - t0;
            logger.debug(() => `MCP ${name} took ${duration}ms`);
        }
    }

    async send(
        ctx: Ctx,
        messages: MessageParam[],
        onEvent: (event: StreamEvent) => void,
        abortSignal?: AbortSignal,
    ): Promise<Anthropic.Messages.Message> {
        const inputTokensCount = this.estimateInputTokens(messages);

        await this.spy(`inputLimiter: ${inputTokensCount}`, () =>
            BaseClient.inputLimiter.removeTokens(inputTokensCount),
        );

        const stream = this.client.messages.stream({
            ...this.config.infer,
            tools: ctx.tools,
            messages,
        }) as MessageStream;

        // Handle streaming text events
        stream.on('text', (text: string) => {
            // Check abort signal during streaming
            if (abortSignal?.aborted) {
                logger.debug(() => 'AnthropicClient sendStreaming: abort signal detected during text streaming');
                stream.abort();
                throw new Error('Operation was cancelled');
            }
            onEvent({ type: 'text_chunk', text });
        });

        // Wait for the stream to complete and return the final message
        return await stream.finalMessage();
    }

    /** Add a turn into ctx.window, truncating old ones; keep a rolling summary */
    private async pushTurn(ctx: Ctx, turn: Turn) {
        ctx.window.push(turn);

        // naïve token guard – replace with your own counter if you have one
        const roughCount = JSON.stringify(ctx.window).length / 4;
        if (roughCount > ctx.maxWindowTokens) {
            // Summarize older half into ctx.summary and keep the latest turns
            const keep = ctx.window.splice(Math.floor(ctx.window.length / 2));
            const toSummarize = ctx.window; // dropped part
            const summary = await this.summarize(toSummarize, ctx.system);
            ctx.summary = ctx.summary ? ctx.summary + '\n\n' + summary : summary;
            ctx.window = keep;
        }
    }

    /** Small helper to build the messages payload each turn */
    private buildMessages(ctx: Ctx): Anthropic.Messages.MessageParam[] {
        const msgs: Anthropic.Messages.MessageParam[] = [];
        if (ctx.summary) {
            msgs.push({ role: 'assistant', content: `Conversation so far (summary): ${ctx.summary}` });
        }
        // include ONLY the recent window you kept
        msgs.push(...ctx.window);
        return msgs;
    }

    /** Enhanced streaming send with native Anthropic streaming */
    private async sendStreaming(
        ctx: Ctx,
        onEvent: (event: StreamEvent) => void,
        abortSignal?: AbortSignal,
    ): Promise<Anthropic.Messages.Message> {
        // Check if already aborted
        if (abortSignal?.aborted) {
            logger.debug(() => 'AnthropicClient sendStreaming: abort signal already aborted');
            throw new Error('Operation was cancelled');
        }

        logger.debug(
            () =>
                `AnthropicClient sendStreaming: received abort signal: ${!!abortSignal}, aborted: ${abortSignal?.aborted}`,
        );

        const messages = this.buildMessages(ctx);
        logger.debug(
            () =>
                `Sending streaming messages to Claude with context: ${JSON.stringify(messages, ellipsesReplacer, 2)}}`,
        );

        const finalMessage = await this.withRetry(async () => {
            return await this.send(ctx, messages, onEvent, abortSignal);
        });

        logger.debug(() => `Final streamed message: ${JSON.stringify(finalMessage, ellipsesReplacer, 2)}}`);

        return finalMessage;
    }

    /** Handle a tool chain step with streaming events */
    private async stepStreaming(
        ctx: Ctx,
        onEvent: (event: StreamEvent) => void,
        abortSignal?: AbortSignal,
    ): Promise<Anthropic.Messages.Message> {
        // Handle tool execution
        if (!this.mcpClient) {
            throw new Error('MCP client not initialized but tool use detected');
        }
        // Check if already aborted
        if (abortSignal?.aborted) {
            logger.debug(() => 'AnthropicClient stepStreaming: abort signal already aborted');
            throw new Error('Operation was cancelled');
        }

        logger.debug(
            () =>
                `AnthropicClient stepStreaming: received abort signal: ${!!abortSignal}, aborted: ${abortSignal?.aborted}`,
        );

        onEvent({ type: 'thinking', message: 'Processing request...' });

        const pageContextMessage = this.createPageContextMessage(ctx.pageContext);
        if (pageContextMessage) {
            ctx.window.push({
                role: 'assistant',
                content: pageContextMessage,
            });
        }

        // Use streaming send for real-time text chunks
        const res = await this.sendStreaming(ctx, onEvent, abortSignal);
        await this.pushTurn(ctx, { role: 'assistant', content: res.content });

        // Find any tool_use in Claude's reply
        const tu = res.content.find((p: any) => p.type === 'tool_use') as Anthropic.Messages.ToolUseBlock;
        if (!tu) return res; // no tool call, done

        // Notify about tool usage
        onEvent({ type: 'tool_use', toolName: tu.name, input: tu.input });

        // Check abort signal before tool execution
        if (abortSignal?.aborted) {
            logger.debug(() => 'AnthropicClient stepStreaming: abort signal detected before tool execution');
            throw new Error('Operation was cancelled');
        }

        const result = await this.mcpClient.callTool({
            name: tu.name,
            arguments: tu.input as Dict<any>,
        });

        // Check abort signal after tool execution
        if (abortSignal?.aborted) {
            logger.debug(() => 'AnthropicClient stepStreaming: abort signal detected after tool execution');
            throw new Error('Operation was cancelled');
        }

        // Stream the raw tool result immediately for fast feedback
        onEvent({
            type: 'tool_result_raw',
            toolName: tu.name,
            result: result.content,
        });

        // Echo tool_result back to Claude so it can interpret/format the result
        const toolResultTurn = {
            role: 'user' as const,
            content: [{ type: 'tool_result', tool_use_id: tu.id, content: result.content } as ToolResultBlockParam],
        };
        await this.pushTurn(ctx, toolResultTurn);

        // TODO: do we need to send the same content twice, first as tool_result_raw and then as tool_result?
        // If we don't send tool_result the tool indicator remains in 'running' state.
        onEvent({
            type: 'tool_result',
            toolName: tu.name,
            result: result.content,
        });
        // TODO: review this hack -- adding an empty line to the copilot response
        onEvent({ type: 'text_chunk', text: '\n#  \n' });

        // Continue the conversation - Claude will process the tool result
        logger.debug(
            () =>
                `AnthropicClient stepStreaming: making recursive call with abort signal: ${!!abortSignal}, aborted: ${abortSignal?.aborted}`,
        );
        return this.stepStreaming(ctx, onEvent, abortSignal);
    }

    private async summarize(turns: Turn[], system: string) {
        const msgs = turns.map(t => ({ role: t.role, content: t.content }));
        const resp = await this.withRetry(async () => {
            return await (this.client.messages as Anthropic.Messages).create({
                ...this.config.summarize,
                system: system,
                messages: [
                    { role: 'user', content: 'Summarize these turns with key facts, decisions, and tool outcomes:' },
                    { role: 'user', content: JSON.stringify(msgs).slice(0, 40_000) },
                ],
            });
        });
        return resp.content.map((c: any) => c.text || '').join('\n');
    }

    public generateConversationTitle(messages: MessageParam[]): Promise<string> {
        const initialQuestion = messages[0]?.content || '';
        return this.withRetry(async (): Promise<string> => {
            const result = await (this.client.messages as Anthropic.Messages).create({
                ...this.config.infer,
                max_tokens: 2000,
                messages: [
                    {
                        role: 'user',
                        content: `
                    The following sentence is the first question of an AI assistant conversation, please write a summary title (max 32 characters) for the conversation based on the following initial question, the response must not contain anything else but the summary.

                    ${initialQuestion}`,
                    },
                ],
            });

            if (result.content[0].type !== 'text') {
                throw new Error('Unexpected non-JSON response from Claude when generating starter questions');
            }

            console.log(result.content[0].text);

            try {
                return result.content[0].text;
            } catch {
                throw new Error('Unexpected non-JSON response from Claude when generating starter questions');
            }
        });
    }

    public async generateStarterQuestions(): Promise<string[]> {
        const userGroups = await CoreHooks.createChatbotManager(this.context.application)?.getUserGroupNames(
            this.context,
        );
        const userGroupsMessage = !_.isEmpty(userGroups)
            ? `The user has the following group assignment, so try to make the questions relevant to these: ${userGroups.join(', ')}.`
            : '';
        const factories = this.context.application.getAllSortedFactories().map(f => f.name);
        return this.withRetry(async (): Promise<string[]> => {
            const result = await (this.client.messages as Anthropic.Messages).create({
                ...this.config.infer,
                max_tokens: 2000,
                messages: [
                    {
                        role: 'user',
                        content: `
                    You must generate exactly 3 conversation starter questions for a chatbot for an ERP business management system called "${this.context.configuration.getProductName()}".
                    This chatbot is capable of answering questions about the data it manages. The product manages the following business objects ${factories.join(', ')}. The questions must be relevant to these business objects. ${userGroupsMessage}
                    They should not be very complex, but rather simple and easy to understand by a typical user of the product.
                    The questions must be simple analytical questions about the data that the system holds.
                    The response must be pure formatted as a valid JSON array of the questions. IT MUST NOT CONTAIN ANY OTHER FORMATTING.`,
                    },
                ],
            });

            if (result.content[0].type !== 'text') {
                throw new Error('Unexpected non-JSON response from Claude when generating starter questions');
            }

            try {
                return JSON.parse(result.content[0].text);
            } catch {
                throw new Error('Unexpected non-JSON response from Claude when generating starter questions');
            }
        });
    }

    async callStreaming(
        messages: MessageParam[],
        pageContext: PageContext,
        onEvent: (event: StreamEvent) => void,
        conversationId?: string,
        abortSignal?: AbortSignal,
    ): Promise<string> {
        try {
            // Check if already aborted
            if (abortSignal?.aborted) {
                throw new Error('Operation was cancelled');
            }

            await this.init();

            const tools = this.mcpClient ? ((await this.getAvailableTools()) ?? []) : [];

            const message = await this.stepStreaming(
                {
                    system: '',
                    tools,
                    window: [
                        {
                            role: 'user',
                            content: `Provide direct, minimal answers without showing your work or reasoning steps unless explicitly requested. Focus only on the final answer the user needs. The current time is ${new Date().toISOString()}`,
                        },
                        ...messages,
                    ],
                    maxWindowTokens: this.config.maxWindowTokens,
                    pageContext,
                },
                onEvent,
                abortSignal,
            );

            const finalText = message.content
                .filter((c: any) => c.type === 'text')
                .map((c: any) => c.text)
                .join('\n');

            const newConversationId = await CoreHooks.createChatbotManager(
                this.context.application,
            )?.createOrUpdateConversation(this.context, {
                id: conversationId,
                messages: [...messages, message],
                title: !conversationId ? await this.generateConversationTitle(messages) : undefined,
                inputTokensUsed: message.usage?.input_tokens,
                outputTokensUsed: message.usage?.output_tokens,
            });

            onEvent({ type: 'completed', finalText, conversationId: newConversationId });
            return finalText;
        } catch (error) {
            onEvent({ type: 'error', error: error.message });
            logger.error(error);
            return error.message;
        }
    }

    /**
     * Cleanup MCP resources (call this when shutting down the application)
     */
    async cleanup(): Promise<void> {
        if (this.mcpClient) {
            try {
                await this.mcpClient.close();
                logger.info('MCP client closed');
            } catch (error) {
                logger.error(() => `Error closing MCP client: ${error.message}`);
            }
            this.mcpClient = undefined;
        }
        if (this.mcpServer) {
            try {
                await this.mcpServer.close();
                logger.info('MCP server closed');
            } catch (error) {
                logger.error(() => `Error closing MCP server: ${error.message}`);
            }
        }
    }
}
