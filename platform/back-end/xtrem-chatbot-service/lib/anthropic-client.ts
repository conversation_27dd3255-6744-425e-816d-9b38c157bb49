import Anthropic from '@anthropic-ai/sdk';
import { ConfigManager } from '@sage/xtrem-config';
import { Context } from '@sage/xtrem-core';
import { BaseClient } from './base-client';

// Use haiku for now to save costs during dev and avoid overloaded errors
// const mainModel = 'claude-3-5-sonnet-20241022';
const mainModel = 'claude-3-5-haiku-latest';

export class AnthropicClient extends BaseClient {
    constructor(context: Context) {
        const apiKey = ConfigManager.current.mcpServer?.anthropicApiKey;
        if (!apiKey) {
            throw new Error('Anthropic API key is required when using anthropic client type');
        }
        super(context, new Anthropic({ apiKey }), mainModel);
    }
}
