# MCP POC

## Setup

Pull mcp-poc branch and build it.

Add this key to your xtrem-config.yml file:

```yaml
mcpServer:
    enabled: true
```

## Playing with it

Load test data and start your application as usual.
Navigate to http://localhost:8240

Click the Sage Copilot icon (S+) in the top bar. This will open the chat window.

Little demo script:

- Show me the orders. // Notice that it shows all orders (sales, purchasing, work, transfer)
- only the sales ones. // Context works
- Which items generated the most revenue this year?
- last year? // Look at the insights
- Which ones are out of stock?
- Where are our customers?
- Ou sont nos clients? // Works also in French
- Wo sind unsere Kunden? // German too
- Who are the most performing sales reps? // Error, SDMO does not manage sales reps
- Which nodes are the most complex? // Insights on metadata (for the geeks)

## Security

The data is accessed exclusively through the GraphQL API.
Users can only read the information that their security profile authorizes them to read.

## How big is the code?

```sh
(base) ➜  xtrem-mcp git:(mcp-poc) ✗ wc ./lib/**/*.ts ./resources/**/*.md
       6      23     338 ./lib/config.ts
      34     107    1034 ./lib/env.ts
      46     113    1199 ./lib/fetch.ts
       1       6      46 ./lib/index.ts
      67     230    2061 ./lib/query-tool.ts
     214     661    7398 ./lib/rag/metadata-cache.ts
      37     135    1340 ./lib/resources.ts
     179     743    6926 ./lib/routes/chat.ts
      28      87     868 ./lib/server.ts
      98     412    3832 ./lib/tools.ts
      10      38     268 ./lib/utils.ts
     199     870    6603 ./resources/xtrem-graphql.md
     919    3425   31913 total
```

Less than 1k lines of code, and 20% of it is a markdown file.

## Technical deep dive

https://www.youtube.com/watch?v=ys4aDrcea0Q&t=32s

English subtitles:

- Lord Godfrey, you are not well.
- What is this devilry?

## Gotchas

- It's actually more a _chatbot_ POC than an MCP POC at this stage, but it will be converted soon to a true MCP.

- It works well with small volumes.
  I haven't implemented aggregate queries yet and it needs some prompt tuning to limit paging and give up (rather than take ages) if it would generate very expensive queries.
