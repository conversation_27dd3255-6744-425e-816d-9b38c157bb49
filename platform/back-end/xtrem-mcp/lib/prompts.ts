export class Prompts {
    static serverDescription(applicationName: string, nodeNames: string[]): string {
        return `
MCP server providing access to ${applicationName} business application data via GraphQL API and providing user assistance which relies on static helper documentation.

This server exposes ${nodeNames.length} business data nodes for querying, filtering, sorting, and mutating. All data access respects the application's security permissions.

**Required workflow for all data operations with GraphQL:**
1. Call \`getApiDocumentation\` (once per session) to get GraphQL syntax rules and available node names
2. Call \`getMetadata\` for specific nodes to understand their properties and relationships
3. Use \`queryGraphql\` to execute data queries and mutations

**Required workflow for product support related user questions:**
Assume that the user wants to achieve tasks using the application GUI, unless the user explicitly states they wish to use the API for that.
1. Call \`getDocumentationTopics\` (once per session) to retrieve the list of the user support documentation pages
2. Try to find the most relevant page for the user question
3. Call \`getDocumentationPageContent\` with the selected topic to retrieve the content of the page
4. Answer the user question using the content of the page. If no page is found, answer the user that no documentation is available for the question.
`;
    }

    static getApiDocumentationDescription(): string {
        return `**REQUIRED FIRST STEP**: Retrieves comprehensive GraphQL API documentation and available node names.

**Purpose**: Provides essential syntax, filtering, sorting, pagination, and mutation patterns specific to XTreeM GraphQL queries, plus the complete list of available node names.

**When to call**: Always call this tool first in any workflow - it contains critical syntax rules that differ from standard GraphQL and the authoritative list of queryable nodes.

**Output**: Complete documentation including query structure, filtering operators, natural key usage, property flags, mutation patterns, and the complete list of available node names.

# 🚨 CRITICAL: REQUIRED TOOL SEQUENCE

**ALWAYS follow this exact sequence:**

1. **getApiDocumentation** (once per session)
2. **getMetadata** for ALL nodes you plan to query
3. **getOperationMetadata** for any custom mutations
4. **queryGraphql** to execute queries

**❌ NEVER call queryGraphql without first getting metadata for all involved nodes**

**Why:** GraphQL operations WILL FAIL without proper property validation and knowledge of operational flags.`;
    }

    static getMetadataDescription(): string {
        return `Retrieves detailed schema information for specific business data nodes.

**Purpose**: Provides complete property lists, data types, relationships, and operational flags needed to construct valid GraphQL queries.

**Input**: Array of node names (from API documentation) relevant to the user's request.

**Output**: For each node, returns:
- Property names, types, and descriptions
- Relationship mappings to other nodes
- Operational flags (Can filter, Can sort, Is output, Is input, Is required)
- Natural key definitions
- Package information

**Critical**: Always call this tool before \`queryGraphql\` - queries will fail without proper property validation.

**Best practice**: Request metadata for all nodes you plan to query, including related nodes for joins/filters.
`;
    }

    static getOperationMetadataToolDescription(): string {
        return `Retrieves detailed metadata for specific node operations (mutations).

**Purpose**: Provides parameter schemas and return types for custom business operations beyond standard CRUD mutations.

**Input**:
- \`nodeName\`: The target node (e.g., "salesOrder")
- \`operationName\`: The specific operation (e.g., "approve", "cancel", "recalculate")

**Output**:
- Parameter schemas with types, validation rules, and requirement flags
- Return value structure and available fields
- Operation-specific constraints and business rules

**When to use**: Required before executing custom mutations or complex business operations that aren't standard create/update/delete.
`;
    }

    static queryGraphqlDescription(): string {
        return `Executes GraphQL queries and mutations against the business application's data API.

**Purpose**: Retrieves, filters, sorts, aggregates, creates, updates, and deletes business data using XTreeM-specific GraphQL syntax.

**Input Parameters**:
- \`query\`: Valid XTreeM GraphQL query or mutation string
- \`waitInSeconds\` (optional): Delay before execution for async operations

**Prerequisites** (execute in order):
1. \`getApiDocumentation\` - Learn XTreeM GraphQL syntax rules
2. \`getMetadata\` - Get property schemas for target nodes
3. \`getOperationMetadata\` - For custom mutations, get operation schemas

**Output**: Complete JSON response from GraphQL API, including data, errors, and metadata.

⚠️ PREREQUISITES:
- Must call getMetadata for all nodes used in this query first
- Must call getOperationMetadata for any custom mutation used in this query

**CRITICAL VALIDATION RULES**:
- Use ONLY node names from API documentation - never guess or assume
- Use ONLY properties from \`getMetadata\` - validate all field names
- Respect property flags (Can filter, Can sort, Is output, Is input)
- Follow XTreeM pagination model (edges/nodes, not direct arrays)
- Exclude textStream/binaryStream properties unless specifically requested
- Use natural key syntax (#value) for user-friendly references

**Supported Operations**: Queries, aggregations, standard mutations (create/update/delete), and custom business operations with full filtering, sorting, and pagination support.
`;
    }

    static getDocumentationTopicsDescription(): string {
        return `Retrieves the list of topics available in the user support documentation.

**Purpose**: Provides an overview of the topics covered in the documentation, helping users find relevant information quickly.

**Input**: None

**Output**: A list of documentation topics

**Output**: A list of topic names, each representing a specific area of the user support documentation.

**When to use**: Call this tool when the user asks for help with the product, such as how to achieve a specific task or when the user asks about product features.
`;
    }

    static getDocumentationPageContentDescription(): string {
        return `Retrieves the content of a documentation page based on a support topic

**Purpose**: User support documentation which provides detailed information about a specific topic on how to use the product

**Input**: The name of the topic

**Output**: The content of the documentation page for the specified topic

**When to use**: Call this tool when the user asks for help with a specific topic or needs detailed information about a feature or functionality.
`;
    }

    static addCreateExportTool(): string {
        return `Generates an exported CSV or Excel file based on user input.

        **Purpose**: Generate a downloadable spreadsheet formatted file which contains the relevant data extracted from the application.

        **Input Parameters**:
          - nodeName: The name of the node to export data from (e.g., "Customer", "SalesOrder")
          - outputFormat: The desired format for the exported file. Valid values are 'csv' or 'xlsx'.
          - templateDefinition: List of properties to include in the export. It is an array of objects, each with a dot-notated property \`path\` and a column \`title\`.
          - filter: GraphQL filter statement to filter the list of records that are exported in JSON5-stringified format
          - orderBy: GraphQL orderBy clause to sort the exported records in JSON5-stringified format

        **Output**: A download link to the exported file

        **When to use**: Call this tool whenever the user wishes to export table-like data such as lists. Before calling this tool verify the parameters by using the queryGraphql with a page size of 1.
`;
    }
}
