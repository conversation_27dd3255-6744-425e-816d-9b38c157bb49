import * as _ from 'lodash';
import fetch from 'node-fetch';
import { getLogger, toolError, ToolResponse, toolSuccess } from './utils.js';

const logger = getLogger(__filename, 'metadata-manager');

export interface FetchOptions {
    url: string;
    headers?: Record<string, string>;
    query: string;
    endpoint?: string; // Optional, default is 'api'
}

export async function rawQuery<T = unknown>({ url, headers, query, endpoint = 'api' }: FetchOptions): Promise<T> {
    const urlToCall = `${_.trimEnd(url, '/')}/${endpoint}`;
    logger.debug(() => `[TOOL] rawQuery called: url=${urlToCall}, query=${query}`);
    const response = await fetch(`${_.trimEnd(url, '/')}/${endpoint}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...headers,
        },
        body: JSON.stringify({
            query,
        }),
    });

    if (!response.ok) {
        const responseText = await response.text();
        throw new Error(`GraphQL request failed: ${response.statusText}\n${responseText}`);
    }

    const data = (await response.json()) as any;

    if (data.errors && data.errors.length > 0) {
        // Contains GraphQL errors
        throw new Error(`The GraphQL response has errors, please fix the query: ${JSON.stringify(data, null, 2)}`);
    }

    return data;
}

export async function executeQuery({ url, headers, query, endpoint }: FetchOptions): Promise<ToolResponse> {
    try {
        const data = await rawQuery<object>({ url, headers, query, endpoint });
        return toolSuccess([JSON.stringify(data, null, 2)]);
    } catch (error) {
        return toolError(error);
    }
}
