import * as _ from 'lodash';
import { DataTypeMeta, NodeMetaWithPageName, OperationMeta, PropertyMeta } from './metadata-manager.js';

export class MetadataFormatter {
    static formatEnums(enumLines: string[]): string {
        return `
# Enums

| Enum               | Values             |
| ------------------ | ------------------ |
${enumLines.join('\n')}

`;
    }

    static parseDataTypeAttributes(attributesString: string) {
        const attributes = JSON.parse(attributesString || '{}');
        const enumName = attributes.enumName;
        const enumValues = Object.values(attributes.enum || {}).filter(v => !/^\d/.test(String(v)));

        return { enumName, enumValues };
    }

    static formatEnum(dt: DataTypeMeta) {
        const { enumName, enumValues } = this.parseDataTypeAttributes(dt.attributes);
        return `| ${enumName} | ${enumValues} |`;
    }

    private static getPropertyEnumName(p: { type?: string; dataType?: DataTypeMeta }) {
        return p.type === 'enum' ? this.parseDataTypeAttributes(p.dataType?.attributes ?? '{}').enumName : '';
    }

    private static formatProperty(p: PropertyMeta): string {
        // Use !p.hasDefaultValue rather than p.isRequired because we don't want to mandate input on properties
        // that are required by the UI but have a default value rule.
        return `| ${p.name} | ${p.type} | ${p.isNullable} | ${!p.hasDefaultValue} | ${this.getPropertyEnumName(p)} | ${p.targetFactory?.name ?? ''} | ${p.isOnInputType} | ${p.isOnOutputType} | ${p.isVital} | ${p.canFilter} | ${p.canSort} |`;
    }

    private static getOperationNames(operations: OperationMeta[], kinds: string[]): string[] {
        return operations.filter(op => kinds.includes(op.kind)).map(op => op.name);
    }

    static invalidNodeName(nodeName: string) {
        return `
# ${nodeName}: INVALID NODE NAME

Only use node names returned by the getApiDocumentation tool!
`;
    }

    static formatNode(node: NodeMetaWithPageName) {
        // We include full property details but we only include operation names.
        // The metadata details of operations are retrieve through a separate getOperationMetadata tool
        // so that we don't pollute the documentation with metadata that the LLM won't use.
        const mutationNames = [
            // TODO: add canCreate, canUpdate, canDelete checks (requires more metadata in MetaNodeFactory)
            'create',
            'update',
            'delete',
            ...this.getOperationNames(node.operations, ['mutation']),
        ];
        const asyncMutationNames = this.getOperationNames(node.operations, ['asyncMutation']);
        const queryNames = this.getOperationNames(node.operations, ['query']);

        return `
# ${node.name} (package: ${_.camelCase(node.package.name.split('/')[1] || node.package.name)})

## Properties

| Property Name | Type | Nullable | Required | Enum | Target Node | Is input | Is output | Is component | can filter | can sort |
| -- | -- | -- | -- | -- | -- | -- | -- | -- |
${node.properties.map(p => this.formatProperty(p)).join('\n')}

## Operations

- Mutation names: ${mutationNames.join(',') || '<none>'},
- Async mutation names: ${asyncMutationNames.join(',') || '<none>'},
- Custom query names: ${queryNames.join(',') || '<none>'}

## Natural Key

${node.naturalKey ? `Natural key: ${node.naturalKey.join(',')}` : 'No natural key defined.'}

## Page Name

${node.pageName ? `Page name: ${node.pageName}` : 'No page name defined (no direct links available).'}
`;
    }

    static formatOperation(operation: OperationMeta): string {
        return JSON.stringify(operation, null, 2);
    }
}
