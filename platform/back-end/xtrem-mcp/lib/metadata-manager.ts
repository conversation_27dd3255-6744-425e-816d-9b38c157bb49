import { Dict } from '@sage/xtrem-client';
import { rawQuery } from './fetch.js';
import { MetadataFormatter } from './metadata-formatter.js';
import { getLogger, toolError, ToolResponse, toolSuccess } from './utils.js';

export type UnPromised<T> = T extends Promise<infer U> ? U : T;

const logger = getLogger(__filename, 'fetch');

export type DataTypeMeta = UnPromised<ReturnType<MetadataManager['fetchDataTypesPage']>>['data'][number];
export type NodeMeta = UnPromised<ReturnType<MetadataManager['fetchNodesMetadataPage']>>['data'][number];
export type NodeMetaWithPageName = NodeMeta & { pageName: string | null };
export type PropertyMeta = NodeMeta['properties'][number];
export type OperationMeta = NodeMeta['operations'][number];
export type ParameterMeta = {
    name: string;
    type: string;
    isMandatory?: boolean;
    items?: ParameterMeta[];
    properties?: ParameterMeta[];
    dataType?: string;
};

export interface MetadataManagerCreateOptions {
    applicationName: string;
    endpoint: string;
    bearerToken: string;
}

import { config } from './utils.js';

/**
 * Metadata manager
 *
 * This is created with MetadataManager.create(options), which caches the metadata manager instance.
 *
 * There is one metadata manager instance per endpoint, as all the tenants and their users share the same metadata
 * (the metadata tables are shared by all tenants and all users have access to it).
 *
 * Note: we need a separate manager for custom fields, because those are specific to each tenant.
 */
export class MetadataManager {
    readonly #metadataByNodeName = {} as Dict<NodeMeta>;

    readonly #dataTypes = {} as Dict<DataTypeMeta>;

    readonly #enumTexts = {} as Dict<string>;

    readonly #nodePageMap = {} as Dict<string | null>;

    readonly #allNodeNames: string[] = [];

    readonly #metaPackageName: string;

    readonly #isX3: boolean = config.mcpServer?.applicationName === 'X3';

    private constructor(private options: MetadataManagerCreateOptions) {
        this.#metaPackageName = this.#isX3 ? 'xtremAppMetadata' : 'xtremMetadata';
    }

    private get endpoint() {
        return this.options.endpoint;
    }

    private getAuthorizationHeader() {
        return {
            Authorization: `Bearer ${this.options.bearerToken}`,
        };
    }

    private async fetchAllPages<T>(
        fetchPage: (
            afterCursor?: string,
        ) => Promise<{ data: T[]; pageInfo: { hasNextPage: boolean; endCursor: string } }>,
    ): Promise<T[]> {
        const results = [] as T[];
        let afterCursor: string | undefined = undefined;
        while (true) {
            const { data, pageInfo } = await fetchPage(afterCursor);
            results.push(...data);
            afterCursor = pageInfo.endCursor;
            if (!pageInfo.hasNextPage) break;
        }
        return results;
    }

    private async fetchDataTypesPage(dataTypeNames: string[], pageSize: number, after?: string) {
        const afterArg = after ? `after: ${JSON.stringify(after)},` : '';
        const filter = JSON.stringify(
            JSON.stringify({
                // isActive: true,
                name: { _in: dataTypeNames },
            }),
        );
        const query = `
query {
    ${this.#metaPackageName} {
        metaDataType {
            query (first: ${pageSize}, ${afterArg} filter: ${filter}) {
                edges {
                    node {
                        name
                        type
                        attributes
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    }
}`;

        const result = await rawQuery<{
            data: {
                [packageName: string]: {
                    metaDataType: {
                        query: {
                            edges: {
                                node: {
                                    name: string;
                                    type: string;
                                    attributes: string;
                                };
                            }[];
                            pageInfo: {
                                hasNextPage: boolean;
                                endCursor: string;
                            };
                        };
                    };
                };
            };
        }>({ query, url: this.endpoint, headers: this.getAuthorizationHeader() });

        if (!result) {
            throw new Error('Failed to fetch metadata');
        }

        const pageInfo = result.data[this.#metaPackageName].metaDataType.query.pageInfo;
        const data = result.data[this.#metaPackageName].metaDataType.query.edges.map(edge => ({
            ...edge.node,
        }));

        return { data, pageInfo };
    }

    private nodesFilter(nodeNames: string[] | null) {
        const filter = { isPublished: true, isAbstract: false } as Dict<any>;
        if (nodeNames) {
            filter.name = { _in: nodeNames };
        }
        return JSON.stringify(JSON.stringify(filter));
    }

    private async fetchAllNodeNamesPage(pageSize: number, after?: string) {
        const afterArg = after ? `after: ${JSON.stringify(after)},` : '';
        const query = `
query {
    ${this.#metaPackageName} {
        metaNodeFactory {
            query (
            first: ${pageSize},
            ${afterArg}
            filter: ${this.nodesFilter(null)}) {
                edges {
                    node {
                        name
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }

            }
        }
    }
}`;
        const result = await rawQuery<{
            data: {
                [packageName: string]: {
                    metaNodeFactory: {
                        query: {
                            edges: {
                                node: {
                                    name: string;
                                };
                            }[];
                            pageInfo: {
                                hasNextPage: boolean;
                                endCursor: string;
                            };
                        };
                    };
                };
            };
        }>({ query, url: this.endpoint, headers: this.getAuthorizationHeader() });

        if (!result) {
            throw new Error('Failed to fetch metadata');
        }

        const data = result.data[this.#metaPackageName].metaNodeFactory.query.edges.map(edge => edge.node.name);
        const pageInfo = result.data[this.#metaPackageName].metaNodeFactory.query.pageInfo;

        return { data, pageInfo };
    }

    private async fetchNodesMetadataPage(nodeNames: string[], pageSize: number, after?: string) {
        const afterArg = after ? `after: ${JSON.stringify(after)},` : '';
        const extraNodeSelector = `canCreate canUpdate canDelete canDuplicate`;
        const extraPropertySelector = `hasDefaultValue`;

        const query = `
query {
    ${this.#metaPackageName} {
        metaNodeFactory {
            query (
            first: ${pageSize},
            ${afterArg}
            filter: ${this.nodesFilter(nodeNames)}) {
                edges {
                    node {
                        name
                        package {
                            name
                        }
                        ${extraNodeSelector}
                        naturalKey
                        properties {
                            # Fetch all properties at once with first: 1000
                            query (first: 1000, filter: "{ isPublished: true }") {
                                edges {
                                    node {
                                        name
                                        type
                                        targetFactory {
                                            name
                                        }
                                        dataType {
                                            name
                                            type
                                            attributes
                                        }
                                        isNullable
                                        isRequired
                                        isOnInputType
                                        isOnOutputType
                                        isVital
                                        isVitalParent
                                        canFilter
                                        canSort
                                        ${extraPropertySelector}                                   }
                                }
                            }
                        }
                        operations {
                            query (first: 1000, filter: "{ isPublished: true, isActive: true }") {
                                edges {
                                    node {
                                        name
                                        kind
                                        action
                                        isPublished
                                        isMutation
                                        parameters
                                        return
                                    }
                                }
                            }
                        }
                    }
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    }
}`;
        const result = await rawQuery<{
            data: {
                [packageName: string]: {
                    metaNodeFactory: {
                        query: {
                            edges: {
                                node: {
                                    name: string;
                                    package: { name: string };
                                    // Flags are optional for now, because of X3
                                    canCreate?: boolean;
                                    canUpdate?: boolean;
                                    canDelete?: boolean;
                                    naturalKey: string | null;
                                    properties: {
                                        query: {
                                            edges: {
                                                node: {
                                                    name: string;
                                                    type: string;
                                                    targetFactory?: { name: string };
                                                    dataType?: { name: string; type: string; attributes: string };
                                                    isNullable: boolean;
                                                    isRequired: boolean;
                                                    // Optional for now because of X3
                                                    hasDefaultValue?: boolean;
                                                    isOnInputType: boolean;
                                                    isOnOutputType: boolean;
                                                    isVital: boolean;
                                                    isVitalParent: boolean;
                                                    canFilter: boolean;
                                                    canSort: boolean;
                                                };
                                            }[];
                                        };
                                    };
                                    operations: {
                                        query: {
                                            edges: {
                                                node: {
                                                    name: string;
                                                    kind: string;
                                                    action: string;
                                                    isPublished: boolean;
                                                    isMutation: boolean;
                                                    parameters: string;
                                                    return: string;
                                                };
                                            }[];
                                        };
                                    };
                                };
                                cursor: string;
                            }[];
                            pageInfo: {
                                hasNextPage: boolean;
                                endCursor: string;
                            };
                        };
                    };
                };
            };
        }>({ query, url: this.endpoint, headers: this.getAuthorizationHeader() });

        if (!result) {
            throw new Error('Failed to fetch metadata');
        }

        const data = result.data[this.#metaPackageName].metaNodeFactory.query.edges.map(edge => {
            const nodeMetadata = {
                // Temporary: force default value on flags not provided (yet) by X3.
                canCreate: true,
                canUpdate: true,
                canDelete: true,
                canDuplicate: true,
                ...edge.node,
                properties: edge.node.properties.query.edges.map(propEdge => {
                    return {
                        // Temporary: force default value not provided (yet) by X3.
                        hasDefaultValue: !propEdge.node.isRequired,
                        ...propEdge.node,
                    };
                }),
                operations: edge.node.operations.query.edges.map(opEdge => {
                    return {
                        ...opEdge.node,
                        parameters: JSON.parse(opEdge.node.parameters || '[]') as ParameterMeta[],
                        return: JSON.parse(opEdge.node.return || '{}') as ParameterMeta,
                    };
                }),
                naturalKey: edge.node.naturalKey ? (JSON.parse(edge.node.naturalKey) as string[]) : null,
                // Collect the data type names at the node level, for easier access later
                dataTypeNames: new Set<string>(),
            };
            return nodeMetadata;
        });
        const pageInfo = result.data[this.#metaPackageName].metaNodeFactory.query.pageInfo;

        return { data, pageInfo };
    }

    private async fetchAllNodeNames(): Promise<string[]> {
        return this.fetchAllPages<string>(async afterCursor => this.fetchAllNodeNamesPage(50, afterCursor));
    }

    private async fetchDataTypes(dataTypeNames: string[]): Promise<DataTypeMeta[]> {
        if (dataTypeNames.length === 0) return [];
        return this.fetchAllPages<DataTypeMeta>(afterCursor => this.fetchDataTypesPage(dataTypeNames, 50, afterCursor));
    }

    private async fetchNodesMetadata(nodeNames: string[]): Promise<NodeMeta[]> {
        return this.fetchAllPages<NodeMeta>(async afterCursor =>
            this.fetchNodesMetadataPage(nodeNames, 50, afterCursor),
        );
    }

    private async fetchMissingMetadata(missingNodeNames: string[]): Promise<void> {
        const missingNodesData = await this.fetchNodesMetadata(missingNodeNames);

        for (const nodeMetadata of missingNodesData) {
            this.#metadataByNodeName[nodeMetadata.name] = nodeMetadata;
        }

        // Gather all data types used in the nodes
        const dataTypeNames = this.collectNodesDataTypeNames(missingNodesData);
        const missingDataTypeNames = dataTypeNames.filter(name => !this.#dataTypes[name]);
        if (missingDataTypeNames.length > 0) {
            const dataTypes = await this.fetchDataTypes(missingDataTypeNames);

            for (const dt of dataTypes) {
                this.#dataTypes[dt.name] = dt;
            }
        }

        // Generate the missing enum texts
        for (const dataTypeName of missingDataTypeNames) {
            const dt = this.#dataTypes[dataTypeName];
            if (!dt) {
                throw new Error(`Data type '${dataTypeName}' not found in metadata`);
            }
            if (dt.type === 'enum' && !this.#enumTexts[dt.name]) {
                this.#enumTexts[dt.name] = MetadataFormatter.formatEnum(dt);
            }
        }
    }

    private collectParamDataTypeNames(node: NodeMeta, param: ParameterMeta): void {
        if (param.type === 'enum') {
            logger.error('ENUM PARAM:' + JSON.stringify(param));
            if (!param.dataType) {
                throw new Error(`Data type for enum '${param.name}' is missing`);
            }
            node.dataTypeNames.add(param.dataType);
        } else if (Array.isArray(param.properties)) {
            param.properties.forEach((prop: any) => this.collectParamDataTypeNames(node, prop));
        }
    }

    private collectNodeDataTypeNames(node: NodeMeta) {
        for (const prop of node.properties) {
            if (prop.dataType) {
                node.dataTypeNames.add(prop.dataType.name);
            }
        }
        for (const op of node.operations) {
            op.parameters.forEach(param => this.collectParamDataTypeNames(node, param));
            this.collectParamDataTypeNames(node, op.return);
        }
    }

    private collectNodesDataTypeNames(nodes: NodeMeta[]) {
        let dataTypeNames = new Set<string>();
        for (const node of nodes) {
            this.collectNodeDataTypeNames(node);
            dataTypeNames = new Set([...dataTypeNames, ...node.dataTypeNames]);
        }
        return Array.from(dataTypeNames);
    }

    async getAllNodeNames(): Promise<string[]> {
        if (this.#allNodeNames.length > 0) {
            return this.#allNodeNames;
        }
        try {
            this.#allNodeNames.push(...(await this.fetchAllNodeNames()).sort((a, b) => a.localeCompare(b)));
        } catch (error: any) {
            logger.error(`Failed to load metadata: ${error.stack || error.message}`);
            throw new Error('Failed to load metadata');
        }

        return this.#allNodeNames;
    }

    private async loadMissingMetadata(nodeNames: string[]): Promise<void> {
        const missingNodeNames = nodeNames.filter(name => !this.#metadataByNodeName[name]);
        if (missingNodeNames.length > 0) await this.fetchMissingMetadata(missingNodeNames);
    }

    private async getMetadataSections(nodeNames: string[]): Promise<string[]> {
        await this.loadMissingMetadata(nodeNames);

        const sections = [] as string[];
        const enumLines = {} as Dict<string>;

        for (const nodeName of nodeNames) {
            const nodeMetadata = this.#metadataByNodeName[nodeName];
            if (!nodeMetadata) {
                sections.push(MetadataFormatter.invalidNodeName(nodeName));
                continue;
            }

            // Get the page name for this node
            // Interop cannot access /metadata endpoint. For now, build the page name manually
            // const pageName = await this.getTunnelPageName(nodeName);
            const pageName = `${nodeMetadata.package.name}/${nodeName}`;
            const nodeWithPageName = { ...nodeMetadata, pageName };

            sections.push(MetadataFormatter.formatNode(nodeWithPageName));
            for (const dtName of nodeMetadata.dataTypeNames) {
                const dt = this.#dataTypes[dtName];
                if (dt?.type !== 'enum') continue;
                if (!this.#enumTexts[dtName]) throw new Error(`Enum text for '${dtName}' not found in metadata`);
                enumLines[dtName] = this.#enumTexts[dtName];
            }
        }
        sections.push(MetadataFormatter.formatEnums(Object.values(enumLines)));
        return sections;
    }

    async getMetadataText(nodeNames: string[]): Promise<ToolResponse> {
        try {
            const sections = await this.getMetadataSections(nodeNames);
            return toolSuccess(sections);
        } catch (error) {
            logger.error(`Failed to get metadata text: ${error.stack || error.message}`);
            return toolError(error);
        }
    }

    private async getOperationMetadataText(nodeName: string, operationName: string): Promise<string> {
        await this.loadMissingMetadata([nodeName]);

        const nodeMetadata = this.#metadataByNodeName[nodeName];
        if (!nodeMetadata) {
            throw new Error(`Node '${nodeName}' not found in metadata`);
        }

        const operations = nodeMetadata.operations.filter(op => op.name === operationName);
        if (!operations.length) {
            throw new Error(`Operation '${operationName}' not found in node '${nodeName}' metadata`);
        }

        let operation: OperationMeta = operations[0];
        if (operations[0].action) {
            const startOperation = operations.find(op => op.action === 'start');
            const trackOperation = operations.find(op => op.action === 'track');
            if (!startOperation || !trackOperation) {
                throw new Error(`Async mutation '${operationName}' not fully defined in node '${nodeName}' metadata`);
            }
            operation = {
                ...startOperation,
                return: trackOperation.return,
            };
        }

        logger.error('Operation metadata:' + JSON.stringify(operation));

        return MetadataFormatter.formatOperation(operation);
    }

    async getOperationMetadata(nodeName: string, operationName: string): Promise<ToolResponse> {
        try {
            const text = await this.getOperationMetadataText(nodeName, operationName);
            return toolSuccess([text]);
        } catch (error) {
            return toolError(error);
        }
    }

    async createExportedList({
        templateDefinition,
        nodeName,
        filter = '{}',
        orderBy = '{}',
        outputFormat = 'csv',
    }: { nodeName: string; outputFormat: 'csv' | 'xlsx'; templateDefinition: { path: string; title: string; }[]; filter?: string; orderBy?: string }): Promise<ToolResponse> {

        const initialQuery = `mutation{global{exportByTemplateDefinition{start(outputFormat:"${outputFormat}",nodeName:"${nodeName}",filter:"${filter}",orderBy:"${orderBy}",templateDefinition:[${templateDefinition.map(col => `{path:"${col.path}",title:"${col.title}"}`).join(',')}]){trackingId}}}}`;
        console.log(initialQuery);
        const result = await rawQuery<{
            data: {
                global: {
                    exportByTemplateDefinition: {
                        start: {
                            trackingId: string;
                        };
                    };
                };
            };
        }>({ query: initialQuery, url: this.endpoint, headers: this.getAuthorizationHeader() });

        const { trackingId } = result.data.global.exportByTemplateDefinition.start;

        try {
            const trackingResult = await new Promise<string>((resolve, reject) => {
                const checkStatus = async () => {
                    logger.info(`Checking Tracking ID: ${trackingId}`);
                    const trackingQuery = `{global{exportByTemplateDefinition{track(trackingId:"${trackingId}") {status,result,errorMessage}}}}`;
                    console.log(trackingQuery);
                    const trackingQueryResult = await rawQuery<{
                        data: {
                            global: {
                                exportByTemplateDefinition: {
                                    track: {
                                        status: string;
                                        errorMessage: string;
                                        result: string;
                                    };
                                };
                            };
                        };
                    }>({ query: trackingQuery, url: this.endpoint, headers: this.getAuthorizationHeader() });

                    console.log(trackingQueryResult.data.global.exportByTemplateDefinition);
                    const { status, errorMessage, result } = trackingQueryResult.data.global.exportByTemplateDefinition.track;
                    logger.info(`Export status: ${status} for Tracking ID ${trackingId}`);
                    if (status === 'success') {
                        logger.info(`Export success: ${result}`);
                        resolve(result);
                    } else if (status === 'error' || status === 'stopped' || status === 'interrupted' || errorMessage) {
                        logger.error(`Export failed - ${status}: ${errorMessage || 'unknown'}`);
                        reject(new Error(errorMessage || 'unknown'));
                    } else {
                        logger.info(`Trying again in a second - ${status}`);
                        setTimeout(checkStatus, 1000);
                    }
                };

                checkStatus().catch(err => reject(err));
            });

            return toolSuccess([trackingResult]);
        } catch (err) {
            return toolError(err);
        }
    }

    async getTunnelPageName(nodeName: string): Promise<string | null> {
        if (Object.prototype.hasOwnProperty.call(this.#nodePageMap, nodeName)) {
            return this.#nodePageMap[nodeName];
        }

        const result = await rawQuery<{ data: { getNodeDetails: { defaultDataTypeDetails: { tunnelPage: string } } } }>(
            {
                url: this.endpoint,
                query: `query{getNodeDetails(nodeName: "${nodeName}") {defaultDataTypeDetails { tunnelPage }}}`,
                endpoint: 'metadata',
                headers: this.getAuthorizationHeader(),
            },
        );
        const page = result.data.getNodeDetails?.defaultDataTypeDetails?.tunnelPage;
        this.#nodePageMap[nodeName] = page ?? null;
        return this.#nodePageMap[nodeName];
    }

    static #instances = {} as Dict<MetadataManager>;

    // The metadata only depends on the application and its version.
    // So we can use the endpoint as instance key.
    private static getInstanceKey(options: MetadataManagerCreateOptions): string {
        return options.endpoint;
    }

    static create(options: MetadataManagerCreateOptions): MetadataManager {
        const key = this.getInstanceKey(options);
        if (!this.#instances[key]) this.#instances[key] = new MetadataManager(options);
        return this.#instances[key];
    }
}
