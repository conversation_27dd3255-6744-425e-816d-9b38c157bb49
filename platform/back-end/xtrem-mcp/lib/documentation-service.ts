import { JSD<PERSON> } from 'jsdom';
import * as _ from 'lodash';
import fetch from 'node-fetch';

let supportPageDictionary: Record<string, string> = {};

export async function getDocumentationTopics(documentationServiceUrl: string): Promise<Record<string, string>> {
    if (!_.isEmpty(supportPageDictionary)) {
        return supportPageDictionary; // Return cached topics if already fetched
    }

    // TODO: This is a temporary solution, we need to work with the content team to provide a proper, reliable sitemap.
    const response = await fetch(`${documentationServiceUrl}/en-US/Data/Tocs/XTreeM_Help_Chunk0.js`);
    if (!response.ok) {
        throw new Error(`Failed to fetch documentation topics: ${response.statusText}`);
    }
    const contentToEvaluate = await response.text();
    // Evaluate the content to extract the topics
    // The content is a JavaScript module that defines a `define` function
    const define = (content: Record<string, { t: string[] }>) => {
        supportPageDictionary = Object.keys(content).reduce((acc, urlFragment) => {
            if (urlFragment === '___') return acc;
            const pageTitle = content[urlFragment].t[0];
            if (!pageTitle) return acc;
            return { ...acc, [pageTitle]: urlFragment }; // Flatten the structure
        }, {});
    };
    const moduleDefinition = new Function(...['define', contentToEvaluate]);

    moduleDefinition(define, contentToEvaluate);

    return supportPageDictionary;
}

export async function getDocumentationPageContent(documentationServiceUrl: string, topic: string): Promise<string> {
    const topicDictionary = await getDocumentationTopics(documentationServiceUrl);
    const pageUrl = topicDictionary[topic];
    const result = await fetch(`${_.trimEnd(documentationServiceUrl, '/')}/en-US/${_.trimStart(pageUrl, '/')}`);
    const text = await result.text();
    const dom = new JSDOM(text);
    const textContent = dom.window.document
        .getElementById('mc-main-content')
        ?.textContent?.replace(/\s\s+/g, ' ')
        ?.trim();
    return textContent || '';
}
