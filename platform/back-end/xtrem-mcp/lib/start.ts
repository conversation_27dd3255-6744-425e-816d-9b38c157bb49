import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { InteropGraphqlClient } from '@sage/xtrem-core';

export async function start(): Promise<void> {
    console.log = console.error;
    console.warn = console.error;

    const { McpServerHelper } = await import('./mcp-server.js');
    const { config, getLogger } = await import('./utils.js');

    const logger = getLogger(__filename, 'server');

    try {
        if (!config.mcpServer || !config.mcpServer.enabled) {
            logger.info(
                'MCP server is not configured or not enabled. Please set mcpServer.enabled in xtrem-config.yml.',
            );
            return;
        }
        const applicationName = config.mcpServer.applicationName || 'Generic';
        const applicationEndpoint = config.mcpServer.applicationEndpoint || 'http://localhost:8240/';

        const bearerToken = await InteropGraphqlClient.getBearerToken({
            tenantId: '7'.repeat(21),
            sourceUserEmail: '<EMAIL>',
            appName: 'x3_connector',
            scope: 'mcp',
        });

        const server = await McpServerHelper.create({
            endpoint: applicationEndpoint,
            applicationName,
            tenantId: '7'.repeat(21),
            userEmail: '<EMAIL>',
            bearerToken,
        });

        logger.info(`Starting MCP server for application: ${applicationName}`);
        const transport = new StdioServerTransport();
        await server.server.connect(transport);
    } catch (error) {
        logger.error(`Failed to start MCP server: ${error.stack || error.message}`);
        process.exit(1);
    }
}

start().catch(console.error);
