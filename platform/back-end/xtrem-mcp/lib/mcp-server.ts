import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import * as _ from 'lodash';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { z } from 'zod'; // Or any validation library that supports Standard Schema
import { getDocumentationPageContent, getDocumentationTopics } from './documentation-service.js';
import { executeQuery } from './fetch.js';
import { MetadataManager } from './metadata-manager.js';
import { Prompts } from './prompts.js';
import { capitalize, ellipsesText, getLogger, getPackageDir, getPackageVersion, ToolResponse } from './utils.js';

const logger = getLogger(__filename, 'server');

export interface CreateOptions {
    endpoint: string;
    applicationName: string;
    tenantId: string;
    userEmail: string;
    bearerToken: string;
    documentationServiceUrl?: string;
}

export interface ConstructOptions extends CreateOptions {
    metadataManager: MetadataManager;
    nodeNames: string[];
}

export interface Tool {
    name: string;
    description: string;
    parameters: Record<string, any>;
    execute: (args: Record<string, any>) => Promise<ToolResponse>;
}

export class McpServerHelper {
    readonly server: McpServer;

    constructor(readonly options: ConstructOptions) {
        logger.info(`Creating MCP server for application: ${options.applicationName}, endpoint: ${options.endpoint}`);
        const version = getPackageVersion();
        this.server = new McpServer({
            name: `sage-xtrem-${_.kebabCase(options.applicationName)}-mcp-server`,
            description: Prompts.serverDescription(options.applicationName, options.nodeNames),
            version,

        });

        this.addGetApiDocumentationTool();
        this.addGetMetadataTool();
        this.addGetOperationMetadataTool();
        this.addQueryTool();
        this.addGetDocumentationTopics();
        this.addGetDocumentationPageContent();
        this.addCreateExportTool();
        logger.info(`MCP Server created`);
    }

    get metadataManager(): MetadataManager {
        return this.options.metadataManager;
    }

    private async executeGraphqlQuery(query: string): Promise<ToolResponse> {
        logger.debug(() => `[TOOL] queryGraphql called: query=${query}`);
        const result = await executeQuery({
            url: this.options.endpoint,
            headers: {
                Authorization: `Bearer ${this.options.bearerToken}`,
                'Content-Type': 'application/json',
            },
            query,
        });
        logger.debug(() => `[TOOL] queryGraphql returns: ${JSON.stringify(result, ellipsesText, 2)}`);
        return result;
    }

    private waitForSeconds(seconds: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, seconds * 1000));
    }

    private addQueryTool(): void {
        this.server.tool(
            'queryGraphql',
            Prompts.queryGraphqlDescription(),
            {
                query: z.string().describe('The GraphQL query to execute'),
                waitInSeconds: z
                    .number()
                    .optional()
                    .describe('An optional wait time in seconds before executing the query'),
            },
            async ({ query, waitInSeconds }: { query: string; waitInSeconds?: number }) => {
                if (waitInSeconds) await this.waitForSeconds(waitInSeconds);
                return this.executeGraphqlQuery(query);
            },
        );
    }

    private addGetMetadataTool(): void {
        this.server.tool(
            'getMetadata',
            Prompts.getMetadataDescription(),
            {
                nodeNames: z.array(z.string()).describe('Node names to retrieve metadata for'),
            },
            async ({ nodeNames }: { nodeNames: string[] }) => {
                logger.debug(() => `[TOOL] getMetadata called: nodeNames=${nodeNames.join(', ')}`);
                const result = await this.metadataManager.getMetadataText(nodeNames.map(capitalize));
                logger.debug(() => `[TOOL] getMetadata returns: ${JSON.stringify(result, null, 2)}`);
                return result;
            },
        );
    }

    private addGetOperationMetadataTool(): void {
        this.server.tool(
            'getOperationMetadata',
            Prompts.getOperationMetadataToolDescription(),
            {
                nodeName: z.string().describe('Node name to retrieve operation metadata for'),
                operationName: z.string().describe('Operation name to retrieve metadata for'),
            },
            async ({ nodeName, operationName }: { nodeName: string; operationName: string }) => {
                logger.debug(
                    () => `[TOOL] getOperationMetadata called: nodeName=${nodeName}, operationName=${operationName}`,
                );
                const result = await this.metadataManager.getOperationMetadata(nodeName, operationName);
                logger.debug(() => `[TOOL] getOperationMetadata returns: ${JSON.stringify(result, ellipsesText, 2)}`);
                return result;
            },
        );
    }

    private addCreateExportTool(): void {
        this.server.tool(
            'createListExport',
            Prompts.addCreateExportTool(),
            {
                nodeName: z.string().describe('Node name to export data from'),
                outputFormat: z.enum(['csv', 'xlsx']).optional().default('csv').describe('The format to export the data to'),
                templateDefinition: z.array(z.object({
                    path: z.string().describe('The dot-notated path of the property to include in the export'),
                    title: z.string().describe('The column title to use in the export'),
                })).describe('List of properties to be exported'),
                filter: z.string().optional().describe('JSON5-stringified GraphQL filter statement'),
                orderBy: z.string().optional().describe('JSON5-stringified GraphQL orderBy statement'),
            },
            async (args: { nodeName: string; outputFormat: 'csv' | 'xlsx'; templateDefinition: { path: string; title: string; }[]; filter?: string, orderBy?: string; }) => {
                logger.debug(
                    () => `[TOOL] createListExport called: nodeName=${args.nodeName}, outputFormat=${args.outputFormat}`,
                );
                console.log(args);
                const result = await this.metadataManager.createExportedList(args);
                logger.debug(() => `[TOOL] createListExport returns: ${JSON.stringify(result, ellipsesText, 2)}`);
                return result;
            },
        );
    }

    private getApiDocumentation(): Promise<string> {
        const filename = path.join(getPackageDir(), 'resources/xtrem-graphql-doc.md');
        return fs.promises.readFile(filename, 'utf-8');
    }

    private addGetApiDocumentationTool(): void {
        this.server.tool(
            'getApiDocumentation',
            Prompts.getApiDocumentationDescription(),
            { cache_control: { type: 'ephemeral' } },
            async () => {
                logger.debug(() => `[TOOL] getApiDocumentation called`);
                const doc = await this.getApiDocumentation();
                const formattedNodeNames = this.options.nodeNames.sort().join(', ');
                const nodeList = `\n\n# Available Node Names\n\nThe API exposes the following ${this.options.nodeNames.length} business data nodes:\n\n${formattedNodeNames}\n\n**IMPORTANT**: Only use node names from the list above - never guess or assume node names exist.`;
                const fullDoc = doc + nodeList;
                logger.debug(() => `[TOOL] getApiDocumentation returns: ${ellipsesText(fullDoc)}`);
                return { isError: false, content: [{ type: 'text', text: fullDoc }] } as ToolResponse;
            },
        );
    }

    private addGetDocumentationTopics(): void {
        this.server.tool('getDocumentationTopics', Prompts.getDocumentationTopicsDescription(), {}, async () => {
            if (!this.options.documentationServiceUrl) {
                logger.debug(() => `[TOOL] getDocumentationTopics: No documentation URL configured`);
                return {
                    isError: true,
                    content: [{ type: 'text', text: 'No documentation available' }],
                } as ToolResponse;
            }

            const topics = await getDocumentationTopics(this.options.documentationServiceUrl);

            return {
                isError: false,
                content: [{ type: 'text', text: Object.keys(topics).join(', ') }],
            } as ToolResponse;
        });
    }

    private addGetDocumentationPageContent(): void {
        this.server.tool(
            'getDocumentationPageContent',
            Prompts.getDocumentationPageContentDescription(),
            {
                topic: z.string().describe('The name of the documentation topic to retrieve content for'),
            },
            async ({ topic }: { topic: string }) => {
                if (!this.options.documentationServiceUrl) {
                    logger.debug(() => `[TOOL] getDocumentationPageContent: No documentation URL configured`);
                    return {
                        isError: true,
                        content: [{ type: 'text', text: 'No documentation available' }],
                    } as ToolResponse;
                }

                if (!topic) {
                    logger.debug(() => `[TOOL] getDocumentationPageContent: No topic specified`);
                    return {
                        isError: true,
                        content: [{ type: 'text', text: 'No documentation available' }],
                    } as ToolResponse;
                }

                const content = await getDocumentationPageContent(this.options.documentationServiceUrl, topic);
                if (!content) {
                    logger.debug(() => `[TOOL] getDocumentationPageContent: No content found for topic ${topic}`);
                    return {
                        isError: true,
                        content: [{ type: 'text', text: 'No documentation available' }],
                    } as ToolResponse;
                }

                return { isError: false, content: [{ type: 'text', text: content }] } as ToolResponse;
            },
        );
    }

    static async create(createOptions: CreateOptions): Promise<McpServer> {
        const metadataManager = MetadataManager.create(createOptions);
        const nodeNames = await metadataManager.getAllNodeNames();
        return (
            await new McpServerHelper({
                ...createOptions,
                metadataManager,
                nodeNames,
            })
        ).server;
    }
}
