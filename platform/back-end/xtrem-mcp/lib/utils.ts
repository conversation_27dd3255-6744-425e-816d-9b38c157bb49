import { ConfigManager } from '@sage/xtrem-config';
import { Logger } from '@sage/xtrem-log';
import * as fs from 'node:fs';
import * as path from 'node:path';

// Logger returned by Lo<PERSON>.getLogger does not output anything.
// So hack it with console output for now.
// Just set DEBUG=1 in your environment to see debug logs.

export function getLogger(_filename: string, _xzdomain: string) {
    if (true) return Logger.getLogger(_filename, _xzdomain);
    return {
        info: (str: string) => process.stderr.write(`[INFO] ${str}\n`),
        warn: (str: string) => process.stderr.write(`[WARN] ${str}\n`),
        error: (str: string) => process.stderr.write(`[ERROR] ${str}\n`),
        debug: (cb: () => string) => {
            if (process.env.DEBUG === '1') process.stderr.write(`[DEBUG] ${cb()}\n`);
        },
    };
}

export interface ToolResponse {
    [x: string]: unknown;
    isError?: boolean;
    content: { type: 'text'; text: string }[];
}

export type Message = {
    role: 'user' | 'assistant' | 'system';
    content: string;
};

export function toolSuccess(texts: string[]): ToolResponse {
    return {
        isError: false,
        content: texts.map(text => ({ type: 'text', text })),
    };
}

export function toolError(error: Error): ToolResponse {
    console.error(`Tool failed: ${error.stack || error.message}`);
    return {
        isError: true,
        content: [{ type: 'text', text: error.message }],
    };
}

export function getPackageDir() {
    return path.join(__dirname, '../..');
}

export function getPackageVersion(): `${number}.${number}.${number}` {
    const packageJson = JSON.parse(fs.readFileSync(path.join(getPackageDir(), 'package.json'), 'utf-8'));
    return packageJson.version;
}

export const config = ConfigManager.load(__dirname);

export function capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
}

export function ellipsesText(value: string, maxLength: number = 80): string {
    if (value.length <= maxLength) return value;
    return value.slice(0, maxLength - 3) + '...';
}

export function ellipsesReplacer(_key: string, value: any): any {
    return typeof value === 'string' ? ellipsesText(value) : value;
}
