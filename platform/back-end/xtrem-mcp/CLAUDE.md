# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an MCP (Model Context Protocol) server for XTreeM, a GraphQL-based business application platform. The server acts as a bridge between AI assistants and XTreeM applications, allowing natural language queries to be converted into GraphQL operations.

## Development Commands

### Build and Development

- `pnpm build` - Compile TypeScript to JavaScript in the `build/` directory
- `pnpm build:binary` - Build production binary with minification
- `pnpm start` - Start the MCP server (requires built code)
- `pnpm test` - Run Mocha tests on built JavaScript files
- `pnpm clean` - Remove build directory

### CLI Usage

The server can be started with: `pnpm start`

### Configuration Requirements

The server requires `mcpServer.enabled: true` in the xtrem-config.yml file to run.

## Architecture

### Core Components

**McpServerHelper** (`lib/mcp-server.ts`) - Main server class that:

- Creates MCP server instance
- Registers GraphQL query tools and metadata tools
- Provides server instance for connection management

**Start Function** (`lib/start.ts`) - Server startup logic:

- Handles configuration validation
- Creates McpServerHelper instance
- Manages stdio transport connection
- Contains all server initialization and startup logic

**MetadataManager** (`lib/metadata-manager.ts`) - Handles GraphQL schema introspection:

- Fetches and caches node metadata from XTreeM applications
- Provides formatted metadata for AI tools
- Manages data types and enumeration information

**GraphQL Integration** (`lib/fetch.ts`) - Handles GraphQL communication:

- Executes queries against XTreeM endpoints
- Manages authentication and headers
- Processes responses and errors

### MCP Tools Exposed

1. **getApiDocumentation** - Returns GraphQL API documentation
2. **getNodeNames** - Lists available GraphQL nodes
3. **getMetadata** - Gets detailed node schema information
4. **getOperationMetadata** - Gets mutation/operation details
5. **queryGraphql** - Executes GraphQL queries with optional delays

### Data Flow

1. Server connects to XTreeM GraphQL endpoint
2. MetadataManager fetches schema information
3. MCP tools are registered with schema-aware capabilities
4. AI assistants can query metadata and execute GraphQL operations
5. All data access respects XTreeM security profiles

### Package Dependencies

Key dependencies include:

- `@modelcontextprotocol/sdk` - MCP protocol implementation
- `@sage/xtrem-*` packages - XTreeM platform integration
- `graphql` - GraphQL query processing
- `zod` - Schema validation for MCP tools

### Testing

Tests are located in `test/` directory and use Mocha framework. Tests run against compiled JavaScript in the `build/` directory, not TypeScript source.

### Configuration

The server uses XTreeM's configuration system (`@sage/xtrem-config`) and expects:

- `mcpServer.enabled: true`
- Optional `mcpServer.applicationName` and `mcpServer.applicationEndpoint`

### GraphQL Documentation

The `resources/xtrem-graphql-doc.md` file contains comprehensive documentation for:

- GraphQL query syntax and patterns
- Filtering and sorting operations
- Pagination models
- Mutation patterns (standard and async)
- Property flags and constraints

This documentation is served via the `getApiDocumentation` tool to help AI assistants understand how to construct valid XTreeM GraphQL queries.
