import { assert } from 'chai';
import { MCPTestClient } from 'mcp-test-client';

describe('MCP Server Tests', () => {
    let client: MCPTestClient;
    before(async () => {
        client = new MCPTestClient({
            serverCommand: 'bun',
            serverArgs: ['./path/to/your/server.ts'],
        });
        await client.init();
    });
    after(async () => {
        await client.cleanup();
    });
    test('should perform addition', async () => {
        await client.assertToolCall('calculate', { operation: 'add', a: 5, b: 3 }, result => {
            assert.strictEqual(result.content[0].text, '8');
        });
    });
});
