# XTreeM GraphQL API Reference

## Query Structure

All XTreeM GraphQL operations follow this standardized structure:

```graphql
query {
    <package-name> {
        <node-name> {
            <verb>(<args>) <selector>
        }
    }
}
```

**Components:**

- `<package-name>`: Package containing the target node (camelCase format)
- `<node-name>`: Target node name (camelCase format)
- `<verb>`: Operation type - `query`, `queryAggregate` for data retrieval
- `<args>`: Optional parameters for filtering, pagination, sorting
- `<selector>`: Fields to retrieve in the response

**Important**: Use the node names from the server description and the `getMetadata` tool to discover the exact package name for each node - never assume package names.

## Pagination

XTreeM uses cursor-based pagination following GraphQL Relay specifications:

**Pagination Arguments:**

- `first: N` - Return first N records (forward pagination)
- `last: N` - Return last N records (backward pagination)
- `after: "cursor"` - Start after specified cursor
- `before: "cursor"` - End before specified cursor

**Response Structure:**

```graphql
{
  edges {
    node { /* actual data */ }
    cursor
  }
  pageInfo {
    hasNextPage
    hasPreviousPage
    startCursor
    endCursor
  }
  totalCount
}
```

**Default Behavior:**

- Page size defaults to 20 records
- For complete result sets, use `first: 100` and iterate with `after` cursor
- Always include `pageInfo` to handle pagination properly

## System Properties

### Primary Key (`_id`)

**Overview**: Every node has a system-generated `_id` property serving as the primary key.

**Characteristics:**

- Always present (not listed in metadata)
- Opaque string format - never display to users
- Required for mutations and updates
- Include in all selectors (minimal performance cost)

**Usage**: Always include `_id` in your field selections for future operations.

### Natural Keys

**Purpose**: User-friendly alternative identifiers that are more meaningful than technical `_id` values.

**Structure:**

- Defined as array of property names in metadata
- Composite keys concatenated with `|` separator
- Reference properties use target record's natural key recursively

**Natural Key Syntax:**
Use `#<natural-key-value>` anywhere an `_id` is expected:

```graphql
# Instead of finding _id for EUR currency first
mutation {
    xtremSales {
        salesOrder {
            create(data: { currency: "#EUR", amount: 1000 }) {
                _id
                orderNumber
            }
        }
    }
}
```

**Benefits:**

- Eliminates lookup queries for known business identifiers
- Makes GraphQL more readable and maintainable
- Works in filters, mutations, and all input contexts

## Filtering

Apply complex business logic filters to narrow query results using JSON5 filter objects.

### Filter Structure

Filters are JSON5-stringified objects mapping node properties to filter conditions. **Recommended**: Use JSON5 syntax for cleaner, more readable filters:

```graphql
query {
    xtremSystem {
        user {
            query(filter: "{ firstName: { _eq: 'John' }, isActive: { _eq: true } }") {
                edges {
                    node {
                        _id
                        firstName
                        lastName
                    }
                }
            }
        }
    }
}
```

**JSON5 advantages**: Unquoted keys, single quotes, trailing commas, and comments make filters more maintainable than strict JSON.

### Reference Property Filtering

Filter on related entities by traversing relationships:

```graphql
# Find users with specific role
query {
    xtremSystem {
        user {
            query(filter: "{ role: { code: { _eq: 'SALES_MANAGER' } } }") {
                edges {
                    node {
                        _id
                        firstName
                        role {
                            code
                            name
                        }
                    }
                }
            }
        }
    }
}
```

**Relationship traversal**: Filters can traverse references to unlimited depth, enabling complex business queries.

### Filter Operators

Complete reference of available filter operators by data type:

| Operator   | Data Types     | Description                 | Example                                                                   |
| ---------- | -------------- | --------------------------- | ------------------------------------------------------------------------- |
| `_eq`      | All            | Equals                      | `{ status: { _eq: 'active' } }`                                           |
| `_ne`      | All            | Not equals                  | `{ status: { _ne: 'cancelled' } }`                                        |
| `_lt`      | Numbers, Dates | Less than                   | `{ price: { _lt: 100 } }`                                                 |
| `_lte`     | Numbers, Dates | Less than or equal          | `{ orderDate: { _lte: '2024-01-01' } }`                                   |
| `_gt`      | Numbers, Dates | Greater than                | `{ amount: { _gt: 1000 } }`                                               |
| `_gte`     | Numbers, Dates | Greater than or equal       | `{ createdAt: { _gte: '2024-01-01' } }`                                   |
| `_in`      | All            | Value in array              | `{ status: { _in: ['active', 'pending'] } }`                              |
| `_nin`     | All            | Value not in array          | `{ category: { _nin: ['obsolete', 'draft'] } }`                           |
| `_regex`   | Strings        | Regular expression match    | `{ name: { _regex: '^A.*' } }`                                            |
| `_atLeast` | Collections    | Minimum items matching      | `{ orderLines: { _atLeast: 3, quantity: { _gt: 10 } } }`                  |
| `_atMost`  | Collections    | Maximum items matching      | `{ tags: { _atMost: 5, category: { _eq: 'internal' } } }`                 |
| `_every`   | Collections    | All items satisfy condition | `{ orderLines: { _every: true, isValid: { _eq: true } } }`                |
| `_none`    | Collections    | No items satisfy condition  | `{ attachments: { _none: true, isPublic: { _eq: true } } }`               |
| `_and`     | Logical        | Logical AND of filter array | `{ _and: [{ price: { _gt: 10 } }, { stock: { _lt: 100 } }] }`             |
| `_or`      | Logical        | Logical OR of filter array  | `{ _or: [{ status: { _eq: 'urgent' } }, { priority: { _eq: 'high' } }] }` |
| `_not`     | Logical        | Logical NOT of filter       | `{ _not: { category: { _eq: 'internal' } } }`                             |

The `_regex` operator may be followed by an `_options` field. For now, the only supported option is `i` to get a case-insensitive match. So `{ name: { _regex: '^a', _options: 'i' } }` will match any string starting with `a` or `A`. Use this option unless you want strict case matching.

**Important**: Only use operators on properties where the metadata indicates "Can filter: true".

### Filter Examples

**Basic OR Logic:**

```graphql
query {
    xtremSystem {
        user {
            query(first: 5, filter: "{ _or: [{ firstName: { _regex: '^Jo' } }, { lastName: { _eq: 'Doe' } }] }") {
                edges {
                    node {
                        _id
                        firstName
                        lastName
                    }
                }
            }
        }
    }
}
```

Finds users with firstName starting with 'Jo' OR lastName equal to 'Doe'.

**Implicit AND (Recommended):**

```graphql
# Multiple conditions in same object = AND logic
filter: "{ firstName: { _regex: '^Jo' }, isActive: { _eq: true } }"
```

**Collection Filtering:**

```graphql
query {
    xtremSystem {
        company {
            query(
                first: 10
                filter: "{ sites: { _atLeast: 1, primaryAddress: { country: { code: { _in: ['US', 'GB'] } } } } }"
            ) {
                edges {
                    node {
                        _id
                        name
                    }
                }
            }
        }
    }
}
```

Companies with at least one site in US or GB.

**Important Note**: Filter properties do NOT need to be included in the selector. You can filter on any "Can filter: true" property without retrieving it in the response.

## Sorting

Control query result ordering using JSON5 sort specifications.

### Sort Structure

Use `orderBy` parameter with JSON5 object mapping properties to sort direction:

- `1` = Ascending order
- `-1` = Descending order

```graphql
query {
    xtremSystem {
        user {
            query(first: 100, orderBy: "{ lastName: 1, firstName: 1 }") {
                edges {
                    node {
                        _id
                        email
                        firstName
                        lastName
                    }
                }
            }
        }
    }
}
```

**Multi-level sorting**: Properties are sorted in the order specified - first by lastName ascending, then by firstName ascending.

### Sort Examples

```graphql
# Recent orders first, then by customer name
orderBy: "{ orderDate: -1, customer: { name: 1 } }"

# Price high to low, then by product name
orderBy: "{ totalPrice: -1, product: { name: 1 } }"
```

**Important**: Only use properties where metadata indicates "Can sort: true". Sort properties do not need to be in the selector.

## Property Validation Flags

Each property returned by `getMetadata` includes operational flags that determine valid usage contexts.

### Flag Definitions

| Flag           | Purpose                                 | Usage Context                      |
| -------------- | --------------------------------------- | ---------------------------------- |
| **Is output**  | Property can be included in selectors   | Field selection in query responses |
| **Is input**   | Property can be used in mutation inputs | Create/update data payloads        |
| **Can filter** | Property supports filtering operations  | `filter` parameter conditions      |
| **Can sort**   | Property supports sorting operations    | `orderBy` parameter specifications |

### Validation Rules

**Critical**: Always validate property flags before use. GraphQL operations will fail if flags are ignored.

```graphql
# Example: Check metadata before using properties
# ✅ Only use properties with "Is output: true" in selectors
# ✅ Only use properties with "Can filter: true" in filters
# ✅ Only use properties with "Can sort: true" in orderBy
```

**Best Practice**: Call `getMetadata` for all relevant nodes before constructing queries to ensure proper property usage.

## Nested Queries

Retrieve related data in a single request by nesting collection queries within parent node selectors.

### Structure

Collection properties support the same `{ verb(args) selector }` pattern as root queries:

```graphql
query {
    xtremSystem {
        company {
            query(first: 20) {
                edges {
                    node {
                        _id
                        name
                        sites {
                            query(first: 100) {
                                edges {
                                    node {
                                        _id
                                        name
                                        primaryAddress {
                                            _id
                                            city
                                            country {
                                                _id
                                                name
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

### Pagination Strategy

**Different limits for different levels**: Inner collections can use higher `first` values than outer queries:

```graphql
# Example: 20 documents with up to 100 lines each
query {
    xtremSales {
        salesOrder {
            query(first: 20) {
                # Outer limit
                edges {
                    node {
                        orderNumber
                        orderLines {
                            query(first: 100) {
                                # Inner limit - higher value
                                edges {
                                    node {
                                        product {
                                            name
                                        }
                                        quantity
                                        unitPrice
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
```

**Rationale**: Inner collections are often bounded by business logic (e.g., orders rarely have >100 lines), making it more efficient to retrieve all items rather than paginate.

### Benefits

- **Single request**: Fetch related data in one operation
- **Flexible pagination**: Optimize limits for each collection level
- **Efficient retrieval**: Avoid multiple round trips for bounded collections
- **Deep traversal**: Navigate complex business relationships

## Aggregate Queries

Perform data analysis and reporting using `queryAggregate` to group records and compute statistical measures.

### Structure

Aggregate queries use a specialized selector format with separate `group` and `values` sections:

```graphql
query {
  <package-name> {
    <node-name> {
      queryAggregate(<args>) {
        edges {
          node {
            group <group-selector>
            values <values-selector>
          }
        }
      }
    }
  }
}
```

### Components

- **`<args>`**: Pagination and filtering (same as regular queries). Note: `orderBy` is not supported
- **`<group-selector>`**: Properties to group by (dimensions)
- **`<values-selector>`**: Properties to aggregate (measures)

### Example: Sales Analysis

```graphql
query {
    xtremSales {
        salesOrder {
            queryAggregate(filter: "{ status: { _ne: 'pending' } }") {
                edges {
                    node {
                        group {
                            billToCustomer {
                                name
                            }
                            orderDate(by: month)
                        }
                        values {
                            _id {
                                distinctCount
                            }
                            totalAmountExcludingTax {
                                min
                                max
                                sum
                                avg
                            }
                        }
                    }
                }
            }
        }
    }
}
```

**Analysis**: Groups non-pending sales orders by customer and month, showing order count and revenue statistics per group.

### Date Grouping

Date properties require a `by` parameter for time period grouping:

- `orderDate(by: year)` - Group by year
- `orderDate(by: month)` - Group by month
- `orderDate(by: day)` - Group by day

### Aggregation Operators

Available statistical functions for numeric properties:

| Operator        | Purpose            | Example                      |
| --------------- | ------------------ | ---------------------------- |
| `min`           | Minimum value      | `{ price: { min } }`         |
| `max`           | Maximum value      | `{ price: { max } }`         |
| `sum`           | Total sum          | `{ amount: { sum } }`        |
| `avg`           | Average value      | `{ quantity: { avg } }`      |
| `distinctCount` | Unique value count | `{ _id: { distinctCount } }` |

### Property Requirements

**Important**: Both `group` and `values` selectors can only use properties where metadata shows "Can filter: true".

## Mutations

Modify business data using mutations for creating, updating, deleting, and executing custom business operations.

### Structure

Mutations follow the same package/node structure as queries:

```graphql
mutation {
  <package-name> {
    <node-name> {
      <mutation-name>(<args>) <return-selector>
    }
  }
}
```

**Components:**

- `<package-name>`: Package containing the target node (camelCase)
- `<node-name>`: Target node name (camelCase)
- `<mutation-name>`: Operation to perform (create, update, delete, or custom)
- `<args>`: Input parameters for the operation
- `<return-selector>`: Fields to return from the modified record

### Standard Mutations

All business nodes support these CRUD operations:

| Mutation    | Signature                            | Purpose                         |
| ----------- | ------------------------------------ | ------------------------------- |
| `create`    | `create(data: <payload>) <selector>` | Create new record               |
| `update`    | `update(data: <payload>) <selector>` | Update existing record          |
| `delete`    | `delete(_id: <id>)`                  | Delete record (no return value) |
| `duplicate` | `duplicate(_id: <id>) <selector>`    | Clone existing record           |

### Payload Rules

**Data validation requirements for create/update payloads:**

| Rule                      | Create               | Update               | Description                                      |
| ------------------------- | -------------------- | -------------------- | ------------------------------------------------ |
| **Required properties**   | ✅ Must include      | ➖ Optional          | All "Required: true" properties must have values |
| **\_id property**         | ❌ Must exclude      | ✅ Must include      | System generates \_id for new records            |
| **Is input flag**         | ✅ Only these        | ✅ Only these        | Only "Is input: true" properties allowed         |
| **Component references**  | 📦 Full payload      | 📦 Full payload      | "Is component: true" refs need nested object     |
| **Simple references**     | 🔗 \_id only         | 🔗 \_id only         | "Is component: false" refs need \_id string      |
| **Component collections** | 📦 Array of payloads | 📦 Array of payloads | "Is component: true" collections                 |
| **Simple collections**    | ❌ Exclude           | ❌ Exclude           | "Is component: false" collections not allowed    |

### Example: Customer Creation

```graphql
mutation {
    xtremBusiness {
        customer {
            create(
                data: {
                    name: "ACME Corp"
                    country: "#US" # Natural key reference
                    primaryAddress: {
                        # Component reference - full payload
                        street: "123 Main St"
                        city: "New York"
                        postalCode: "10001"
                    }
                }
            ) {
                _id
                name
                primaryAddress {
                    street
                    city
                }
            }
        }
    }
}
```

**Explanation:**

- `country`: Simple reference using natural key syntax (`#US`)
- `primaryAddress`: Component reference requiring full nested payload
- Returns created record with selected fields

### Custom Business Operations

Nodes expose specialized business operations beyond standard CRUD mutations.

**Examples of custom operations:**

- `approve`, `reject`, `cancel` (workflow operations)
- `recalculate`, `validate`, `refresh` (computation operations)
- `import`, `export`, `synchronize` (data operations)

**Required workflow:**

1. Call `getOperationMetadata` with node name and operation name
2. Use returned metadata to understand parameters and return structure
3. Execute the custom mutation with proper arguments

```graphql
# Example: Approve a sales order
mutation {
    xtremSales {
        salesOrder {
            approve(_id: "12345", approvalNote: "Budget approved") {
                _id
                status
                approvalDate
            }
        }
    }
}
```

### Asynchronous Operations

Long-running business operations use a start/track pattern for extended processing times.

### API Structure

**Step 1: Start Operation**

```graphql
mutation {
  <package-name> {
    <node-name> {
      <mutation-name> {
        start(<args>) { trackingId }
      }
    }
  }
}
```

**Step 2: Track Progress**

```graphql
query {
  <package-name> {
    <node-name> {
      <mutation-name> {
        track(trackingId: <trackingId>) {
          status
          result <return-selector>
          errorMessage
          logMessages { level message }
        }
      }
    }
  }
}
```

### API Components

**Fixed elements** (provided by GraphQL API):

- `start` action initiates the operation
- `track` action monitors progress
- `trackingId` links start and track operations
- `status`, `result`, `errorMessage`, `logMessages` are standard response fields

**Mutation-specific elements** (varies per operation):

- `<mutation-name>`: The async operation name
- `<args>`: Operation-specific input parameters
- `<return-selector>`: Operation-specific result structure

### Status Values

| Status               | Description               | Action Required           |
| -------------------- | ------------------------- | ------------------------- |
| `pending`            | Queued for processing     | Continue polling          |
| `running`            | Currently executing       | Continue polling          |
| `success`            | Completed successfully    | Read `result` field       |
| `error`              | Operation failed          | Read `errorMessage` field |
| `stopRequested`      | Stop was requested        | Continue polling          |
| `stopped`            | Operation halted          | Operation complete        |
| `interruptRequested` | System shutdown initiated | Continue polling          |
| `interrupted`        | Gracefully interrupted    | Operation complete        |

### Polling Strategy

**Recommended timing:**

- Initial poll: Immediate (`waitInSeconds: 0` in queryGraphql tool)
- Subsequent polls: 1-second intervals (`waitInSeconds: 1`)
- For operations exceeding 10 seconds: Stop polling and inform user they'll receive application notification when complete

**Important**: Use `getOperationMetadata` to understand the specific `<args>` and `<return-selector>` structure before executing async operations.

## Critical Validation Rules

**Property Validation:**

- Always verify properties exist in node metadata before using them
- Only use properties with appropriate operational flags:
    - Selectors: "Is output: true"
    - Filters: "Can filter: true"
    - Sorting: "Can sort: true"
    - Mutations: "Is input: true"
- Remove invalid properties rather than guessing alternatives

**Performance Considerations:**

- Exclude `textStream` and `binaryStream` properties unless explicitly requested
- Use appropriate pagination limits for nested queries
- Consider natural key syntax (`#value`) for known business identifiers

**Data Integrity:**

- Always include `_id` in selectors for future operations
- Validate required fields for mutations using metadata
- Use component vs. simple reference patterns correctly

---

# LLM Instructions for Request Processing

## Constraint Handling Protocol

**When encountering system constraints or validation errors:**

1. **STOP** - Do not automatically adjust values
2. **EXPLAIN** - Describe the specific constraint or limitation clearly
3. **PROPOSE** - Present multiple options for the user to choose from
4. **WAIT** - Get explicit user confirmation before proceeding

**Never modify user requests** (quantities, dates, items, etc.) without explicit permission.

## Error Recovery Process

**For GraphQL errors or limitations:**

1. Explain the technical issue in business terms
2. Provide specific alternatives based on available metadata
3. Ask for user preference rather than making assumptions
4. If exact requirements aren't possible, request clarification instead of approximating

## Confirmation Requirements

**Before executing mutations that differ from user intent:**

- Confirm key details if they deviate from original request
- Highlight any automatic adjustments made for technical compliance
- Ensure user understands the implications of data changes

**Goal**: Maintain transparency and user control over all business data operations.

## Direct Links to Business Data Records

When responding with information about business data records, you can provide direct links to their detail pages in the application interface. This allows users to quickly navigate to view full record details.

### Link Format

Direct links follow this pattern:

```
/{pageName}/{recordId}
```

Where:

- `pageName`: The page name associated with the node type (available in node metadata)
- `recordId`: The unique identifier (`_id`) of the specific record

### Usage Guidelines

**When to provide links:**

- When listing or referencing specific business data records
- When the response contains record details that users might want to explore further
- For any business entity that has an associated detail page

**When links are not available:**

- Some node types may not have associated detail pages
- The pageName will be null or empty for nodes without UI pages
- Only provide links when the pageName is available in the metadata

### Example

```
Customer: ACME Corp (ID: 12345)
[View Details](/@sage/xtrem-master-data/Customer/12345)

Sales Order: SO-2024-001 (ID: 67890)
[View Details](/@sage/xtrem-sales/SalesOrder/67890)
```

**Important**: Always check the node metadata for the `pageName` property to determine if links can be generated for a specific node type.
