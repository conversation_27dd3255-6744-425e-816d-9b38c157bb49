{"name": "@sage/xtrem-mcp", "version": "59.0.9", "description": "MCP server for XTreeM", "license": "UNLICENSED", "author": "Sage", "files": ["build", "resources"], "main": "build/index.js", "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "start": "node build/lib/start.js", "test": "mocha --recursive --exit \"build/test/**/*.js\"", "clean": "rm -rf build"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "diff": "^5.1.0", "express": "^5.0.0", "glob": "^10.3.10", "graphql": "16.1.0-experimental-stream-defer.6", "jsdom": "^26.0.0", "lodash": "^4.17.21", "minimatch": "^10.0.0", "node-fetch": "^3.3.2", "zod": "^3.24.1", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@sage/xtrem-client": "workspace:*", "@types/body-parser": "^1.19.6", "@types/chai": "^4.3.6", "@types/cors": "^2.8.19", "@types/diff": "^5.0.9", "@types/express": "^5.0.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "chai": "^4.3.10", "mcp-test-client": "^1.0.1", "mocha": "^10.8.2", "typescript": "^5.3.3"}}