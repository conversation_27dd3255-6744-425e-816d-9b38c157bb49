import { integer } from '@sage/xtrem-shared';
import { Datetime } from '..';
import { Context } from '../runtime';

export interface ChatConversation {
    _createStamp: Datetime;
    id: string;
    title: string;
    messages: any[];
    inputTokensUsed: integer;
    outputTokensUsed: integer;
}

export interface ChatbotManager {
    canUseChatbot(context: Context): Promise<boolean>;

    getUserGroupNames(context: Context): Promise<string[]>;

    getConversationHistory(context: Context): Promise<ChatConversation[]>;

    createOrUpdateConversation(context: Context, conversation: Partial<ChatConversation>): Promise<string>;
}

export class ChatbotManagerStub implements ChatbotManager {
    // eslint-disable-next-line class-methods-use-this
    canUseChatbot(): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getUserGroupNames(): Promise<string[]> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getConversationHistory(): Promise<ChatConversation[]> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    createOrUpdateConversation(): Promise<string> {
        throw new Error('Method not implemented.');
    }

}
