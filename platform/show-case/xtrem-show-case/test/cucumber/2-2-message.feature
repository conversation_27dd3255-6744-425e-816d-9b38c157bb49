Feature: 2-2-message
    # Tests the message field component across different devices, verifying message field value and type

    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify a message field using label
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled message field on the main page
        Then the value of the message field is
            """
            This is some long content
            with line breaks and
            it can be translated too.
            """
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify a message field using bind
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "exampleField" bound message field on the main page
        Then the value of the message field is
            """
            This is some long content
            with line breaks and
            it can be translated too.
            """
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the exact value of the message field
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "textField" bound message field on the main page
        Then the value of the message field is "Ali Express"

        Given the user selects the "markdownExample" bound message field on the main page
        Then the value of the message field is
            """
            Hi there!

            This is some markdown content.



            See


            It can do


            bullet points



            And the dangerous tags are escaped: <iframe src="http://wwww.sage.com"></iframe>


            <script>alert("this could be dangerous");</script>
            """
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the value of the message field contains
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "textField" bound message field on the main page
        Then the value of the message field contains "Express"

        Given the user selects the "markdownExample" bound message field on the main page
        Then the value of the message field contains
            """
            This is some markdown content.
            """
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |



    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the type of the message field
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "infoVariant" bound message field on the main page
        Then the message field type is info
        # And takes a screenshot

        Given the user selects the "errorVariant" bound message field on the main page
        Then the message field type is error
        # And takes a screenshot

        Given the user selects the "warningVariant" bound message field on the main page
        Then the message field type is warning
        # And takes a screenshot

        Given the user selects the "successVariant" bound message field on the main page
        Then the message field type is success
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    #message Field: Set / check properties
    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the message field title
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "exampleField" bound message field on the main page
        Then the title of the message field is "Sample title"
        Then the title of the message field is displayed
        # And takes a screenshot


        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "exampleField" bound message field on the main page
        Then the title of the message field is hidden
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the message field helper text
        XT-98758
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Given the user selects the "exampleField" bound message field on the main page
        Then the helper text of the message field is hidden
        # And takes a screenshot
        #
        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "exampleField" bound message field on the main page
        Then the helper text of the message field is "Sample text"
        Then the helper text of the message field is displayed
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    @ymo
    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the message field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MessageField/eyJfaWQiOiIxIn0="
        Then the "Field - Message" titled page is displayed

        Then the "exampleField" bound message field on the main page is displayed
        # And takes a screenshot

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the "exampleField" bound message field on the main page is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
