import * as utils from '../../../step-definitions-utils';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class MessageFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'message', identifier, lookupStrategy, context });
    }

    async expectMessageText(toBe: string, matchType: 'is' | 'contains') {
        const selector = await this.find(
            `${utils.getDataComponentSelector('div', 'content')} div[data-element="content-body"]`,
            true,
        );
        const baseSelector = selector.selector.toString();
        const fullSelector = `${this.cssSelector} ${baseSelector}`;

        this.attachTextValue('Expected value', toBe);
        await utils.scrollIntoViewViaJS(fullSelector);

        let actualValue = '';
        const normalize = (text: string) => text.replace(/\s+/g, ' ').trim().toLowerCase();

        try {
            actualValue = await $(fullSelector).getText();
            const messageValue = normalize(actualValue);
            const expectedValue = normalize(toBe);

            const isMatch = matchType === 'is' ? messageValue === expectedValue : messageValue.includes(expectedValue);

            if (!isMatch) {
                throw new Error(
                    `Expected value: "${toBe}"\n\nActual value: "${actualValue}"\n\nSelector: ${fullSelector}`,
                );
            }

            await waitForPromises(300, 'compare text');
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            const errorDetails = `Expected value: "${toBe}"\n\nActual value: "${actualValue}"\n\nSelector: ${fullSelector}`;
            this.attachTextValue('Error details', errorDetails);
            throw new Error(errorDetails);
        }
    }

    async expectMessageType(expectedType: string) {
        const selectorToUse = this.find(`${utils.getDataComponentSelector('div', 'Message')} span`, true);
        const fullSelector = `${this.cssSelector} ${await selectorToUse.selector}`;
        const element = await $(fullSelector);

        await utils.scrollIntoViewViaJS(fullSelector);

        let actualType = await element.getAttribute('type');
        actualType = actualType === 'tick_circle' ? (actualType = 'success') : actualType;

        if (actualType !== expectedType) {
            throw new Error(
                `Expected type to be: "${expectedType}"\nActual type: "${actualType}"\nSelector: ${fullSelector}`,
            );
        }

        await waitForPromises(300, 'compare type');
    }
}
