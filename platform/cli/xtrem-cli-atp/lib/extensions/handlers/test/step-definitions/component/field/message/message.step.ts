import { Then } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { MessageFieldObject } from './message-object';

// ----------
// Static store field steps
// ----------
Then(
    /^the value of the message field (is|contains) "(.*)"$/,
    async (isContains: 'is' | 'contains', messageSegmentValue: string) => {
        await pageModel.waitForFinishLoading();
        const field = <MessageFieldObject>StaticStore.getStoredField(utils.fieldTypes.message);
        const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(messageSegmentValue);
        await field.expectMessageText(storedDateString, isContains);
    },
);

Then(
    /^the value of the message field (is|contains)$/,
    async (isContains: 'is' | 'contains', messageSegmentValue: string) => {
        await pageModel.waitForFinishLoading();
        const field = <MessageFieldObject>StaticStore.getStoredField(utils.fieldTypes.message);
        const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(messageSegmentValue);
        await field.expectMessageText(storedDateString, isContains);
    },
);

Then(/^the message field type is (info|error|success|warning)$/, async (messageType: string) => {
    const field = <MessageFieldObject>StaticStore.getStoredField(utils.fieldTypes.message);
    const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(messageType);
    await field.expectMessageType(storedDateString);
});
