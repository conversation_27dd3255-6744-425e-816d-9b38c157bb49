@import '../render/style/variables.scss';
@import '../render/style/mixins.scss';

.e-copilot {
    position: fixed;
    bottom: 16px;
    right: 16px;
    width: 520px;
    background-color: var(--colorsYang100);
    border-radius: 24px;
    height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.10), 10px 10px 60px 0px rgba(0, 0, 0, 0.10);
    min-width: 320px;
    min-height: 200px;
    max-width: 90vw;
    max-height: 95vh;
    z-index: 2999;

    /* Create relative positioning context for intellisense dropdown without affecting main positioning */
    .e-copilot-footer {
        position: relative;

        .e-copilot-disclaimer {
            max-width: 200px;
        }
    }

    &.e-copilot-full-screen {
        width: 100vw;
        height: 100vh;
        padding: 0;
        margin: 0;
        top: 0;
        left: 0;
        border-radius: 0;
        max-width: 100vw;
        max-height: 100vh;

        .e-copilot-header {
            padding: 4px 24px;
        }
    }
}

.e-copilot-header {
    background: #000;
    color: var(--colorsYang100);
    height: 40px;
    padding: 12px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.e-copilot-header-title {
    color: var(--colorsYang100);
    font-family: var(--fontFamiliesDefault);
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    flex: 1;
}

.e-copilot-header-logo {
    margin-right: 12px;
    height: 24px;
}

.e-copilot-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.e-copilot-header-close {
    height: 24px;
}

.e-copilot-body {
    flex: 1;
    padding: 24px;
    font-size: 14px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

.e-copilot-footer {
    padding-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    overflow: visible;
    /* Allow intellisense dropdown to extend outside footer bounds */
}

.e-copilot-footer-actions {
    padding: 12px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.e-copilot-message {
    border-radius: 16px;
    padding: 16px;
    max-width: 80%;
    margin-bottom: 24px;

    a {
        color: var(--colorsActionMajor500);
        cursor: pointer;
    }

    p {
        margin: 0;
    }

    table {
        font-size: 12px;

        th,
        td {
            word-wrap: break-word;
        }
    }

    &.e-copilot-message-type-user {
        background-color: rgb(245, 245, 245);
        align-self: flex-end;
    }

    &.e-copilot-message-type-assistant {
        border-width: 1px;
        border-color: var(--colorsYin030);
        border-style: solid;
        align-self: flex-start;

        &.e-copilot-message-type-tools {
            padding-left: 0;
            padding-right: 0;
        }
    }
}

.e-copilot-processing {
    height: 48px;
    color: var(--colorsYin065);
    display: flex;
    align-items: center;
    font-size: 14px;

    .e-copilot-processing-image {
        height: 48px;
    }
}


/* Tool Events Components */
.e-copilot-tools-container {
    border: 1px solid var(--colorsUtilityMajor200);
    border-radius: var(--borderRadius100);
    margin-bottom: 16px;
    box-shadow: var(--boxShadow100);
    font-family: var(--fontFamiliesDefault);
}

.e-copilot-tools-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    text-align: left;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: var(--borderRadius100);
    transition: background-color 0.2s ease;

    &:hover {
        background: var(--colorsUtilityMajor050);
    }

    &:focus {
        outline: none;
        box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
    }
}

.e-copilot-tools-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.e-copilot-tools-icon {
    font-size: 16px;
    font-weight: var(--fontWeights500);
    color: var(--colorsYin090);
}

.e-copilot-tools-count {
    font-size: 14px;
    color: var(--colorsYin065);
}

.e-copilot-tools-badges {
    display: flex;
    gap: 8px;
}

.e-copilot-tools-badge {
    padding: 2px 8px;
    border-radius: var(--borderRadius050);
    font-size: 12px;
    font-weight: var(--fontWeights500);
    border: 1px solid;

    &.e-copilot-tools-badge-success {
        color: var(--colorsSemanticPositive500);
        border-color: var(--colorsSemanticPositive500);
        background: var(--colorsYang100);
    }

    &.e-copilot-tools-badge-running {
        color: var(--colorsSemanticFocus500);
        border-color: var(--colorsSemanticFocus500);
        background: var(--colorsYang100);
    }

    &.e-copilot-tools-badge-error {
        color: var(--colorsSemanticNegative500);
        border-color: var(--colorsSemanticNegative500);
        background: var(--colorsYang100);
    }
}

.e-copilot-tools-chevron {
    color: var(--colorsUtilityMajor400);
    transform-origin: center;
    transition: transform 0.2s ease;
}

/* Individual Tool Cards */
.e-copilot-tool-card {
    border: 1px solid;
    border-radius: var(--borderRadius050);
    margin-bottom: 12px;
    transition: all 0.2s ease;
    box-shadow: var(--boxShadow100);

    &:hover {
        box-shadow: var(--boxShadow200);
    }

    &.border-blue-200 {
        border-color: var(--colorsSemanticFocus500);
    }

    &.border-green-200 {
        border-color: var(--colorsSemanticPositive500);
    }

    &.border-red-200 {
        border-color: var(--colorsSemanticNegative500);
    }

    &.border-gray-200 {
        border-color: var(--colorsUtilityMajor200);
    }
}

.e-copilot-tool-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    text-align: left;
    border: none;
    background: transparent;
    cursor: pointer;
    border-top-left-radius: var(--borderRadius050);
    border-top-right-radius: var(--borderRadius050);
    transition: background-color 0.2s ease;

    &:hover {
        background: var(--colorsUtilityMajor050);
    }

    &:focus {
        outline: none;
        box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
    }
}

.e-copilot-tool-status {
    display: flex;
    align-items: center;
    gap: 12px;
}

.e-copilot-tool-icon {
    font-size: 20px;
    display: inline-block;

    &.e-copilot-tool-icon-spinning {
        animation: spin 2s linear infinite;
    }
}

.e-copilot-tool-info {
    display: flex;
    flex-direction: column;
}

.e-copilot-tool-name {
    font-size: 16px;
    font-weight: var(--fontWeights500);
    line-height: 1.2;
}

.e-copilot-tool-status-text {
    font-size: 12px;
    color: var(--colorsYin055);
    text-transform: capitalize;
    margin-top: 2px;
}

.e-copilot-tool-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.e-copilot-tool-badge {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: var(--borderRadius050);
    font-weight: var(--fontWeights500);
    border: 1px solid;
    background: var(--colorsYang100);
    box-shadow: var(--boxShadow100);

}

.e-copilot-tool-chevron {
    color: var(--colorsUtilityMajor400);
    margin-left: 8px;
    transform-origin: center;
    transition: transform 0.2s ease;
}

.e-copilot-tool-section {
    margin: 0 -12px;
}

.e-copilot-tool-section-header {
    margin: 8px 0 4px;
    display: flex;
    justify-content: space-between;
}

.e-copilot-tool-code-block {
    border: 1px solid var(--colorsUtilityMajor200);
    border-radius: var(--borderRadius050);
    padding: 12px;
    max-height: 160px;
    overflow: auto;
    background: var(--colorsYang100);
}

.e-copilot-tool-code {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: var(--colorsYin090);
    white-space: pre-wrap;
    word-wrap: break-word;
}

.e-copilot-tool-running {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 1px solid var(--colorsSemanticFocus500);
    border-radius: var(--borderRadius050);
    background: var(--colorsYang100);
}

.e-copilot-tool-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--colorsSemanticFocus500);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.e-copilot-tool-running-text {
    font-size: 14px;
    color: var(--colorsSemanticFocus500);
    font-weight: var(--fontWeights500);
}

/* Intellisense Dropdown Styles */
.e-copilot-intellisense {
    position: absolute;
    z-index: 10000;
    font-family: var(--fontFamiliesDefault);
}

.e-copilot-intellisense-list {
    background: var(--colorsYang100);
    border: 1px solid var(--colorsUtilityMajor200);
    border-radius: var(--borderRadius100);
    box-shadow: var(--boxShadow200);
    max-height: 300px;
    overflow-y: auto;
    min-width: 320px;
    max-width: 480px;
}

.e-copilot-intellisense-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover,
    &.e-copilot-intellisense-item-selected {
        background: var(--colorsUtilityMajor050);
    }

    &.e-copilot-intellisense-item-selected {
        border-left: 3px solid var(--colorsSemanticFocus500);
    }
}

.e-copilot-intellisense-item-icon {
    font-size: 16px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.e-copilot-intellisense-item-content {
    flex: 1;
    min-width: 0; // Allow text truncation
}

.e-copilot-intellisense-item-text {
    font-size: 14px;
    font-weight: var(--fontWeights500);
    color: var(--colorsYin090);
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.e-copilot-intellisense-item-description {
    font-size: 12px;
    color: var(--colorsYin065);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.e-copilot-intellisense-item-type {
    font-size: 10px;
    font-weight: var(--fontWeights500);
    color: var(--colorsUtilityMajor400);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
    padding: 2px 6px;
    background: var(--colorsUtilityMajor100);
    border-radius: var(--borderRadius050);
    white-space: nowrap;
}