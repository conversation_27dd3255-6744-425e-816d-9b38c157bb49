import * as React from 'react';
import Icon from 'carbon-react/esm/components/icon';
import type { ToolEvent as ToolEventType } from './copilot-types';
import { AccordionGroup } from 'carbon-react/esm/components/accordion';
import Typography from 'carbon-react/esm/components/typography';
import { ToolEvent } from './tool-event-component';

export function CollapsibleToolEvents({ toolEvents }: { toolEvents: ToolEventType[] }): React.ReactElement | null {
    if (toolEvents.length === 0) return null;

    return (
        <div>
            <Typography variant="segment-header" marginBottom="12px" paddingLeft="12px">
                <Icon type="spanner" marginRight="8px" />
                Tools
            </Typography>
            <AccordionGroup>
                {toolEvents.map((event, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <ToolEvent key={index} event={event} />
                ))}
            </AccordionGroup>
        </div>
    );
}
