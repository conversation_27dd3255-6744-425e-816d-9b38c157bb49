import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import Icon, { IconType } from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { ToolEvent as ToolEventType } from './copilot-types';
import { localize } from '../service/i18n-service';
import * as tokens from '@sage/design-tokens/js/base/common';
import { Accordion } from 'carbon-react/esm/components/accordion';
import Typography from 'carbon-react/esm/components/typography';
import { copyToClipboard } from '../utils/dom';

export function ToolEvent({ event }: { event: ToolEventType }): React.ReactElement | null {
    const inputContent = React.useMemo(() => {
        if (event.toolName === 'queryGraphql') {
            return event.input?.query;
        }

        return event.input
            ? typeof event.input === 'string'
                ? event.input
                : JSON.stringify(event.input, null, 2)
            : 'No input data';
    }, [event.input, event.toolName]);

    const outputContent = React.useMemo(() => {
        if (event.toolName === 'queryGraphql' && event.result?.[0]?.text) {
            try {
                return JSON.stringify(JSON.parse(event.result?.[0]?.text).data, null, 2);
            } catch (error) {
                // Intentionally left empty.
            }
        }
        if (event.toolName === 'generateLink' && event.result?.[0]?.text) {
            try {
                return event.result?.[0]?.text;
            } catch (error) {
                // Intentionally left empty.
            }
        }

        return typeof event.result === 'string' ? event.result : JSON.stringify(event.result, null, 2);
    }, [event.result, event.toolName]);

    const statusIcon = React.useMemo((): IconType => {
        switch (event.status) {
            case 'running':
                return 'in_progress'; // Spinning arrows, will be animated with CSS
            case 'completed':
                return 'tick_circle';
            case 'error':
                return 'cross_circle';
            default:
                return 'none';
        }
    }, [event.status]);

    const statusBorderColor = React.useMemo(() => {
        switch (event.status) {
            case 'running':
                return tokens.colorsSemanticFocus500;
            case 'completed':
                return tokens.colorsSemanticPositive500;
            case 'error':
                return tokens.colorsSemanticNegative500;
            default:
                return tokens.colorsUtilityMajor200;
        }
    }, [event.status]);

    const onCopyInput = React.useCallback(() => {
        copyToClipboard(inputContent);
    }, [inputContent]);

    const onOutputCopy = React.useCallback(() => {
        copyToClipboard(outputContent);
    }, [outputContent]);

    return (
        <Accordion
            size="small"
            title={
                <Typography variant="segment-subheader">
                    <Icon color={statusBorderColor} type={statusIcon} marginRight="8px" />
                    {event.toolName}
                </Typography>
            }
        >
            <div className="e-copilot-tool-section">
                <div className="e-copilot-tool-section-header">
                    <span className="e-copilot-tool-section-label">
                        <Icon type="import" /> Input
                    </span>
                    <IconButton onClick={onCopyInput}>
                        <Icon
                            tooltipMessage={localize('@sage/xtrem-ui/table-copy', 'Copy')}
                            type="copy"
                            color="var(--colorsUtilityMajor400)"
                        />
                    </IconButton>
                </div>
                <div className="e-copilot-tool-code-block">
                    <pre className="e-copilot-tool-code">{inputContent}</pre>
                </div>
            </div>

            {event.result && (
                <div className="e-copilot-tool-section">
                    <div className="e-copilot-tool-section-header">
                        <span className="e-copilot-tool-section-label">
                            <Icon type="export" /> Result
                        </span>
                        <IconButton onClick={onOutputCopy}>
                            <Icon
                                tooltipMessage={localize('@sage/xtrem-ui/table-copy', 'Copy')}
                                type="copy"
                                color="var(--colorsUtilityMajor400)"
                            />
                        </IconButton>
                    </div>
                    <div className="e-copilot-tool-code-block">
                        <pre className="e-copilot-tool-code">{outputContent}</pre>
                    </div>
                </div>
            )}

            {/* Status indicator for running tools */}
            {event.status === 'running' && (
                <div className="e-copilot-tool-section">
                    <div className="e-copilot-tool-running">
                        <div className="e-copilot-tool-spinner" />
                        <span className="e-copilot-tool-running-text">
                            <Loader variant="gradient" />
                        </span>
                    </div>
                </div>
            )}
        </Accordion>
    );
}
ToolEvent.displayName = 'Accordion';
