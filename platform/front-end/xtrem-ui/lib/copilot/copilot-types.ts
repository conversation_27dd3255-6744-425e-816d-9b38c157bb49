import type { MessageParam } from '@anthropic-ai/sdk/resources';

export type MessageRole = MessageParam['role'];
export interface ConversationMessage extends MessageParam {
    toolEvents?: ToolEvent[];
}

export type Conversation = Array<ConversationMessage>;

// Tool event interface for UI tracking
export interface ToolEvent {
    type: 'tool_use' | 'tool_result';
    toolName: string;
    input?: any;
    result?: any;
    status: 'running' | 'completed' | 'error';
}

// Streaming event types (matching backend)
export type StreamEvent =
    | { type: 'connected' }
    | { type: 'thinking'; message: string }
    | { type: 'tool_use'; toolName: string; input: any }
    | { type: 'tool_result_raw'; toolName: string; result: any }
    | { type: 'tool_result'; toolName: string; result: any }
    | { type: 'text_chunk'; text: string }
    | { type: 'completed'; finalText: string, conversationId: string }
    | { type: 'error'; error: string };

// Callback type for handling stream events
export type StreamEventHandler = (event: StreamEvent) => void;
