import { isEmpty } from 'lodash';
import type { PageContext } from '../redux/state';
import type { Conversation, ConversationMessage, MessageRole, StreamEvent, StreamEventHandler } from './copilot-types';
import uid from 'uid';

export async function fetchConversationStarterQuestions(path = ''): Promise<string[]> {
    const response = await fetch(`${path}/chat/conversation-starter`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Accept: 'text/event-stream',
        },
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();

}

/**
 * Send a copilot message with streaming support using fetch API
 * This provides more control over the streaming process and better browser support
 */
export async function sendCopilotMessageStreaming(
    conversation: Conversation,
    onStreamEvent: StreamEventHandler,
    conversationId?: string,
    pageContext?: PageContext | null,
    abortSignal?: AbortSignal,
    path = '',
): Promise<Conversation> {
    const updatedConversation: Conversation = conversation.reduce((prevValue, c) => {
        // Ensure all messages are properly formatted
        const toolMap: Record<string, { use: any; result: any }> | undefined = c.toolEvents?.reduce((acc, toolEvent) => {
            const id = uid();

            return {
                ...acc,
                [id]: {
                    use: {
                        id,
                        type: 'tool_use',
                        name: toolEvent.toolName,
                        input: toolEvent.input,
                    },
                    result: {
                        tool_use_id: id,
                        type: 'tool_result',
                        content: toolEvent.result,
                    }
                },
            };
        }, {} as Record<string, { use: any; result: any }>);
        return [...prevValue, {
            ...c,
            toolEvents: undefined,
            content: c.content,
        },
        ...(isEmpty(toolMap) ? [] : Object.keys(toolMap).reduce((prevValue, id) => {
            return [...prevValue, {
                role: 'assistant' as MessageRole,
                content: [toolMap[id].use],
            }, {
                role: 'user' as MessageRole,
                content: [toolMap[id].result],
            }];
        }, [] as ConversationMessage[])),
        ];
    }, [] as ConversationMessage[]);

    try {
        const response = await fetch(`${path}/chat/stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'text/event-stream',
            },
            body: JSON.stringify({
                conversationId,
                messages: updatedConversation,
                context: pageContext ? {
                    nodeName: pageContext?.nodeName,
                    screenTitle: pageContext?.screenTitle,
                    recordId: pageContext?.recordFilter?._id,
                } : null,
            }),
            signal: abortSignal,
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('No response body available for streaming');
        }

        return await processStreamResponse(response.body, onStreamEvent, updatedConversation);
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown streaming error';
        onStreamEvent({ type: 'error', error: errorMessage });
        throw error;
    }
}

/**
 * Process the streaming response from the server
 */
async function processStreamResponse(
    body: ReadableStream<Uint8Array>,
    onStreamEvent: StreamEventHandler,
    updatedConversation: Conversation,
): Promise<Conversation> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let assistantMessage = '';

    try {
        let buffer = '';

        // eslint-disable-next-line no-constant-condition
        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                break;
            }

            // Decode the chunk and add to buffer
            buffer += decoder.decode(value, { stream: true });

            // Process complete lines from buffer
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep incomplete line in buffer

            // Process each line and update assistant message
            const processedMessages = lines.map(line => processStreamLine(line, onStreamEvent));
            const completedMessage = processedMessages.find(msg => msg !== null);
            if (completedMessage !== undefined) {
                assistantMessage = completedMessage;
            }
        }

        return [...updatedConversation, { role: 'assistant', content: assistantMessage }];
    } finally {
        reader.releaseLock();
    }
}

/**
 * Process a single line from the stream
 * @returns The final message content if completed, null otherwise
 */
function processStreamLine(line: string, onStreamEvent: StreamEventHandler): string | null {
    if (!line.startsWith('data: ')) {
        return null;
    }

    const eventData = line.slice(6); // Remove 'data: ' prefix

    if (eventData === '[DONE]') {
        return null;
    }

    try {
        const streamEvent: StreamEvent = JSON.parse(eventData);

        // Forward the event to the caller
        onStreamEvent(streamEvent);

        switch (streamEvent.type) {
            case 'text_chunk':
                return null; // Continue streaming

            case 'completed':
                return streamEvent.finalText;

            case 'error':
                throw new Error(streamEvent.error);

            case 'connected':
            case 'thinking':
            case 'tool_use':
            case 'tool_result_raw':
            case 'tool_result':
                return null; // Continue streaming

            default:
                return null; // Unknown event type, continue
        }
    } catch (parseError) {
        // Silently ignore parse errors for malformed events
        return null;
    }
}

/**
 * Simple streaming function that accumulates text chunks and returns the final message
 * Use this when you want streaming but don't need to handle individual events
 */
export async function sendCopilotMessageStreamingSimple(
    conversation: Conversation,
    conversationId?: string,
    onTextChunk?: (text: string) => void,
    pageContext?: PageContext | null,
    abortSignal?: AbortSignal,
): Promise<Conversation> {
    return sendCopilotMessageStreaming(
        conversation,
        (event: StreamEvent) => {
            switch (event.type) {
                case 'text_chunk':
                    if (onTextChunk) {
                        onTextChunk(event.text);
                    }
                    break;
                case 'error':
                    throw new Error(event.error);
                // Handle other events silently
                default:
                    break;
            }
        },
        conversationId,
        pageContext,
        abortSignal,
    );
}

/**
 * Check if streaming is supported in the current environment
 */
export function isStreamingSupported(): boolean {
    return typeof fetch !== 'undefined' && typeof ReadableStream !== 'undefined';
}
