/* eslint-disable react/no-array-index-key */
import * as React from 'react';
import Textbox from 'carbon-react/esm/components/textbox';
import Button from 'carbon-react/esm/components/button';
import Message from 'carbon-react/esm/components/message';
import Loader from 'carbon-react/esm/components/loader';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import { xtremConsole } from '../utils/console';
import * as showdown from 'showdown';
import { CopilotIcon } from './copilot-icon';
import {
    sendCopilotMessageStreaming,
    isStreamingSupported,
    fetchConversationStarterQuestions,
} from './copilot-service';
import type { ConversationMessage, StreamEvent, ToolEvent } from './copilot-types';
import type { ApplicationContext, PageContext } from '../integration';
import { localize } from '../service/i18n-service';
import { confirmationDialog } from '../service/dialog-service';
import { DASHBOARD_SCREEN_ID } from '../utils/constants';
import Switch from 'carbon-react/esm/components/switch';
import { isEmpty, isString } from 'lodash';
import { CollapsibleToolEvents } from './tool-events-component';

const TOOLS_DISPLAYED_STORAGE_KEY = 'e-copilot-tools-displayed';
const CONVERSATION_STORAGE_KEY = 'e-copilot-conversation';
const CONVERSATION_ID_STORAGE_KEY = 'e-copilot-conversation-id';
const CONVERSATION_FAILED_STORAGE_KEY = 'e-copilot-conversation-failed';

// TODO: openLinksInNewWindow?
const converter = new showdown.Converter({
    tables: true,
    tasklists: true,
    disableForced4SpacesIndentedSublists: true,
    emoji: true,
});

export interface CopilotContextProps {
    onClose: () => void;
    pageContext?: PageContext | null;
    applicationContext: ApplicationContext;
}

export function CopilotComponent({
    onClose,
    pageContext,
    applicationContext,
}: CopilotContextProps): React.ReactElement {
    const bodyRef = React.useRef<HTMLDivElement>(null);
    const containerRef = React.useRef<HTMLDivElement>(null);
    const footerRef = React.useRef<HTMLDivElement>(null);
    const textboxRef = React.useRef<HTMLDivElement>(null);
    const [draftMessage, setDraftMessage] = React.useState('');
    const [conversationStarters, setConversationStarters] = React.useState<string[]>([]);
    const [isProcessing, setIsProcessing] = React.useState(false);
    const [isFullScreen, setFullScreen] = React.useState(false);
    const [conversation, setConversation] = React.useState<ConversationMessage[]>(() => {
        // Load conversation from localStorage on component mount
        const saved = localStorage.getItem(CONVERSATION_STORAGE_KEY);
        if (saved) {
            try {
                return JSON.parse(saved);
            } catch {
                // Fallback to empty conversation if parsing fails
                return [];
            }
        }
        return [];
    });
    const [conversationId, setConversationId] = React.useState<string | undefined>(() => {
        // Load conversationId from localStorage on component mount
        const saved = localStorage.getItem(CONVERSATION_ID_STORAGE_KEY);
        if (saved) {
            try {
                return saved || undefined;
            } catch {
                return undefined;
            }
        }
        return undefined;
    });
    const [hasConversationFailed, setConversationFailed] = React.useState<boolean>(() => {
        // Load conversationId from localStorage on component mount
        const saved = localStorage.getItem(CONVERSATION_FAILED_STORAGE_KEY);
        if (saved) {
            try {
                return JSON.parse(saved) ?? false;
            } catch {
                return false;
            }
        }
        return false;
    });

    const [areToolsDisplayed, setAreToolsDisplayed] = React.useState(
        () => localStorage.getItem(TOOLS_DISPLAYED_STORAGE_KEY) === 'true',
    );
    const [isStreaming, setIsStreaming] = React.useState(false);
    const [currentStreamingMessage, setCurrentStreamingMessage] = React.useState('');
    const [streamingToolEvents, setStreamingToolEvents] = React.useState<ToolEvent[]>([]);

    // Cancel/abort controller for stopping ongoing operations
    const [abortController, setAbortController] = React.useState<AbortController | null>(null);

    const copilotCssClasses = React.useMemo(() => {
        let classes = 'e-copilot';
        if (isFullScreen) {
            classes += ' e-copilot-full-screen';
        }
        return classes;
    }, [isFullScreen]);

    const onInputChange = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setDraftMessage(value);
    }, []);

    const onToggleFullScreen = React.useCallback(() => {
        setFullScreen(prev => !prev);
    }, []);

    const onNewChat = React.useCallback(async () => {
        // If there's an active conversation, ask for confirmation
        if (conversation.length > 0 || currentStreamingMessage || isStreaming || isProcessing) {
            try {
                await confirmationDialog(
                    DASHBOARD_SCREEN_ID,
                    'warn',
                    'Leaving conversation',
                    'Are you sure you want to start a new chat? This will clear the current conversation.',
                );
            } catch (_error) {
                return;
            }
        }

        // Clear conversation state
        setConversationFailed(false);
        setConversation([]);
        setConversationId(undefined);
        setCurrentStreamingMessage('');
        setStreamingToolEvents([]);
        setDraftMessage('');
        setIsStreaming(false);
        setIsProcessing(false);

        // Clear from localStorage
        localStorage.removeItem(CONVERSATION_STORAGE_KEY);

        // Scroll to top
        if (bodyRef.current) {
            bodyRef.current.scrollTop = 0;
        }
    }, [conversation.length, currentStreamingMessage, isStreaming, isProcessing]);

    const onSendMessage = React.useCallback(
        async (starterMessage?: string) => {
            const userMessage = starterMessage ?? draftMessage.trim();
            if (!userMessage || isProcessing || isStreaming) return;

            setDraftMessage(''); // Clear input immediately

            // Check if streaming is supported
            if (isStreamingSupported()) {
                setIsStreaming(true);
                setCurrentStreamingMessage('');
                setStreamingToolEvents([]);

                // Create abort controller for cancellation
                const controller = new AbortController();
                setAbortController(controller);

                try {
                    const newConversation: ConversationMessage[] = [
                        ...conversation,
                        { role: 'user', content: userMessage },
                    ];
                    setConversation(newConversation);

                    let assistantMessage = '';
                    let currentToolEvents: ToolEvent[] = [];

                    const onStreamEvent = (event: StreamEvent): void => {
                        switch (event.type) {
                            case 'text_chunk':
                                assistantMessage += event.text;
                                setCurrentStreamingMessage(assistantMessage);
                                break;
                            case 'tool_use':
                                // Add tool use to the tracking array
                                const newToolEvent: ToolEvent = {
                                    type: 'tool_use',
                                    toolName: event.toolName,
                                    input: event.input,
                                    status: 'running',
                                };
                                currentToolEvents = [...currentToolEvents, newToolEvent];
                                setStreamingToolEvents(currentToolEvents);
                                xtremConsole.log('Tool use:', event.toolName, event.input);
                                break;
                            case 'tool_result':
                                // Update the last tool use with result
                                currentToolEvents = currentToolEvents.map((tool, index) =>
                                    index === currentToolEvents.length - 1 && tool.toolName === event.toolName
                                        ? { ...tool, type: 'tool_result', result: event.result, status: 'completed' }
                                        : tool,
                                );
                                setStreamingToolEvents(currentToolEvents);
                                xtremConsole.log('Tool result:', event.toolName, event.result);
                                break;
                            case 'thinking':
                                xtremConsole.log('Thinking:', event.message);
                                break;
                            case 'error':
                                // Mark the last tool as errored if applicable
                                if (currentToolEvents.length > 0) {
                                    currentToolEvents = currentToolEvents.map((tool, index) =>
                                        index === currentToolEvents.length - 1 ? { ...tool, status: 'error' } : tool,
                                    );
                                    setStreamingToolEvents(currentToolEvents);
                                }
                                setConversationFailed(true);
                                xtremConsole.error('Stream error:', event.error);
                                break;
                            case 'completed':
                                const finalConversationMessage: ConversationMessage = {
                                    role: 'assistant',
                                    content: assistantMessage,
                                    toolEvents: [...currentToolEvents],
                                };
                                xtremConsole.log('Saving tool events to conversation:', currentToolEvents);
                                const finalConversation: ConversationMessage[] = [
                                    ...newConversation,
                                    finalConversationMessage,
                                ];
                                setConversationId(event.conversationId);
                                setConversation(finalConversation);
                                setCurrentStreamingMessage('');
                                setStreamingToolEvents([]);
                                setIsStreaming(false);
                                setAbortController(null); // Clear abort controller on completion
                                break;
                            case 'connected':
                                xtremConsole.log('Stream connected');
                                break;
                            case 'tool_result_raw':
                                xtremConsole.log('Tool result raw:', event.toolName, event.result);
                                break;
                            default:
                                // Handle any unknown event types
                                break;
                        }
                    };

                    await sendCopilotMessageStreaming(
                        newConversation,
                        onStreamEvent,
                        conversationId,
                        pageContext,
                        controller.signal,
                        applicationContext.path,
                    );
                } catch (error) {
                    if (error instanceof Error && error.name === 'AbortError') {
                        xtremConsole.log('Streaming request was cancelled');
                    } else {
                        xtremConsole.error('Error in streaming message:', error);
                    }
                    setIsStreaming(false);
                    setCurrentStreamingMessage('');
                } finally {
                    setAbortController(null);
                }
            }
        },
        [draftMessage, isProcessing, isStreaming, conversation, conversationId, pageContext, applicationContext.path],
    );

    const onToolsToggle = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const checked = e.target.checked;
        setAreToolsDisplayed(checked);
        localStorage.setItem(TOOLS_DISPLAYED_STORAGE_KEY, String(checked));
    }, []);

    // Handle canceling/stopping the current processing operation
    const onCancelProcessing = React.useCallback(() => {
        if (abortController) {
            abortController.abort();
            setAbortController(null);
        }

        // Reset processing states
        if (isStreaming) {
            setIsStreaming(false);
            setCurrentStreamingMessage('');
            setStreamingToolEvents([]);
        }

        if (isProcessing) {
            setIsProcessing(false);
        }

        xtremConsole.log('Processing cancelled by user');
    }, [abortController, isStreaming, isProcessing]);

    // Handle canceling/stopping the current processing operation
    const onKeyDown = React.useCallback(
        (event: React.KeyboardEvent<HTMLInputElement>) => {
            if (event.key === 'Enter' && !(event.ctrlKey || event.metaKey)) {
                event.preventDefault(); // Prevent default Enter behavior (e.g., form submission)
                onSendMessage(); // Call the send message function
            }
        },
        [onSendMessage],
    );

    const fullScreenButtonTooltip = React.useMemo(
        () =>
            isFullScreen
                ? localize('@sage/xtrem-ui/copilot-minimize', 'Minimize chat window')
                : localize('@sage/xtrem-ui/copilot-maximize', 'Maximize chat window'),
        [isFullScreen],
    );

    const newConversationButtonTooltip = React.useMemo(
        () => localize('@sage/xtrem-ui/copilot-new-chat', 'New conversation'),
        [],
    );

    React.useEffect(() => {
        setTimeout(() => {
            if (bodyRef.current) {
                bodyRef.current.scrollTop = bodyRef.current.scrollHeight;
            }
        }, 100);
    }, [conversation, isProcessing, isStreaming, currentStreamingMessage, streamingToolEvents]);

    // Save conversation to localStorage when it changes
    React.useEffect(() => {
        if (conversation.length === 0) {
            fetchConversationStarterQuestions(applicationContext.path).then(setConversationStarters);
        } else {
            setConversationStarters([]);
            localStorage.setItem(CONVERSATION_STORAGE_KEY, JSON.stringify(conversation));
        }
    }, [applicationContext.path, conversation]);

    React.useEffect(() => {
        localStorage.setItem(CONVERSATION_ID_STORAGE_KEY, conversationId ?? '');
    }, [conversationId]);

    React.useEffect(() => {
        localStorage.setItem(CONVERSATION_FAILED_STORAGE_KEY, JSON.stringify(hasConversationFailed));
    }, [hasConversationFailed]);

    return (
        <div ref={containerRef} className={copilotCssClasses}>
            <div
                className="e-copilot-header"
                id="copilot-window"
                role="dialog"
                aria-modal="false"
                aria-label="Sage Copilot"
            >
                <div className="e-copilot-header-logo">
                    <CopilotIcon height={24} />
                </div>
                <div className="e-copilot-header-title">Sage Copilot</div>
                <div className="e-copilot-header-actions">
                    {conversation.length > 0 && (
                        <IconButton aria-label={newConversationButtonTooltip} onClick={onNewChat}>
                            <Icon
                                tooltipMessage={newConversationButtonTooltip}
                                type="plus"
                                color="var(--colorsYang100)"
                            />
                        </IconButton>
                    )}
                    <IconButton aria-label={fullScreenButtonTooltip} onClick={onToggleFullScreen}>
                        <Icon
                            tooltipMessage={fullScreenButtonTooltip}
                            type={isFullScreen ? 'normalscreen' : 'fullscreen'}
                            color="var(--colorsYang100)"
                        />
                    </IconButton>
                    <IconButton aria-label={localize('@sage/xtrem-ui/action-close', 'Close')} onClick={onClose}>
                        <Icon
                            tooltipMessage={localize('@sage/xtrem-ui/action-close', 'Close')}
                            type="close"
                            color="var(--colorsYang100)"
                        />
                    </IconButton>
                </div>
            </div>
            <div className="e-copilot-body" ref={bodyRef} role="log" aria-live="polite">
                {isEmpty(conversationStarters) && isEmpty(conversation) && <Loader variant="gradient" size="small" />}
                {conversationStarters.map(q => (
                    <ButtonMinor
                        key={q}
                        size="small"
                        buttonType="tertiary"
                        onClick={() => onSendMessage(q)}
                        marginBottom="24px"
                    >
                        {q}
                    </ButtonMinor>
                ))}
                {conversation
                    //.filter(m => m.role !== 'system')
                    .map((message, index) => (
                        <>
                            {areToolsDisplayed && message.toolEvents && message.toolEvents.length > 0 && (
                                <div
                                    className="e-copilot-message e-copilot-message-type-assistant e-copilot-message-type-tools"
                                    key={`tool-events-${index}`}
                                >
                                    <CollapsibleToolEvents toolEvents={message.toolEvents} />
                                </div>
                            )}
                            {isString(message.content) && (
                                <div
                                    key={`message-${index}`}
                                    className={`e-copilot-message e-copilot-message-type-${message.role}`}
                                    // eslint-disable-next-line react/no-danger
                                    dangerouslySetInnerHTML={{
                                        __html: converter.makeHtml(message.content),
                                    }}
                                />
                            )}
                        </>
                    ))}
                {isStreaming && areToolsDisplayed && streamingToolEvents.length > 0 && (
                    <div className="e-copilot-message e-copilot-message-type-assistant">
                        <CollapsibleToolEvents toolEvents={streamingToolEvents} />
                    </div>
                )}
                {isStreaming && currentStreamingMessage && (
                    <div className="e-copilot-message e-copilot-message-type-assistant e-copilot-message-streaming">
                        <div
                            // eslint-disable-next-line react/no-danger
                            dangerouslySetInnerHTML={{
                                __html: converter.makeHtml(currentStreamingMessage),
                            }}
                        />
                        <span className="e-copilot-streaming-cursor">
                            <Loader variant="gradient" size="small" />
                        </span>
                    </div>
                )}
                {hasConversationFailed && (
                    <Message variant="error">
                        {localize(
                            '@sage/xtrem-ui/copilot-conversation-failed',
                            'Conversation failed. Try starting a new one.',
                        )}
                    </Message>
                )}
                {!hasConversationFailed && (isProcessing || isStreaming) && (
                    <div className="e-copilot-processing">
                        <img
                            className="e-copilot-processing-image"
                            src="/images/copilot-processing.gif"
                            alt="Loading"
                        />
                        <span>{localize('@sage/xtrem-ui/copilot-processing', 'Processing...')}</span>
                        <ButtonMinor
                            size="small"
                            buttonType="tertiary"
                            onClick={onCancelProcessing}
                            className="e-copilot-cancel-button"
                            iconType="cross"
                            marginLeft="12px"
                        >
                            {localize('@sage/xtrem-ui/cancel', 'Cancel')}
                        </ButtonMinor>
                    </div>
                )}
            </div>
            <div className="e-copilot-footer" ref={footerRef}>
                <div ref={textboxRef}>
                    <Textbox
                        onChange={onInputChange}
                        value={draftMessage}
                        marginX="24px"
                        placeholder={localize(
                            '@sage/xtrem-ui/copilot-placeholder-type-your-message',
                            'Type your message...',
                        )}
                        disabled={hasConversationFailed || isProcessing || isStreaming}
                        onKeyDown={onKeyDown}
                    />
                </div>
                <div className="e-copilot-footer-actions">
                    <span className="e-copilot-disclaimer">
                        {localize(
                            '@sage/xtrem-ui/copilot-disclaimer',
                            'Sage Copilot can make mistakes. Check important info.',
                        )}
                    </span>
                    <Switch label="Tools displayed?" labelInline checked={areToolsDisplayed} onChange={onToolsToggle} />
                    <Button
                        size="small"
                        disabled={hasConversationFailed || !draftMessage.trim() || isProcessing || isStreaming}
                        onClick={() => onSendMessage()}
                        iconType="send"
                        iconPosition="after"
                        buttonType="primary"
                    >
                        {localize('@sage/xtrem-ui/copilot-send-message', 'Send')}
                    </Button>
                </div>
            </div>
        </div>
    );
}
