import {
    asyncArray,
    AsyncArray,
    BinaryStream,
    Collection,
    Context,
    datetime,
    decorators,
    Dict,
    FileStorageManager,
    Logger,
    LogicError,
    Node,
    NodePayloadData,
    parseFilterForGenericBulkMutation,
    Reference,
    sleepMillis,
    TextStream,
} from '@sage/xtrem-core';
import * as xtremDateTime from '@sage/xtrem-date-time';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { mergePDFs } from '@sage/xtrem-pdf-generator';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import * as archiver from 'archiver';
import axios from 'axios';
import { createWriteStream } from 'fs';
import * as fs from 'fs/promises';
import * as J<PERSON><PERSON><PERSON> from 'jszip';
import { camelCase, kebabCase } from 'lodash';
import { nanoid } from 'nanoid';
import { URL } from 'node:url';
import * as os from 'os';
import * as fsp from 'path';
import * as xtremReporting from '..';
import { defaultHtmlFooter, defaultHtmlHeader } from '../functions/html-document-builder';
import { generatePdfData } from '../functions/pdf-utils';

const logger = Logger.getLogger(__filename, 'reporting-report-node');

@decorators.node<Report>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    async controlEnd(cx) {
        if (await this.$.context.isServiceOptionEnabled(xtremReporting.serviceOptions.reportAssignment)) {
            const printingType = await this.printingType;
            const variableCount = await this.variables.length;
            const reportType = await this.reportType;

            const mainReferenceVariables =
                variableCount > 0
                    ? await this.variables
                          .filter(async variable => {
                              const variableIsReference = (await variable.type) === 'reference';
                              const isMainReference = !!(await variable.isMainReference);
                              return variableIsReference && isMainReference;
                          })
                          .toArray()
                    : [];

            if (reportType === 'printedDocument' && printingType === 'single' && mainReferenceVariables.length === 0) {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report__a-mandatory-reference-variable-required-for-single-print-reports',
                        'A main reference variable is required for single print reports.',
                    ),
                );
            }

            if (mainReferenceVariables.length > 1) {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report__only-one-reference-variable-allowed-per-report',
                        'Only one main reference variable is allowed per report.',
                    ),
                );
            }
        }
    },
})
export class Report extends Node {
    @decorators.stringProperty<Report, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen() {
            return this.isFactory;
        },
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Report, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<Report, 'parentPackage'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.shortDescription,
    })
    readonly parentPackage: Promise<string>;

    @decorators.referenceProperty<Report, 'activeTemplate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        isOwnedByCustomer: true,
        node: () => xtremReporting.nodes.ReportTemplate,
    })
    readonly activeTemplate: Reference<xtremReporting.nodes.ReportTemplate | null>;

    @decorators.collectionProperty<Report, 'reportTemplates'>({
        node: () => xtremReporting.nodes.ReportTemplate,
        isPublished: true,
        reverseReference: 'report',
    })
    readonly reportTemplates: Collection<xtremReporting.nodes.ReportTemplate>;

    @decorators.enumProperty<Report, 'reportType'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremReporting.enums.ReportTypeDataType,
        defaultValue() {
            return 'printedDocument';
        },
    })
    readonly reportType: Promise<xtremReporting.enums.ReportType>;

    @decorators.collectionProperty<Report, 'variables'>({
        isPublished: true,
        isVital: true,
        dependsOn: ['printingType'],
        reverseReference: 'report',
        node: () => xtremReporting.nodes.ReportVariable,
    })
    readonly variables: Collection<xtremReporting.nodes.ReportVariable>;

    @decorators.booleanProperty<Report, 'isFactory'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly isFactory: Promise<boolean>;

    @decorators.collectionProperty<Report, 'pageAssignments'>({
        isPublished: true,
        isAssociation: true,
        reverseReference: 'report',
        node: () => xtremReporting.nodes.ReportAssignmentAssociation,
    })
    pageAssignments: Collection<xtremReporting.nodes.ReportAssignmentAssociation>;

    @decorators.enumProperty<Report, 'printingType'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['reportType'],
        dataType: () => xtremReporting.enums.printingTypeDataType,
        defaultValue() {
            return 'notApplicable';
        },
        async control(cx, val) {
            const reportType = await this.reportType;
            if (reportType !== 'printedDocument' && val !== 'notApplicable') {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report__only-printed-document-reports-can-be-single-or-multiple-prints',
                        "Only '{{printedDocument}}' reports can be single or multiple prints.",
                        {
                            printedDocument: this.$.context.localizeEnumMember(
                                '@sage/xtrem-reporting/ReportType',
                                'printedDocument',
                            ),
                        },
                    ),
                );
            }
            if (reportType === 'printedDocument' && val === 'notApplicable') {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report__printed-document-reports-must-be-single-or-multiple-prints',
                        'PrintedDocument type of report must be single or multiple prints.',
                    ),
                );
            }
        },
    })
    readonly printingType: Promise<xtremReporting.enums.PrintingType>;

    @decorators.referenceProperty<Report, 'preProcessingOperation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        filters: {
            lookup: {
                isPublished: true,
                name: {
                    _nin: ['asyncExport'],
                },
                _or: [
                    {
                        kind: {
                            _in: ['mutation', 'query'],
                        },
                    },
                    {
                        kind: 'asyncMutation',
                        action: 'start',
                    },
                ],
            },
        },
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        async isFrozen() {
            return !(await this.$.context.isServiceOptionEnabled(xtremSystem.serviceOptions.DevTools));
        },
    })
    preProcessingOperation: Reference<xtremMetadata.nodes.MetaNodeOperation | null>;

    @decorators.referenceProperty<Report, 'postProcessingOperation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        filters: {
            lookup: {
                isPublished: true,
                name: {
                    _nin: ['asyncExport'],
                },
                _or: [
                    {
                        kind: {
                            _in: ['mutation', 'query'],
                        },
                    },
                    {
                        kind: 'asyncMutation',
                        action: 'start',
                    },
                ],
            },
        },
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        async isFrozen() {
            return !(await this.$.context.isServiceOptionEnabled(xtremSystem.serviceOptions.DevTools));
        },
    })
    postProcessingOperation: Reference<xtremMetadata.nodes.MetaNodeOperation | null>;

    /**
     * @deprecated please use printRecords or generateUploadedFile instead
     **/
    @decorators.mutation<typeof Report, 'generateReports'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportSettings',
                type: 'array',
                isMandatory: true,
                item: {
                    type: 'object',
                    name: 'ReportSettings',
                    properties: {
                        reportVariables: {
                            name: 'reportVariables',
                            type: 'string',
                            isMandatory: true,
                        },
                        locale: {
                            name: 'locale',
                            type: 'string',
                            isMandatory: false,
                        },
                        paperFormat: {
                            name: 'paperFormat',
                            type: 'string',
                            isMandatory: false,
                        },
                        pageOrientation: {
                            name: 'pageOrientation',
                            type: 'string',
                            isMandatory: false,
                        },
                        leftMarginCm: {
                            name: 'leftMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        rightMarginCm: {
                            name: 'rightMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        topMarginCm: {
                            name: 'topMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        bottomMarginCm: {
                            name: 'bottomMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        documentTitle: {
                            name: 'documentTitle',
                            type: 'string',
                            isMandatory: false,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'binaryStream',
            },
        },
    })
    static async generateReports(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings[],
    ): Promise<BinaryStream[]> {
        const parsedInput: xtremReporting.functions.ReportGenerationSettings[] =
            reportSettings.map<xtremReporting.functions.ReportGenerationSettings>(r => ({
                ...r,
                locale: xtremReporting.functions.getLocaleToUse(context, r.locale),
                variables: r.variables ? JSON.parse(r.variables) : {},
            }));

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const htmlDocuments = await xtremReporting.functions.generateReports(context, {
            reportName,
            reportTemplateName,
            reportSettings: parsedInput,
        });

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultPageOrientation = await reportTemplate.defaultPageOrientation;
        const defaultTopMargin = await reportTemplate.defaultTopMargin;
        const defaultBottomMargin = await reportTemplate.defaultBottomMargin;
        const defaultLeftMargin = await reportTemplate.defaultLeftMargin;
        const defaultRightMargin = await reportTemplate.defaultRightMargin;

        return asyncArray(htmlDocuments)
            .map(
                async (
                    {
                        populatedBodyContent,
                        populatedHeaderContent,
                        populatedFooterContent,
                        populatedAttachmentContent,
                    },
                    index: number,
                ) => {
                    const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
                        parsedInput[index].locale,
                        parsedInput[index].paperFormat || defaultTemplatePaperFormat,
                    );

                    const topMarginCm = parsedInput[index].topMarginCm || defaultTopMargin || 2;
                    const bottomMarginCm = parsedInput[index].bottomMarginCm || defaultBottomMargin || 2;
                    const leftMarginCm = parsedInput[index].leftMarginCm || defaultLeftMargin || 2;
                    const rightMarginCm = parsedInput[index].rightMarginCm || defaultRightMargin || 2;

                    const pageOrientationToUse = parsedInput[index].pageOrientation || defaultPageOrientation;

                    return BinaryStream.fromBuffer(
                        await generatePdfData(context, {
                            reportName,
                            reportObject: {
                                ...reportObject,
                                settings: [reportObject.settings[index]],
                            },
                            populatedBodyContent,
                            populatedHeaderContent,
                            populatedFooterContent,
                            paperFormat: paperFormatToUse,
                            pageOrientation: pageOrientationToUse,
                            outputPath: '',
                            populatedAttachment: populatedAttachmentContent,
                            attachmentName: await reportTemplate.attachmentName,
                            attachmentMimeType: await reportTemplate.attachmentMimeType,
                            bottomMarginCm,
                            leftMarginCm,
                            rightMarginCm,
                            topMarginCm,
                            documentTitle: parsedInput[index].documentTitle,
                        }),
                    );
                },
            )
            .toArray();
    }

    /**
     *
     * @param context
     * @param parameters
     * @private
     */
    static async createUploadedFile(
        context: Context,
        {
            objectKey,
            filename,
            reportName,
            timestamp,
            isArchive,
        }: {
            kind: xtremUpload.enums.UploadedFileKind;
            objectKey: string;
            filename: string;
            reportName: string;
            timestamp: string;
            isArchive?: boolean;
        },
    ): Promise<xtremUpload.nodes.UploadedFile> {
        const uploadedFileId = await context.runInWritableContext(async writableContext => {
            const createUploadedFileNode = async (key: string, keyFilename: string) => {
                const uploadFileNode = await writableContext.create(xtremUpload.nodes.UploadedFile, {
                    key,
                    status: 'verified',
                    canSkipAntivirusScan: true,
                    filename: keyFilename,
                    mimeType: isArchive ? 'application/zip' : 'application/pdf',
                    kind: 'upload',
                    expirationDate: datetime.now().addDays(10),
                });
                await uploadFileNode.$.save();
                return uploadFileNode;
            };

            let count = 0;
            let result: xtremUpload.nodes.UploadedFile | undefined;
            let _filename = filename;
            let _objectKey = objectKey;

            while (result == null) {
                const fileExist = await writableContext.tryRead(xtremUpload.nodes.UploadedFile, { key: _objectKey });
                if (!fileExist) {
                    result = await createUploadedFileNode(_objectKey, _filename);
                } else {
                    await sleepMillis(1000);
                    count += 1;
                    _filename = `${kebabCase(reportName)}-${timestamp}-(${count}).${isArchive ? 'zip' : 'pdf'}`;
                    _objectKey = `print-output/${_filename}`;
                }
            }

            return result._id;
        });

        return context.read(xtremUpload.nodes.UploadedFile, { _id: uploadedFileId });
    }

    static deleteUploadedFile(context: Context, uploadedFile: xtremUpload.nodes.UploadedFile) {
        return context.runInIsolatedContext(async isolatedContext => {
            const deleteSafeUploaded = await isolatedContext.read(
                xtremUpload.nodes.UploadedFile,
                { _id: uploadedFile._id },
                {
                    forUpdate: true,
                },
            );

            return deleteSafeUploaded.$.delete();
        });
    }

    static getReportSettingFromDocumentsToPrint(
        recordsToPrint: AsyncArray<Node>,
        commonReportSetting: xtremReporting.functions.ReportGenerationSettings,
        report?: Report | null,
    ): Promise<xtremReporting.functions.ReportGenerationSettings[]> {
        return recordsToPrint
            .map(async record => {
                const additionalVariables: any = {};

                if (report) {
                    const mainReferenceVariable = await report.variables.find(variable => variable.isMainReference);

                    additionalVariables[(await mainReferenceVariable?.name) ?? '__recordId'] = record._id;
                }

                return {
                    ...commonReportSetting,
                    variables: {
                        ...commonReportSetting.variables,
                        ...additionalVariables,
                    },
                };
            })
            .toArray();
    }

    static async withUploadedFile(
        context: Context,
        reportName: string,
        fileFormat: string,
        body: (uploadedFile: xtremUpload.nodes.UploadedFile, filename: string) => Promise<any>,
    ): Promise<xtremUpload.nodes.UploadedFile> {
        const timestamp = xtremDateTime.datetime.now().format(undefined, 'YYYY-MM-DD-HH-mm-ss');
        const filename = Report._generateReportFilename(reportName, timestamp);

        const _objectKey = `print-output/${filename}.${fileFormat}`;

        const uploadedFile = await Report.createUploadedFile(context, {
            kind: 'upload',
            objectKey: _objectKey,
            filename: `${filename}.${fileFormat}`,
            reportName,
            timestamp,
            ...(fileFormat === 'zip' ? { isArchive: true } : {}),
        });

        try {
            await body(uploadedFile, filename);
        } catch (error) {
            await Report.deleteUploadedFile(context, uploadedFile);

            // let the framework handle the error
            // throw error;
        }

        return uploadedFile;
    }

    @decorators.asyncMutation<typeof Report, 'printRecords'>({
        isPublished: true,
        startsReadOnly: true,
        isGlobal: true,
        parameters: [
            {
                name: 'filter',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'nodeName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplate',
                type: 'reference',
                node: () => xtremReporting.nodes.ReportTemplate,
                isMandatory: true,
            },
            {
                name: 'paperFormat',
                type: 'enum',
                dataType: () => xtremReporting.enums.ReportPaperFormatDataType,
                isMandatory: true,
            },
            {
                name: 'pageOrientation',
                type: 'enum',
                dataType: () => xtremReporting.enums.ReportPageOrientationDataType,
                isMandatory: true,
            },
            {
                name: 'reportParameters',
                type: 'json',
                isMandatory: true,
            },
            {
                name: 'joinDocuments',
                type: 'boolean',
            },
            {
                name: 'notifyUser',
                type: 'boolean',
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremUpload.nodes.UploadedFile,
        },
    })
    static async printRecords(
        context: Context,
        filter: string,
        nodeName: string,
        reportTemplate: xtremReporting.nodes.ReportTemplate,
        paperFormat: xtremReporting.enums.ReportPaperFormat,
        pageOrientation: xtremReporting.enums.ReportPageOrientation,
        reportParameters: Dict<any>,
        joinDocuments?: boolean,
        notifyUser?: boolean,
    ): Promise<xtremUpload.nodes.UploadedFile> {
        const nodeFactory = context.application.tryToGetFactoryByName(nodeName);
        const parsedFilter =
            nodeFactory && filter ? await parseFilterForGenericBulkMutation(context, nodeFactory, filter) : undefined;

        const commonReportSetting = {
            paperFormat,
            pageOrientation,
            locale: xtremReporting.functions.getLocaleToUse(context) ?? 'en_US',
            variables: reportParameters ?? {},
        };

        const report = await reportTemplate.report;
        const reportName = (await report?.name) ?? '';
        const reportTemplateName = await reportTemplate.name;

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, [
            commonReportSetting,
        ]);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const isSinglePrintReport = (await reportObject.instance?.printingType) === 'single';

        let isBulkPrint = isSinglePrintReport;

        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultPageOrientation = await reportTemplate.defaultPageOrientation;
        let nbOfRecordsToBePrinted = 0;

        if (isSinglePrintReport) {
            if (!parsedFilter || !nodeFactory) {
                throw new LogicError(
                    context.localize(
                        '@sage/xtrem-reporting/nodes__report__invalid-query-settings',
                        'Unable to find records to print because query settings are not valid.',
                    ),
                );
            }

            const recordsToBePrinted = context.query(nodeFactory.nodeConstructor, {
                filter: parsedFilter,
            });
            nbOfRecordsToBePrinted = await recordsToBePrinted.length;

            reportObject.settings = await Report.getReportSettingFromDocumentsToPrint(
                recordsToBePrinted,
                commonReportSetting,
                reportObject.instance,
            );

            isBulkPrint = nbOfRecordsToBePrinted > 1;
        }

        const htmlDocuments = await xtremReporting.functions.generateReports(context, {
            reportName,
            reportTemplateName,
            reportSettings: reportObject.settings,
            additionalParameters: {
                isBulkPrint: nbOfRecordsToBePrinted > 1,
            },
        });

        logger.debug(() => `Prepared ${htmlDocuments.length} html reports`);

        let successCount = 0;
        const nbOfDocumentToGenerate = htmlDocuments.length;

        const nbOfRecordsSkipped = nbOfRecordsToBePrinted - nbOfDocumentToGenerate;

        if (nbOfRecordsSkipped > 0 && context.batch) {
            await context.batch.logMessage(
                'warning',
                context.localize(
                    '@sage/xtrem-reporting/nodes__report__some-records-skipped',
                    'Some records were skipped while generating your report.',
                ),
            );
        }

        return Report.withUploadedFile(
            context,
            reportName,
            nbOfDocumentToGenerate < 2 || joinDocuments ? 'pdf' : 'zip',
            async (uploadedFile: xtremUpload.nodes.UploadedFile, filename: string) => {
                const reports = asyncArray(htmlDocuments)
                    .map(
                        async (
                            {
                                populatedBodyContent,
                                populatedHeaderContent,
                                populatedFooterContent,
                                populatedAttachmentContent,
                            },
                            index: number,
                        ) => {
                            if (context.batch && (await context.batch.isStopRequested())) {
                                await context.batch.end();
                                return Promise.resolve(null);
                            }

                            const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
                                reportObject.settings[index].locale,
                                reportObject.settings[index].paperFormat || defaultTemplatePaperFormat,
                            );
                            const pageOrientationToUse =
                                reportObject.settings[index].pageOrientation || defaultPageOrientation;

                            return generatePdfData(context, {
                                reportName,
                                reportObject: {
                                    ...reportObject,
                                    settings: [reportObject.settings[index]],
                                },
                                populatedBodyContent,
                                populatedHeaderContent,
                                populatedFooterContent,
                                paperFormat: paperFormatToUse,
                                pageOrientation: pageOrientationToUse,
                                populatedAttachment: populatedAttachmentContent,
                                attachmentName: await reportTemplate.attachmentName,
                                attachmentMimeType: await reportTemplate.attachmentMimeType,
                                leftMarginCm: reportObject.settings[index].leftMarginCm || 2,
                                rightMarginCm: reportObject.settings[index].rightMarginCm || 2,
                                topMarginCm: reportObject.settings[index].topMarginCm || 2,
                                bottomMarginCm: reportObject.settings[index].bottomMarginCm || 2,
                                documentTitle:
                                    nbOfRecordsToBePrinted > 1 ? reportObject.settings[index].documentTitle : undefined,
                                additionalParameters: {
                                    isBulkPrint: nbOfRecordsToBePrinted > 1,
                                    uploadedFile,
                                },
                            });
                        },
                    )
                    .filter(async (pdfData: Buffer | null) => {
                        const isPdfData = !!pdfData;

                        if (isPdfData && context.batch) {
                            successCount += 1;

                            await context.batch.updateProgress({
                                phase: 'pdf_generation',
                                totalCount: nbOfDocumentToGenerate,
                                successCount,
                            });
                        }

                        return isPdfData;
                    }) as AsyncArray<Buffer>;

                if (isSinglePrintReport) {
                    if (joinDocuments) {
                        if (context.batch) {
                            await context.batch.updateProgress({
                                phase: 'pdf_merge',
                                totalCount: 1,
                                successCount: 0,
                            });
                        }

                        const mergeResult = await mergePDFs({
                            documents: reports,
                            documentTitle: filename,
                            productName: context.configuration.getProductName(),
                        });

                        if (mergeResult.status === 'error') {
                            throw new Error('Error merging PDFs');
                        }

                        await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                            context,
                            await uploadedFile.key,
                            `Generated single print report - ${reportName}`,
                            mergeResult.data,
                            FileTimeToLive.Expire10Days,
                        );

                        if (context.batch) {
                            await context.batch.updateProgress({
                                phase: 'pdf_merge',
                                totalCount: 1,
                                successCount: 1,
                            });
                        }
                    } else {
                        if (context.batch) {
                            await context.batch.updateProgress({
                                phase: 'pdf_zip',
                                totalCount: 1,
                                successCount: 0,
                            });
                        }

                        const zip = new JSZip();

                        await reports.forEach((reportPdf: Buffer<ArrayBufferLike>, index: number) => {
                            zip.file(`${filename}-${index}.pdf`, reportPdf, { binary: true });
                        });

                        const zipContent = await zip.generateAsync({ type: 'nodebuffer' });
                        await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                            context,
                            await uploadedFile.key,
                            `Generated report archive - ${reportName}`,
                            zipContent,
                            FileTimeToLive.Expire10Days,
                        );

                        if (context.batch) {
                            await context.batch.updateProgress({
                                phase: 'pdf_zip',
                                totalCount: 1,
                                successCount: 1,
                            });
                        }
                    }
                } else {
                    await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                        context,
                        await uploadedFile.key,
                        `Generated report - ${reportName}`,
                        await reports.elementAt(0),
                        FileTimeToLive.Expire10Days,
                    );
                }

                await context.batch.logMessage('result', await uploadedFile.filename, {
                    data: {
                        downloadUrl: await uploadedFile.downloadUrl,
                        filename: await uploadedFile.filename,
                    },
                });

                if (notifyUser || isBulkPrint) {
                    await xtremReporting.functions.sendUserNotification(context, {
                        status: await uploadedFile.status,
                        reportName,
                        rejectReason: await uploadedFile.rejectReason,
                        downloadUrl: await uploadedFile.downloadUrl,
                    });
                }
            },
        );
    }

    /**
     * @deprecated please use printRecords or generateUploadedFile instead
     **/
    @decorators.asyncMutation<typeof Report, 'generateReportPdf'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                key: 'string',
                filename: 'string',
                mimeType: 'string',
                contentLength: 'integer',
                status: 'string',
                rejectReason: 'string',
                canSkipAntivirusScan: 'boolean',
                uploadUrl: 'string',
                downloadUrl: 'string',
            },
        },
    })
    static async generateReportPdf(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
        return (await Report.generateUploadedFile(context, reportName, reportTemplateName, reportSettings)).$.payload();
    }

    @decorators.asyncMutation<typeof Report, 'generateUploadedFile'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremUpload.nodes.UploadedFile,
        },
    })
    static async generateUploadedFile(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<xtremUpload.nodes.UploadedFile> {
        logger.verbose(() => `Generating report ${reportName} with settings: ${JSON.stringify(reportSettings)}`);

        const parsedInput = [
            {
                ...reportSettings,
                locale: xtremReporting.functions.getLocaleToUse(context, reportSettings.locale),
                variables: reportSettings.variables ? JSON.parse(reportSettings.variables) : {},
            },
        ] as xtremReporting.functions.ReportGenerationSettings[];

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const variables = reportSettings.variables ? JSON.parse(reportSettings.variables) : {};
        const localeToUse = xtremReporting.functions.getLocaleToUse(context, reportSettings.locale);
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultTemplatePageOrientation = await reportTemplate.defaultPageOrientation;
        const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
            localeToUse,
            reportSettings.paperFormat || defaultTemplatePaperFormat,
        );

        const topMarginCm = reportSettings.topMarginCm || (await reportTemplate?.defaultTopMargin) || 2;
        const bottomMarginCm = reportSettings.bottomMarginCm || (await reportTemplate?.defaultBottomMargin) || 2;
        const leftMarginCm = reportSettings.leftMarginCm || (await reportTemplate?.defaultLeftMargin) || 2;
        const rightMarginCm = reportSettings.rightMarginCm || (await reportTemplate?.defaultRightMargin) || 2;

        const { populatedBodyContent, populatedFooterContent, populatedHeaderContent, populatedAttachmentContent } =
            await xtremReporting.functions.generateReport(context, {
                reportName,
                reportTemplateName,
                reportSettings: {
                    variables,
                    locale: localeToUse,
                    paperFormat: paperFormatToUse,
                },
            });

        const pageOrientationToUse = reportSettings.pageOrientation || defaultTemplatePageOrientation;

        return Report.withUploadedFile(
            context,
            reportName,
            'pdf',
            async (uploadedFile: xtremUpload.nodes.UploadedFile, filename: string) => {
                await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                    context,
                    await uploadedFile.key,
                    `Generated report - ${reportName}`,
                    await generatePdfData(context, {
                        reportName,
                        reportObject,
                        populatedBodyContent,
                        populatedHeaderContent,
                        populatedFooterContent,
                        paperFormat: paperFormatToUse,
                        pageOrientation: pageOrientationToUse,
                        populatedAttachment: populatedAttachmentContent,
                        attachmentName: await reportTemplate.attachmentName,
                        attachmentMimeType: await reportTemplate.attachmentMimeType,
                        leftMarginCm,
                        rightMarginCm,
                        topMarginCm,
                        bottomMarginCm,
                        documentTitle: reportSettings.documentTitle ?? filename,
                        additionalParameters: {
                            uploadedFile,
                        },
                    }),
                    FileTimeToLive.Expire10Days,
                );

                if (reportSettings.isBulk) {
                    await context.batch.logMessage('info', await uploadedFile.filename);
                } else {
                    await context.batch.logMessage('result', await uploadedFile.filename, {
                        data: {
                            downloadUrl: await uploadedFile.downloadUrl,
                            filename: await uploadedFile.filename,
                        },
                    });
                    await context.batch.updateProgress({
                        totalCount: 1,
                        successCount: 1,
                    });
                }
            },
        );
    }

    @decorators.asyncMutation<typeof Report, 'generateReportAndNotifyUser'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: 'boolean',
    })
    static async generateReportAndNotifyUser(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<boolean> {
        let status = 'rejected';
        let rejectReason = '';
        let downloadUrl = '';
        let success = true;
        try {
            const result = await xtremReporting.nodes.Report.generateReportPdf(
                context,
                reportName,
                reportTemplateName,
                reportSettings,
            );
            status = result.status ?? 'rejected';
            rejectReason = result.rejectReason ?? '';
            downloadUrl = result.downloadUrl ?? '';
        } catch (error) {
            rejectReason = context.localize(
                '@sage/xtrem-reporting/nodes__report__error_generating_report',
                'Error during report generation: {{errorMessage}}',
                { errorMessage: error.message },
            );
            logger.error(error.message, error.stack);
            success = false;
        }

        await xtremReporting.functions.sendUserNotification(context, {
            status,
            reportName,
            rejectReason,
            downloadUrl,
        });

        return success;
    }

    @decorators.asyncMutation<typeof Report, 'generateReportZip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'reportSettings',
                type: 'array',
                isMandatory: true,
                item: {
                    type: 'object',
                    name: 'ReportSettings',
                    properties: {
                        variables: {
                            name: 'variables',
                            type: 'string',
                            isMandatory: true,
                        },
                        locale: {
                            name: 'locale',
                            type: 'string',
                            isMandatory: false,
                        },
                        paperFormat: {
                            name: 'paperFormat',
                            type: 'string',
                            isMandatory: false,
                        },
                        documentTitle: {
                            name: 'documentTitle',
                            type: 'string',
                            isMandatory: false,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                key: 'string',
                filename: 'string',
                mimeType: 'string',
                contentLength: 'integer',
                status: 'string',
                rejectReason: 'string',
                canSkipAntivirusScan: 'boolean',
                uploadUrl: 'string',
                downloadUrl: 'string',
            },
        },
    })
    static async generateReportZip(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings[],
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
        const parsedInput: xtremReporting.functions.ReportGenerationSettings[] =
            reportSettings.map<xtremReporting.functions.ReportGenerationSettings>(r => ({
                ...r,
                locale: xtremReporting.functions.getLocaleToUse(context, r.locale),
                variables: r.variables ? JSON.parse(r.variables) : {},
            }));

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const htmlDocuments = await xtremReporting.functions.generateReports(context, {
            reportName,
            reportTemplateName,
            reportSettings: parsedInput,
            additionalParameters: {
                isBulkPrint: true,
            },
        });

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultPageOrientation = await reportTemplate.defaultPageOrientation;

        return (
            await Report.withUploadedFile(
                context,
                reportName,
                'zip',
                async (uploadedFile: xtremUpload.nodes.UploadedFile, filename: string) => {
                    const reports = await asyncArray(htmlDocuments)
                        .map(
                            async (
                                {
                                    populatedBodyContent,
                                    populatedHeaderContent,
                                    populatedFooterContent,
                                    populatedAttachmentContent,
                                },
                                index: number,
                            ) => {
                                const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
                                    parsedInput[index].locale,
                                    parsedInput[index].paperFormat || defaultTemplatePaperFormat,
                                );
                                const pageOrientationToUse =
                                    parsedInput[index].pageOrientation || defaultPageOrientation;

                                return BinaryStream.fromBuffer(
                                    await generatePdfData(context, {
                                        reportName,
                                        reportObject: {
                                            ...reportObject,
                                            settings: [reportObject.settings[index]],
                                        },
                                        populatedBodyContent,
                                        populatedHeaderContent,
                                        populatedFooterContent,
                                        paperFormat: paperFormatToUse,
                                        pageOrientation: pageOrientationToUse,
                                        populatedAttachment: populatedAttachmentContent,
                                        attachmentName: await reportTemplate.attachmentName,
                                        attachmentMimeType: await reportTemplate.attachmentMimeType,
                                        leftMarginCm: parsedInput[index].leftMarginCm || 2,
                                        rightMarginCm: parsedInput[index].rightMarginCm || 2,
                                        topMarginCm: parsedInput[index].topMarginCm || 2,
                                        bottomMarginCm: parsedInput[index].bottomMarginCm || 2,
                                        documentTitle: parsedInput[index].documentTitle,
                                        additionalParameters: {
                                            uploadedFile,
                                        },
                                    }),
                                );
                            },
                        )
                        .toArray();
                    const zip = new JSZip();

                    reports.forEach((report: BinaryStream, index: number) => {
                        zip.file(`${filename}-${index}.pdf`, report.value, { binary: true });
                    });

                    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });
                    await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                        context,
                        await uploadedFile.key,
                        `Generated archive - ${reportName}`,
                        zipContent,
                        FileTimeToLive.Expire10Days,
                    );

                    await context.batch.logMessage('result', await uploadedFile.filename, {
                        data: {
                            downloadUrl: await uploadedFile.downloadUrl,
                            filename: await uploadedFile.filename,
                        },
                    });
                },
            )
        ).$.payload();
    }

    static async generateZipToS3(
        context: Context,
        reports: xtremUpload.nodes.UploadedFile[],
        reportName: string,
    ): Promise<xtremUpload.nodes.UploadedFile> {
        const timestamp = xtremDateTime.datetime.now().format(undefined, 'YYYY-MM-DD-HH-mm-ss');
        const filename = Report._generateReportFilename(reportName, timestamp);

        const tempPath = fsp.join(os.tmpdir(), kebabCase(reportName), timestamp);
        const tempZipDir = fsp.join(tempPath, 'zip');
        const tempFilesDir = fsp.join(tempPath, 'reports');

        const zipFilename = fsp.join(tempZipDir, `${filename}.zip`);

        try {
            await fs.mkdir(tempPath, { recursive: true });
            await fs.mkdir(tempFilesDir, { recursive: true });
            await fs.mkdir(tempZipDir, { recursive: true });

            await Promise.all(reports.map(report => this.writeReport(context, tempFilesDir, report)));

            const output = createWriteStream(zipFilename);
            const archive = archiver('zip');
            archive.pipe(output);
            archive.directory(tempFilesDir, false);
            await archive.finalize();

            const zipContent = await fs.readFile(zipFilename);

            const result = await xtremReporting.nodes.Report.createUploadedFile(context, {
                kind: 'upload',
                objectKey: `print-output/${filename}.zip`,
                filename: `${filename}.zip`,
                reportName,
                timestamp,
                isArchive: true,
            });

            await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                context,
                await result.key,
                `Generated archive - ${reportName}`,
                zipContent,
                FileTimeToLive.Expire10Days,
            );
            return result;
        } finally {
            if (await fs.stat(tempPath)) await fs.rm(tempPath, { recursive: true });
        }
    }

    @decorators.mutation<typeof Report, 'createOrUpdateReport'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    _id: {
                        name: '_id',
                        type: 'string',
                    },
                    name: {
                        name: 'name',
                        type: 'string',
                    },
                    templateId: {
                        name: 'templateId',
                        type: 'string',
                    },
                    templateName: {
                        name: 'templateName',
                        type: 'string',
                    },
                    description: {
                        name: 'description',
                        type: 'string',
                    },
                    parentPackage: {
                        name: 'parentPackage',
                        type: 'string',
                    },
                    externalHtmlTemplate: {
                        name: 'externalHtmlTemplate',
                        type: 'textStream',
                    },
                    dataSource: {
                        name: 'dataSource',
                        type: 'reference',
                        node: () => xtremMetadata.nodes.MetaNodeFactory,
                    },
                    selectedProperties: {
                        name: 'selectedProperties',
                        type: 'string',
                    },
                    parameters: {
                        name: 'parameters',
                        type: 'string',
                    },
                    filters: {
                        name: 'filters',
                        type: 'string',
                    },
                    content: {
                        name: 'content',
                        type: 'string',
                    },
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: false,
                    },
                    reportSettings: {
                        type: 'object',
                        name: 'reportSettings',
                        properties: {
                            locale: {
                                name: 'locale',
                                type: 'string',
                                isMandatory: false,
                            },
                            paperFormat: {
                                name: 'paperFormat',
                                type: 'string',
                                isMandatory: false,
                            },
                            pageOrientation: {
                                name: 'pageOrientation',
                                type: 'string',
                                isMandatory: false,
                            },
                            leftMarginCm: {
                                name: 'leftMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            rightMarginCm: {
                                name: 'rightMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            topMarginCm: {
                                name: 'topMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            bottomMarginCm: {
                                name: 'bottomMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            isDefaultHeaderFooter: {
                                name: 'isDefaultHeaderFooter',
                                type: 'boolean',
                                isMandatory: false,
                            },
                        },
                    },
                    printingType: {
                        name: 'printingType',
                        type: 'enum',
                        dataType: () => xtremReporting.enums.printingTypeDataType,
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                reportId: {
                    name: 'reportId',
                    type: 'string',
                },
                reportTemplateId: {
                    name: 'reportTemplateId',
                    type: 'string',
                },
            },
        },
    })
    static async createOrUpdateReport(
        context: Context,
        data: xtremReporting.functions.ReportCreationInput,
    ): Promise<{ reportId: string; reportTemplateId: string }> {
        const {
            _id,
            name,
            templateId,
            templateName,
            description,
            parentPackage,
            externalHtmlTemplate,
            dataSource,
            selectedProperties,
            content,
            filters,
            parameters,
            variables,
            printingType,
        } = data;
        const {
            locale,
            paperFormat,
            pageOrientation,
            leftMarginCm,
            rightMarginCm,
            topMarginCm,
            bottomMarginCm,
            isDefaultHeaderFooter,
        } = data.reportSettings;

        logger.debug(
            () => `Start createOrUpdate _id:${_id} name:${name} templateId:${templateId} templateName:${templateName}`,
        );
        let report: Report | null = null;
        const reportData = {
            name,
            description,
            parentPackage,
            ...(variables ? { variables: JSON.parse(variables) } : {}),
            printingType,
        };
        if (_id) {
            report = await context.read(xtremReporting.nodes.Report, { _id }, { forUpdate: true });

            await report.$.set(reportData);
        } else {
            report = await context.create(xtremReporting.nodes.Report, reportData);
        }
        await report.$.save();
        let reportTemplate: xtremReporting.nodes.ReportTemplate | null = null;
        if (templateId) {
            reportTemplate = await context.tryRead(
                xtremReporting.nodes.ReportTemplate,
                { _id: templateId },
                { forUpdate: true },
            );
        }

        const reportTemplateName = Report.getTemplateName(templateName, await reportTemplate?.name, await report.name);
        logger.debug(() => `Creating report template ${reportTemplateName}`);

        const templateData = {
            report,
            isFactory: false,
            name: camelCase(reportTemplateName),
            isExpertDocument: false,
            externalHtmlTemplate,
            reportWizard: {
                id: camelCase(reportTemplateName),
                name: reportTemplateName,
                description,
                dataSource,
                selectedProperties: JSON.parse(selectedProperties),
                content: JSON.parse(content),
                filters: JSON.parse(filters),
                parameters: JSON.parse(parameters),
            },
            baseLocale: locale,
            defaultPaperFormat: paperFormat,
            defaultPageOrientation: pageOrientation,
            defaultTopMargin: topMarginCm,
            defaultBottomMargin: bottomMarginCm,
            defaultLeftMargin: leftMarginCm,
            defaultRightMargin: rightMarginCm,
            isDefaultHeaderFooter,
            footerHtmlTemplate: isDefaultHeaderFooter ? new TextStream(defaultHtmlFooter()) : new TextStream(''),
            headerHtmlTemplate: isDefaultHeaderFooter
                ? new TextStream(defaultHtmlHeader(await report.name))
                : new TextStream(''),
        };

        if (reportTemplate) {
            await reportTemplate.$.set(templateData);
        } else {
            reportTemplate = await context.create(xtremReporting.nodes.ReportTemplate, templateData);
        }

        await reportTemplate.$.save();

        await logger.debugAsync(async () => `Report active template ${await report?.activeTemplate}`);
        if (!(await report?.activeTemplate)) {
            logger.debug(() => `Update active template ${reportTemplate?._id}`);
            await report.$.set({ activeTemplate: reportTemplate });
            await report.$.save();
            await logger.debugAsync(async () => `Updated active template ${await report?.activeTemplate}`);
        }

        return { reportId: `${report._id}`, reportTemplateId: `${reportTemplate._id}` };
    }

    static async writeReport(
        context: Context,
        tempFilesDir: string,
        report: xtremUpload.nodes.UploadedFile,
    ): Promise<void> {
        const downloadUrlString = await report.downloadUrl;

        // we should log the error, but for now there is a weird issue with loading that makes the process crash
        if (!report.key || !report.filename || !downloadUrlString) {
            return;
        }
        const downloadUrl = new URL(downloadUrlString);
        const encodedTarget = downloadUrl.searchParams.get('t');
        if (!encodedTarget) {
            return;
        }
        const targetLocation = await FileStorageManager.getTargetDownloadUrl(context, encodedTarget);
        const response = await axios.get(targetLocation, { responseType: 'stream' });
        await fs.writeFile(fsp.join(tempFilesDir, `${report.filename}`), response.data);
    }

    static getTemplateName(
        templateName: string | undefined,
        reportTemplateName: string | undefined,
        reportName: string,
    ): string {
        if (templateName && templateName !== '') {
            return templateName;
        }
        if (reportTemplateName && reportTemplateName !== '') {
            return reportTemplateName;
        }
        if (reportName && reportName !== '') {
            return reportName;
        }
        return '';
    }

    /**
     * Generates a unique report filename
     * @param reportName
     * @param timestamp
     * @returns
     */
    private static _generateReportFilename(reportName: string, timestamp: string): string {
        return `${kebabCase(reportName)}-${timestamp}-${nanoid(8)}`;
    }
}
