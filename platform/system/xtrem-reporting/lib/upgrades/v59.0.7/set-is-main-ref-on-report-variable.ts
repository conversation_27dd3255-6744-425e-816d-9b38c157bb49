import { CustomSqlAction } from '@sage/xtrem-system';

export const setIsMainReferenceOnReportVariables = new CustomSqlAction({
    description: '',
    body: async helper => {
        await helper.executeSql(`
            UPDATE ${helper.schemaName}.report_variable rv
            SET
                is_main_reference = true,
                is_mandatory = true
            WHERE rv.type = 'reference' ::${helper.schemaName}.meta_property_type_enum
            AND (
                SELECT count(*)
                FROM ${helper.schemaName}.report_variable rv2
                WHERE
                rv2._tenant_id = rv._tenant_id
                AND rv2.report = rv.report
                AND rv2.type = 'reference' ::${helper.schemaName}.meta_property_type_enum
            ) = 1;
        `);
    },
});
