declare module '@sage/xtrem-chatbot-api-partial' {
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface ChatbotConversation extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        title: string;
        messages: string;
        inputTokensUsed: integer;
        outputTokensUsed: integer;
    }
    export interface ChatbotConversationInput extends ClientNodeInput {
        user?: integer | string;
        title?: string;
        messages?: string;
        inputTokensUsed?: integer | string;
        outputTokensUsed?: integer | string;
    }
    export interface ChatbotConversationBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        title: string;
        messages: any;
        inputTokensUsed: integer;
        outputTokensUsed: integer;
    }
    export interface ChatbotConversation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ChatbotConversation$Lookups {
        user: QueryOperation<User>;
    }
    export interface ChatbotConversation$Operations {
        query: QueryOperation<ChatbotConversation>;
        read: ReadOperation<ChatbotConversation>;
        aggregate: {
            read: AggregateReadOperation<ChatbotConversation>;
            query: AggregateQueryOperation<ChatbotConversation>;
        };
        create: CreateOperation<ChatbotConversationInput, ChatbotConversation>;
        getDuplicate: GetDuplicateOperation<ChatbotConversation>;
        update: UpdateOperation<ChatbotConversationInput, ChatbotConversation>;
        updateById: UpdateByIdOperation<ChatbotConversationInput, ChatbotConversation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ChatbotConversation$AsyncOperations;
        lookups(dataOrId: string | { data: ChatbotConversationInput }): ChatbotConversation$Lookups;
        getDefaults: GetDefaultsOperation<ChatbotConversation>;
    }
    export interface ChatbotTokenUsage extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        usageDate: string;
        inputTokensUsed: integer;
        outputTokensUsed: integer;
        totalTokensUsed: integer;
    }
    export interface ChatbotTokenUsageInput extends ClientNodeInput {
        user?: integer | string;
        usageDate?: string;
        inputTokensUsed?: integer | string;
        outputTokensUsed?: integer | string;
        totalTokensUsed?: integer | string;
    }
    export interface ChatbotTokenUsageBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        usageDate: string;
        inputTokensUsed: integer;
        outputTokensUsed: integer;
        totalTokensUsed: integer;
    }
    export interface ChatbotTokenUsage$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ChatbotTokenUsage$Lookups {
        user: QueryOperation<User>;
    }
    export interface ChatbotTokenUsage$Operations {
        query: QueryOperation<ChatbotTokenUsage>;
        read: ReadOperation<ChatbotTokenUsage>;
        aggregate: {
            read: AggregateReadOperation<ChatbotTokenUsage>;
            query: AggregateQueryOperation<ChatbotTokenUsage>;
        };
        create: CreateOperation<ChatbotTokenUsageInput, ChatbotTokenUsage>;
        getDuplicate: GetDuplicateOperation<ChatbotTokenUsage>;
        update: UpdateOperation<ChatbotTokenUsageInput, ChatbotTokenUsage>;
        updateById: UpdateByIdOperation<ChatbotTokenUsageInput, ChatbotTokenUsage>;
        asyncOperations: ChatbotTokenUsage$AsyncOperations;
        lookups(dataOrId: string | { data: ChatbotTokenUsageInput }): ChatbotTokenUsage$Lookups;
        getDefaults: GetDefaultsOperation<ChatbotTokenUsage>;
    }
    export interface ChatbotModel extends ClientNode {
        id: string;
        provider: string;
        modelId: string;
        inputCost: string;
        outputCost: string;
    }
    export interface ChatbotModelInput extends ClientNodeInput {
        id?: string;
        provider?: string;
        modelId?: string;
        inputCost?: decimal | string;
        outputCost?: decimal | string;
    }
    export interface ChatbotModelBinding extends ClientNode {
        id: string;
        provider: string;
        modelId: string;
        inputCost: string;
        outputCost: string;
    }
    export interface ChatbotModel$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ChatbotModel$Operations {
        query: QueryOperation<ChatbotModel>;
        read: ReadOperation<ChatbotModel>;
        aggregate: {
            read: AggregateReadOperation<ChatbotModel>;
            query: AggregateQueryOperation<ChatbotModel>;
        };
        create: CreateOperation<ChatbotModelInput, ChatbotModel>;
        getDuplicate: GetDuplicateOperation<ChatbotModel>;
        update: UpdateOperation<ChatbotModelInput, ChatbotModel>;
        updateById: UpdateByIdOperation<ChatbotModelInput, ChatbotModel>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ChatbotModel$AsyncOperations;
        getDefaults: GetDefaultsOperation<ChatbotModel>;
    }
    export interface ChatbotSubscription extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        model: ChatbotModel;
        monthlyBudget: string;
        numberOfUsers: integer;
    }
    export interface ChatbotSubscriptionInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        model?: integer | string;
        monthlyBudget?: decimal | string;
        numberOfUsers?: integer | string;
    }
    export interface ChatbotSubscriptionBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        model: ChatbotModel;
        monthlyBudget: string;
        numberOfUsers: integer;
    }
    export interface ChatbotSubscription$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ChatbotSubscription$Lookups {
        _vendor: QueryOperation<SysVendor>;
        model: QueryOperation<ChatbotModel>;
    }
    export interface ChatbotSubscription$Operations {
        query: QueryOperation<ChatbotSubscription>;
        read: ReadOperation<ChatbotSubscription>;
        aggregate: {
            read: AggregateReadOperation<ChatbotSubscription>;
            query: AggregateQueryOperation<ChatbotSubscription>;
        };
        update: UpdateOperation<ChatbotSubscriptionInput, ChatbotSubscription>;
        updateById: UpdateByIdOperation<ChatbotSubscriptionInput, ChatbotSubscription>;
        asyncOperations: ChatbotSubscription$AsyncOperations;
        lookups(dataOrId: string | { data: ChatbotSubscriptionInput }): ChatbotSubscription$Lookups;
        getDefaults: GetDefaultsOperation<ChatbotSubscription>;
    }
    export interface UserBlock extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        blockStart: string;
        blockEnd: string;
        reason: string;
        escalationLevel: integer;
        isActive: boolean;
    }
    export interface UserBlockInput extends ClientNodeInput {
        user?: integer | string;
        blockStart?: string;
        blockEnd?: string;
        reason?: string;
        escalationLevel?: integer | string;
        isActive?: boolean | string;
    }
    export interface UserBlockBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        blockStart: string;
        blockEnd: string;
        reason: string;
        escalationLevel: integer;
        isActive: boolean;
    }
    export interface UserBlock$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UserBlock$Lookups {
        user: QueryOperation<User>;
    }
    export interface UserBlock$Operations {
        query: QueryOperation<UserBlock>;
        read: ReadOperation<UserBlock>;
        aggregate: {
            read: AggregateReadOperation<UserBlock>;
            query: AggregateQueryOperation<UserBlock>;
        };
        create: CreateOperation<UserBlockInput, UserBlock>;
        getDuplicate: GetDuplicateOperation<UserBlock>;
        update: UpdateOperation<UserBlockInput, UserBlock>;
        updateById: UpdateByIdOperation<UserBlockInput, UserBlock>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: UserBlock$AsyncOperations;
        lookups(dataOrId: string | { data: UserBlockInput }): UserBlock$Lookups;
        getDefaults: GetDefaultsOperation<UserBlock>;
    }
    export interface Package {
        '@sage/xtrem-chatbot/ChatbotConversation': ChatbotConversation$Operations;
        '@sage/xtrem-chatbot/ChatbotTokenUsage': ChatbotTokenUsage$Operations;
        '@sage/xtrem-chatbot/ChatbotModel': ChatbotModel$Operations;
        '@sage/xtrem-chatbot/ChatbotSubscription': ChatbotSubscription$Operations;
        '@sage/xtrem-chatbot/UserBlock': UserBlock$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-chatbot-api' {
    export type * from '@sage/xtrem-chatbot-api-partial';
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-chatbot-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-chatbot-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-chatbot-api';
    export interface GraphApi extends GraphApiExtension {}
}
