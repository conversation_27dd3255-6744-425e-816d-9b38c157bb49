/* eslint-disable class-methods-use-this */
import { Context, Datetime, Logger } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import { ConfigurationError } from '@sage/xtrem-shared';
import Bottleneck from 'bottleneck';
import * as xtremChatbot from '../index';

export const logger = Logger.getLogger(__filename, 'rate-limiter');

interface UserQuota {
    dailyBudget: number;
    usedBudget: number;
    lastReset: DateValue;
    limiter: Bottleneck;
}

interface BlockInfo {
    isBlocked: boolean;
    blockEnd?: Datetime;
    reason?: string;
    escalationLevel?: number;
}

export class RateLimitManager {
    private userQuotas = new Map<string, UserQuota>();

    private readonly blockDurationsInMinutes = [15, 60, 2 * 60, 4 * 60, 8 * 60];

    async checkUserBlock(context: Context, userId: string): Promise<BlockInfo> {
        const activeBlock = await context
            .query<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
                filter: {
                    user: userId,
                    isActive: true,
                },
            })
            .at(0);

        if (!activeBlock) {
            return { isBlocked: false };
        }

        const now = Datetime.now();
        const blockEnd = await activeBlock.blockEnd;

        if (now.value > blockEnd.value) {
            // Block expired, deactivate it
            await activeBlock.$.set({ isActive: false });
            await activeBlock.$.save();
            return { isBlocked: false };
        }

        return {
            isBlocked: true,
            blockEnd,
            reason: await activeBlock.reason,
            escalationLevel: await activeBlock.escalationLevel,
        };
    }

    async createUserBlock(context: Context, userId: string, reason: string, escalationLevel: number): Promise<void> {
        const blockDurationInMinutes =
            this.blockDurationsInMinutes[Math.min(escalationLevel, this.blockDurationsInMinutes.length - 1)];
        const now = Datetime.now();
        const blockEnd = now.addMinutes(blockDurationInMinutes);

        // Deactivate any existing blocks
        const existingBlocks = context.query<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
            filter: { user: userId, isActive: true },
        });

        for (const block of await existingBlocks.toArray()) {
            await block.$.set({ isActive: false });
            await block.$.save();
        }

        // Create new block
        const newBlock = await context.create<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
            user: userId,
            blockStart: now,
            blockEnd,
            reason,
            escalationLevel,
            isActive: true,
        });

        await newBlock.$.save();
    }

    async getActiveSubscription(context: Context): Promise<xtremChatbot.nodes.ChatbotSubscription | null> {
        const subscription = await context
            .query<xtremChatbot.nodes.ChatbotSubscription>(xtremChatbot.nodes.ChatbotSubscription, {})
            .toArray();

        // For now, we deliver a single setup record in the subscription table and we only allow update
        // So it should be a singleton.
        if (subscription.length !== 1)
            throw new ConfigurationError(`expected 1 subscription, got ${subscription.length}`);

        return (await subscription[0]?.isActive) ? subscription[0] : null;
    }

    async calculateTokenCost(
        context: Context,
        modelId: string,
        inputTokens: number,
        outputTokens: number,
    ): Promise<number> {
        const model = await context.read<xtremChatbot.nodes.ChatbotModel>(xtremChatbot.nodes.ChatbotModel, {
            modelId,
        });

        const inputCostPerMillion = await model.inputCost;
        const outputCostPerMillion = await model.outputCost;

        const inputCost = (inputTokens / 1_000_000) * inputCostPerMillion;
        const outputCost = (outputTokens / 1_000_000) * outputCostPerMillion;

        return inputCost + outputCost;
    }

    async getUserDailyQuota(context: Context, userId: string): Promise<UserQuota> {
        const today = DateValue.today();
        const userKey = `${userId}-${today.toString()}`;

        let quota = this.userQuotas.get(userKey);

        if (!quota || this.isNewDay(quota.lastReset)) {
            const subscription = await this.getActiveSubscription(context);
            if (!subscription) {
                throw new Error('No active subscription found');
            }

            const monthlyBudget = await subscription.monthlyBudget;
            const numberOfUsers = await subscription.numberOfUsers;
            const daysInMonth = today.endOfMonth().day;
            const dailyBudget = monthlyBudget / daysInMonth / numberOfUsers;

            quota = {
                dailyBudget,
                usedBudget: 0,
                lastReset: today,
                limiter: new Bottleneck({
                    reservoir: Math.floor(dailyBudget * 1000), // Convert to 1/10 cents for integer math
                    reservoirRefreshAmount: Math.floor(dailyBudget * 1000),
                    reservoirRefreshInterval: 24 * 60 * 60 * 1000, // 24 hours
                }),
            };

            this.userQuotas.set(userKey, quota);
        }

        return quota;
    }

    private isNewDay(lastReset: DateValue): boolean {
        const today = DateValue.today();
        return lastReset.value !== today.value;
    }

    async canProcessRequest(
        context: Context,
        userId: string,
        modelId: string,
        inputTokens: number,
        outputTokens: number,
    ): Promise<{ allowed: boolean; reason?: string; blockInfo?: BlockInfo }> {
        // Check if user is blocked
        const blockInfo = await this.checkUserBlock(context, userId);
        if (blockInfo.isBlocked) {
            return {
                allowed: false,
                reason: `User blocked until ${blockInfo.blockEnd}: ${blockInfo.reason}`,
                blockInfo,
            };
        }

        // Calculate cost
        const cost = await this.calculateTokenCost(context, modelId, inputTokens, outputTokens);

        // Get user quota
        const quota = await this.getUserDailyQuota(context, userId);

        // Check if request would exceed daily budget
        if (quota.usedBudget + cost > quota.dailyBudget) {
            // Get escalation level for this user
            const recentBlocks = await context
                .query<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
                    filter: { user: userId },
                    orderBy: { _createStamp: -1 },
                    first: 1,
                })
                .toArray();

            const lastBlock = recentBlocks[0];
            const escalationLevel = lastBlock ? (await lastBlock.escalationLevel) + 1 : 0;

            // Create block
            await this.createUserBlock(
                context,
                userId,
                `Budget exceeded: $${cost.toFixed(4)} would exceed daily budget of $${quota.dailyBudget.toFixed(4)}`,
                escalationLevel,
            );

            return {
                allowed: false,
                reason: `Daily budget exceeded. Request cost: $${cost.toFixed(4)}, Daily budget: $${quota.dailyBudget.toFixed(4)}, Used: $${quota.usedBudget.toFixed(4)}`,
            };
        }

        // Try to reserve the budget using bottleneck
        try {
            await quota.limiter.schedule(() => Promise.resolve());
            quota.usedBudget += cost;
            return { allowed: true };
        } catch (error) {
            return {
                allowed: false,
                reason: `Rate limit exceeded: ${error.message}`,
            };
        }
    }

    async resetDailyQuotas(): Promise<void> {
        const today = DateValue.today();

        // Clear old quotas
        for (const [key, quota] of this.userQuotas.entries()) {
            if (!key.endsWith(`-${today.toString()}`)) {
                await quota.limiter.stop();
                this.userQuotas.delete(key);
            }
        }
    }
}
