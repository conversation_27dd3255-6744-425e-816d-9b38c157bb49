/* eslint-disable class-methods-use-this */
import * as xtremAuthorization from '@sage/xtrem-authorization';
import { ChatConversation, Context, Datetime, DateValue, ChatbotManager as IChatbotManager } from '@sage/xtrem-core';
import * as xtremChatbot from '../index';
import { logger, RateLimitManager } from './rate-limit-manager';

export class ChatbotManager implements IChatbotManager {
    private rateLimitManager = new RateLimitManager();

    async canUseChatbot(context: Context): Promise<boolean> {
        const serviceEnabled = await context.isServiceOptionEnabled(xtremChatbot.serviceOptions.chatbotServiceOption);
        if (!serviceEnabled) {
            return false;
        }

        // Check if user is currently blocked
        const blockInfo = await this.rateLimitManager.checkUserBlock(context, String(context.userId));
        return !blockInfo.isBlocked;
    }

    getUserGroupNames(context: Context): Promise<string[]> {
        return context
            .query(xtremAuthorization.nodes.UserGroup, { filter: { user: context.userId } })
            .map(async userGroup => (await userGroup.group).name)
            .toArray();
    }

    async getConversationHistory(context: Context): Promise<ChatConversation[]> {
        return context
            .query<xtremChatbot.nodes.ChatbotConversation>(xtremChatbot.nodes.ChatbotConversation, {
                first: 10,
                orderBy: { _createStamp: -1 },
                filter: { user: context.userId },
            })
            .map(async conversation => ({
                _createStamp: await conversation._createStamp,
                id: String(conversation._id),
                title: await conversation.title,
                messages: await conversation.messages,
                inputTokensUsed: await conversation.inputTokensUsed,
                outputTokensUsed: await conversation.outputTokensUsed,
            }))
            .toArray();
    }

    private async updateTokenUsage(context: Context, inputTokens: number = 0, outputTokens: number = 0): Promise<void> {
        const today = DateValue.today();

        const existingUsage = await context.tryRead<xtremChatbot.nodes.ChatbotTokenUsage>(
            xtremChatbot.nodes.ChatbotTokenUsage,
            {
                user: context.userId,
                usageDate: today,
            },
            { forUpdate: true },
        );

        if (existingUsage) {
            await existingUsage.$.set({
                inputTokensUsed: (await existingUsage.inputTokensUsed) + inputTokens,
                outputTokensUsed: (await existingUsage.outputTokensUsed) + outputTokens,
                totalTokensUsed: (await existingUsage.totalTokensUsed) + inputTokens + outputTokens,
            });

            await existingUsage.$.save();
        } else {
            const newUsage = await context.create<xtremChatbot.nodes.ChatbotTokenUsage>(
                xtremChatbot.nodes.ChatbotTokenUsage,
                {
                    user: context.userId,
                    usageDate: today,
                    inputTokensUsed: inputTokens,
                    outputTokensUsed: outputTokens,
                    totalTokensUsed: inputTokens + outputTokens,
                },
            );

            await newUsage.$.save();
        }
    }

    async createOrUpdateConversation(
        context: Context,
        conversation: Partial<ChatConversation>,
        modelId?: string,
    ): Promise<string> {
        const inputTokens = conversation.inputTokensUsed ?? 0;
        const outputTokens = conversation.outputTokensUsed ?? 0;

        // Apply rate limiting only for new conversations or when tokens are being added
        if ((inputTokens > 0 || outputTokens > 0) && modelId) {
            const rateLimitResult = await this.rateLimitManager.canProcessRequest(
                context,
                String(context.userId),
                modelId,
                inputTokens,
                outputTokens,
            );
            logger.debug(() => `Rate limit result: ${JSON.stringify(rateLimitResult)}`);

            if (!rateLimitResult.allowed) {
                throw new Error(`Rate limit exceeded: ${rateLimitResult.reason}`);
            }
        }

        if (conversation.id) {
            const savedConversation = await context.read<xtremChatbot.nodes.ChatbotConversation>(
                xtremChatbot.nodes.ChatbotConversation,
                {
                    _id: conversation.id,
                    user: context.userId,
                },
                { forUpdate: true },
            );

            await savedConversation.$.set({
                messages: conversation.messages ?? (await savedConversation.messages),
                outputTokensUsed: (await savedConversation.outputTokensUsed) + outputTokens,
                inputTokensUsed: (await savedConversation.inputTokensUsed) + inputTokens,
            });

            await savedConversation.$.save();

            await this.updateTokenUsage(context, inputTokens, outputTokens);

            return String(savedConversation._id);
        }
        const newConversation = await context.create<xtremChatbot.nodes.ChatbotConversation>(
            xtremChatbot.nodes.ChatbotConversation,
            {
                user: context.userId,
                messages: conversation.messages,
                title: conversation.title,
                inputTokensUsed: inputTokens,
                outputTokensUsed: outputTokens,
            },
        );

        await newConversation.$.save();

        await this.updateTokenUsage(context, inputTokens, outputTokens);

        return String(newConversation._id);
    }

    async getUserBlockInfo(
        context: Context,
    ): Promise<{ isBlocked: boolean; blockEnd?: Datetime; reason?: string; escalationLevel?: number }> {
        return this.rateLimitManager.checkUserBlock(context, String(context.userId));
    }

    async resetDailyQuotas(): Promise<void> {
        await this.rateLimitManager.resetDailyQuotas();
    }
}
