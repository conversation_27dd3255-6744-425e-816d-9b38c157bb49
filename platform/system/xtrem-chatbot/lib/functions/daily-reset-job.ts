/* eslint-disable class-methods-use-this */
import { Context, Datetime, DateValue, Time } from '@sage/xtrem-core';
import * as xtremChatbot from '../index';
import { logger, RateLimitManager } from './rate-limit-manager';

export class DailyResetJob {
    private rateLimitManager = new RateLimitManager();

    async runDailyReset(context: Context): Promise<void> {
        logger.info('Starting daily quota reset job...');

        try {
            // Reset in-memory quotas
            await this.rateLimitManager.resetDailyQuotas();

            // Clean up expired blocks
            await this.cleanupExpiredBlocks(context);

            // Reset escalation levels that are older than 24 hours
            await this.resetEscalationLevels(context);

            logger.info('Daily quota reset job completed successfully');
        } catch (error) {
            logger.error('Error during daily reset job:', error);
            throw error;
        }
    }

    private async cleanupExpiredBlocks(context: Context): Promise<void> {
        const expiredBlocks = context.query<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
            filter: { isActive: true },
        });

        const now = Datetime.now();
        let cleanupCount = 0;

        for (const block of await expiredBlocks.toArray()) {
            const blockEnd = await block.blockEnd;
            if (now.value > blockEnd.value) {
                await block.$.set({ isActive: false });
                await block.$.save();
                cleanupCount += 1;
            }
        }

        logger.info(`Cleaned up ${cleanupCount} expired blocks`);
    }

    private async resetEscalationLevels(context: Context): Promise<void> {
        const yesterday = DateValue.today()
            .addDays(-1)
            .at(Time.make(0, 0, 0));

        // Find users who haven't been blocked in the last 24 hours
        // and reset their escalation by creating inactive "reset" entries
        const recentBlocks = context.query<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
            filter: {
                isActive: false,
            },
            orderBy: { _createStamp: -1 },
        });

        const usersToReset = new Set<string>();

        for (const block of await recentBlocks.toArray()) {
            const blockStart = await block.blockStart;
            const userId = String(block.user);

            // If the last block for this user was more than 24 hours ago, reset escalation
            if (blockStart.value < yesterday.value && !usersToReset.has(userId)) {
                usersToReset.add(userId);

                // Create a reset marker
                const resetStartValue = yesterday;
                const resetEndValue = yesterday;

                const resetBlock = await context.create<xtremChatbot.nodes.UserBlock>(xtremChatbot.nodes.UserBlock, {
                    user: Number(userId),
                    blockStart: resetStartValue,
                    blockEnd: resetEndValue,
                    reason: 'escalation_reset',
                    escalationLevel: 0,
                    isActive: false,
                });

                await resetBlock.$.save();
            }
        }

        logger.info(`Reset escalation levels for ${usersToReset.size} users`);
    }
}
