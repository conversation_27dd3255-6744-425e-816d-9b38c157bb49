import { Datetime, decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<UserBlock>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    indexes: [{ orderBy: { user: 1, blockStart: -1 }, isUnique: false }],
})
export class UserBlock extends Node {
    @decorators.referenceProperty<UserBlock, 'user'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.datetimeProperty<UserBlock, 'blockStart'>({
        isStored: true,
        isPublished: true,
    })
    readonly blockStart: Promise<Datetime>;

    @decorators.datetimeProperty<UserBlock, 'blockEnd'>({
        isStored: true,
        isPublished: true,
    })
    readonly blockEnd: Promise<Datetime>;

    @decorators.stringProperty<UserBlock, 'reason'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly reason: Promise<string>;

    @decorators.integerProperty<UserBlock, 'escalationLevel'>({
        isStored: true,
        isPublished: true,
    })
    readonly escalationLevel: Promise<integer>;

    @decorators.booleanProperty<UserBlock, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;
}
