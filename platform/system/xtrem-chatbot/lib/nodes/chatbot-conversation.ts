import { decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<ChatbotConversation>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { _id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class ChatbotConversation extends Node {
    @decorators.referenceProperty<ChatbotConversation, 'user'>({
        isStored: true,
        isPublished: true,
        allowedInUniqueIndex: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.stringProperty<ChatbotConversation, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.title,
    })
    readonly title: Promise<string>;

    @decorators.jsonProperty<ChatbotConversation, 'messages'>({
        isStored: true,
        isPublished: true,
    })
    readonly messages: Promise<any[]>;

    @decorators.integerProperty<ChatbotConversation, 'inputTokensUsed'>({
        isStored: true,
        isPublished: true,
    })
    readonly inputTokensUsed: Promise<integer>;

    @decorators.integerProperty<ChatbotConversation, 'outputTokensUsed'>({
        isStored: true,
        isPublished: true,
    })
    readonly outputTokensUsed: Promise<integer>;
}
