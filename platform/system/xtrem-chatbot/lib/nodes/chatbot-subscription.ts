import { decimal, decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremChatbot from '../../index';

@decorators.node<ChatbotSubscription>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    indexes: [{ orderBy: { model: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class ChatbotSubscription extends Node {
    @decorators.booleanProperty<ChatbotSubscription, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.referenceProperty<ChatbotSubscription, 'model'>({
        isStored: true,
        isPublished: true,
        node: () => xtremChatbot.nodes.ChatbotModel,
    })
    readonly model: Reference<xtremChatbot.nodes.ChatbotModel>;

    @decorators.decimalProperty<ChatbotSubscription, 'monthlyBudget'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.quantityDataType,
    })
    readonly monthlyBudget: Promise<decimal>;

    @decorators.integerProperty<ChatbotSubscription, 'numberOfUsers'>({
        isStored: true,
        isPublished: true,
    })
    readonly numberOfUsers: Promise<integer>;
}
