import { decimal, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<ChatbotModel>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isSharedByAllTenants: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class ChatbotModel extends Node {
    @decorators.stringProperty<ChatbotModel, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<ChatbotModel, 'provider'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly provider: Promise<string>;

    @decorators.stringProperty<ChatbotModel, 'modelId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly modelId: Promise<string>;

    /** Cost of input tokens, in USD per million */
    @decorators.decimalProperty<ChatbotModel, 'inputCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.quantityDataType,
    })
    readonly inputCost: Promise<decimal>;

    /** Cost of output tokens, in USD per million */
    @decorators.decimalProperty<ChatbotModel, 'outputCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.quantityDataType,
    })
    readonly outputCost: Promise<decimal>;
}
