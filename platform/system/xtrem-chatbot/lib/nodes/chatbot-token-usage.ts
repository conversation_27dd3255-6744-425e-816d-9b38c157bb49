import { DateValue, decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<ChatbotTokenUsage>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDelete: false,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    indexes: [{ orderBy: { user: 1, usageDate: 1 }, isUnique: true, isNaturalKey: true }],
})
export class ChatbotTokenUsage extends Node {
    @decorators.referenceProperty<ChatbotTokenUsage, 'user'>({
        isStored: true,
        isPublished: true,
        allowedInUniqueIndex: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.dateProperty<ChatbotTokenUsage, 'usageDate'>({
        isStored: true,
        isPublished: true,
        allowedInUniqueIndex: true,
    })
    readonly usageDate: Promise<DateValue>;

    @decorators.integerProperty<ChatbotTokenUsage, 'inputTokensUsed'>({
        isStored: true,
        isPublished: true,
    })
    readonly inputTokensUsed: Promise<integer>;

    @decorators.integerProperty<ChatbotTokenUsage, 'outputTokensUsed'>({
        isStored: true,
        isPublished: true,
    })
    readonly outputTokensUsed: Promise<integer>;

    @decorators.integerProperty<ChatbotTokenUsage, 'totalTokensUsed'>({
        isStored: true,
        isPublished: true,
    })
    readonly totalTokensUsed: Promise<integer>;
}
