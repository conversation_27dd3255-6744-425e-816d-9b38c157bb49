import { CoreHooks } from '@sage/xtrem-core';
import * as activities from './activities';
import { ChatbotManager } from './functions/chatbot-manager';
import * as nodes from './nodes';
import * as serviceOptions from './service-options';

export { activities, nodes, serviceOptions };

export function updateContext() {
    CoreHooks.createChatbotManager = () => new ChatbotManager();
}

updateContext();
