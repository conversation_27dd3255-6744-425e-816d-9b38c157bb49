import { Activity } from '@sage/xtrem-core';
import { ChatbotModel, ChatbotSubscription, ChatbotTokenUsage } from '../nodes';
import { ChatbotConversation } from '../nodes/chatbot-conversation';

export const chatbot = new Activity({
    description: 'Chatbot',
    node: () => ChatbotConversation,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'delete'],
                on: [() => ChatbotConversation],
            },
            {
                operations: ['read'],
                on: [() => ChatbotTokenUsage, () => ChatbotModel],
            },
            {
                operations: ['read', 'update'],
                on: [() => ChatbotSubscription],
            },
        ],
    },
});
