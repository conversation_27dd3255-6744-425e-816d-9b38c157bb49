# Xtrem Chatbot Package - Development Guide

## Overview
The `@sage/xtrem-chatbot` package is a platform-level chatbot system within the Sage Xtrem ERP ecosystem. It provides conversation management, user permission controls, and token tracking capabilities through a Node.js/TypeScript backend architecture.

## Architecture

### Core Components
- **Package Structure**: Workspace-dependent package in the Xtrem monorepo platform layer
- **Framework**: Built on the Xtrem Core framework with TypeScript
- **Storage**: SQL-based storage for conversation persistence
- **Authorization**: Integrates with `@sage/xtrem-authorization` for user/group permissions
- **API**: GraphQL-based API with auto-generated type definitions

### Key Files and Structure
```
/Users/<USER>/dev/xtrem/platform/system/xtrem-chatbot/
├── lib/
│   ├── activities/chatbot-activity.ts     # Activity definition with permissions
│   ├── functions/chatbot-manager.ts       # Core business logic
│   ├── nodes/chatbot-conversation.ts      # Data model for conversations
│   ├── service-options/                   # Feature toggle configuration
│   └── i18n/                             # Internationalization files
├── api/api.d.ts                          # Generated GraphQL API types
├── data/layers/test/                     # Test data configuration
├── routing.json                          # Message routing configuration
└── index.ts                              # Package entry point
```

### Data Model
**ChatbotConversation Node**:
- `user`: Reference to system user
- `messages`: JSON array of conversation messages
- `inputTokensUsed`: Integer tracking input token consumption
- `outputTokensUsed`: Integer tracking output token consumption
- Indexed by user ID for efficient queries
- Supports full CRUD operations

### Business Logic
**ChatbotManager** implements the core interface:
- `canUseChatbot()`: Checks service option activation
- `getUserGroupNames()`: Retrieves user authorization groups
- `getConversationHistory()`: Fetches recent conversations (last 10)
- `createOrUpdateConversation()`: Manages conversation persistence

## Development Commands

### Build & Compilation
```bash
# Standard TypeScript compilation
pnpm build              # Equivalent to: xtrem compile

# API client generation only
pnpm build:api          # Equivalent to: xtrem build --only-api-client

# Production binary build
pnpm build:binary       # Equivalent to: xtrem compile --binary --prod

# Cached build (uses Turbo)
pnpm build:cache        # Uses XTREM_CONCURRENCY (default: 10)

# Clean build artifacts
pnpm clean              # Removes build/ directory
```

### Testing
```bash
# Unit and GraphQL tests with test data layers
pnpm test               # xtrem test --noTimeout --unit --graphql --layers=test

# CI testing with coverage
pnpm test:ci            # Includes coverage reporting

# QA Cucumber tests
pnpm qa:cucumber        # Integration tests with QA layers
pnpm qa:cucumber:browser # Browser-based integration tests
```

### Data Management
```bash
# Test data layer management
pnpm extract:test:data  # Extract test layer data
pnpm load:test:data     # Load setup and test layers

# Development server
pnpm start              # xtrem start
```

### Code Quality
```bash
# ESLint with Xtrem rules
pnpm lint               # xtrem lint

# Filename linting
pnpm lint:filename      # Checks file naming conventions

# SonarQube analysis
pnpm sonarqube:scanner  # Runs tests + SonarQube scanner
```

## Configuration Details

### TypeScript Configuration
- Extends `../../tsconfig-base.json` (monorepo base config)
- Output: `build/` directory
- Includes: Source files, API types, test files
- Excludes: Client-side files (pages, widgets, stickers)
- References: Platform dependencies (authorization, core, system, UI)

### ESLint Configuration
- Uses ESLint v9+ with flat config structure
- Custom Xtrem plugin (`@sage/eslint-plugin-xtrem`)
- Separate configs for artifact files vs source files
- TypeScript parser with project-specific tsconfig
- Temporarily disabled rules during ESLint v9 migration

### Testing Framework
- **Mocha** for test runner
- **Chai** for assertions (including chai-as-promised)
- **Sinon** for mocking
- **C8** for coverage reporting (text-summary, clover, json, lcov)
- Coverage includes `lib/**/*.ts`, excludes `test/**/*` and `data/**/*`

## Service Integration

### Service Options
The chatbot feature is controlled by `chatbotServiceOption`:
- Status: "workInProgress"
- Can be enabled/disabled per tenant
- Test data activates the service (`sys-service-option-state.csv`)

### Permissions & Activities
- **Read Permission**: View conversations
- **Manage Permission**: Full CRUD operations
- **Activity**: "Chatbot Activity" with node-based operation grants
- Integrates with Xtrem authorization system

### Message Routing
Configured in `routing.json` for async operations:
- Topic: `ChatbotConversation/asyncExport/start`
- Queue: `import-export`
- Source: `chatbot-conversation.ts`

## Development Notes

### Xtrem CLI Tool
The project uses a custom CLI tool (`xtrem`) for all operations:
- `xtrem compile`: TypeScript compilation with platform optimizations
- `xtrem test`: Test runner with layer-based data management
- `xtrem start`: Development server
- `xtrem layers`: Data layer management
- `xtrem lint`: Code quality checks

### Workspace Dependencies
All Xtrem packages use `workspace:*` for internal dependencies, indicating this is part of a larger monorepo with packages in:
- `platform/system/` (authorization, system)
- `back-end/` (core)
- `shared/` (utilities)
- `front-end/` (UI components)

### Internationalization
Supports 11 languages with structured key naming:
- Base translations in `base.json`
- Empty templates for localization
- Keys follow pattern: `@sage/xtrem-chatbot/{component}__{item}__{property}`

### API Generation
- Auto-generated GraphQL API types in `api/api.d.ts`
- Extends multiple platform APIs (authorization, communication, system)
- Module augmentation pattern for type safety

## Key Implementation Patterns

1. **Node-based Architecture**: Data models extend Xtrem Node class
2. **Decorator-driven Configuration**: Uses decorators for properties and storage
3. **Context-based Operations**: All operations use Xtrem Context for security
4. **Reference Integrity**: Strong typing with reference relationships
5. **Layer-based Data**: Test/setup data managed through layers
6. **Service Option Control**: Feature flags through service options