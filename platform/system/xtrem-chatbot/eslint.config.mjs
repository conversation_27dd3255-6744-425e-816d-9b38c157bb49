// eslint.config.mjs (ESLint v9+)
import { defineConfig, globalIgnores } from 'eslint/config';
import globals from 'globals';
import baseConfig from '../../eslint-base.config.mjs';
import xtremPlugin from '@sage/eslint-plugin-xtrem';

const artifactFiles = [
    'lib/pages/**/*.{ts,tsx}',
    'lib/widgets/**/*.{ts,tsx}',
    'lib/page-extensions/**/*.{ts,tsx}',
    'lib/page-fragments/**/*.{ts,tsx}',
    'lib/stickers/**/*.{ts,tsx}',
    'api/api.d.ts',
    'lib/menu-items/**/*.{ts,tsx}',
    'lib/client-functions/**/*.{ts,tsx}',
    'lib/shared-functions/**/*.{ts,tsx}',
];

export default defineConfig([
    globalIgnores(['build/**', 'tmp/**', 'node_modules/**', '**/coverage', '**/i18n/*.json', 'data/**']),
    {
        files: artifactFiles,
        languageOptions: {
            parserOptions: {
                tsconfigRootDir: import.meta.dirname,
                project: 'tsconfig-artifacts.json',
            },
        },
    },
    {
        files: ['**/*.{ts,tsx,mts,cts}'],
        ignores: artifactFiles,
        languageOptions: {
            parser: '@typescript-eslint/parser',
            parserOptions: {
                tsconfigRootDir: import.meta.dirname,
                project: 'tsconfig.json',
            },
        },
    },
    {},
    ...baseConfig,
    {
        plugins: {
            '@sage/xtrem': xtremPlugin,
        },
    },
    ...xtremPlugin.configs.recommended,

    {
        files: ['**/*.js'],
        rules: {
            '@typescript-eslint/no-require-imports': 'off',
        },
    },
    {
        files: ['**/*.{ts,tsx,mts,cts}'],
        rules: {
            // To review: ESLint V9 migration (all the following are temporarily disabled)
            '@typescript-eslint/no-unnecessary-type-assertion': 'off',
            '@typescript-eslint/no-unsafe-enum-comparison': 'off',
            '@typescript-eslint/no-wrapper-object-types': 'off',
            '@typescript-eslint/require-await': 'off',
        },
    },
    // config envs
    {
        languageOptions: {
            globals: { ...globals.browser, ...globals.node },
        },
    },
]);
